// Utility to detect if developer tools are open

export const isDevToolsOpen = (): boolean => {
  // Method 1: Check window dimensions
  const widthThreshold = window.outerWidth - window.innerWidth > 160;
  const heightThreshold = window.outerHeight - window.innerHeight > 160;

  if (widthThreshold || heightThreshold) {
    return true;
  }

  // Method 2: Console timing attack
  // This method works because when dev tools are open, console.log is much slower
  const startTime = performance.now();
  console.log();
  const endTime = performance.now();

  // If it takes more than 5ms, dev tools are likely open
  if (endTime - startTime > 5) {
    return true;
  }

  return false;
};

export const addDevToolsWarning = () => {
  if (typeof window === "undefined") return;

  // Override console methods
  const originalConsoleLog = console.log;
  const originalConsoleWarn = console.warn;
  const originalConsoleError = console.error;
  const originalConsoleInfo = console.info;

  const warningMessage =
    "This website is protected. Developer tools usage is monitored.";

  console.log = function (...args) {
    originalConsoleWarn.call(console, warningMessage);
    originalConsoleLog.call(console, ...args);
  };

  console.warn = function (...args) {
    originalConsoleWarn.call(console, warningMessage);
    originalConsoleWarn.call(console, ...args);
  };

  console.error = function (...args) {
    originalConsoleWarn.call(console, warningMessage);
    originalConsoleError.call(console, ...args);
  };

  console.info = function (...args) {
    originalConsoleWarn.call(console, warningMessage);
    originalConsoleInfo.call(console, ...args);
  };

  // Add a warning message when users try to leave the page with developer tools open
  window.addEventListener("beforeunload", function (e: BeforeUnloadEvent) {
    if (isDevToolsOpen()) {
      // Modern approach for beforeunload
      e.preventDefault();
      // For older browsers that still use returnValue
      // Note: This is deprecated but still needed for some browsers
      // Using type assertion with a more specific interface
      (e as { returnValue: string }).returnValue = warningMessage;
      return warningMessage;
    }
  });
};
