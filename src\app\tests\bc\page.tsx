"use client";

import { useState, useEffect, useRef } from "react";
import {
  MicrophoneIcon,
  ClipboardDocumentListIcon,
  PlayIcon,
  StopIcon,
  ArrowRightIcon,
  ArrowLeftIcon,
  CheckCircleIcon,
  XCircleIcon,
  UserIcon,
  NewspaperIcon,
  GlobeAsiaAustraliaIcon,
  ScaleIcon,
  AcademicCapIcon,
  ClockIcon,
  HomeIcon,
  TrophyIcon,
  ShieldCheckIcon,
  BuildingLibraryIcon,
  UserCircleIcon,
  CalculatorIcon,
  MapIcon,
  BanknotesIcon,
  PuzzlePieceIcon,
} from "@heroicons/react/24/outline";
import Link from "next/link";
import NavigationButton from "@/components/NavigationButton";
import { questionCategories } from "./questionData";
import AudioRecordingManager from "@/components/AudioRecordingManager";

// Recording interface
interface Recording {
  url: string;
  duration: number;
  blob: Blob;
}

// Define the category cards with colors and styling
const categoryCards = [
  {
    id: "personal",
    name: "Personal Introduction & Etiquette",
    description:
      "Questions about yourself, your background, and proper military etiquette",
    icon: UserIcon,
    count:
      questionCategories.find((c) => c.id === "personal")?.questions.length ||
      0,
    color: "bg-blue-100 border-blue-300",
    buttonColor: "bg-blue-600 hover:bg-blue-500",
  },
  {
    id: "current-affairs",
    name: "Current Affairs & News",
    description:
      "Questions about recent events, politics, and their relevance to military operations",
    icon: NewspaperIcon,
    count:
      questionCategories.find((c) => c.id === "current-affairs")?.questions
        .length || 0,
    color: "bg-green-100 border-green-300",
    buttonColor: "bg-green-600 hover:bg-green-500",
  },

  {
    id: "family",
    name: "Family & Lifestyle",
    description:
      "Questions about your family background, values, and how they align with military service",
    icon: HomeIcon,
    count:
      questionCategories.find((c) => c.id === "family")?.questions.length || 0,
    color: "bg-pink-100 border-pink-300",
    buttonColor: "bg-pink-600 hover:bg-pink-500",
  },
  {
    id: "academics",
    name: "Academics & Education",
    description:
      "Questions about your Computer Engineering education and how it applies to military roles",
    icon: AcademicCapIcon,
    count:
      questionCategories.find((c) => c.id === "academics")?.questions.length ||
      0,
    color: "bg-indigo-100 border-indigo-300",
    buttonColor: "bg-indigo-600 hover:bg-indigo-500",
  },
  {
    id: "sports",
    name: "Sports & Physical Fitness",
    description:
      "Questions about your physical fitness routine, sports experience, and teamwork",
    icon: TrophyIcon,
    count:
      questionCategories.find((c) => c.id === "sports")?.questions.length || 0,
    color: "bg-emerald-100 border-emerald-300",
    buttonColor: "bg-emerald-600 hover:bg-emerald-500",
  },
  {
    id: "army",
    name: "Nepal Army-Specific",
    description:
      "Questions about the Nepal Army's history, values, and how your technical skills can contribute",
    icon: ShieldCheckIcon,
    count:
      questionCategories.find((c) => c.id === "army")?.questions.length || 0,
    color: "bg-red-100 border-red-300",
    buttonColor: "bg-red-600 hover:bg-red-500",
  },
  {
    id: "government",
    name: "Government & Politics",
    description:
      "Questions about Nepal's political system, governance, and civilian-military relations",
    icon: BuildingLibraryIcon,
    count:
      questionCategories.find((c) => c.id === "government")?.questions.length ||
      0,
    color: "bg-orange-100 border-orange-300",
    buttonColor: "bg-orange-600 hover:bg-orange-500",
  },
  {
    id: "leadership",
    name: "Leadership & Situational",
    description:
      "Scenarios testing your leadership abilities, problem-solving, and decision-making skills",
    icon: UserCircleIcon,
    count:
      questionCategories.find((c) => c.id === "leadership")?.questions.length ||
      0,
    color: "bg-cyan-100 border-cyan-300",
    buttonColor: "bg-cyan-600 hover:bg-cyan-500",
  },
  {
    id: "general",
    name: "General Knowledge",
    description:
      "Questions testing your awareness of Nepal's geography, history, and infrastructure",
    icon: GlobeAsiaAustraliaIcon,
    count:
      questionCategories.find((c) => c.id === "general")?.questions.length || 0,
    color: "bg-purple-100 border-purple-300",
    buttonColor: "bg-purple-600 hover:bg-purple-500",
  },
  {
    id: "math",
    name: "Mathematics & Analytical",
    description:
      "Questions testing your analytical thinking, problem-solving, and mathematical skills",
    icon: CalculatorIcon,
    count:
      questionCategories.find((c) => c.id === "math")?.questions.length || 0,
    color: "bg-blue-100 border-blue-300",
    buttonColor: "bg-blue-600 hover:bg-blue-500",
  },
  {
    id: "ethical",
    name: "Ethical & Hypothetical",
    description:
      "Scenarios testing your judgment, ethics, and decision-making in military contexts",
    icon: ScaleIcon,
    count:
      questionCategories.find((c) => c.id === "ethical")?.questions.length || 0,
    color: "bg-amber-100 border-amber-300",
    buttonColor: "bg-amber-600 hover:bg-amber-500",
  },
  {
    id: "geography",
    name: "Geography & Culture",
    description:
      "Questions about Nepal's geography, cultural diversity, and their significance",
    icon: MapIcon,
    count:
      questionCategories.find((c) => c.id === "geography")?.questions.length ||
      0,
    color: "bg-lime-100 border-lime-300",
    buttonColor: "bg-lime-600 hover:bg-lime-500",
  },
  {
    id: "economics",
    name: "Economics",
    description:
      "Questions about Nepal's economy, market prices, and economic sectors",
    icon: BanknotesIcon,
    count:
      questionCategories.find((c) => c.id === "economics")?.questions.length ||
      0,
    color: "bg-green-100 border-green-300",
    buttonColor: "bg-green-600 hover:bg-green-500",
  },
  {
    id: "miscellaneous",
    name: "Miscellaneous",
    description:
      "Additional questions about your career goals, technical expertise, and military interests",
    icon: PuzzlePieceIcon,
    count:
      questionCategories.find((c) => c.id === "miscellaneous")?.questions
        .length || 0,
    color: "bg-violet-100 border-violet-300",
    buttonColor: "bg-violet-600 hover:bg-violet-500",
  },
];

export default function BCPage() {
  // Practice state
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [isRecording, setIsRecording] = useState(false);
  const [recordings, setRecordings] = useState<Record<string, Recording>>({});
  const [isPracticeStarted, setIsPracticeStarted] = useState(false);
  const [isPracticeComplete, setIsPracticeComplete] = useState(false);
  const [audioStream, setAudioStream] = useState<MediaStream | null>(null);
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(
    null
  );

  // Recording time state
  const [recordingTime, setRecordingTime] = useState(0);
  const recordingTimerRef = useRef<NodeJS.Timeout | null>(null);
  const currentRecordingTimeRef = useRef(0);
  const audioChunksRef = useRef<BlobPart[]>([]);

  // Playback state
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentPlayingKey, setCurrentPlayingKey] = useState<string | null>(
    null
  );
  const audioPlayerRef = useRef<HTMLAudioElement | null>(null);

  // Function to start practice with a specific category
  const startPractice = (categoryId: string) => {
    // Scroll to top of the page before starting the test
    window.scrollTo({ top: 0, behavior: "smooth" });

    setSelectedCategory(categoryId);
    setCurrentQuestionIndex(0);
    setIsPracticeStarted(true);

    // Reset any previous practice state
    setIsPracticeComplete(false);
    setRecordings({});
  };

  // Function to start recording
  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      setAudioStream(stream);

      const recorder = new MediaRecorder(stream);
      setMediaRecorder(recorder);

      audioChunksRef.current = [];

      recorder.ondataavailable = (e) => {
        audioChunksRef.current.push(e.data);
      };

      recorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, {
          type: "audio/webm",
        });
        const audioUrl = URL.createObjectURL(audioBlob);

        // Save recording for current question with the final recording time
        const finalDuration = currentRecordingTimeRef.current;

        setRecordings((prev) => ({
          ...prev,
          [`${selectedCategory}-${currentQuestionIndex}`]: {
            url: audioUrl,
            duration: finalDuration,
            blob: audioBlob,
          },
        }));
      };

      recorder.start();

      // Just set isRecording to true - the useEffect will handle the timer
      setIsRecording(true);
    } catch (error) {
      console.error("Error accessing microphone:", error);
      alert("Could not access your microphone. Please check your permissions.");
    }
  };

  // Function to stop recording
  const stopRecording = () => {
    if (mediaRecorder && mediaRecorder.state !== "inactive") {
      mediaRecorder.stop();
    }

    if (audioStream) {
      audioStream.getTracks().forEach((track) => track.stop());
      setAudioStream(null);
    }

    setIsRecording(false);
    setMediaRecorder(null);
  };

  // Function to play recording
  const playRecording = (recordingKey: string) => {
    // If already playing this recording, stop it
    if (isPlaying && currentPlayingKey === recordingKey) {
      stopPlayback();
      return;
    }

    // If another recording is playing, stop it first
    if (isPlaying && audioPlayerRef.current) {
      stopPlayback();
    }

    // Create and play the new audio
    const audio = new Audio(recordings[recordingKey]?.url);
    audioPlayerRef.current = audio;

    // Set up event listeners
    audio.addEventListener("ended", () => {
      setIsPlaying(false);
      setCurrentPlayingKey(null);
      audioPlayerRef.current = null;
    });

    audio.play().catch((error) => {
      console.error("Error playing audio:", error);
      setIsPlaying(false);
      setCurrentPlayingKey(null);
      audioPlayerRef.current = null;
    });

    setIsPlaying(true);
    setCurrentPlayingKey(recordingKey);
  };

  // Function to stop playback
  const stopPlayback = () => {
    if (audioPlayerRef.current) {
      audioPlayerRef.current.pause();
      audioPlayerRef.current = null;
    }
    setIsPlaying(false);
    setCurrentPlayingKey(null);
  };

  // Function to go to next question
  const nextQuestion = () => {
    // Stop and save any ongoing recording first
    if (isRecording) {
      stopRecording();
    }

    // Stop any playing audio
    if (isPlaying) {
      stopPlayback();
    }

    // Scroll to top of the page
    window.scrollTo({ top: 0, behavior: "smooth" });

    const category = questionCategories.find((c) => c.id === selectedCategory);
    if (!category) return;

    // If we're at the last question, complete the practice
    if (currentQuestionIndex >= category.questions.length - 1) {
      setIsPracticeComplete(true);
    } else {
      // Otherwise, go to the next question
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    }
  };

  // Function to go to previous question
  const prevQuestion = () => {
    // Stop and save any ongoing recording first
    if (isRecording) {
      stopRecording();
    }

    // Stop any playing audio
    if (isPlaying) {
      stopPlayback();
    }

    // Scroll to top of the page
    window.scrollTo({ top: 0, behavior: "smooth" });

    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    }
  };

  // Function to format time (seconds to MM:SS)
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  };

  // This useEffect has been replaced by the one below
  // Keeping this comment as a placeholder to maintain line numbers

  // Clean up resources when component unmounts
  useEffect(() => {
    return () => {
      // Stop any recording
      if (mediaRecorder && mediaRecorder.state !== "inactive") {
        mediaRecorder.stop();
      }

      // Stop any audio stream
      if (audioStream) {
        audioStream.getTracks().forEach((track) => track.stop());
      }

      // Clear any timers
      if (recordingTimerRef.current) {
        clearInterval(recordingTimerRef.current);
      }

      // Stop any audio playback
      if (audioPlayerRef.current) {
        audioPlayerRef.current.pause();
        audioPlayerRef.current = null;
      }
    };
  }, [mediaRecorder, audioStream]);

  // Handle recording timer
  useEffect(() => {
    if (isRecording) {
      // Reset timer
      setRecordingTime(0);
      currentRecordingTimeRef.current = 0;

      // Start timer
      recordingTimerRef.current = setInterval(() => {
        setRecordingTime((prevTime) => {
          const newTime = prevTime + 1;
          currentRecordingTimeRef.current = newTime;
          return newTime;
        });
      }, 1000);
    } else {
      // Clear timer
      if (recordingTimerRef.current) {
        clearInterval(recordingTimerRef.current);
        recordingTimerRef.current = null;
      }
    }

    return () => {
      if (recordingTimerRef.current) {
        clearInterval(recordingTimerRef.current);
      }
    };
  }, [isRecording]);

  // Render the main content based on practice state
  const renderContent = () => {
    // If practice is not started, show the main BC page
    if (!isPracticeStarted) {
      return renderMainPage();
    }

    // If practice is complete, show the results page
    if (isPracticeComplete) {
      return renderResultsPage();
    }

    // Otherwise, show the practice page
    return renderPracticePage();
  };

  // Main page content
  const renderMainPage = () => {
    return (
      <>
        <div className="mx-auto max-w-3xl text-center">
          <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
            Board Conference (BC)
          </h2>
          <p className="mt-6 text-lg leading-8 text-gray-600 max-w-3xl mx-auto">
            The Board Conference is a comprehensive speaking test that evaluates
            your knowledge, personality, and suitability for a military career.
            You&apos;ll face questions from multiple officers across various
            topics to assess your communication skills, critical thinking, and
            ability to perform under pressure.
          </p>
          <div className="mt-6 flex justify-center">
            <div className="inline-flex items-center rounded-md bg-blue-50 px-6 py-4 text-lg font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10">
              <MicrophoneIcon className="h-6 w-6 mr-3 text-blue-500" />
              <span>
                This is a speaking test, you can expect 50-100 questions with
                many board members
              </span>
            </div>
          </div>

          {/* Practice Button */}
          <div className="mt-8">
            <button
              onClick={() => {
                document.getElementById("question-categories")?.scrollIntoView({
                  behavior: "smooth",
                  block: "start",
                });
              }}
              className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 shadow-sm"
            >
              <PlayIcon className="h-5 w-5 mr-2" />
              Start Practice Session
            </button>
            <p className="mt-2 text-sm text-gray-500">
              Select a question category below to practice with voice recording
            </p>
          </div>
        </div>

        <div id="question-categories" className="mx-auto mt-16 max-w-5xl">
          <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">
            Question Categories
          </h3>

          <div className="mb-8 bg-blue-50 rounded-xl border border-blue-200 p-6">
            <p className="text-center text-gray-700">
              The Board Conference covers 14 different question categories with
              over 100 potential questions. Practice each category to prepare
              for your interview.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {categoryCards.map((category) => (
              <div
                key={category.id}
                className={`rounded-xl border-2 p-5 ${category.color} transition-transform hover:scale-105`}
              >
                <div className="flex items-center space-x-3 mb-3">
                  <div className="p-2 rounded-full bg-white">
                    <category.icon className="h-6 w-6 text-gray-800" />
                  </div>
                  <h3 className="text-lg font-bold leading-7 text-gray-900">
                    {category.name}
                  </h3>
                </div>
                <p className="text-sm leading-5 text-gray-700 mb-3 min-h-[60px]">
                  {category.description}
                </p>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-semibold">
                    {category.count} Questions
                  </span>
                  <button
                    onClick={() => startPractice(category.id)}
                    className={`inline-flex items-center rounded-md ${category.buttonColor} px-3 py-1.5 text-xs font-semibold text-white shadow-sm focus-visible:outline-2 focus-visible:outline-offset-2`}
                  >
                    Practice
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </>
    );
  };

  // Practice page content
  const renderPracticePage = () => {
    const category = questionCategories.find((c) => c.id === selectedCategory);

    // Safety check - if category is undefined, show a fallback
    if (!category) {
      return (
        <div className="mx-auto max-w-3xl text-center">
          <p>Something went wrong. Please go back and try again.</p>
          <button
            onClick={() => {
              setIsPracticeStarted(false);
              setSelectedCategory(null);
            }}
            className="mt-4 inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            Back to Categories
          </button>
        </div>
      );
    }

    const currentQuestion = category.questions[currentQuestionIndex];
    const recordingKey = `${selectedCategory}-${currentQuestionIndex}`;
    const hasRecording = recordings[recordingKey] !== undefined;

    return (
      <div className="mx-auto max-w-3xl">
        {/* Header */}
        <div className="mb-8 flex justify-between items-center">
          <button
            onClick={() => {
              // Confirm before exiting if there are recordings
              if (Object.keys(recordings).length > 0) {
                if (
                  window.confirm(
                    "Are you sure you want to exit? Your recordings will be lost."
                  )
                ) {
                  // Scroll to top of the page
                  window.scrollTo({ top: 0, behavior: "smooth" });
                  // Reset all state variables
                  setIsPracticeStarted(false);
                  setSelectedCategory(null);
                  setRecordings({});
                  setIsPracticeComplete(false);
                  setCurrentQuestionIndex(0);
                }
              } else {
                // Scroll to top of the page
                window.scrollTo({ top: 0, behavior: "smooth" });
                // Reset all state variables
                setIsPracticeStarted(false);
                setSelectedCategory(null);
                setIsPracticeComplete(false);
                setCurrentQuestionIndex(0);
              }
            }}
            className="inline-flex items-center text-gray-600 hover:text-gray-900"
          >
            <ArrowLeftIcon className="h-5 w-5 mr-2" />
            Back to Categories
          </button>
          <div className="text-sm font-medium text-gray-500">
            Question {currentQuestionIndex + 1} of {category.questions.length}
          </div>
        </div>

        {/* Category Title */}
        <div className="mb-6 flex items-center space-x-3">
          <div className="p-2 rounded-full bg-indigo-100">
            <category.icon className="h-6 w-6 text-indigo-600" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900">{category.name}</h2>
        </div>

        {/* Question Card */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
          <h3 className="text-xl font-semibold text-gray-900 mb-4">
            {currentQuestion}
          </h3>

          {/* Recording Controls */}
          <div className="mt-8">
            {!isRecording && !hasRecording && (
              <button
                onClick={startRecording}
                className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
              >
                <MicrophoneIcon className="h-5 w-5 mr-2" />
                Start Recording
              </button>
            )}

            {isRecording && (
              <div className="flex flex-col items-center space-y-4">
                <div className="text-lg font-semibold text-red-600 animate-pulse">
                  Recording... {formatTime(recordingTime)}
                </div>
                <button
                  onClick={stopRecording}
                  className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700"
                >
                  <StopIcon className="h-5 w-5 mr-2" />
                  Stop Recording
                </button>
              </div>
            )}

            {hasRecording && !isRecording && (
              <div className="flex flex-col items-center space-y-4">
                <div className="text-sm text-gray-500">
                  Recording: {formatTime(recordings[recordingKey].duration)}
                </div>
                <div className="flex space-x-4">
                  <button
                    onClick={() => playRecording(recordingKey)}
                    className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
                  >
                    {isPlaying && currentPlayingKey === recordingKey ? (
                      <>
                        <StopIcon className="h-5 w-5 mr-2" />
                        Stop
                      </>
                    ) : (
                      <>
                        <PlayIcon className="h-5 w-5 mr-2" />
                        Play
                      </>
                    )}
                  </button>
                  <button
                    onClick={() => {
                      // Remove the recording
                      setRecordings((prev) => {
                        const newRecordings = { ...prev };
                        delete newRecordings[recordingKey];
                        return newRecordings;
                      });
                    }}
                    className="inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                  >
                    <XCircleIcon className="h-5 w-5 mr-2" />
                    Delete
                  </button>
                  <button
                    onClick={startRecording}
                    className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
                  >
                    <MicrophoneIcon className="h-5 w-5 mr-2" />
                    Re-record
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Navigation Buttons */}
        <div className="flex justify-between">
          <button
            onClick={prevQuestion}
            disabled={currentQuestionIndex === 0}
            className={`inline-flex items-center px-4 py-2 border ${
              currentQuestionIndex === 0
                ? "border-gray-200 text-gray-400 cursor-not-allowed"
                : "border-gray-300 text-gray-700 hover:bg-gray-50"
            } text-sm font-medium rounded-md bg-white`}
          >
            <ArrowLeftIcon className="h-5 w-5 mr-2" />
            Previous Question
          </button>
          <button
            onClick={nextQuestion}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
          >
            {currentQuestionIndex === category.questions.length - 1
              ? "Finish Practice"
              : "Next Question"}
            <ArrowRightIcon className="h-5 w-5 ml-2" />
          </button>
        </div>
      </div>
    );
  };

  // Results page content
  const renderResultsPage = () => {
    const category = questionCategories.find((c) => c.id === selectedCategory);

    // Safety check - if category is undefined, show a fallback
    if (!category) {
      return (
        <div className="mx-auto max-w-3xl text-center">
          <p>Something went wrong. Please go back and try again.</p>
          <button
            onClick={() => {
              setIsPracticeStarted(false);
              setSelectedCategory(null);
            }}
            className="mt-4 inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            Back to Categories
          </button>
        </div>
      );
    }

    return (
      <div className="mx-auto max-w-3xl">
        <div className="text-center">
          <div className="inline-flex items-center justify-center p-2 bg-green-100 rounded-full mb-4">
            <CheckCircleIcon className="h-8 w-8 text-green-600" />
          </div>
          <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
            Practice Complete!
          </h2>
          <p className="mt-4 text-lg text-gray-600">
            You&apos;ve completed the {category.name} practice session
          </p>
        </div>

        <div className="mt-16">
          <h3 className="text-xl font-bold text-gray-900 mb-6">
            Your Recorded Responses
          </h3>

          <div className="space-y-6">
            {category.questions.map((question, index) => {
              const recordingKey = `${selectedCategory}-${index}`;
              const hasRecording = !!recordings[recordingKey];

              return (
                <div
                  key={index}
                  className="bg-white rounded-xl shadow-sm border border-gray-200 p-6"
                >
                  <h4 className="text-lg font-semibold text-gray-900 mb-4">
                    {index + 1}. {question}
                  </h4>

                  {hasRecording ? (
                    <div className="flex items-center space-x-4">
                      <div className="text-sm text-gray-500">
                        Recording:{" "}
                        {formatTime(recordings[recordingKey].duration)}
                      </div>
                      <button
                        onClick={() => playRecording(recordingKey)}
                        className="inline-flex items-center justify-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
                      >
                        {isPlaying && currentPlayingKey === recordingKey ? (
                          <>
                            <StopIcon className="h-4 w-4 mr-1" />
                            Stop
                          </>
                        ) : (
                          <>
                            <PlayIcon className="h-4 w-4 mr-1" />
                            Play
                          </>
                        )}
                      </button>
                    </div>
                  ) : (
                    <div className="text-sm text-gray-500 italic">
                      No recording for this question
                    </div>
                  )}
                </div>
              );
            })}
          </div>

          {/* Audio Recording Manager */}
          <div className="mt-12">
            <AudioRecordingManager
              recordings={recordings}
              testType="BC"
              isPlaying={isPlaying}
              currentPlayingKey={currentPlayingKey}
              onPlay={playRecording}
              onStop={stopPlayback}
              className="bg-gray-50 rounded-xl p-6 border border-gray-200"
            />
          </div>

          <div className="mt-8 flex justify-between">
            <button
              onClick={() => {
                // Stop any playing audio
                stopPlayback();
                // Scroll to top of the page
                window.scrollTo({ top: 0, behavior: "smooth" });
                // Reset all state variables
                setIsPracticeStarted(false);
                setSelectedCategory(null);
                setRecordings({});
                setIsPracticeComplete(false);
                setCurrentQuestionIndex(0);
              }}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Choose Another Category
            </button>

            <button
              onClick={() => {
                // Stop any playing audio
                stopPlayback();
                // Scroll to top of the page
                window.scrollTo({ top: 0, behavior: "smooth" });
                // Reset to first question
                setCurrentQuestionIndex(0);
                // Clear all recordings
                setRecordings({});
                // Exit results screen
                setIsPracticeComplete(false);
              }}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
            >
              Practice Again
              <ArrowRightIcon className="h-4 w-4 ml-2" />
            </button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="bg-white py-24 sm:py-32">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">{renderContent()}</div>
    </div>
  );
}
