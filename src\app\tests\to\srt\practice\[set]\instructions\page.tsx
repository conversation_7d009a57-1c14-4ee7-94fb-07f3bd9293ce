"use client";

import { useRouter } from "next/navigation";
import NavigationButton from "@/components/NavigationButton";
import SimpleButton from "@/components/SimpleButton";
import { ArrowLeftIcon } from "@heroicons/react/24/outline";
import { use } from "react";

export default function SRTInstructions({
  params,
}: {
  params: Promise<{ set: string }>;
}) {
  const router = useRouter();
  const { set } = use(params);
  const setName = decodeURIComponent(set);

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="mx-auto max-w-3xl px-6">
        <SimpleButton
          onClick={() => router.back()}
          className="mb-8 flex items-center text-gray-600 hover:text-gray-900"
        >
          <ArrowLeftIcon className="h-5 w-5 mr-2" />
          Back to Practice Sets
        </SimpleButton>

        <div className="bg-white rounded-2xl shadow-sm p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">
            SRT Practice {setName} - Instructions
          </h1>

          <div className="space-y-6">
            <div className="text-gray-500">
              <h2 className="text-xl font-semibold mb-4">Before You Begin:</h2>
              <div className="space-y-4">
                <p>
                  You will be presented with 60 different situations. For each
                  situation, you have 30 seconds to write your immediate
                  reaction. Focus on providing practical and realistic
                  solutions.
                </p>

                <div className="space-y-4">
                  <h3 className="font-semibold text-gray-900">
                    Important Points:
                  </h3>
                  <ul className="list-disc list-inside space-y-2">
                    <li>Read each situation carefully but quickly</li>
                    <li>Write your first practical thought</li>
                    <li>Focus on immediate action steps</li>
                    <li>Consider safety and ethical implications</li>
                    <li>Be decisive in your responses</li>
                  </ul>
                </div>

                <div className="bg-blue-50 p-4 rounded-lg">
                  <h3 className="font-semibold text-blue-800 mb-2">
                    Example Situation:
                  </h3>
                  <p className="text-blue-900 mb-2">
                    {`When you were in your friend's house, your friend got
                    struck by electric current. You would...`}
                  </p>
                  <p className="text-blue-700">
                    A good response would focus on immediate life-saving
                    actions: turning off power, using non-conductive material to
                    separate them from current, calling for medical help, etc.
                  </p>
                </div>

                <div className="bg-yellow-50 p-4 rounded-lg">
                  <h3 className="font-semibold text-yellow-800 mb-2">Note:</h3>
                  <p className="text-yellow-700">
                    This is a practice session. Take your time to understand the
                    format. The timer will start once you begin the test.
                  </p>
                </div>
              </div>
            </div>

            <div className="text-center mt-8">
              <SimpleButton
                onClick={() =>
                  router.push(`/tests/to/srt/practice/${set}/test`)
                }
                className="inline-flex justify-center rounded-md bg-blue-600 px-6 py-3 text-base font-semibold text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                Start Practice Test
              </SimpleButton>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
