import { jsPDF } from "jspdf";
import "jspdf-autotable";

// Helper function to add watermark and social links to all pages of a PDF
const addWatermark = (doc: jsPDF) => {
  const totalPages = doc.getNumberOfPages();

  // Save the current state
  const fontSize = doc.getFontSize();
  const textColor = doc.getTextColor();

  // Set watermark properties
  doc.setFontSize(10);
  doc.setTextColor(150, 150, 150); // Light gray color

  // Add watermark and links to each page
  for (let i = 1; i <= totalPages; i++) {
    doc.setPage(i);

    const pageWidth = doc.internal.pageSize.getWidth();
    const pageHeight = doc.internal.pageSize.getHeight();

    // Add a divider line
    doc.setDrawColor(200, 200, 200);
    doc.line(15, pageHeight - 15, pageWidth - 15, pageHeight - 15);

    // Add website name at the bottom right of the page
    doc.textWithLink(
      "balaramshiwakoti.com.np",
      pageWidth - 15,
      pageHeight - 10,
      {
        align: "right",
        url: "https://balaramshiwakoti.com.np",
      }
    );

    // Add Facebook link at the bottom left of the page
    doc.setTextColor(59, 89, 152); // Facebook blue color
    doc.setFontSize(12);
    doc.textWithLink("My Facebook", 15, pageHeight - 10, {
      url: "https://www.facebook.com/hercules.shiwakoti",
    });
    doc.setFontSize(10);

    // Add page number
    doc.setTextColor(150, 150, 150);
    doc.text(`Page ${i} of ${totalPages}`, 196, 10, { align: "right" });
  }

  // Restore the original state
  doc.setFontSize(fontSize);
  doc.setTextColor(textColor);

  return doc;
};

/**
 * Generate a PDF with SDT test responses
 */
export const generateSDTPDF = async (
  setName: string,
  responses: string[],
  questions: string[]
): Promise<Blob> => {
  // Create a new PDF document
  const doc = new jsPDF();

  // Add title
  doc.setFontSize(18);
  doc.text(`Personal Interview QnA - ${setName}`, 14, 20);

  // Add subtitle
  doc.setFontSize(12);
  doc.setTextColor(100, 100, 100);
  doc.text("Self Description Test (SDT) Responses", 14, 30);

  // Add date
  const today = new Date();
  const dateStr = today.toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
  doc.text(`Generated on: ${dateStr}`, 14, 40);

  // Add responses
  doc.setFontSize(16);
  doc.setTextColor(0, 0, 0);
  doc.text("Your Responses", 14, 55);

  // Add responses
  let yPos = 65;
  questions.forEach((question, index) => {
    // Check if we need a new page
    if (yPos > 260) {
      doc.addPage();
      yPos = 20;
    }

    // Add question number and text
    doc.setFontSize(11);
    doc.setTextColor(0, 0, 0);
    doc.text(`${index + 1}. ${question}`, 14, yPos);
    yPos += 8;

    // Add response
    doc.setFontSize(10);
    doc.setTextColor(80, 80, 80);

    const response = responses[index] || "Not answered";
    const responseLines = doc.splitTextToSize(response, 180);
    doc.text(responseLines, 20, yPos);

    yPos += responseLines.length * 6 + 10; // Add space after response
  });

  // Add watermark to all pages
  addWatermark(doc);

  // Return the PDF as a blob
  return doc.output("blob");
};
