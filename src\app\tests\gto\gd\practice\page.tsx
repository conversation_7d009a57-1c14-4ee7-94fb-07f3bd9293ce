"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import { useSearchPara<PERSON>, useRouter } from "next/navigation";
import {
  ArrowLeftIcon,
  UserGroupIcon,
  ClockIcon,
  MicrophoneIcon,
  StopIcon,
  PlayIcon,
  CheckIcon,
} from "@heroicons/react/24/outline";
import NavigationButton from "@/components/NavigationButton";
import SimpleButton from "@/components/SimpleButton";
import AudioRecordingManager from "@/components/AudioRecordingManager";

// Recording interface
interface Recording {
  url: string;
  duration: number;
  blob: Blob;
}

// All 32 GD Topics
const gdTopics = [
  {
    id: "ai-agriculture",
    title: "AI in Agriculture",
    topic:
      "How can Nepal integrate AI into its agricultural practices to boost productivity?",
    keyAngles: ["Precision farming", "Resource allocation", "Farmer training"],
  },
  {
    id: "space-disaster",
    title: "Space Technology for Disaster Management",
    topic:
      "Should Nepal invest in space technology for better disaster management?",
    keyAngles: [
      "Satellite imagery",
      "Early warning systems",
      "Cost implications",
    ],
  },
  {
    id: "digital-art-craft",
    title: "Digital Marketing for Traditional Arts",
    topic:
      "Can Nepal's traditional art and craft industries benefit from digital marketing strategies?",
    keyAngles: [
      "Global reach",
      "E-commerce platforms",
      "Preserving cultural heritage",
    ],
  },
  {
    id: "education-digital-age",
    title: "Education for Digital Age",
    topic:
      "Is Nepal's education system adequately preparing students for the digital age?",
    keyAngles: ["Curriculum reforms", "Teacher training", "Digital divide"],
  },
  {
    id: "hydropower-export",
    title: "Hydropower Energy Export",
    topic:
      "How can Nepal leverage its hydropower potential to become a major energy exporter?",
    keyAngles: [
      "Infrastructure development",
      "International partnerships",
      "Environmental impact",
    ],
  },
  {
    id: "smart-cities",
    title: "Smart Cities Development",
    topic:
      "Should Nepal prioritize the development of smart cities to improve urban living?",
    keyAngles: [
      "Sustainable urban planning",
      "Technology integration",
      "Citizen engagement",
    ],
  },
  {
    id: "vr-ar-tourism",
    title: "VR/AR in Tourism",
    topic:
      "Can Nepal's tourism industry benefit from virtual reality and augmented reality technologies?",
    keyAngles: [
      "Enhanced visitor experiences",
      "Marketing strategies",
      "Technological investment",
    ],
  },
  {
    id: "brain-drain",
    title: "Addressing Brain Drain",
    topic:
      "How can Nepal address the brain drain of its skilled professionals?",
    keyAngles: [
      "Job creation",
      "Incentives for return",
      "International collaboration",
    ],
  },
  {
    id: "climate-change",
    title: "Climate Change Mitigation",
    topic:
      "Should Nepal adopt a more aggressive stance on climate change mitigation?",
    keyAngles: [
      "Carbon emissions reduction",
      "Renewable energy adoption",
      "International agreements",
    ],
  },
  {
    id: "telemedicine",
    title: "Telemedicine in Healthcare",
    topic: "Can Nepal's healthcare system be improved through telemedicine?",
    keyAngles: [
      "Access to remote areas",
      "Doctor-patient interaction",
      "Technological challenges",
    ],
  },
  {
    id: "youth-entrepreneurship",
    title: "Youth Entrepreneurship",
    topic: "How can Nepal promote entrepreneurship among its youth?",
    keyAngles: [
      "Startup ecosystem",
      "Funding opportunities",
      "Educational support",
    ],
  },
  {
    id: "pharmaceutical-industry",
    title: "Pharmaceutical Industry Development",
    topic: "Should Nepal focus on developing its own pharmaceutical industry?",
    keyAngles: [
      "Self-reliance",
      "Quality control",
      "International competition",
    ],
  },
  {
    id: "cultural-festivals",
    title: "Cultural Festivals for Unity",
    topic:
      "Can Nepal's cultural festivals be used to promote national unity and tourism?",
    keyAngles: [
      "Event management",
      "Cultural preservation",
      "Economic benefits",
    ],
  },
  {
    id: "waste-management",
    title: "Waste Management Improvement",
    topic: "How can Nepal improve its waste management practices?",
    keyAngles: [
      "Recycling programs",
      "Public awareness",
      "Government policies",
    ],
  },
  {
    id: "electric-vehicles",
    title: "Electric Vehicle Infrastructure",
    topic: "Should Nepal invest in electric vehicle infrastructure?",
    keyAngles: [
      "Environmental benefits",
      "Economic feasibility",
      "Public adoption",
    ],
  },
  {
    id: "traditional-medicine",
    title: "Traditional Medicine Integration",
    topic:
      "Can Nepal's traditional medicine practices be integrated with modern healthcare?",
    keyAngles: [
      "Complementary treatments",
      "Research and development",
      "Patient acceptance",
    ],
  },
  {
    id: "cyber-defense",
    title: "Cyber Defense Capabilities",
    topic: "How can Nepal enhance its cyber defense capabilities?",
    keyAngles: [
      "Training programs",
      "International cooperation",
      "Technological upgrades",
    ],
  },
  {
    id: "biodiversity-conservation",
    title: "Biodiversity Conservation",
    topic: "Should Nepal prioritize the conservation of its biodiversity?",
    keyAngles: [
      "Ecological importance",
      "Economic benefits",
      "Conservation strategies",
    ],
  },
  {
    id: "sustainable-textile",
    title: "Sustainable Textile Industry",
    topic:
      "Can Nepal's textile industry compete globally with sustainable practices?",
    keyAngles: [
      "Eco-friendly materials",
      "Fair labor practices",
      "Market demand",
    ],
  },
  {
    id: "public-transportation",
    title: "Public Transportation System",
    topic: "How can Nepal improve its public transportation system?",
    keyAngles: [
      "Infrastructure development",
      "Public-private partnerships",
      "User convenience",
    ],
  },
  {
    id: "it-outsourcing",
    title: "IT Outsourcing Industry",
    topic: "Should Nepal focus on developing its IT outsourcing industry?",
    keyAngles: ["Skilled workforce", "Global demand", "Economic benefits"],
  },
  {
    id: "organic-farming",
    title: "Organic Farming Practices",
    topic:
      "Can Nepal's agricultural sector benefit from organic farming practices?",
    keyAngles: ["Soil health", "Market demand", "Certification processes"],
  },
  {
    id: "youth-unemployment",
    title: "Youth Unemployment",
    topic: "How can Nepal address the issue of youth unemployment?",
    keyAngles: [
      "Skill development programs",
      "Job creation",
      "Entrepreneurship support",
    ],
  },
  {
    id: "nuclear-energy",
    title: "Nuclear Energy Alternative",
    topic:
      "Should Nepal invest in nuclear energy as an alternative to hydropower?",
    keyAngles: [
      "Energy security",
      "Environmental concerns",
      "Public perception",
    ],
  },
  {
    id: "film-industry",
    title: "Film Industry Economic Growth",
    topic: "Can Nepal's film industry contribute to its economic growth?",
    keyAngles: [
      "Cultural promotion",
      "Job creation",
      "International recognition",
    ],
  },
  {
    id: "domestic-cybersecurity",
    title: "Domestic Cybersecurity Infrastructure",
    topic:
      "Should Nepal prioritize developing domestic cybersecurity infrastructure over relying on foreign tech alliances?",
    keyAngles: [
      "Sovereignty vs. cost-effectiveness",
      "Skill gaps",
      "Geopolitical risks",
    ],
  },
  {
    id: "youth-it-startups",
    title: "Youth IT Startups for Military-Civilian Tech",
    topic:
      "Can Nepal's youth-driven IT startups bridge the gap between military and civilian technological needs?",
    keyAngles: [
      "Dual-use technologies",
      "Collaboration models",
      "Funding challenges",
    ],
  },
  {
    id: "digital-literacy-army",
    title: "Digital Literacy for Army Personnel",
    topic:
      "Is mandatory digital literacy training necessary for all Nepal Army personnel?",
    keyAngles: [
      "Operational efficiency",
      "Resistance to change",
      "Resource allocation",
    ],
  },
  {
    id: "open-source-security",
    title: "Open-Source Software vs National Security",
    topic:
      "How can Nepal balance leveraging open-source software with ensuring national security?",
    keyAngles: [
      "Vulnerability risks",
      "Cost savings",
      "Dependency on global communities",
    ],
  },
  {
    id: "ai-surveillance",
    title: "AI-Driven Border Surveillance",
    topic:
      "Should Nepal adopt AI-driven surveillance systems in border regions?",
    keyAngles: ["Ethical concerns", "Accuracy", "Manpower reduction"],
  },
  {
    id: "cyber-law-framework",
    title: "Cyber-Law Framework Adequacy",
    topic:
      "Is Nepal's current cyber-law framework adequate to combat modern threats?",
    keyAngles: [
      "Legal loopholes",
      "Enforcement challenges",
      "Public awareness",
    ],
  },
  {
    id: "ethical-hacking-hub",
    title: "Ethical Hacking Regional Hub",
    topic: "Can Nepal become a regional hub for ethical hacking talent?",
    keyAngles: [
      "Education reforms",
      "Global competitions",
      "Private-sector partnerships",
    ],
  },
];

export default function GDPracticePage() {
  const searchParams = useSearchParams();
  const router = useRouter();

  const [selectedTopic, setSelectedTopic] = useState<
    (typeof gdTopics)[0] | null
  >(null);
  const [stage, setStage] = useState<
    "selection" | "preparation" | "discussion" | "completed" | "results"
  >("selection");
  const [timeLeft, setTimeLeft] = useState(900); // 15 minutes in seconds
  const [notes, setNotes] = useState("");

  // Audio recording states
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [recordings, setRecordings] = useState<Record<string, Recording>>({});
  const [audioStream, setAudioStream] = useState<MediaStream | null>(null);
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(
    null
  );
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentPlayingKey, setCurrentPlayingKey] = useState<string | null>(
    null
  );

  const audioChunksRef = useRef<BlobPart[]>([]);
  const currentRecordingTimeRef = useRef(0);
  const startTimeRef = useRef(0);
  const audioPlayerRef = useRef<HTMLAudioElement | null>(null);

  // Handle URL parameters to pre-select topic
  useEffect(() => {
    const topicParam = searchParams.get("topic");
    if (topicParam) {
      const topic = gdTopics.find((t) => t.id === topicParam);
      if (topic) {
        setSelectedTopic(topic);
        setStage("preparation");
      }
    }
  }, [searchParams]);

  // Timer functionality - only runs when recording
  useEffect(() => {
    let discussionTimer: NodeJS.Timeout | null = null;

    if (isRecording && timeLeft > 0) {
      discussionTimer = setInterval(() => {
        setTimeLeft((prev) => {
          if (prev <= 1) {
            // Auto-stop recording when timer reaches 0
            if (mediaRecorder && mediaRecorder.state === "recording") {
              mediaRecorder.stop();
              setIsRecording(false);
              if (audioStream) {
                audioStream.getTracks().forEach((track) => track.stop());
              }
            }
            setStage("completed");
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }

    return () => {
      if (discussionTimer) {
        clearInterval(discussionTimer);
      }
    };
  }, [isRecording, mediaRecorder, audioStream]);

  // Recording timer functionality
  useEffect(() => {
    let recordingTimer: NodeJS.Timeout | null = null;

    if (isRecording) {
      startTimeRef.current = Date.now();
      setRecordingTime(0);

      recordingTimer = setInterval(() => {
        const elapsedSeconds = Math.floor(
          (Date.now() - startTimeRef.current) / 1000
        );
        setRecordingTime(elapsedSeconds);
        currentRecordingTimeRef.current = elapsedSeconds;
      }, 1000);
    } else {
      setRecordingTime(0);
    }

    return () => {
      if (recordingTimer) {
        clearInterval(recordingTimer);
      }
    };
  }, [isRecording]);

  // Cleanup function
  useEffect(() => {
    return () => {
      if (mediaRecorder && mediaRecorder.state !== "inactive") {
        mediaRecorder.stop();
      }
      if (audioStream) {
        audioStream.getTracks().forEach((track) => track.stop());
      }
      if (audioPlayerRef.current) {
        audioPlayerRef.current.pause();
        audioPlayerRef.current = null;
      }
    };
  }, [mediaRecorder, audioStream]);

  // Function to start recording
  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      setAudioStream(stream);

      const recorder = new MediaRecorder(stream);
      setMediaRecorder(recorder);

      audioChunksRef.current = [];

      recorder.ondataavailable = (e) => {
        audioChunksRef.current.push(e.data);
      };

      recorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, {
          type: "audio/webm",
        });
        const audioUrl = URL.createObjectURL(audioBlob);

        const finalDuration = currentRecordingTimeRef.current;
        const recordingKey = selectedTopic ? selectedTopic.id : "unknown";

        setRecordings((prev) => ({
          ...prev,
          [recordingKey]: {
            url: audioUrl,
            duration: finalDuration,
            blob: audioBlob,
          },
        }));
      };

      recorder.start();
      setIsRecording(true);
    } catch (error) {
      console.error("Error accessing microphone:", error);
      alert("Could not access your microphone. Please check your permissions.");
    }
  };

  // Function to stop recording
  const stopRecording = useCallback(() => {
    if (mediaRecorder && isRecording) {
      const finalDuration = Math.floor(
        (Date.now() - startTimeRef.current) / 1000
      );
      currentRecordingTimeRef.current = finalDuration;

      mediaRecorder.stop();
      setIsRecording(false);

      if (audioStream) {
        audioStream.getTracks().forEach((track) => track.stop());
      }
    }
  }, [mediaRecorder, isRecording, audioStream]);

  // Function to play recording
  const playRecording = (recordingKey: string) => {
    if (isPlaying && currentPlayingKey === recordingKey) {
      stopPlayback();
      return;
    }

    if (isPlaying && audioPlayerRef.current) {
      stopPlayback();
    }

    const audio = new Audio(recordings[recordingKey]?.url);
    audioPlayerRef.current = audio;

    audio.addEventListener("ended", () => {
      setIsPlaying(false);
      setCurrentPlayingKey(null);
      audioPlayerRef.current = null;
    });

    audio.play().catch((error) => {
      console.error("Error playing audio:", error);
      setIsPlaying(false);
      setCurrentPlayingKey(null);
      audioPlayerRef.current = null;
    });

    setIsPlaying(true);
    setCurrentPlayingKey(recordingKey);
  };

  // Function to stop playback
  const stopPlayback = () => {
    if (audioPlayerRef.current) {
      audioPlayerRef.current.pause();
      audioPlayerRef.current = null;
    }
    setIsPlaying(false);
    setCurrentPlayingKey(null);
  };

  // Format time as MM:SS
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  };

  // Handle reset
  const handleReset = () => {
    if (isRecording) {
      stopRecording();
    }
    stopPlayback();
    setStage("selection");
    setSelectedTopic(null);
    setTimeLeft(900);
    setNotes("");
    setRecordings({});
  };

  // Render topic selection
  const renderTopicSelection = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          Select a Group Discussion Topic
        </h2>
        <p className="text-gray-600 mb-8">
          Choose a topic to practice your group discussion skills. You'll have
          unlimited time to prepare and up to 15 minutes for recording your
          discussion.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {gdTopics.map((topic) => (
          <div
            key={topic.id}
            className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow cursor-pointer"
            onClick={() => {
              setSelectedTopic(topic);
              setStage("preparation");
            }}
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              {topic.title}
            </h3>
            <p className="text-gray-700 mb-4">{topic.topic}</p>
            <div className="space-y-1">
              <p className="text-sm font-medium text-gray-600">Key angles:</p>
              <ul className="text-sm text-gray-500">
                {topic.keyAngles.map((angle, index) => (
                  <li key={index}>• {angle}</li>
                ))}
              </ul>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  // Render preparation stage
  const renderPreparation = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Preparation Time
        </h2>
        <p className="text-gray-600 mb-4">
          Take your time to prepare your thoughts on the topic. The timer will
          start when you begin recording.
        </p>
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-blue-900 mb-2">
          {selectedTopic?.title}
        </h3>
        <p className="text-blue-800 mb-4">{selectedTopic?.topic}</p>
        <div className="space-y-1">
          <p className="text-sm font-medium text-blue-700">
            Consider these angles:
          </p>
          <ul className="text-sm text-blue-600">
            {selectedTopic?.keyAngles.map((angle, index) => (
              <li key={index}>• {angle}</li>
            ))}
          </ul>
        </div>
      </div>

      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          Preparation Notes
        </h3>
        <textarea
          value={notes}
          onChange={(e) => setNotes(e.target.value)}
          placeholder="Write your key points, arguments, and examples here..."
          className="w-full h-40 p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

      <div className="flex justify-center">
        <SimpleButton
          onClick={() => setStage("discussion")}
          className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-8 rounded-lg shadow transition duration-200"
        >
          Start Discussion
        </SimpleButton>
      </div>
    </div>
  );

  // Render discussion stage
  const renderDiscussion = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">
          Group Discussion in Progress
        </h2>
        <div
          className={`px-4 py-2 rounded-full flex items-center ${
            isRecording
              ? "bg-red-100 text-red-800"
              : "bg-gray-100 text-gray-600"
          }`}
        >
          <ClockIcon className="h-5 w-5 mr-2" />
          <span className="font-mono font-medium">
            {formatTime(timeLeft)}
            {!isRecording && " (Paused)"}
          </span>
        </div>
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-blue-900 mb-2">
          {selectedTopic?.title}
        </h3>
        <p className="text-blue-800">{selectedTopic?.topic}</p>
      </div>

      {/* Recording Controls */}
      <div className="bg-gray-50 p-6 rounded-lg border border-gray-200">
        <h3 className="text-lg font-medium text-gray-800 mb-4">
          Record Your Discussion
        </h3>
        <p className="text-gray-600 mb-4">
          Record your discussion to review your performance later. The 15-minute
          timer will start when you begin recording and pause when you stop.
        </p>

        {!isRecording && !recordings[selectedTopic?.id || ""] ? (
          <button
            onClick={startRecording}
            className="inline-flex items-center px-4 py-2 border border-transparent text-base font-medium rounded-md text-white bg-red-600 hover:bg-red-700 shadow-sm"
          >
            <MicrophoneIcon className="h-5 w-5 mr-2" />
            Start Recording
          </button>
        ) : isRecording ? (
          <div className="space-y-4">
            <div className="flex items-center text-red-600">
              <span className="animate-pulse mr-2">●</span>
              <span>
                Recording...{" "}
                {recordingTime > 0 ? formatTime(recordingTime) : "0:00"}
              </span>
            </div>
            <button
              onClick={stopRecording}
              className="inline-flex items-center px-4 py-2 border border-transparent text-base font-medium rounded-md text-white bg-gray-600 hover:bg-gray-700 shadow-sm"
            >
              <StopIcon className="h-5 w-5 mr-2" />
              Stop Recording
            </button>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex items-center text-green-600">
              <CheckIcon className="h-5 w-5 mr-2" />
              <span>
                Recording completed (
                {formatTime(recordings[selectedTopic?.id || ""]?.duration || 0)}
                )
              </span>
            </div>
            <div className="flex space-x-4">
              <button
                onClick={() => playRecording(selectedTopic?.id || "")}
                className={`inline-flex items-center px-4 py-2 border text-base font-medium rounded-md ${
                  isPlaying && currentPlayingKey === selectedTopic?.id
                    ? "border-red-300 text-red-700 bg-red-50 hover:bg-red-100"
                    : "border-green-300 text-green-700 bg-green-50 hover:bg-green-100"
                }`}
              >
                {isPlaying && currentPlayingKey === selectedTopic?.id ? (
                  <>
                    <StopIcon className="h-5 w-5 mr-2" />
                    Stop Playing
                  </>
                ) : (
                  <>
                    <PlayIcon className="h-5 w-5 mr-2" />
                    Play Recording
                  </>
                )}
              </button>
              <button
                onClick={startRecording}
                className="inline-flex items-center px-4 py-2 border border-transparent text-base font-medium rounded-md text-white bg-red-600 hover:bg-red-700 shadow-sm"
              >
                <MicrophoneIcon className="h-5 w-5 mr-2" />
                Record Again
              </button>
            </div>
          </div>
        )}
      </div>

      <div className="flex justify-center">
        <SimpleButton
          onClick={() => {
            if (isRecording) {
              stopRecording();
            }
            setStage("completed");
          }}
          className="bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-8 rounded-lg shadow transition duration-200"
        >
          End Discussion
        </SimpleButton>
      </div>
    </div>
  );

  // Render completed stage
  const renderCompleted = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          Discussion Completed!
        </h2>
        <p className="text-gray-600">
          Great job! You've completed the group discussion practice.
        </p>
      </div>

      <div className="flex justify-center">
        <SimpleButton
          onClick={() => setStage("results")}
          className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-8 rounded-lg shadow transition duration-200"
        >
          View Results
        </SimpleButton>
      </div>
    </div>
  );

  // Render results
  const renderResults = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          Practice Results
        </h2>
        <p className="text-gray-600">Review your performance and recordings</p>
      </div>

      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Topic: {selectedTopic?.title}
        </h3>
        <p className="text-gray-700 mb-4">{selectedTopic?.topic}</p>

        {recordings[selectedTopic?.id || ""] && (
          <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <h4 className="font-medium mb-2">Your Recording</h4>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-500">
                Duration:{" "}
                {formatTime(recordings[selectedTopic?.id || ""]?.duration || 0)}
              </span>
              <button
                onClick={() => playRecording(selectedTopic?.id || "")}
                className={`inline-flex items-center px-3 py-1 border text-sm font-medium rounded-md ${
                  isPlaying && currentPlayingKey === selectedTopic?.id
                    ? "border-red-300 text-red-700 bg-red-50 hover:bg-red-100"
                    : "border-blue-300 text-blue-700 bg-blue-50 hover:bg-blue-100"
                }`}
              >
                {isPlaying && currentPlayingKey === selectedTopic?.id ? (
                  <>
                    <StopIcon className="h-4 w-4 mr-1" />
                    Stop
                  </>
                ) : (
                  <>
                    <PlayIcon className="h-4 w-4 mr-1" />
                    Play
                  </>
                )}
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Audio Recording Manager */}
      <div className="mt-12">
        <AudioRecordingManager
          recordings={recordings}
          testType="GD"
          isPlaying={isPlaying}
          currentPlayingKey={currentPlayingKey}
          onPlay={playRecording}
          onStop={stopPlayback}
          className="bg-gray-50 rounded-xl p-6 border border-gray-200"
        />
      </div>

      <div className="flex justify-center">
        <SimpleButton
          onClick={handleReset}
          className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg shadow transition duration-200"
        >
          Practice Another Topic
        </SimpleButton>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="mx-auto max-w-4xl px-6">
        <NavigationButton
          href="/tests/gto/gd"
          className="mb-8 inline-flex items-center text-gray-600 hover:text-gray-900"
        >
          <ArrowLeftIcon className="h-5 w-5 mr-2" />
          Back to GD Overview
        </NavigationButton>

        <div className="bg-white rounded-2xl shadow-sm p-8 border-t-4 border-blue-500">
          <div className="flex items-center space-x-3 mb-6">
            <div className="p-2 rounded-full bg-blue-100">
              <UserGroupIcon className="h-8 w-8 text-blue-600" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900">
              Group Discussion Practice
            </h1>
          </div>

          {stage === "selection" && renderTopicSelection()}
          {stage === "preparation" && renderPreparation()}
          {stage === "discussion" && renderDiscussion()}
          {stage === "completed" && renderCompleted()}
          {stage === "results" && renderResults()}
        </div>
      </div>
    </div>
  );
}
