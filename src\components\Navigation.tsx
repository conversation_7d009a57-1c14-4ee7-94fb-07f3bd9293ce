"use client";

import Link from "next/link";
import { useState, useRef, useEffect } from "react";
import { usePathname } from "next/navigation";
import Image from "next/image";
import {
  Bars3Icon,
  XMarkIcon,
  UserCircleIcon,
  ChevronDownIcon,
} from "@heroicons/react/24/outline";
import { useAuth } from "@/context/GoogleAuthContext";

const navigation = [
  { name: "Army", href: "/army" },
  {
    name: "Tests",
    href: "#",
    dropdown: true,
    items: [
      { name: "TO", href: "/tests/to", fullName: "Technical Officer" },
      { name: "GTO", href: "/tests/gto", fullName: "Group Testing Officer" },
      { name: "I<PERSON>", href: "/tests/io", fullName: "Interviewing Officer" },
      { name: "BC", href: "/tests/bc", fullName: "Board Conference" },
    ],
  },
  { name: "Forum", href: "/forum" },
];

export default function Navigation() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [openDropdown, setOpenDropdown] = useState<string | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const pathname = usePathname();
  const { user } = useAuth();

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setOpenDropdown(null);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Close dropdown when navigating
  useEffect(() => {
    setOpenDropdown(null);
  }, [pathname]);

  // Function to check if a navigation item is active
  const isActive = (href: string) => {
    // Exact match for home page
    if (href === "/" && pathname === "/") {
      return true;
    }

    // For dropdown items, check if any of their items are active
    if (href === "#") {
      const item = navigation.find((item) => item.href === href);
      if (item && item.dropdown && item.items) {
        return item.items.some((subItem) => pathname?.startsWith(subItem.href));
      }
      return false;
    }

    // For other pages, check if the pathname starts with the href
    // This handles nested routes like /tests/to being active when on /tests/to/srt
    if (href !== "/" && pathname?.startsWith(href)) {
      return true;
    }

    return false;
  };

  // Toggle dropdown
  const toggleDropdown = (name: string) => {
    setOpenDropdown(openDropdown === name ? null : name);
  };

  return (
    <header className="bg-white shadow-sm">
      <nav
        className="mx-auto flex max-w-7xl items-center justify-between p-4 sm:p-6 lg:px-8"
        aria-label="Global"
      >
        <div className="flex lg:flex-1">
          <Link href="/" className="-m-1.5 p-1.5">
            <span className="sr-only">Military Selection Test Prep</span>
            <span
              className="text-xl font-bold text-indigo-700"
              style={{ color: "#4338ca !important" }}
            >
              MilSelect
            </span>
          </Link>
        </div>
        <div className="flex lg:hidden">
          <button
            type="button"
            className="-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700"
            onClick={() => setMobileMenuOpen(true)}
          >
            <span className="sr-only">Open main menu</span>
            <Bars3Icon className="h-6 w-6" aria-hidden="true" />
          </button>
        </div>
        <div className="hidden lg:flex lg:gap-x-8" ref={dropdownRef}>
          {navigation.map((item) => (
            <div key={item.name} className="relative">
              {item.dropdown ? (
                <>
                  <button
                    onClick={() => toggleDropdown(item.name)}
                    className={`flex items-center text-base font-semibold leading-6 ${
                      isActive(item.href)
                        ? "text-indigo-600"
                        : "text-gray-900 hover:text-indigo-600"
                    }`}
                  >
                    {item.name}
                    <ChevronDownIcon
                      className={`ml-1 h-4 w-4 transition-transform ${
                        openDropdown === item.name ? "rotate-180" : ""
                      }`}
                    />
                  </button>

                  {openDropdown === item.name && (
                    <div className="absolute left-0 z-10 mt-2 w-48 origin-top-left rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                      {item.items?.map((subItem) => (
                        <Link
                          key={subItem.name}
                          href={subItem.href}
                          className={`block px-4 py-2 text-base ${
                            isActive(subItem.href)
                              ? "bg-gray-100 text-indigo-600"
                              : "text-gray-700 hover:bg-gray-50"
                          }`}
                          onClick={() => setOpenDropdown(null)}
                        >
                          <span className="font-medium">{subItem.name}</span>
                          <span className="ml-1 text-sm text-gray-500">
                            {subItem.fullName}
                          </span>
                        </Link>
                      ))}
                    </div>
                  )}
                </>
              ) : (
                <Link
                  href={item.href}
                  className={`text-base font-semibold leading-6 ${
                    isActive(item.href)
                      ? "text-indigo-600 relative after:absolute after:bottom-0 after:left-0 after:w-full after:h-0.5 after:bg-indigo-600"
                      : "text-gray-900 hover:text-indigo-600"
                  }`}
                >
                  {item.name}
                </Link>
              )}
            </div>
          ))}
        </div>
        <div className="hidden lg:flex lg:flex-1 lg:justify-end">
          {user ? (
            <Link
              href="/profile"
              className="flex items-center text-base font-semibold leading-6 text-gray-900 hover:text-indigo-600"
            >
              <div className="relative">
                {user.photoURL ? (
                  <div className="h-6 w-6 rounded-full overflow-hidden mr-2">
                    <Image
                      src={user.photoURL}
                      alt={user.displayName || "User"}
                      width={24}
                      height={24}
                      className="object-cover"
                    />
                  </div>
                ) : (
                  <UserCircleIcon className="h-5 w-5 mr-1" />
                )}
              </div>
              <span>{user.displayName || "Profile"}</span>
            </Link>
          ) : (
            <Link
              href="/login"
              className="text-base font-semibold leading-6 text-gray-900 hover:text-indigo-600"
            >
              Log in <span aria-hidden="true">&rarr;</span>
            </Link>
          )}
        </div>
      </nav>
      {mobileMenuOpen && (
        <div className="lg:hidden">
          <div
            className="fixed inset-0 z-50 bg-gray-900/30"
            onClick={() => setMobileMenuOpen(false)}
          />
          <div className="fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10">
            <div className="flex items-center justify-between">
              <Link
                href="/"
                className="-m-1.5 p-1.5"
                onClick={() => setMobileMenuOpen(false)}
              >
                <span className="sr-only">Military Selection Test Prep</span>
                <span
                  className="text-xl font-bold text-indigo-700"
                  style={{ color: "#4338ca !important" }}
                >
                  MilSelect
                </span>
              </Link>
              <button
                type="button"
                className="-m-2.5 rounded-md p-2.5 text-gray-700"
                onClick={() => setMobileMenuOpen(false)}
              >
                <span className="sr-only">Close menu</span>
                <XMarkIcon className="h-6 w-6" aria-hidden="true" />
              </button>
            </div>
            <div className="mt-6 flow-root">
              <div className="-my-6 divide-y divide-gray-500/10">
                <div className="space-y-2 py-6">
                  {/* Regular navigation items */}
                  {navigation.map((item) =>
                    !item.dropdown ? (
                      <Link
                        key={item.name}
                        href={item.href}
                        className={`-mx-3 block rounded-lg px-3 py-2 text-lg font-semibold leading-7 ${
                          isActive(item.href)
                            ? "bg-indigo-50 text-indigo-600"
                            : "text-gray-900 hover:bg-gray-50"
                        }`}
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        {item.name}
                      </Link>
                    ) : (
                      // For Tests dropdown, show a header and then all items directly
                      <div key={item.name} className="mb-2">
                        <div className="-mx-3 px-3 py-2 text-lg font-semibold leading-7 text-gray-900">
                          {item.name}
                        </div>
                        <div className="mt-1 space-y-1 pl-3">
                          {item.items?.map((subItem) => (
                            <Link
                              key={subItem.name}
                              href={subItem.href}
                              className={`-mx-3 block rounded-lg px-6 py-2 text-base leading-7 ${
                                isActive(subItem.href)
                                  ? "bg-indigo-50 text-indigo-600"
                                  : "text-gray-600 hover:bg-gray-50"
                              }`}
                              onClick={() => setMobileMenuOpen(false)}
                            >
                              <span className="font-medium">
                                {subItem.name}
                              </span>
                              <span className="ml-1 text-sm text-gray-500">
                                {subItem.fullName}
                              </span>
                            </Link>
                          ))}
                        </div>
                      </div>
                    )
                  )}
                </div>
                <div className="py-6">
                  {user ? (
                    <Link
                      href="/profile"
                      className="-mx-3 flex items-center rounded-lg px-3 py-2.5 text-lg font-semibold leading-7 text-gray-900 hover:bg-gray-50"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      <div className="relative">
                        {user.photoURL ? (
                          <div className="h-6 w-6 rounded-full overflow-hidden mr-2">
                            <Image
                              src={user.photoURL}
                              alt={user.displayName || "User"}
                              width={24}
                              height={24}
                              className="object-cover"
                            />
                          </div>
                        ) : (
                          <UserCircleIcon className="h-5 w-5 mr-2" />
                        )}
                      </div>
                      <span>Profile</span>
                    </Link>
                  ) : (
                    <Link
                      href="/login"
                      className="-mx-3 block rounded-lg px-3 py-2.5 text-lg font-semibold leading-7 text-gray-900 hover:bg-gray-50"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      Log in
                    </Link>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </header>
  );
}
