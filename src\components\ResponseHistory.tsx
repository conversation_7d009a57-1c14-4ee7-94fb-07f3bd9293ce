"use client";

import { useState, useEffect } from "react";
import {
  TestResponse,
  TestType,
  getResponseHistory,
  deleteResponse,
} from "@/services/responseService";
import { useAuth } from "@/context/GoogleAuthContext";
import {
  ClockIcon,
  DocumentTextIcon,
  TrashIcon,
} from "@heroicons/react/24/outline";
import ConfirmationModal from "./ConfirmationModal";

interface ResponseHistoryProps {
  testType: TestType;
  setName: string;
  onSelectResponse?: (response: TestResponse) => void;
}

export default function ResponseHistory({
  testType,
  setName,
  onSelectResponse,
}: ResponseHistoryProps) {
  const { user } = useAuth();
  const [history, setHistory] = useState<TestResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expanded, setExpanded] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [responseToDelete, setResponseToDelete] = useState<TestResponse | null>(
    null
  );

  // Function to fetch response history
  const fetchHistory = async () => {
    if (!user) {
      setLoading(false);
      return;
    }

    try {
      const responseHistory = await getResponseHistory(
        user.uid,
        testType,
        setName
      );
      setHistory(responseHistory);
    } catch (err) {
      console.error("Error fetching response history:", err);
      setError("Failed to load response history");
    } finally {
      setLoading(false);
    }
  };

  // Handle deleting a response
  const handleDeleteResponse = async () => {
    if (!responseToDelete || !responseToDelete.docId || !user) return;

    setIsDeleting(true);
    try {
      await deleteResponse(responseToDelete.docId);
      // Refresh the history after deletion
      await fetchHistory();
    } catch (error) {
      console.error("Error deleting response:", error);
      setError("Failed to delete response. Please try again.");
    } finally {
      setIsDeleting(false);
      setResponseToDelete(null);
      setShowDeleteModal(false);
    }
  };

  // Handle click on delete button
  const handleDeleteClick = (e: React.MouseEvent, response: TestResponse) => {
    e.stopPropagation(); // Prevent triggering the parent onClick
    setResponseToDelete(response);
    setShowDeleteModal(true);
  };

  useEffect(() => {
    fetchHistory();
  }, [user, testType, setName]);

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow p-4 my-4">
        <p className="text-gray-500 text-center">Loading response history...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow p-4 my-4">
        <p className="text-red-500">{error}</p>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="bg-white rounded-lg shadow p-4 my-4">
        <p className="text-gray-500 text-center">
          Please log in to view your response history
        </p>
      </div>
    );
  }

  if (history.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow p-4 my-4">
        <p className="text-gray-500 text-center">No previous attempts found</p>
      </div>
    );
  }

  // Get color based on test type
  const getColorClass = () => {
    switch (testType) {
      case "srt":
        return "blue";
      case "wat":
        return "green";
      case "tat":
        return "red";
      case "sdt":
        return "amber";
      default:
        return "indigo";
    }
  };

  const color = getColorClass();

  // Format date from timestamp
  const formatDate = (timestamp?: number) => {
    if (!timestamp) return "Unknown date";
    const date = new Date(timestamp);
    return date.toLocaleString();
  };

  // Show only the 3 most recent attempts by default
  const displayedHistory = expanded ? history : history.slice(0, 3);
  const hasMoreHistory = history.length > 3;

  return (
    <div className={`bg-${color}-50 rounded-lg shadow p-4 my-4`}>
      <h3 className={`text-${color}-800 font-semibold mb-2 flex items-center`}>
        <ClockIcon className="h-5 w-5 mr-2" />
        Your Previous Attempts
      </h3>

      <div className="space-y-3">
        {displayedHistory.map((response, index) => (
          <div
            key={response.timestamp || index}
            className={`bg-white rounded p-3 shadow-sm hover:shadow-md transition-shadow cursor-pointer border border-${color}-100`}
            onClick={() => onSelectResponse && onSelectResponse(response)}
          >
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <DocumentTextIcon
                  className={`h-5 w-5 text-${color}-600 mr-2`}
                />
                <span className="font-medium">Attempt {index + 1}</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-500">
                  {formatDate(response.timestamp)}
                </span>
                <button
                  onClick={(e) => handleDeleteClick(e, response)}
                  disabled={isDeleting}
                  className={`text-white bg-red-600 hover:bg-red-700 p-2 rounded-md border border-red-600`}
                  title="Delete this response"
                >
                  <TrashIcon className="h-5 w-5" />
                </button>
              </div>
            </div>
            <div className="mt-2 text-sm text-gray-600">
              <span className="font-medium">Completed Questions:</span>{" "}
              {response.responses.filter((r) => r && r.trim() !== "").length} of{" "}
              {response.responses.length}
            </div>
          </div>
        ))}
      </div>

      {hasMoreHistory && (
        <button
          onClick={() => setExpanded(!expanded)}
          className={`mt-3 text-${color}-600 text-sm font-medium hover:text-${color}-800 focus:outline-none`}
        >
          {expanded ? "Show Less" : `Show All (${history.length} attempts)`}
        </button>
      )}

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={handleDeleteResponse}
        title="Delete Response"
        message={`Are you sure you want to delete this response? This action cannot be undone.`}
        confirmButtonText="Delete"
        confirmButtonColor={color}
      />
    </div>
  );
}
