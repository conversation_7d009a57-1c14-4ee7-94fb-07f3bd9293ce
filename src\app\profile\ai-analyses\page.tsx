"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/context/GoogleAuthContext";
import { useRoleCheck } from "@/utils/authUtils";
import {
  getAllAIAnalyses,
  AIAnalysisDocument,
} from "@/services/aiAnalysisService";
import AIAnalysisDisplay from "@/components/AIAnalysisDisplay";
import {
  ArrowLeftIcon,
  SparklesIcon,
  ShieldExclamationIcon,
} from "@heroicons/react/24/outline";
import Link from "next/link";

export default function AIAnalysesPage() {
  const { user, loading } = useAuth();
  const { canAccessAIAnalysis } = useRoleCheck();
  const router = useRouter();
  const [analyses, setAnalyses] = useState<AIAnalysisDocument[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedTestType, setSelectedTestType] = useState<string | null>(null);

  useEffect(() => {
    // Redirect if not logged in or doesn't have AI access
    if (!loading) {
      if (!user) {
        router.push("/login");
      } else if (!canAccessAIAnalysis) {
        router.push("/profile");
      }
    }
  }, [user, loading, router, canAccessAIAnalysis]);

  const fetchAnalyses = async () => {
    if (user && canAccessAIAnalysis) {
      try {
        console.log("Fetching AI analyses...");
        setIsLoading(true);
        const userAnalyses = await getAllAIAnalyses(user.uid);
        console.log(`Received ${userAnalyses.length} AI analyses`);
        setAnalyses(userAnalyses);
      } catch (error) {
        console.error("Error fetching AI analyses:", error);
      } finally {
        setIsLoading(false);
      }
    } else {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (user && canAccessAIAnalysis) {
      fetchAnalyses();
    }
  }, [user, canAccessAIAnalysis]);

  // Handle analysis deletion
  const handleDeleteAnalysis = () => {
    // Refresh the analyses list after deletion
    fetchAnalyses();
  };

  // Group analyses by test type
  const analysesByTestType = analyses.reduce((acc, analysis) => {
    const testType = analysis.testType;
    if (!acc[testType]) {
      acc[testType] = [];
    }
    acc[testType].push(analysis);
    return acc;
  }, {} as Record<string, AIAnalysisDocument[]>);

  // Get test type display name
  const getTestTypeDisplay = (testType: string) => {
    switch (testType) {
      case "srt":
        return "Situation Reaction Test";
      case "wat":
        return "Word Association Test";
      case "tat":
        return "Thematic Apperception Test";
      case "sdt":
        return "Self Description Test";
      default:
        return testType.toUpperCase();
    }
  };

  // Get test type color
  const getTestTypeColor = (testType: string) => {
    switch (testType) {
      case "srt":
        return "blue";
      case "wat":
        return "green";
      case "tat":
        return "red";
      case "sdt":
        return "amber";
      default:
        return "gray";
    }
  };

  // Format date
  const formatDate = (timestamp?: number) => {
    if (!timestamp) return "Unknown date";
    return new Date(timestamp).toLocaleString();
  };

  // Render test type selection
  const renderTestTypeSelection = () => {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {Object.keys(analysesByTestType).map((testType) => (
          <div
            key={testType}
            onClick={() => setSelectedTestType(testType)}
            className={`bg-white rounded-xl p-6 shadow-sm border-t-4 border-${getTestTypeColor(
              testType
            )}-500 hover:shadow-md cursor-pointer transition-shadow`}
          >
            <div className="flex items-center space-x-3 mb-4">
              <div
                className={`p-2 rounded-full bg-${getTestTypeColor(
                  testType
                )}-100`}
              >
                <SparklesIcon
                  className={`h-6 w-6 text-${getTestTypeColor(testType)}-600`}
                />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">
                {getTestTypeDisplay(testType)}
              </h3>
            </div>
            <p className="text-sm text-gray-600 mb-2">
              {analysesByTestType[testType].length} analyses available
            </p>
            <p className="text-xs text-gray-500">
              Latest:{" "}
              {formatDate(analysesByTestType[testType][0]?.responseTimestamp)}
            </p>
          </div>
        ))}
      </div>
    );
  };

  // Render analyses for selected test type
  const renderAnalyses = () => {
    if (!selectedTestType) return null;

    return (
      <div>
        <div className="flex items-center mb-6">
          <button
            onClick={() => setSelectedTestType(null)}
            className="mr-4 text-gray-600 hover:text-gray-900"
          >
            <ArrowLeftIcon className="h-5 w-5" />
          </button>
          <h2 className="text-2xl font-bold text-gray-900">
            {getTestTypeDisplay(selectedTestType)} Analyses
          </h2>
        </div>

        <div className="space-y-6">
          {analysesByTestType[selectedTestType].map((analysis) => (
            <AIAnalysisDisplay
              key={`${analysis.testType}_${analysis.setName}_${
                analysis.responseTimestamp
              }_${analysis.aiProvider || "google"}`}
              analysis={analysis}
              onDelete={handleDeleteAnalysis}
            />
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white shadow overflow-hidden sm:rounded-lg">
          <div className="px-4 py-5 sm:px-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  AI Analyses
                </h1>
                <p className="mt-1 text-sm text-gray-500">
                  View your saved AI analyses from all tests
                </p>
              </div>
              <Link
                href="/profile"
                className="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <ArrowLeftIcon className="h-4 w-4 mr-1" />
                Back to Profile
              </Link>
            </div>
          </div>

          <div className="border-t border-gray-200 px-4 py-5 sm:p-6">
            {isLoading ? (
              <div className="flex justify-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
              </div>
            ) : analyses.length === 0 ? (
              <div className="text-center py-12">
                <SparklesIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No AI Analyses Yet
                </h3>
                <p className="text-gray-500 max-w-md mx-auto mb-4">
                  Complete a test and use the AI Analysis feature to generate
                  insights about your responses. Your analyses will be saved
                  here for future reference.
                </p>
                <Link
                  href="/tests"
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <SparklesIcon className="h-5 w-5 mr-2" />
                  Go to Tests
                </Link>
              </div>
            ) : selectedTestType ? (
              renderAnalyses()
            ) : (
              renderTestTypeSelection()
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
