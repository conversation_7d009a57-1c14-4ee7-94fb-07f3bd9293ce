"use client";

import { useRouter } from "next/navigation";
import NavigationButton from "@/components/NavigationButton";
import SimpleButton from "@/components/SimpleButton";

import { ArrowLeftIcon, PlayIcon } from "@heroicons/react/24/outline";
import { use } from "react";

export default function TATInstructions({
  params,
}: {
  params: Promise<{ set: string }>;
}) {
  const router = useRouter();
  const { set } = use(params);
  const setName = decodeURIComponent(set);

  // Using SimpleButton with href instead of onClick for better performance
  const testUrl = `/tests/to/tat/practice/${set}/test`;

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="mx-auto max-w-3xl px-6">
        <SimpleButton
          onClick={() => router.back()}
          className="mb-8 flex items-center text-gray-600 hover:text-gray-900"
        >
          <ArrowLeftIcon className="h-5 w-5 mr-2" />
          Back to Set Selection
        </SimpleButton>

        <div className="bg-white rounded-2xl shadow-sm p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">
            Instructions for {setName}
          </h1>

          <div className="space-y-6 text-gray-700">
            <div>
              <h2 className="text-xl font-semibold mb-2">Test Format:</h2>
              <ul className="list-disc list-inside space-y-2">
                <li>
                  You will be shown 12 images one at a time (including one blank
                  image)
                </li>
                <li>Each image will be displayed for 30 seconds</li>
                <li>After viewing the image, it will be hidden</li>
                <li>
                  You will have 4 minutes to write a story about the image
                </li>
                <li>The process repeats for all 12 images</li>
              </ul>
            </div>

            <div>
              <h2 className="text-xl font-semibold mb-2">Story Guidelines:</h2>
              <ul className="list-disc list-inside space-y-2">
                <li>Include what led up to the scene (past)</li>
                <li>Describe what is happening in the image (present)</li>
                <li>Explain what the characters are thinking and feeling</li>
                <li>Provide an outcome or resolution (future)</li>
                <li>
                  For the blank image, create a story from your imagination
                </li>
              </ul>
            </div>

            <div>
              <h2 className="text-xl font-semibold mb-2">Tips:</h2>
              <ul className="list-disc list-inside space-y-2">
                <li>Focus on character development and emotions</li>
                <li>Be creative but coherent</li>
                <li>Use the full 4 minutes for each story</li>
                <li>Don&apos;t overthink - trust your instincts</li>
                <li>You can pause the test if needed</li>
              </ul>
            </div>

            <div className="text-center mt-8">
              <SimpleButton
                href={testUrl}
                className="inline-flex justify-center rounded-md bg-red-600 px-6 py-3 text-base font-semibold text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
              >
                <PlayIcon className="h-5 w-5 inline-block mr-2" />
                Start Practice
              </SimpleButton>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
