"use client";

import React, { useState } from "react";
import { useAuth } from "@/context/GoogleAuthContext";
import {
  doc,
  deleteDoc,
  updateDoc,
  serverTimestamp,
  increment,
} from "firebase/firestore";
import { db } from "@/lib/firebase";
import {
  PencilIcon,
  TrashIcon,
  CheckIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import ReportButton from "./ReportButton";
import { filterInappropriateContent } from "@/utils/contentModeration";
import { recalculateTopicReplyCount } from "@/services/forumService";

interface ReplyActionsProps {
  replyId: string;
  topicId: string;
  authorId: string;
  authorName: string;
  content: string;
  onReplyUpdated?: () => void;
}

export default function ReplyActions({
  replyId,
  topicId,
  authorId,
  authorName,
  content,
  onReplyUpdated,
}: ReplyActionsProps) {
  const { user } = useAuth();
  const [isDeleting, setIsDeleting] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editedContent, setEditedContent] = useState(content);
  const [error, setError] = useState<string | null>(null);
  const [showConfirmDelete, setShowConfirmDelete] = useState(false);

  // Check if the current user is the author
  const isAuthor = user?.uid === authorId;

  // Handle edit button click
  const handleEditClick = () => {
    setIsEditing(true);
    setEditedContent(content);
  };

  // Handle save edit
  const handleSaveEdit = async () => {
    try {
      setError(null);

      if (!user) {
        throw new Error("You must be logged in to edit a reply.");
      }

      if (editedContent.trim() === "") {
        throw new Error("Reply content cannot be empty.");
      }

      // Filter content for inappropriate material
      const filteredContent = await filterInappropriateContent(editedContent);

      // Update the reply
      const replyRef = doc(db, "forumReplies", replyId);
      await updateDoc(replyRef, {
        content: filteredContent,
        updatedAt: serverTimestamp(),
      });

      setIsEditing(false);

      // Call the callback if provided
      if (onReplyUpdated) {
        onReplyUpdated();
      }
    } catch (error) {
      console.error("Error updating reply:", error);
      setError(
        error instanceof Error
          ? error.message
          : "An error occurred while updating your reply. Please try again."
      );
    }
  };

  // Handle cancel edit
  const handleCancelEdit = () => {
    setIsEditing(false);
    setError(null);
  };

  // Handle delete
  const handleDelete = async () => {
    try {
      setIsDeleting(true);
      setError(null);

      if (!user) {
        throw new Error("You must be logged in to delete a reply.");
      }

      // Delete the reply first
      const replyRef = doc(db, "forumReplies", replyId);
      await deleteDoc(replyRef);

      // Recalculate the topic's reply count, but don't let it block the deletion
      try {
        await recalculateTopicReplyCount(topicId);
      } catch (updateError) {
        console.error("Error recalculating topic reply count:", updateError);
        // Continue even if this fails - the reply is already deleted
      }

      // Call the callback if provided
      if (onReplyUpdated) {
        onReplyUpdated();
      }
    } catch (error) {
      console.error("Error deleting reply:", error);
      setError(
        error instanceof Error
          ? error.message
          : "An error occurred while deleting your reply. Please try again."
      );
      setShowConfirmDelete(false);
    } finally {
      setIsDeleting(false);
    }
  };

  if (isEditing) {
    return (
      <div className="mt-4">
        {error && <div className="text-sm text-red-600 mb-2">{error}</div>}
        <textarea
          value={editedContent}
          onChange={(e) => setEditedContent(e.target.value)}
          className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm mb-2"
          rows={4}
        />
        <div className="flex justify-end space-x-2">
          <button
            type="button"
            onClick={handleCancelEdit}
            className="inline-flex items-center text-sm text-gray-600 hover:text-gray-800 px-2 py-1 rounded border border-gray-300"
          >
            <XMarkIcon className="h-4 w-4 mr-1" />
            Cancel
          </button>
          <button
            type="button"
            onClick={handleSaveEdit}
            className="inline-flex items-center text-sm text-white bg-indigo-600 hover:bg-indigo-700 px-2 py-1 rounded"
          >
            <CheckIcon className="h-4 w-4 mr-1" />
            Save
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="mt-4 flex justify-end">
      {isAuthor ? (
        // Author actions: Edit and Delete
        <div className="flex space-x-4">
          {error && <div className="text-sm text-red-600 mr-4">{error}</div>}

          <button
            type="button"
            onClick={handleEditClick}
            className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800"
          >
            <PencilIcon className="h-4 w-4 mr-1" />
            Edit
          </button>

          {showConfirmDelete ? (
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">Are you sure?</span>
              <button
                type="button"
                onClick={handleDelete}
                disabled={isDeleting}
                className="inline-flex items-center text-sm text-white bg-red-600 hover:bg-red-700 px-2 py-1 rounded"
              >
                {isDeleting ? "Deleting..." : "Yes, Delete"}
              </button>
              <button
                type="button"
                onClick={() => setShowConfirmDelete(false)}
                className="inline-flex items-center text-sm text-gray-600 hover:text-gray-800 px-2 py-1 rounded border border-gray-300"
              >
                Cancel
              </button>
            </div>
          ) : (
            <button
              type="button"
              onClick={() => setShowConfirmDelete(true)}
              className="inline-flex items-center text-sm text-red-600 hover:text-red-800"
            >
              <TrashIcon className="h-4 w-4 mr-1" />
              Delete
            </button>
          )}
        </div>
      ) : (
        // Non-author action: Report
        <ReportButton
          contentId={replyId}
          contentType="reply"
          authorName={authorName}
        />
      )}
    </div>
  );
}
