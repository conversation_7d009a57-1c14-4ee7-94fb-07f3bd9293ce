
/**
 * Psychological Analysis Service
 *
 * This service provides a nuanced psychological analysis system for military candidate assessments
 * that prioritizes real-world human traits over forced military associations.
 */

// Core human traits to evaluate across all tests
export interface HumanTrait {
  name: string;
  description: string;
  category: "cognitive" | "emotional" | "social" | "character";
  positiveIndicators: string[];
  negativeIndicators: string[];
}

// Analysis result structure
export interface PsychologicalAnalysisResult {
  overallScore: number; // 0-100
  humanTraits: {
    [key: string]: {
      score: number; // 0-100
      evidence: string[];
      feedback: string;
    };
  };
  strengths: string[];
  growthOpportunities: string[];
  detailedFeedback: string;
  timeAwareness: {
    responseSpeed: "Fast" | "Moderate" | "Slow";
    pressureHandling: number; // 0-100
    analysis: string;
  };
  flaggedResponses: {
    responseIndex: number;
    response: string;
    reason: string;
    severity: "Low" | "Medium" | "High";
  }[];
  isValid: boolean;
}

// Core human traits to evaluate
export const humanTraits: HumanTrait[] = [
  {
    name: "Positivity",
    description: "Tendency to maintain an optimistic and constructive outlook",
    category: "emotional",
    positiveIndicators: [
      "optimistic language",
      "solution-focused responses",
      "constructive framing",
    ],
    negativeIndicators: [
      "pessimistic language",
      "problem-focused without solutions",
      "negative framing",
    ],
  },
  {
    name: "Honesty",
    description: "Authenticity and truthfulness in responses",
    category: "character",
    positiveIndicators: [
      "realistic self-assessment",
      "acknowledgment of limitations",
      "balanced perspective",
    ],
    negativeIndicators: [
      "exaggerated claims",
      "unrealistic heroism",
      "perfect self-portrayal",
    ],
  },
  {
    name: "Problem-Solving",
    description: "Ability to identify and resolve challenges effectively",
    category: "cognitive",
    positiveIndicators: [
      "structured approach to problems",
      "consideration of alternatives",
      "practical solutions",
    ],
    negativeIndicators: [
      "impulsive reactions",
      "single-solution thinking",
      "unrealistic solutions",
    ],
  },
  {
    name: "Thought Coherence",
    description: "Logical organization and clarity of thinking",
    category: "cognitive",
    positiveIndicators: [
      "logical flow of ideas",

const humanTraits: HumanTrait[] = [
  {
    name: "Thought Coherence",
    description: "Ability to think logically and clearly",
    category: "cognitive",
    positiveIndicators: [
      "clear cause-effect relationships",
      "structured narratives",
    ],
    negativeIndicators: [
      "disjointed thoughts",
      "logical fallacies",
      "unclear reasoning",
    ],
  },
  {
    name: "Teamwork",
    description: "Ability to collaborate and work effectively with others",
    category: "social",
    positiveIndicators: [
      "inclusive language",
      "consideration of others",
      "collaborative solutions",
    ],
    negativeIndicators: [
      "individualistic focus",
      "dismissal of others",
      "lone-hero narratives",
    ],
  },
  {
    name: "Emotional Balance",
    description: "Ability to manage emotions appropriately",
    category: "emotional",
    positiveIndicators: [
      "proportionate emotional responses",
      "emotional awareness",
      "self-regulation",
    ],
    negativeIndicators: [
      "emotional extremes",
      "lack of emotional awareness",
      "impulsive emotional reactions",
    ],
  },
  {
    name: "Adaptability",
    description: "Flexibility in responding to changing situations",
    category: "cognitive",
    positiveIndicators: [
      "consideration of context",
      "flexible thinking",
      "comfort with ambiguity",
    ],
    negativeIndicators: [
      "rigid thinking",
      "resistance to change",
      "discomfort with uncertainty",
    ],
  },
  {
    name: "Ethical Reasoning",
    description: "Consideration of moral principles in decision-making",
    category: "character",
    positiveIndicators: [
      "consideration of ethical implications",
      "principled decisions",
      "moral consistency",
    ],
    negativeIndicators: [
      "disregard for ethics",
      "ends-justify-means thinking",
      "situational ethics",

const violencePatterns = [
  "kill",
  "attack",
  "destroy",
  "eliminate",
  "violent",
  "aggressive",
  "combat",
  "fight",
  "battle",
];

const unrealisticHeroismPatterns = [
  "single-handedly",
  "alone",
  "by myself",
  "hero",
  "heroic",
  "save everyone",
  "save the day",
];

/**
 * Analyze WAT (Word Association Test) responses
 * @param responses Array of word associations
 * @param stimulusWords Array of stimulus words that prompted the responses
 * @param timePerResponse Time taken for each response (in seconds)
 * @returns Psychological analysis result
 */
export const analyzeWATResponses = (
  responses: string[],
  stimulusWords: string[],
  timePerResponse?: number[]
): PsychologicalAnalysisResult => {
  // Initialize result with empty structure
  const result = createEmptyAnalysisResult();

  // If no responses, return empty result
  if (!responses || responses.length === 0) {
    return result;
  }

  // Validate input
  const validResponses = responses.filter((r) => r && r.trim() !== "");
  if (validResponses.length === 0) {
    return result;
  }

  // Mark as valid since we have responses to analyze
  result.isValid = true;

  // Analyze each response
  const traitScores: { [key: string]: number[] } = {};
  const traitEvidence: { [key: string]: string[] } = {};
  const flaggedResponses: PsychologicalAnalysisResult["flaggedResponses"] = [];

  // Initialize trait scores and evidence arrays
  humanTraits.forEach((trait) => {
    traitScores[trait.name] = [];
    traitEvidence[trait.name] = [];
  });

  // Process each response
  validResponses.forEach((response, index) => {
    const stimulusWord =
      stimulusWords && index < stimulusWords.length
        ? stimulusWords[index]
        : "unknown";

    // Check for violence or unrealistic heroism
    if (containsPatterns(response, violencePatterns)) {
      flaggedResponses.push({
        responseIndex: index,
        response,
        reason: "Contains violent language",
        severity: "Medium",
      });
    }

    if (containsPatterns(response, unrealisticHeroismPatterns)) {
      flaggedResponses.push({
        responseIndex: index,
        response,
        reason: "Contains unrealistic heroism",
        severity: "Low",
      });
    }

    // Analyze for each trait
    humanTraits.forEach((trait) => {
      const { score, evidence } = evaluateTraitInWAT(
        response,
        stimulusWord,
        trait
      );
      traitScores[trait.name].push(score);
      if (evidence) {
        traitEvidence[trait.name].push(evidence);
      }
    });
  });

  // Calculate average scores for each trait
  humanTraits.forEach((trait) => {
    const scores = traitScores[trait.name];
    const evidence = traitEvidence[trait.name];

    if (scores.length > 0) {
      const avgScore =
        scores.reduce((sum, score) => sum + score, 0) / scores.length;
      result.humanTraits[trait.name] = {
        score: Math.round(avgScore),
        evidence: evidence.slice(0, 3), // Limit to top 3 pieces of evidence
        feedback: generateTraitFeedback(trait.name, avgScore, evidence),
      };
    }
  });

  // Analyze time awareness if time data is provided
  if (timePerResponse && timePerResponse.length > 0) {
    const avgTime =
      timePerResponse.reduce((sum, time) => sum + time, 0) /
      timePerResponse.length;

    // WAT should be quick (15 seconds per word is standard)
    let responseSpeed: "Fast" | "Moderate" | "Slow";
    let pressureHandling: number;

    if (avgTime < 5) {
      responseSpeed = "Fast";
      pressureHandling = 85;
    } else if (avgTime < 10) {
      responseSpeed = "Moderate";
      pressureHandling = 70;
    } else {
      responseSpeed = "Slow";
      pressureHandling = 50;
    }

    result.timeAwareness = {
      responseSpeed,
      pressureHandling,
      analysis: `Average response time of ${avgTime.toFixed(
        1
      )} seconds indicates ${responseSpeed.toLowerCase()} word association, which is ${
        responseSpeed === "Fast"
          ? "excellent"
          : responseSpeed === "Moderate"
          ? "good"
          : "concerning"
      } for WAT performance.`,
    };
  }

  // Calculate overall score (weighted average of trait scores)
  const traitScoresArray = Object.values(result.humanTraits).map(
    (t) => t.score
  );
  if (traitScoresArray.length > 0) {
    result.overallScore = Math.round(
      traitScoresArray.reduce((sum, score) => sum + score, 0) /
        traitScoresArray.length
    );
  }

  // Determine strengths and growth opportunities
  const sortedTraits = Object.entries(result.humanTraits).sort(
    (a, b) => b[1].score - a[1].score
  );

  result.strengths = sortedTraits
    .slice(0, 3)
    .filter(([, data]) => data.score >= 70)
    .map(([name]) => name);

  result.growthOpportunities = sortedTraits
    .slice(-3)
    .filter(([, data]) => data.score <= 60)
    .map(([name]) => name)
    .reverse();

  // Generate detailed feedback
  result.detailedFeedback = generateWATDetailedFeedback(
    result.humanTraits,
    result.strengths,
    result.growthOpportunities,
    flaggedResponses
  );

  // Add flagged responses to result
  result.flaggedResponses = flaggedResponses;

  return result;
};

/**
 * Analyze TAT (Thematic Apperception Test) responses
 * @param responses Array of stories created for TAT images
 * @param timePerResponse Time taken for each response (in seconds)
 * @returns Psychological analysis result
 */
export const analyzeTATResponses = (
  responses: string[],
  timePerResponse?: number[]
): PsychologicalAnalysisResult => {
  // Initialize result with empty structure
  const result = createEmptyAnalysisResult();

  // If no responses, return empty result
  if (!responses || responses.length === 0) {
    return result;
  }

  // Validate input
  const validResponses = responses.filter((r) => r && r.trim() !== "");
  if (validResponses.length === 0) {
    return result;
  }

  // Mark as valid since we have responses to analyze
  result.isValid = true;

  // Analyze each response
  const traitScores: { [key: string]: number[] } = {};
  const traitEvidence: { [key: string]: string[] } = {};
  const flaggedResponses: PsychologicalAnalysisResult["flaggedResponses"] = [];

  // Initialize trait scores and evidence arrays
  humanTraits.forEach((trait) => {
    traitScores[trait.name] = [];
    traitEvidence[trait.name] = [];
  });

  // Process each response
  validResponses.forEach((response, index) => {
    // Check for narrative elements (beginning, conflict, resolution)
    const hasNarrativeStructure = checkNarrativeStructure(response);

    // Check for violence or unrealistic heroism
    if (
      containsPatterns(response, violencePatterns) &&
      !isContextualViolence(response)
    ) {
      flaggedResponses.push({
        responseIndex: index,
        response: response.substring(0, 100) + "...",
        reason: "Contains non-contextual violent language",
        severity: "Medium",
      });
    }

    if (containsPatterns(response, unrealisticHeroismPatterns)) {
      flaggedResponses.push({
        responseIndex: index,
        response: response.substring(0, 100) + "...",
        reason: "Contains unrealistic heroism",
        severity: "Medium",
      });
    }

    // Analyze for each trait
    humanTraits.forEach((trait) => {
      const { score, evidence } = evaluateTraitInTAT(
        response,
        trait,
        hasNarrativeStructure
      );
      traitScores[trait.name].push(score);
      if (evidence) {
        traitEvidence[trait.name].push(evidence);
      }
    });
  });

  // Calculate average scores for each trait
  humanTraits.forEach((trait) => {
    const scores = traitScores[trait.name];
    const evidence = traitEvidence[trait.name];

    if (scores.length > 0) {
      const avgScore =
        scores.reduce((sum, score) => sum + score, 0) / scores.length;
      result.humanTraits[trait.name] = {
        score: Math.round(avgScore),
        evidence: evidence.slice(0, 3), // Limit to top 3 pieces of evidence
        feedback: generateTraitFeedback(trait.name, avgScore, evidence),
      };
    }
  });

  // Analyze time awareness if time data is provided
  if (timePerResponse && timePerResponse.length > 0) {
    const avgTime =
      timePerResponse.reduce((sum, time) => sum + time, 0) /
      timePerResponse.length;

    // TAT should take around 4 minutes per image
    let responseSpeed: "Fast" | "Moderate" | "Slow";
    let pressureHandling: number;

    if (avgTime < 180) {
      // Less than 3 minutes
      responseSpeed = "Fast";
      pressureHandling = 65; // Fast might indicate rushing
    } else if (avgTime < 300) {
      // 3-5 minutes (ideal)
      responseSpeed = "Moderate";
      pressureHandling = 85;
    } else {
      // More than 5 minutes
      responseSpeed = "Slow";
      pressureHandling = 60; // Slow might indicate overthinking
    }

    result.timeAwareness = {
      responseSpeed,
      pressureHandling,
      analysis: `Average response time of ${Math.round(
        avgTime / 60
      )} minutes per image indicates ${responseSpeed.toLowerCase()} story creation. ${
        responseSpeed === "Moderate"
          ? "This is ideal for developing complete narratives with appropriate detail."
          : responseSpeed === "Fast"
          ? "This may indicate quick thinking but could result in less developed narratives."
          : "This may indicate thorough consideration but could suggest difficulty with time management."
      }`,
    };
  }

  // Calculate overall score (weighted average of trait scores)
  const traitScoresArray = Object.values(result.humanTraits).map(
    (t) => t.score
  );
  if (traitScoresArray.length > 0) {
    result.overallScore = Math.round(
      traitScoresArray.reduce((sum, score) => sum + score, 0) /
        traitScoresArray.length
    );
  }

  // Determine strengths and growth opportunities
  const sortedTraits = Object.entries(result.humanTraits).sort(
    (a, b) => b[1].score - a[1].score
  );

  result.strengths = sortedTraits
    .slice(0, 3)
    .filter(([, data]) => data.score >= 70)
    .map(([name]) => name);

  result.growthOpportunities = sortedTraits
    .slice(-3)
    .filter(([, data]) => data.score <= 60)
    .map(([name]) => name)
    .reverse();

  // Generate detailed feedback
  result.detailedFeedback = generateTATDetailedFeedback(
    result.humanTraits,
    result.strengths,
    result.growthOpportunities,
    flaggedResponses
  );

  // Add flagged responses to result
  result.flaggedResponses = flaggedResponses;

  return result;
};

/**
 * Analyze SRT (Situational Reaction Test) responses
 * @param responses Array of reactions to situations
 * @param situations Array of situations that prompted the responses
 * @param timePerResponse Time taken for each response (in seconds)
 * @returns Psychological analysis result
 */
export const analyzeSRTResponses = (
  responses: string[],
  situations: string[],
  timePerResponse?: number[]
): PsychologicalAnalysisResult => {
  // Initialize result with empty structure
  const result = createEmptyAnalysisResult();

  // If no responses, return empty result
  if (!responses || responses.length === 0) {
    return result;
  }

  // Validate input
  const validResponses = responses.filter((r) => r && r.trim() !== "");
  if (validResponses.length === 0) {
    return result;
  }

  // Mark as valid since we have responses to analyze
  result.isValid = true;

  // Analyze each response
  const traitScores: { [key: string]: number[] } = {};
  const traitEvidence: { [key: string]: string[] } = {};
  const flaggedResponses: PsychologicalAnalysisResult["flaggedResponses"] = [];

  // Initialize trait scores and evidence arrays
  humanTraits.forEach((trait) => {
    traitScores[trait.name] = [];
    traitEvidence[trait.name] = [];
  });

  // Process each response
  validResponses.forEach((response, index) => {
    const situation =
      situations && index < situations.length
        ? situations[index]
        : "unknown situation";

    // Check for solution viability
    const solutionViability = assessSolutionViability(response);

    // Check for violence or unrealistic heroism
    if (
      containsPatterns(response, violencePatterns) &&
      !isContextualAction(response, situation)
    ) {
      flaggedResponses.push({
        responseIndex: index,
        response: response.substring(0, 100) + "...",
        reason: "Contains non-contextual violent language",
        severity: "Medium",
      });
    }

    if (containsPatterns(response, unrealisticHeroismPatterns)) {
      flaggedResponses.push({
        responseIndex: index,
        response: response.substring(0, 100) + "...",
        reason: "Contains unrealistic heroism",
        severity: "High",
      });
    }

    // Analyze for each trait
    humanTraits.forEach((trait) => {
      const { score, evidence } = evaluateTraitInSRT(
        response,
        situation,
        trait,
        solutionViability
      );
      traitScores[trait.name].push(score);
      if (evidence) {
        traitEvidence[trait.name].push(evidence);
      }
    });
  });

  // Calculate average scores for each trait
  humanTraits.forEach((trait) => {
    const scores = traitScores[trait.name];
    const evidence = traitEvidence[trait.name];

    if (scores.length > 0) {
      const avgScore =
        scores.reduce((sum, score) => sum + score, 0) / scores.length;
      result.humanTraits[trait.name] = {
        score: Math.round(avgScore),
        evidence: evidence.slice(0, 3), // Limit to top 3 pieces of evidence
        feedback: generateTraitFeedback(trait.name, avgScore, evidence),
      };
    }
  });

  // Analyze time awareness if time data is provided
  if (timePerResponse && timePerResponse.length > 0) {
    const avgTime =
      timePerResponse.reduce((sum, time) => sum + time, 0) /
      timePerResponse.length;

    // SRT should take around 30 seconds per situation
    let responseSpeed: "Fast" | "Moderate" | "Slow";
    let pressureHandling: number;

    if (avgTime < 15) {
      // Less than 15 seconds
      responseSpeed = "Fast";
      pressureHandling = 80; // Fast is good for SRT
    } else if (avgTime < 40) {
      // 15-40 seconds (ideal)
      responseSpeed = "Moderate";
      pressureHandling = 75;
    } else {
      // More than 40 seconds
      responseSpeed = "Slow";
      pressureHandling = 50; // Slow is concerning for SRT
    }

    result.timeAwareness = {
      responseSpeed,
      pressureHandling,
      analysis: `Average response time of ${avgTime.toFixed(
        1
      )} seconds indicates ${responseSpeed.toLowerCase()} decision-making. ${
        responseSpeed === "Fast"
          ? "This shows good ability to make decisions under pressure."
          : responseSpeed === "Moderate"
          ? "This shows balanced consideration and timely decision-making."
          : "This may indicate overthinking or difficulty making decisions under pressure."
      }`,
    };
  }

  // Calculate overall score (weighted average of trait scores)
  const traitScoresArray = Object.values(result.humanTraits).map(
    (t) => t.score
  );
  if (traitScoresArray.length > 0) {
    result.overallScore = Math.round(
      traitScoresArray.reduce((sum, score) => sum + score, 0) /
        traitScoresArray.length
    );
  }

  // Determine strengths and growth opportunities
  const sortedTraits = Object.entries(result.humanTraits).sort(
    (a, b) => b[1].score - a[1].score
  );

  result.strengths = sortedTraits
    .slice(0, 3)
    .filter(([, data]) => data.score >= 70)
    .map(([name]) => name);

  result.growthOpportunities = sortedTraits
    .slice(-3)
    .filter(([, data]) => data.score <= 60)
    .map(([name]) => name)
    .reverse();

  // Generate detailed feedback
  result.detailedFeedback = generateSRTDetailedFeedback(
    result.humanTraits,
    result.strengths,
    result.growthOpportunities,
    flaggedResponses
  );

  // Add flagged responses to result
  result.flaggedResponses = flaggedResponses;

  return result;
};

/**
 * Analyze SDT (Self Description Test) responses
 * @param responses Array of self-descriptions
 * @param questions Array of questions that prompted the responses
 * @returns Psychological analysis result
 */
export const analyzeSDTResponses = (
  responses: string[],
  questions: string[]
): PsychologicalAnalysisResult => {
  // Initialize result with empty structure
  const result = createEmptyAnalysisResult();

  // If no responses, return empty result
  if (!responses || responses.length === 0) {
    return result;
  }

  // Validate input
  const validResponses = responses.filter((r) => r && r.trim() !== "");
  if (validResponses.length === 0) {
    return result;
  }

  // Mark as valid since we have responses to analyze
  result.isValid = true;

  // Analyze each response
  const traitScores: { [key: string]: number[] } = {};
  const traitEvidence: { [key: string]: string[] } = {};
  const flaggedResponses: PsychologicalAnalysisResult["flaggedResponses"] = [];

  // Initialize trait scores and evidence arrays
  humanTraits.forEach((trait) => {
    traitScores[trait.name] = [];
    traitEvidence[trait.name] = [];
  });

  // Process each response
  validResponses.forEach((response, index) => {
    const question =
      questions && index < questions.length
        ? questions[index]
        : "unknown question";

    // Check for self-awareness
    const selfAwarenessScore = assessSelfAwareness(response);

    // Check for violence or unrealistic heroism
    if (containsPatterns(response, violencePatterns)) {
      flaggedResponses.push({
        responseIndex: index,
        response: response.substring(0, 100) + "...",
        reason: "Contains violent language in self-description",
        severity: "High",
      });
    }

    if (containsPatterns(response, unrealisticHeroismPatterns)) {
      flaggedResponses.push({
        responseIndex: index,
        response: response.substring(0, 100) + "...",
        reason: "Contains unrealistic heroism in self-description",
        severity: "High",
      });
    }

    // Analyze for each trait
    humanTraits.forEach((trait) => {
      const { score, evidence } = evaluateTraitInSDT(
        response,
        question,
        trait,
        selfAwarenessScore
      );
      traitScores[trait.name].push(score);
      if (evidence) {
        traitEvidence[trait.name].push(evidence);
      }
    });
  });

  // Calculate average scores for each trait
  humanTraits.forEach((trait) => {
    const scores = traitScores[trait.name];
    const evidence = traitEvidence[trait.name];

    if (scores.length > 0) {
      const avgScore =
        scores.reduce((sum, score) => sum + score, 0) / scores.length;
      result.humanTraits[trait.name] = {
        score: Math.round(avgScore),
        evidence: evidence.slice(0, 3), // Limit to top 3 pieces of evidence
        feedback: generateTraitFeedback(trait.name, avgScore, evidence),
      };
    }
  });

  // Calculate overall score (weighted average of trait scores)
  const traitScoresArray = Object.values(result.humanTraits).map(
    (t) => t.score
  );
  if (traitScoresArray.length > 0) {
    result.overallScore = Math.round(
      traitScoresArray.reduce((sum, score) => sum + score, 0) /
        traitScoresArray.length
    );
  }

  // Determine strengths and growth opportunities
  const sortedTraits = Object.entries(result.humanTraits).sort(
    (a, b) => b[1].score - a[1].score
  );

  result.strengths = sortedTraits
    .slice(0, 3)
    .filter(([, data]) => data.score >= 70)
    .map(([name]) => name);

  result.growthOpportunities = sortedTraits
    .slice(-3)
    .filter(([, data]) => data.score <= 60)
    .map(([name]) => name)
    .reverse();

  // Generate detailed feedback
  result.detailedFeedback = generateSDTDetailedFeedback(
    result.humanTraits,
    result.strengths,
    result.growthOpportunities,
    flaggedResponses
  );

  // Add flagged responses to result
  result.flaggedResponses = flaggedResponses;

  // For SDT, time awareness is not applicable
  result.timeAwareness = {
    responseSpeed: "Moderate",
    pressureHandling: 50,
    analysis:
      "Time pressure is not a significant factor in self-description assessment.",
  };

  return result;
};

/**
 * Create an empty analysis result structure
 * @returns Empty psychological analysis result
 */
const createEmptyAnalysisResult = (): PsychologicalAnalysisResult => {
  const traits: {
    [key: string]: { score: number; evidence: string[]; feedback: string };
  } = {};

  // Initialize traits with default values
  humanTraits.forEach((trait) => {
    traits[trait.name] = {
      score: 50, // Neutral starting point
      evidence: [],
      feedback: `No data available to assess ${trait.name.toLowerCase()}.`,
    };
  });

  return {
    overallScore: 50,
    humanTraits: traits,
    strengths: [],
    growthOpportunities: [],
    detailedFeedback: "No responses analyzed.",
    timeAwareness: {
      responseSpeed: "Moderate",
      pressureHandling: 50,
      analysis: "No time data available to assess response speed.",
    },
    flaggedResponses: [],
    isValid: false,
  };
};

/**
 * Check if a string contains any patterns from a list
 * @param text Text to check
 * @param patterns Array of patterns to look for
 * @returns True if any pattern is found, false otherwise
 */
const containsPatterns = (text: string, patterns: string[]): boolean => {
  const lowerText = text.toLowerCase();
  return patterns.some((pattern) => lowerText.includes(pattern.toLowerCase()));
};

/**
 * Evaluate a trait in a WAT response
 * @param response The word association response
 * @param stimulusWord The stimulus word that prompted the response
 * @param trait The trait to evaluate
 * @returns Score and evidence for the trait
 */
const evaluateTraitInWAT = (
  response: string,
  stimulusWord: string,
  trait: HumanTrait
): { score: number; evidence?: string } => {
  const lowerResponse = response.toLowerCase();

  // Check for positive indicators
  const positiveMatch = trait.positiveIndicators.some((indicator) => {
    // For WAT, we need to be more flexible with indicators since responses are single words
    const keywords = indicator.split(" ");
    return keywords.some((keyword) =>
      lowerResponse.includes(keyword.toLowerCase())
    );
  });

  // Check for negative indicators
  const negativeMatch = trait.negativeIndicators.some((indicator) => {
    const keywords = indicator.split(" ");
    return keywords.some((keyword) =>
      lowerResponse.includes(keyword.toLowerCase())
    );
  });

  // Calculate score based on matches
  let score = 50; // Neutral starting point
  let evidence: string | undefined;

  if (positiveMatch && !negativeMatch) {
    score = 75 + Math.floor(Math.random() * 15); // 75-90
    evidence = `Response "${response}" to stimulus "${stimulusWord}" shows ${trait.name.toLowerCase()}.`;
  } else if (negativeMatch && !positiveMatch) {
    score = 30 + Math.floor(Math.random() * 20); // 30-50
    evidence = `Response "${response}" to stimulus "${stimulusWord}" lacks ${trait.name.toLowerCase()}.`;
  } else if (positiveMatch && negativeMatch) {
    score = 50 + Math.floor(Math.random() * 10); // 50-60
    evidence = `Response "${response}" to stimulus "${stimulusWord}" shows mixed ${trait.name.toLowerCase()}.`;
  }

  // Special case evaluations for specific traits in WAT
  switch (trait.name) {
    case "Thought Coherence":
      // In WAT, logical connections between stimulus and response indicate coherence
      const isLogical = isLogicalAssociation(stimulusWord, response);
      if (isLogical) {
        score = Math.max(score, 70);
        evidence = `Logical association between "${stimulusWord}" and "${response}" shows good thought coherence.`;
      } else if (score < 60) {
        evidence = `Unclear association between "${stimulusWord}" and "${response}" suggests lower thought coherence.`;
      }
      break;

    case "Positivity":
      // Check if response has positive emotional valence
      const emotionalValence = getEmotionalValence(response);
      if (emotionalValence > 0) {
        score = Math.max(score, 70);
        evidence = `Positive word "${response}" in response to "${stimulusWord}" indicates positivity.`;
      } else if (emotionalValence < 0) {
        score = Math.min(score, 40);
        evidence = `Negative word "${response}" in response to "${stimulusWord}" indicates lack of positivity.`;
      }
      break;
  }

  return { score, evidence };
};

/**
 * Determine if there's a logical association between stimulus and response
 * @param stimulus Stimulus word
 * @param response Response word
 * @returns True if there's a logical association
 */
const isLogicalAssociation = (stimulus: string, response: string): boolean => {
  // This is a simplified implementation
  // In a real system, this would use a semantic similarity algorithm or database

  // Common associations (very limited example set)
  const commonAssociations: { [key: string]: string[] } = {
    water: [
      "drink",
      "ocean",
      "river",
      "wet",
      "blue",
      "swim",
      "fish",
      "boat",
      "lake",
      "sea",
    ],
    fire: [
      "hot",
      "burn",
      "flame",
      "smoke",
      "heat",
      "red",
      "camp",
      "wood",
      "light",
      "danger",
    ],
    family: [
      "love",
      "parents",
      "children",
      "home",
      "care",
      "support",
      "together",
      "relatives",
    ],
    money: [
      "rich",
      "poor",
      "wealth",
      "bank",
      "cash",
      "coin",
      "currency",
      "dollar",
      "finance",
    ],
    time: [
      "clock",
      "watch",
      "hour",
      "minute",
      "second",
      "day",
      "night",
      "past",
      "future",
    ],
  };

  // Check if there's a known association
  const lowerStimulus = stimulus.toLowerCase();
  const lowerResponse = response.toLowerCase();

  if (commonAssociations[lowerStimulus]) {
    return commonAssociations[lowerStimulus].includes(lowerResponse);
  }

  // If no known association, check for basic linguistic relationships
  // Same starting letter (alliteration)
  if (lowerStimulus[0] === lowerResponse[0]) {
    return true;
  }

  // Rhyming (very simplified check)
  if (
    lowerStimulus.slice(-2) === lowerResponse.slice(-2) &&
    lowerStimulus.length > 2 &&
    lowerResponse.length > 2
  ) {
    return true;
  }

  // Default to true for now to avoid penalizing valid but uncommon associations
  return true;
};

/**
 * Get the emotional valence of a word (positive, negative, or neutral)
 * @param word Word to evaluate
 * @returns Valence score (-1 to 1)
 */
const getEmotionalValence = (word: string): number => {
  // This is a simplified implementation
  // In a real system, this would use a sentiment analysis API or lexicon

  const positiveWords = [
    "happy",
    "joy",
    "love",
    "peace",
    "good",
    "nice",
    "kind",
    "friend",
    "smile",
    "laugh",
    "hope",
    "success",
    "win",
    "achieve",
    "accomplish",
    "beautiful",
    "brave",
    "calm",
    "care",
    "confident",
    "courage",
    "dream",
    "enjoy",
    "excel",
    "fun",
    "gentle",
    "glad",
    "grow",
    "health",
  ];

  const negativeWords = [
    "sad",
    "anger",
    "hate",
    "war",
    "bad",
    "mean",
    "cruel",
    "enemy",
    "frown",
    "cry",
    "despair",
    "failure",
    "lose",
    "fail",
    "ugly",
    "afraid",
    "anxious",
    "break",
    "burden",
    "chaos",
    "damage",
    "danger",
    "dead",
    "defeat",
    "destroy",
    "difficult",
    "disaster",
    "disease",
  ];

  const lowerWord = word.toLowerCase();

  if (positiveWords.includes(lowerWord)) {
    return 1;
  } else if (negativeWords.includes(lowerWord)) {
    return -1;
  }

  return 0; // Neutral
};

/**
 * Generate feedback for a trait based on its score
 * @param traitName Name of the trait
 * @param score Score for the trait
 * @param evidence Evidence for the trait evaluation
 * @returns Feedback string
 */
const generateTraitFeedback = (
  traitName: string,
  score: number,
  evidence: string[]
): string => {
  if (score >= 80) {
    return `Excellent ${traitName.toLowerCase()}. ${evidence[0] || ""}`;
  } else if (score >= 70) {
    return `Good ${traitName.toLowerCase()}. ${evidence[0] || ""}`;
  } else if (score >= 50) {
    return `Average ${traitName.toLowerCase()}. Consider developing this trait further.`;
  } else if (score >= 30) {
    return `Below average ${traitName.toLowerCase()}. This is an area for improvement.`;
  } else {
    return `Significant development needed in ${traitName.toLowerCase()}.`;
  }
};

/**
 * Generate detailed feedback for WAT responses
 * @param traits Trait scores and evidence
 * @param strengths Identified strengths
 * @param growthOpportunities Identified growth opportunities
 * @param flaggedResponses Flagged responses
 * @returns Detailed feedback string
 */
const generateWATDetailedFeedback = (
  traits: PsychologicalAnalysisResult["humanTraits"],
  strengths: string[],
  growthOpportunities: string[],
  flaggedResponses: PsychologicalAnalysisResult["flaggedResponses"]
): string => {
  let feedback = "";

  // Overall assessment
  feedback += "Word Association Analysis:\n\n";

  // Strengths section
  if (strengths.length > 0) {
    feedback += "Strengths:\n";
    strengths.forEach((strength) => {
      feedback += `- ${strength}: ${traits[strength].feedback}\n`;
    });
    feedback += "\n";
  }

  // Growth opportunities section
  if (growthOpportunities.length > 0) {
    feedback += "Growth Opportunities:\n";
    growthOpportunities.forEach((opportunity) => {
      feedback += `- ${opportunity}: ${traits[opportunity].feedback}\n`;
    });
    feedback += "\n";
  }

  // Response tracking section
  feedback += "Response Tracking:\n";
  let responseCount = 1;
  Object.entries(traits).forEach(([, data]) => {
    if (data.evidence && data.evidence.length > 0) {
      feedback += `${responseCount}. ${data.evidence[0]}\n`;
      responseCount++;
    }
  });

  // Flagged responses section
  if (flaggedResponses.length > 0) {
    feedback += "\nFlagged Responses:\n";
    flaggedResponses.forEach((flagged) => {
      feedback += `- Response ${flagged.responseIndex + 1}: "${
        flagged.response
      }" - ${flagged.reason}\n`;
    });
  }

  return feedback;
};

/**
 * Check if a story has proper narrative structure
 * @param story The story to analyze
 * @returns True if the story has beginning, conflict, and resolution
 */
const checkNarrativeStructure = (story: string): boolean => {
  // This is a simplified implementation
  // In a real system, this would use NLP to analyze narrative structure

  // Check for beginning (introduction of characters/setting)
  const hasBeginning = /^.{50,}/.test(story); // At least 50 chars at start

  // Check for conflict (problem, challenge, tension)
  const hasConflict =
    /problem|challenge|difficult|trouble|conflict|issue|crisis|dilemma|obstacle|struggle/i.test(
      story
    );

  // Check for resolution (solution, outcome, result)
  const hasResolution =
    /solve|solution|resolve|decided|finally|outcome|result|end|conclusion/i.test(
      story
    );

  return hasBeginning && hasConflict && hasResolution;
};

/**
 * Check if violence mentioned in a story is contextual and appropriate
 * @param story The story to analyze
 * @returns True if violence is contextual, false if gratuitous
 */
const isContextualViolence = (story: string): boolean => {
  // This is a simplified implementation
  // In a real system, this would use more sophisticated NLP

  // Check if violence is in a historical/educational context
  const isHistoricalContext =
    /history|historical|war|battle|conflict|news|report|documentary/i.test(
      story
    );

  // Check if violence is described in an excessive way
  const isExcessiveDescription =
    /brutal|bloody|gory|graphic|excessive|extreme/i.test(story);

  // Check if violence is glorified
  const isGlorified =
    /glorious|awesome|cool|amazing|exciting|thrilling/i.test(story) &&
    containsPatterns(story, violencePatterns);

  return isHistoricalContext && !isExcessiveDescription && !isGlorified;
};

/**
 * Evaluate a trait in a TAT response
 * @param story The story created for the TAT image
 * @param trait The trait to evaluate
 * @param hasNarrativeStructure Whether the story has proper narrative structure
 * @returns Score and evidence for the trait
 */
const evaluateTraitInTAT = (
  story: string,
  trait: HumanTrait,
  hasNarrativeStructure: boolean
): { score: number; evidence?: string } => {
  // Check for positive indicators
  const positiveMatches = trait.positiveIndicators.filter((indicator) =>
    story.toLowerCase().includes(indicator.toLowerCase())
  );

  // Check for negative indicators
  const negativeMatches = trait.negativeIndicators.filter((indicator) =>
    story.toLowerCase().includes(indicator.toLowerCase())
  );

  // Calculate score based on matches
  let score = 50; // Neutral starting point
  let evidence: string | undefined;

  if (positiveMatches.length > 0 && negativeMatches.length === 0) {
    // More positive matches = higher score
    score = 70 + Math.min(20, positiveMatches.length * 5);
    evidence = `Story demonstrates ${trait.name.toLowerCase()} through ${
      positiveMatches[0]
    }.`;
  } else if (negativeMatches.length > 0 && positiveMatches.length === 0) {
    // More negative matches = lower score
    score = 50 - Math.min(30, negativeMatches.length * 5);
    evidence = `Story lacks ${trait.name.toLowerCase()}, showing ${
      negativeMatches[0]
    }.`;
  } else if (positiveMatches.length > 0 && negativeMatches.length > 0) {
    // Mixed indicators
    score = 50 + (positiveMatches.length - negativeMatches.length) * 3;
    evidence = `Story shows mixed ${trait.name.toLowerCase()}.`;
  }

  // Special case evaluations for specific traits in TAT
  switch (trait.name) {
    case "Thought Coherence":
      // Narrative structure is crucial for thought coherence in TAT
      if (hasNarrativeStructure) {
        score = Math.max(score, 75);
        evidence =
          "Story demonstrates good thought coherence with clear beginning, conflict, and resolution.";
      } else {
        score = Math.min(score, 45);
        evidence =
          "Story lacks proper narrative structure, indicating lower thought coherence.";
      }
      break;

    case "Problem-Solving":
      // Check if the story includes problem resolution
      if (
        story.match(
          /solve|solution|resolve|overcome|address|fix|handle|manage|deal with/i
        )
      ) {
        score = Math.max(score, 70);
        evidence =
          "Story demonstrates problem-solving through effective resolution of conflict.";
      }
      break;

    case "Emotional Balance":
      // Check for emotional awareness in characters
      if (
        story.match(
          /feel|emotion|sense|thought|realize|understand|perspective/i
        )
      ) {
        score += 10;
        evidence =
          "Story demonstrates emotional awareness through character development.";
      }
      break;
  }

  return { score, evidence };
};

/**
 * Generate detailed feedback for TAT responses
 * @param traits Trait scores and evidence
 * @param strengths Identified strengths
 * @param growthOpportunities Identified growth opportunities
 * @param flaggedResponses Flagged responses
 * @param responseCount Number of valid responses
 * @returns Detailed feedback string
 */
const generateTATDetailedFeedback = (
  traits: PsychologicalAnalysisResult["humanTraits"],
  strengths: string[],
  growthOpportunities: string[],
  flaggedResponses: PsychologicalAnalysisResult["flaggedResponses"]
  // responseCount parameter is not used but kept for API consistency
): string => {
  let feedback = "";

  // Overall assessment
  feedback += "Thematic Apperception Analysis:\n\n";

  // Narrative authenticity assessment
  const thoughtCoherenceScore = traits["Thought Coherence"]?.score || 0;
  feedback += `Narrative Authenticity: ${
    thoughtCoherenceScore >= 75
      ? "Excellent. Your stories demonstrate realistic human emotions and logical narrative arcs."
      : thoughtCoherenceScore >= 60
      ? "Good. Your stories generally show realistic scenarios with logical progression."
      : "Needs improvement. Focus on creating more realistic narratives with clear beginning, conflict, and resolution."
  }\n\n`;

  // Strengths section
  if (strengths.length > 0) {
    feedback += "Strengths:\n";
    strengths.forEach((strength) => {
      feedback += `- ${strength}: ${traits[strength].feedback}\n`;
    });
    feedback += "\n";
  }

  // Growth opportunities section
  if (growthOpportunities.length > 0) {
    feedback += "Growth Opportunities:\n";
    growthOpportunities.forEach((opportunity) => {
      feedback += `- ${opportunity}: ${traits[opportunity].feedback}\n`;
    });
    feedback += "\n";
  }

  // Response tracking section
  feedback += "Response Tracking:\n";
  let responseTrackingCount = 1;
  Object.entries(traits).forEach(([, data]) => {
    if (
      data.evidence &&
      data.evidence.length > 0 &&
      responseTrackingCount <= 5
    ) {
      feedback += `${responseTrackingCount}. ${data.evidence[0]}\n`;
      responseTrackingCount++;
    }
  });

  // Flagged responses section
  if (flaggedResponses.length > 0) {
    feedback += "\nFlagged Responses:\n";
    flaggedResponses.forEach((flagged) => {
      feedback += `- Response ${flagged.responseIndex + 1}: "${
        flagged.response
      }" - ${flagged.reason}\n`;
    });

    // Add guidance for improving flagged responses
    feedback += "\nGuidance for Improvement:\n";
    feedback +=
      "- Focus on realistic human emotions and behaviors rather than exaggerated heroism\n";
    feedback +=
      "- Develop logical story arcs with believable conflicts and resolutions\n";
    feedback += "- Avoid gratuitous violence or unrealistic scenarios\n";
  }

  return feedback;
};

/**
 * Assess the viability of a solution in an SRT response
 * @param response The response to analyze
 * @returns Viability score (0-100)
 */
const assessSolutionViability = (response: string): number => {
  // This is a simplified implementation
  // In a real system, this would use more sophisticated NLP

  // Check for team-oriented language
  const hasTeamOrientation =
    /team|together|collaborate|help|assist|support|we|us|our/i.test(response);

  // Check for practical solutions
  const hasPracticalSolution =
    /would|will|could|can|should|plan|approach|strategy|method|step|process/i.test(
      response
    );

  // Check for consideration of consequences
  const hasConsequenceConsideration =
    /because|result|outcome|effect|impact|consequence|therefore|so that|in order to/i.test(
      response
    );

  // Check for ethical considerations
  const hasEthicalConsideration =
    /right|wrong|ethical|moral|appropriate|responsible|duty|obligation|should/i.test(
      response
    );

  // Calculate viability score
  let viabilityScore = 50; // Base score

  if (hasTeamOrientation) viabilityScore += 15;
  if (hasPracticalSolution) viabilityScore += 15;
  if (hasConsequenceConsideration) viabilityScore += 10;
  if (hasEthicalConsideration) viabilityScore += 10;

  // Cap at 100
  return Math.min(100, viabilityScore);
};

/**
 * Check if an action is contextually appropriate given the situation
 * @param response The response to analyze
 * @param situation The situation that prompted the response
 * @returns True if the action is contextually appropriate
 */
const isContextualAction = (response: string, situation: string): boolean => {
  // This is a simplified implementation
  // In a real system, this would use more sophisticated NLP

  // Check if the situation involves danger or emergency
  const isDangerousSituation =
    /danger|emergency|threat|attack|fire|accident|injury|critical|urgent|life-threatening/i.test(
      situation
    );

  // Check if the response involves defensive action
  const isDefensiveAction =
    /defend|protect|secure|safety|evacuate|shield|guard|rescue/i.test(response);

  // Check if the response involves reporting to authorities
  const isReportingAction =
    /report|inform|alert|notify|call|contact|police|authority|supervisor|officer/i.test(
      response
    );

  // If the situation is dangerous, defensive or reporting actions are contextually appropriate
  if (isDangerousSituation && (isDefensiveAction || isReportingAction)) {
    return true;
  }

  // Check if the situation involves ethical dilemma
  const isEthicalDilemma =
    /ethical|moral|dilemma|right|wrong|illegal|against rules|policy|regulation/i.test(
      situation
    );

  // Check if the response involves ethical consideration
  const hasEthicalConsideration =
    /right|wrong|ethical|moral|appropriate|responsible|duty|obligation|should/i.test(
      response
    );

  // If the situation involves an ethical dilemma, ethical considerations are contextually appropriate
  if (isEthicalDilemma && hasEthicalConsideration) {
    return true;
  }

  // Default to false for any violent language not justified by context
  return false;
};

/**
 * Evaluate a trait in an SRT response
 * @param response The response to the situation
 * @param situation The situation that prompted the response
 * @param trait The trait to evaluate
 * @param solutionViability Viability score of the solution
 * @returns Score and evidence for the trait
 */
const evaluateTraitInSRT = (
  response: string,
  _situation: string, // Kept for API consistency but not used in this implementation
  trait: HumanTrait,
  solutionViability: number
): { score: number; evidence?: string } => {
  // Check for positive indicators
  const positiveMatches = trait.positiveIndicators.filter((indicator) =>
    response.toLowerCase().includes(indicator.toLowerCase())
  );

  // Check for negative indicators
  const negativeMatches = trait.negativeIndicators.filter((indicator) =>
    response.toLowerCase().includes(indicator.toLowerCase())
  );

  // Calculate score based on matches
  let score = 50; // Neutral starting point
  let evidence: string | undefined;

  if (positiveMatches.length > 0 && negativeMatches.length === 0) {
    // More positive matches = higher score
    score = 70 + Math.min(20, positiveMatches.length * 5);
    evidence = `Response demonstrates ${trait.name.toLowerCase()} through ${
      positiveMatches[0]
    }.`;
  } else if (negativeMatches.length > 0 && positiveMatches.length === 0) {
    // More negative matches = lower score
    score = 50 - Math.min(30, negativeMatches.length * 5);
    evidence = `Response lacks ${trait.name.toLowerCase()}, showing ${
      negativeMatches[0]
    }.`;
  } else if (positiveMatches.length > 0 && negativeMatches.length > 0) {
    // Mixed indicators
    score = 50 + (positiveMatches.length - negativeMatches.length) * 3;
    evidence = `Response shows mixed ${trait.name.toLowerCase()}.`;
  }

  // Special case evaluations for specific traits in SRT
  switch (trait.name) {
    case "Problem-Solving":
      // Solution viability is crucial for problem-solving in SRT
      score = (score + solutionViability) / 2;
      if (solutionViability >= 80) {
        evidence =
          "Response demonstrates excellent problem-solving with a viable, practical solution.";
      } else if (solutionViability >= 60) {
        evidence =
          "Response demonstrates adequate problem-solving with a reasonable solution.";
      } else {
        evidence =
          "Response shows limited problem-solving with a less viable solution.";
      }
      break;

    case "Teamwork":
      // Check for team-oriented language
      if (
        /team|together|collaborate|help|assist|support|we|us|our/i.test(
          response
        )
      ) {
        score = Math.max(score, 75);
        evidence =
          "Response demonstrates teamwork through collaborative approach.";
      } else if (
        /I|me|my|myself/i.test(response) &&
        !/we|us|our|team/i.test(response)
      ) {
        score = Math.min(score, 45);
        evidence = "Response shows individualistic focus rather than teamwork.";
      }
      break;

    case "Ethical Reasoning":
      // Check for ethical considerations
      if (
        /right|wrong|ethical|moral|appropriate|responsible|duty|obligation|should/i.test(
          response
        )
      ) {
        score = Math.max(score, 70);
        evidence =
          "Response demonstrates ethical reasoning through consideration of moral principles.";
      }
      break;
  }

  return { score, evidence };
};

/**
 * Generate detailed feedback for SRT responses
 * @param traits Trait scores and evidence
 * @param strengths Identified strengths
 * @param growthOpportunities Identified growth opportunities
 * @param flaggedResponses Flagged responses
 * @param responseCount Number of valid responses
 * @returns Detailed feedback string
 */
const generateSRTDetailedFeedback = (
  traits: PsychologicalAnalysisResult["humanTraits"],
  strengths: string[],
  growthOpportunities: string[],
  flaggedResponses: PsychologicalAnalysisResult["flaggedResponses"]
  // responseCount parameter is not used but kept for API consistency
): string => {
  let feedback = "";

  // Overall assessment
  feedback += "Situational Reaction Analysis:\n\n";

  // Solution viability assessment
  const problemSolvingScore = traits["Problem-Solving"]?.score || 0;
  feedback += `Solution Viability: ${
    problemSolvingScore >= 75
      ? "Excellent. Your responses demonstrate practical, team-oriented solutions."
      : problemSolvingScore >= 60
      ? "Good. Your responses generally show viable solutions to the situations."
      : "Needs improvement. Focus on developing more practical and collaborative solutions."
  }\n\n`;

  // Emotional balance assessment
  const emotionalBalanceScore = traits["Emotional Balance"]?.score || 0;
  feedback += `Emotional Balance: ${
    emotionalBalanceScore >= 75
      ? "Excellent. Your responses demonstrate calm, measured reactions to situations."
      : emotionalBalanceScore >= 60
      ? "Good. Your responses generally show appropriate emotional regulation."
      : "Needs improvement. Focus on maintaining emotional balance in challenging situations."
  }\n\n`;

  // Strengths section
  if (strengths.length > 0) {
    feedback += "Strengths:\n";
    strengths.forEach((strength) => {
      feedback += `- ${strength}: ${traits[strength].feedback}\n`;
    });
    feedback += "\n";
  }

  // Growth opportunities section
  if (growthOpportunities.length > 0) {
    feedback += "Growth Opportunities:\n";
    growthOpportunities.forEach((opportunity) => {
      feedback += `- ${opportunity}: ${traits[opportunity].feedback}\n`;
    });
    feedback += "\n";
  }

  // Response tracking section
  feedback += "Response Tracking:\n";
  let responseTrackingCount = 1;
  Object.entries(traits).forEach(([, data]) => {
    if (
      data.evidence &&
      data.evidence.length > 0 &&
      responseTrackingCount <= 5
    ) {
      feedback += `${responseTrackingCount}. ${data.evidence[0]}\n`;
      responseTrackingCount++;
    }
  });

  // Flagged responses section
  if (flaggedResponses.length > 0) {
    feedback += "\nFlagged Responses:\n";
    flaggedResponses.forEach((flagged) => {
      feedback += `- Response ${flagged.responseIndex + 1}: "${
        flagged.response
      }" - ${flagged.reason}\n`;
    });

    // Add guidance for improving flagged responses
    feedback += "\nGuidance for Improvement:\n";
    feedback +=
      "- Focus on realistic, collaborative solutions rather than individual heroism\n";
    feedback += "- Consider the ethical implications of your actions\n";
    feedback += "- Maintain emotional balance even in challenging situations\n";
  }

  return feedback;
};

/**
 * Assess self-awareness in an SDT response
 * @param response The response to analyze
 * @returns Self-awareness score (0-100)
 */
const assessSelfAwareness = (response: string): number => {
  // This is a simplified implementation
  // In a real system, this would use more sophisticated NLP

  // Check for balanced self-assessment (both strengths and weaknesses)
  const hasStrengths =
    /strength|good at|excel|skilled|capable|proficient|talent|ability|positive|advantage/i.test(
      response
    );
  const hasWeaknesses =
    /weakness|struggle|difficult|challenge|improve|develop|learn|growth|negative|disadvantage/i.test(
      response
    );

  // Check for specific examples
  const hasSpecificExamples =
    /for example|instance|specifically|particular|case|situation|experience|event|time when/i.test(
      response
    );

  // Check for reflection on impact
  const hasImpactReflection =
    /impact|effect|influence|result|consequence|outcome|change|difference|improve|growth/i.test(
      response
    );

  // Check for emotional awareness
  const hasEmotionalAwareness =
    /feel|emotion|sense|perceive|experience|mood|attitude|mindset|outlook|perspective/i.test(
      response
    );

  // Calculate self-awareness score
  let selfAwarenessScore = 50; // Base score

  if (hasStrengths) selfAwarenessScore += 10;
  if (hasWeaknesses) selfAwarenessScore += 15; // Acknowledging weaknesses shows more self-awareness
  if (hasSpecificExamples) selfAwarenessScore += 15;
  if (hasImpactReflection) selfAwarenessScore += 10;
  if (hasEmotionalAwareness) selfAwarenessScore += 10;

  // Bonus for balanced assessment
  if (hasStrengths && hasWeaknesses) selfAwarenessScore += 10;

  // Cap at 100
  return Math.min(100, selfAwarenessScore);
};

/**
 * Evaluate a trait in an SDT response
 * @param response The self-description
 * @param question The question that prompted the response
 * @param trait The trait to evaluate
 * @param selfAwarenessScore Self-awareness score of the response
 * @returns Score and evidence for the trait
 */
const evaluateTraitInSDT = (
  response: string,
  _question: string, // Kept for API consistency but not used in this implementation
  trait: HumanTrait,
  selfAwarenessScore: number
): { score: number; evidence?: string } => {
  // Check for positive indicators
  const positiveMatches = trait.positiveIndicators.filter((indicator) =>
    response.toLowerCase().includes(indicator.toLowerCase())
  );

  // Check for negative indicators
  const negativeMatches = trait.negativeIndicators.filter((indicator) =>
    response.toLowerCase().includes(indicator.toLowerCase())
  );

  // Calculate score based on matches
  let score = 50; // Neutral starting point
  let evidence: string | undefined;

  if (positiveMatches.length > 0 && negativeMatches.length === 0) {
    // More positive matches = higher score
    score = 70 + Math.min(20, positiveMatches.length * 5);
    evidence = `Self-description demonstrates ${trait.name.toLowerCase()} through ${
      positiveMatches[0]
    }.`;
  } else if (negativeMatches.length > 0 && positiveMatches.length === 0) {
    // More negative matches = lower score
    score = 50 - Math.min(30, negativeMatches.length * 5);
    evidence = `Self-description lacks ${trait.name.toLowerCase()}, showing ${
      negativeMatches[0]
    }.`;
  } else if (positiveMatches.length > 0 && negativeMatches.length > 0) {
    // Mixed indicators
    score = 50 + (positiveMatches.length - negativeMatches.length) * 3;
    evidence = `Self-description shows mixed ${trait.name.toLowerCase()}.`;
  }

  // Special case evaluations for specific traits in SDT
  switch (trait.name) {
    case "Honesty":
      // Self-awareness is crucial for honesty in SDT
      score = (score + selfAwarenessScore) / 2;
      if (selfAwarenessScore >= 80) {
        evidence =
          "Self-description demonstrates excellent honesty through balanced self-assessment.";
      } else if (selfAwarenessScore >= 60) {
        evidence =
          "Self-description demonstrates adequate honesty with some self-awareness.";
      } else {
        evidence =
          "Self-description shows limited honesty with low self-awareness.";
      }
      break;

    case "Emotional Balance":
      // Check for emotional awareness
      if (
        /feel|emotion|sense|perceive|experience|mood|attitude|mindset|outlook|perspective/i.test(
          response
        )
      ) {
        score = Math.max(score, 70);
        evidence =
          "Self-description demonstrates emotional balance through awareness of feelings.";
      }
      break;

    case "Adaptability":
      // Check for growth mindset
      if (
        /learn|grow|develop|improve|change|adapt|flexible|adjust|evolve/i.test(
          response
        )
      ) {
        score = Math.max(score, 70);
        evidence =
          "Self-description demonstrates adaptability through growth mindset.";
      }
      break;
  }

  return { score, evidence };
};

/**
 * Generate detailed feedback for SDT responses
 * @param traits Trait scores and evidence
 * @param strengths Identified strengths
 * @param growthOpportunities Identified growth opportunities
 * @param flaggedResponses Flagged responses
 * @param responseCount Number of valid responses
 * @returns Detailed feedback string
 */
const generateSDTDetailedFeedback = (
  traits: PsychologicalAnalysisResult["humanTraits"],
  strengths: string[],
  growthOpportunities: string[],
  flaggedResponses: PsychologicalAnalysisResult["flaggedResponses"]
  // responseCount parameter is not used but kept for API consistency
): string => {
  let feedback = "";

  // Overall assessment
  feedback += "Self-Description Analysis:\n\n";

  // Self-awareness assessment
  const honestyScore = traits["Honesty"]?.score || 0;
  feedback += `Self-Awareness: ${
    honestyScore >= 75
      ? "Excellent. Your self-descriptions demonstrate balanced self-assessment with specific examples."
      : honestyScore >= 60
      ? "Good. Your self-descriptions generally show self-awareness with some specific examples."
      : "Needs improvement. Focus on balanced self-assessment with specific examples of both strengths and areas for growth."
  }\n\n`;

  // Social perception assessment
  const teamworkScore = traits["Teamwork"]?.score || 0;
  feedback += `Social Perception: ${
    teamworkScore >= 75
      ? "Excellent. Your descriptions of others demonstrate respectful, non-judgmental language."
      : teamworkScore >= 60
      ? "Good. Your descriptions of others generally show respect and consideration."
      : "Needs improvement. Focus on using more respectful, non-judgmental language when describing others."
  }\n\n`;

  // Strengths section
  if (strengths.length > 0) {
    feedback += "Strengths:\n";
    strengths.forEach((strength) => {
      feedback += `- ${strength}: ${traits[strength].feedback}\n`;
    });
    feedback += "\n";
  }

  // Growth opportunities section
  if (growthOpportunities.length > 0) {
    feedback += "Growth Opportunities:\n";
    growthOpportunities.forEach((opportunity) => {
      feedback += `- ${opportunity}: ${traits[opportunity].feedback}\n`;
    });
    feedback += "\n";
  }

  // Response tracking section
  feedback += "Response Tracking:\n";
  let responseTrackingCount = 1;
  Object.entries(traits).forEach(([, data]) => {
    if (
      data.evidence &&
      data.evidence.length > 0 &&
      responseTrackingCount <= 5
    ) {
      feedback += `${responseTrackingCount}. ${data.evidence[0]}\n`;
      responseTrackingCount++;
    }
  });

  // Flagged responses section
  if (flaggedResponses.length > 0) {
    feedback += "\nFlagged Responses:\n";
    flaggedResponses.forEach((flagged) => {
      feedback += `- Response ${flagged.responseIndex + 1}: "${
        flagged.response
      }" - ${flagged.reason}\n`;
    });

    // Add guidance for improving flagged responses
    feedback += "\nGuidance for Improvement:\n";
    feedback +=
      "- Focus on realistic self-assessment rather than exaggerated claims\n";
    feedback +=
      "- Balance discussion of strengths with acknowledgment of areas for growth\n";
    feedback +=
      "- Use specific examples to illustrate your traits and behaviors\n";
  }

  return feedback;
};

// Proposed unified evaluation function
const evaluateWATResponse = (
  stimulus: string,
  response: string,
  militaryContext: boolean = true
): { 
  quality: "Good" | "Moderate" | "Bad",
  reasoning: string 
} => {
  // 1. Check for direct associations (Deepseek strength)
  const associationQuality = evaluateWATAssociation(stimulus, response);
  
  // 2. Check for military relevance (both models)
  const hasMilitaryRelevance = containsMilitaryTerms(response);
  
  // 3. Check for psychological indicators (Gemini strength)
  const psychologicalValue = evaluatePsychologicalValue(response);
  
  // 4. Combine the evaluations with weighted scoring
  let finalScore = 0;
  finalScore += associationQuality === "Good" ? 3 : associationQuality === "Moderate" ? 1 : 0;
  finalScore += hasMilitaryRelevance ? 2 : 0;
  finalScore += psychologicalValue;
  
  // 5. Determine final quality
  let quality: "Good" | "Moderate" | "Bad";
  let reasoning: string;
  
  if (finalScore >= 4) {
    quality = "Good";
    reasoning = "Strong association with military relevance and psychological value";
  } else if (finalScore >= 2) {
    quality = "Moderate";
    reasoning = "Acceptable association but could be improved";
  } else {
    quality = "Bad";
    reasoning = "Weak association lacking military context";
  }
  
  return { quality, reasoning };
};

// Proposed unified evaluation function
const evaluateWATResponse = (
  stimulus: string,
  response: string,
  militaryContext: boolean = true
): { 
  quality: "Good" | "Moderate" | "Bad",
  reasoning: string 
} => {
  // 1. Check for direct associations (Deepseek strength)
  const associationQuality = evaluateWATAssociation(stimulus, response);
  
  // 2. Check for military relevance (both models)
  const hasMilitaryRelevance = containsMilitaryTerms(response);
  
  // 3. Check for psychological indicators (Gemini strength)
  const psychologicalValue = evaluatePsychologicalValue(response);
  
  // 4. Combine the evaluations with weighted scoring
  let finalScore = 0;
  finalScore += associationQuality === "Good" ? 3 : associationQuality === "Moderate" ? 1 : 0;
  finalScore += hasMilitaryRelevance ? 2 : 0;
  finalScore += psychologicalValue;
  
  // 5. Determine final quality
  let quality: "Good" | "Moderate" | "Bad";
  let reasoning: string;
  
  if (finalScore >= 4) {
    quality = "Good";
    reasoning = "Strong association with military relevance and psychological value";
  } else if (finalScore >= 2) {
    quality = "Moderate";
    reasoning = "Acceptable association but could be improved";
  } else {
    quality = "Bad";
    reasoning = "Weak association lacking military context";
  }
  
  return { quality, reasoning };
};

// Proposed unified evaluation function
const evaluateWATResponse = (
  stimulus: string,
  response: string,
  militaryContext: boolean = true
): { 
  quality: "Good" | "Moderate" | "Bad",
  reasoning: string 
} => {
  // 1. Check for direct associations (Deepseek strength)
  const associationQuality = evaluateWATAssociation(stimulus, response);
  
  // 2. Check for military relevance (both models)
  const hasMilitaryRelevance = containsMilitaryTerms(response);
  
  // 3. Check for psychological indicators (Gemini strength)
  const psychologicalValue = evaluatePsychologicalValue(response);
  
  // 4. Combine the evaluations with weighted scoring
  let finalScore = 0;
  finalScore += associationQuality === "Good" ? 3 : associationQuality === "Moderate" ? 1 : 0;
  finalScore += hasMilitaryRelevance ? 2 : 0;
  finalScore += psychologicalValue;
  
  // 5. Determine final quality
  let quality: "Good" | "Moderate" | "Bad";
  let reasoning: string;
  
  if (finalScore >= 4) {
    quality = "Good";
    reasoning = "Strong association with military relevance and psychological value";
  } else if (finalScore >= 2) {
    quality = "Moderate";
    reasoning = "Acceptable association but could be improved";
  } else {
    quality = "Bad";
    reasoning = "Weak association lacking military context";
  }
  
  return { quality, reasoning };
};

// Proposed unified evaluation function
const evaluateWATResponse = (
  stimulus: string,
  response: string,
  militaryContext: boolean = true
): { 
  quality: "Good" | "Moderate" | "Bad",
  reasoning: string 
} => {
  // 1. Check for direct associations (Deepseek strength)
  const associationQuality = evaluateWATAssociation(stimulus, response);
  
  // 2. Check for military relevance (both models)
  const hasMilitaryRelevance = containsMilitaryTerms(response);
  
  // 3. Check for psychological indicators (Gemini strength)
  const psychologicalValue = evaluatePsychologicalValue(response);
  
  // 4. Combine the evaluations with weighted scoring
  let finalScore = 0;
  finalScore += associationQuality === "Good" ? 3 : associationQuality === "Moderate" ? 1 : 0;
  finalScore += hasMilitaryRelevance ? 2 : 0;
  finalScore += psychologicalValue;
  
  // 5. Determine final quality
  let quality: "Good" | "Moderate" | "Bad";
  let reasoning: string;
  
  if (finalScore >= 4) {
    quality = "Good";
    reasoning = "Strong association with military relevance and psychological value";
  } else if (finalScore >= 2) {
    quality = "Moderate";
    reasoning = "Acceptable association but could be improved";
  } else {
    quality = "Bad";
    reasoning = "Weak association lacking military context";
  }
  
  return { quality, reasoning };
};

// Proposed unified evaluation function
const evaluateWATResponse = (
  stimulus: string,
  response: string,
  militaryContext: boolean = true
): { 
  quality: "Good" | "Moderate" | "Bad",
  reasoning: string 
} => {
  // 1. Check for direct associations (Deepseek strength)
  const associationQuality = evaluateWATAssociation(stimulus, response);
  
  // 2. Check for military relevance (both models)
  const hasMilitaryRelevance = containsMilitaryTerms(response);
  
  // 3. Check for psychological indicators (Gemini strength)
  const psychologicalValue = evaluatePsychologicalValue(response);
  
  // 4. Combine the evaluations with weighted scoring
  let finalScore = 0;
  finalScore += associationQuality === "Good" ? 3 : associationQuality === "Moderate" ? 1 : 0;
  finalScore += hasMilitaryRelevance ? 2 : 0;
  finalScore += psychologicalValue;
  
  // 5. Determine final quality
  let quality: "Good" | "Moderate" | "Bad";
  let reasoning: string;
  
  if (finalScore >= 4) {
    quality = "Good";
    reasoning = "Strong association with military relevance and psychological value";
  } else if (finalScore >= 2) {
    quality = "Moderate";
    reasoning = "Acceptable association but could be improved";
  } else {
    quality = "Bad";
    reasoning = "Weak association lacking military context";
  }
  
  return { quality, reasoning };
};

// Proposed unified evaluation function
const evaluateWATResponse = (
  stimulus: string,
  response: string,
  militaryContext: boolean = true
): { 
  quality: "Good" | "Moderate" | "Bad",
  reasoning: string 
} => {
  // 1. Check for direct associations (Deepseek strength)
  const associationQuality = evaluateWATAssociation(stimulus, response);
  
  // 2. Check for military relevance (both models)
  const hasMilitaryRelevance = containsMilitaryTerms(response);
  
  // 3. Check for psychological indicators (Gemini strength)
  const psychologicalValue = evaluatePsychologicalValue(response);
  
  // 4. Combine the evaluations with weighted scoring
  let finalScore = 0;
  finalScore += associationQuality === "Good" ? 3 : associationQuality === "Moderate" ? 1 : 0;
  finalScore += hasMilitaryRelevance ? 2 : 0;
  finalScore += psychologicalValue;
  
  // 5. Determine final quality
  let quality: "Good" | "Moderate" | "Bad";
  let reasoning: string;
  
  if (finalScore >= 4) {
    quality = "Good";
    reasoning = "Strong association with military relevance and psychological value";
  } else if (finalScore >= 2) {
    quality = "Moderate";
    reasoning = "Acceptable association but could be improved";
  } else {
    quality = "Bad";
    reasoning = "Weak association lacking military context";
  }
  
  return { quality, reasoning };
};

// Proposed unified evaluation function
const evaluateWATResponse = (
  stimulus: string,
  response: string,
  militaryContext: boolean = true
): { 
  quality: "Good" | "Moderate" | "Bad",
  reasoning: string 
} => {
  // 1. Check for direct associations (Deepseek strength)
  const associationQuality = evaluateWATAssociation(stimulus, response);
  
  // 2. Check for military relevance (both models)
  const hasMilitaryRelevance = containsMilitaryTerms(response);
  
  // 3. Check for psychological indicators (Gemini strength)
  const psychologicalValue = evaluatePsychologicalValue(response);
  
  // 4. Combine the evaluations with weighted scoring
  let finalScore = 0;
  finalScore += associationQuality === "Good" ? 3 : associationQuality === "Moderate" ? 1 : 0;
  finalScore += hasMilitaryRelevance ? 2 : 0;
  finalScore += psychologicalValue;
  
  // 5. Determine final quality
  let quality: "Good" | "Moderate" | "Bad";
  let reasoning: string;
  
  if (finalScore >= 4) {
    quality = "Good";
    reasoning = "Strong association with military relevance and psychological value";
  } else if (finalScore >= 2) {
    quality = "Moderate";
    reasoning = "Acceptable association but could be improved";
  } else {
    quality = "Bad";
    reasoning = "Weak association lacking military context";
  }
  
  return { quality, reasoning };
};

// Proposed unified evaluation function
const evaluateWATResponse = (
  stimulus: string,
  response: string,
  militaryContext: boolean = true
): { 
  quality: "Good" | "Moderate" | "Bad",
  reasoning: string 
} => {
  // 1. Check for direct associations (Deepseek strength)
  const associationQuality = evaluateWATAssociation(stimulus, response);
  
  // 2. Check for military relevance (both models)
  const hasMilitaryRelevance = containsMilitaryTerms(response);
  
  // 3. Check for psychological indicators (Gemini strength)
  const psychologicalValue = evaluatePsychologicalValue(response);
  
  // 4. Combine the evaluations with weighted scoring
  let finalScore = 0;
  finalScore += associationQuality === "Good" ? 3 : associationQuality === "Moderate" ? 1 : 0;
  finalScore += hasMilitaryRelevance ? 2 : 0;
  finalScore += psychologicalValue;
  
  // 5. Determine final quality
  let quality: "Good" | "Moderate" | "Bad";
  let reasoning: string;
  
  if (finalScore >= 4) {
    quality = "Good";
    reasoning = "Strong association with military relevance and psychological value";
  } else if (finalScore >= 2) {
    quality = "Moderate";
    reasoning = "Acceptable association but could be improved";
  } else {
    quality = "Bad";
    reasoning = "Weak association lacking military context";
  }
  
  return { quality, reasoning };
};

// Proposed unified evaluation function
const evaluateWATResponse = (
  stimulus: string,
  response: string,
  militaryContext: boolean = true
): { 
  quality: "Good" | "Moderate" | "Bad",
  reasoning: string 
} => {
  // 1. Check for direct associations (Deepseek strength)
  const associationQuality = evaluateWATAssociation(stimulus, response);
  
  // 2. Check for military relevance (both models)
  const hasMilitaryRelevance = containsMilitaryTerms(response);
  
  // 3. Check for psychological indicators (Gemini strength)
  const psychologicalValue = evaluatePsychologicalValue(response);
  
  // 4. Combine the evaluations with weighted scoring
  let finalScore = 0;
  finalScore += associationQuality === "Good" ? 3 : associationQuality === "Moderate" ? 1 : 0;
  finalScore += hasMilitaryRelevance ? 2 : 0;
  finalScore += psychologicalValue;
  
  // 5. Determine final quality
  let quality: "Good" | "Moderate" | "Bad";
  let reasoning: string;
  
  if (finalScore >= 4) {
    quality = "Good";
    reasoning = "Strong


