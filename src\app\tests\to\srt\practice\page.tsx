"use client";

import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import SimpleButton from "@/components/SimpleButton";
import { useLoading } from "@/context/LoadingContext";
import {
  ArrowLeftIcon,
  PlayIcon,
  BookOpenIcon,
} from "@heroicons/react/24/outline";
import { situationSets } from "@/data/situationsSets";
import SetManagementButton from "@/components/SetManagementButton";
import AddSetModal from "@/components/AddSetModal";
import { useAuth } from "@/context/GoogleAuthContext";
import { addSet, getSets, SetData } from "@/services/setManagementService";

export default function SRTPractice() {
  const router = useRouter();
  const { startLoading } = useLoading();
  const { user } = useAuth();
  const [isAddSetModalOpen, setIsAddSetModalOpen] = useState(false);
  const [customSets, setCustomSets] = useState<SetData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load custom sets from Firestore
  useEffect(() => {
    const loadCustomSets = async () => {
      if (!user) {
        setIsLoading(false);
        return;
      }

      try {
        const sets = await getSets("srt");
        setCustomSets(sets);
      } catch (error) {
        console.error("Error loading custom SRT sets:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadCustomSets();
  }, [user]);

  const handleSetSelect = (setName: string) => {
    startLoading();
    router.push(
      `/tests/to/srt/practice/${encodeURIComponent(setName)}/instructions`
    );
  };

  const handleAddSet = async (setData: SetData) => {
    if (!user) return;
    setError(null);

    try {
      await addSet(user.uid, "srt", setData);

      // Reload custom sets
      const sets = await getSets("srt");
      setCustomSets(sets);
    } catch (error) {
      console.error("Error adding SRT set:", error);
      if (error instanceof Error) {
        setError(error.message);
      } else {
        setError("An unknown error occurred");
      }
      throw error;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="mx-auto max-w-3xl px-6">
        <SimpleButton
          onClick={() => router.back()}
          className="mb-8 flex items-center text-gray-600 hover:text-gray-900"
        >
          <ArrowLeftIcon className="h-5 w-5 mr-2" />
          Back to TO Test
        </SimpleButton>

        <div className="bg-white rounded-2xl shadow-sm p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">
            Situation Reaction Test (SRT) Practice
          </h1>
          <p className="text-gray-600 mb-4">
            The Situation Reaction Test (SRT) presents you with various
            scenarios and asks how you would react. This test evaluates your
            decision-making, judgment, and character.
          </p>

          {/* Sample Answers Link */}
          <div className="bg-blue-50 p-3 sm:p-4 rounded-lg border border-blue-200 mb-8">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-0">
              <div className="flex items-start sm:items-center">
                <BookOpenIcon className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600 mr-2 sm:mr-3 mt-0.5 sm:mt-0 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-blue-800 mb-0.5 sm:mb-1 text-sm sm:text-base">
                    Need help with SRT responses?
                  </h3>
                  <p className="text-xs sm:text-sm text-blue-700">
                    View sample responses to common SRT situations
                  </p>
                </div>
              </div>
              <SimpleButton
                onClick={() => router.push("/tests/to/srt/sample-answers")}
                className="w-full sm:w-auto px-3 sm:px-4 py-1.5 sm:py-2 text-xs sm:text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors text-center"
              >
                View Sample Answers
              </SimpleButton>
            </div>
          </div>

          <div className="space-y-6 text-gray-700">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold">Select Practice Set:</h2>
              <SetManagementButton
                onClick={() => setIsAddSetModalOpen(true)}
                testType="SRT"
              />
            </div>

            {/* Display errors */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
                <p className="text-red-700">{error}</p>
              </div>
            )}

            {isLoading ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
              </div>
            ) : (
              <div className="grid grid-cols-1 gap-6">
                {/* Default sets from data file */}
                {Object.entries(situationSets).map(([setName, setData]) => {
                  return (
                    <SimpleButton
                      key={setName}
                      onClick={() => handleSetSelect(setName)}
                      className="group relative overflow-hidden rounded-xl border border-gray-200 bg-white p-6 shadow-sm transition-all hover:border-blue-500 hover:shadow-md w-full text-left"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex items-start space-x-4">
                          <div className="rounded-lg bg-blue-50 p-3 text-blue-600 transition-colors group-hover:bg-blue-100">
                            <setData.icon className="h-6 w-6" />
                          </div>
                          <div className="text-left">
                            <h3 className="text-lg font-semibold text-gray-900">
                              {setName}
                            </h3>
                            <p className="mt-1 text-sm text-gray-500">
                              {setData.description}
                            </p>
                            <p className="mt-2 text-sm font-medium text-blue-600">
                              {setData.situations.length} situations
                            </p>
                          </div>
                        </div>
                        <div className="rounded-full bg-gray-50 p-2 text-gray-400 transition-colors group-hover:bg-blue-50 group-hover:text-blue-600">
                          <PlayIcon className="h-5 w-5" />
                        </div>
                      </div>
                      <div className="absolute inset-0 bg-gradient-to-r from-blue-50/0 via-blue-50/0 to-blue-50/0 opacity-0 transition-opacity group-hover:opacity-10" />
                    </SimpleButton>
                  );
                })}

                {/* Custom sets from Firestore */}
                {customSets.map((set) => (
                  <SimpleButton
                    key={set.id}
                    onClick={() => handleSetSelect(set.name)}
                    className="group relative overflow-hidden rounded-xl border border-gray-200 bg-white p-6 shadow-sm transition-all hover:border-blue-500 hover:shadow-md w-full text-left"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-4">
                        <div className="rounded-lg bg-blue-50 p-3 text-blue-600 transition-colors group-hover:bg-blue-100">
                          {/* Use a default icon for custom sets */}
                          <PlayIcon className="h-6 w-6" />
                        </div>
                        <div className="text-left">
                          <div className="flex items-center">
                            <h3 className="text-lg font-semibold text-gray-900">
                              {set.name}
                            </h3>
                            <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                              Custom
                            </span>
                          </div>
                          <p className="mt-1 text-sm text-gray-500">
                            {set.description}
                          </p>
                          <p className="mt-2 text-sm font-medium text-blue-600">
                            {set.situations?.length || 0} situations
                          </p>
                        </div>
                      </div>
                      <div className="rounded-full bg-gray-50 p-2 text-gray-400 transition-colors group-hover:bg-blue-50 group-hover:text-blue-600">
                        <PlayIcon className="h-5 w-5" />
                      </div>
                    </div>
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-50/0 via-blue-50/0 to-blue-50/0 opacity-0 transition-opacity group-hover:opacity-10" />
                  </SimpleButton>
                ))}
              </div>
            )}
          </div>

          {/* Add Set Modal */}
          <AddSetModal
            testType="SRT"
            isOpen={isAddSetModalOpen}
            onClose={() => setIsAddSetModalOpen(false)}
            onAddSet={handleAddSet}
          />
        </div>
      </div>
    </div>
  );
}
