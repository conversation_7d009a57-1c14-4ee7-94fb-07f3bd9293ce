"use client";

import React, { useState } from "react";
import { XMarkIcon } from "@heroicons/react/24/outline";
import AdminOnlyComponent from "./AdminOnlyComponent";
import LoadingButton from "./LoadingButton";

interface AddSetModalProps {
  testType: "TAT" | "WAT" | "SRT";
  isOpen: boolean;
  onClose: () => void;
  onAddSet: (setData: any) => Promise<void>;
}

export default function AddSetModal({
  testType,
  isOpen,
  onClose,
  onAddSet,
}: AddSetModalProps) {
  const [setName, setSetName] = useState("");
  const [description, setDescription] = useState("");
  const [items, setItems] = useState<string[]>([""]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Get the appropriate color based on test type
  const getButtonColor = () => {
    switch (testType) {
      case "TAT":
        return "bg-red-600 hover:bg-red-700 focus:ring-red-500";
      case "WAT":
        return "bg-green-600 hover:bg-green-700 focus:ring-green-500";
      case "SRT":
        return "bg-blue-600 hover:bg-blue-700 focus:ring-blue-500";
      default:
        return "bg-indigo-600 hover:bg-indigo-700 focus:ring-indigo-500";
    }
  };

  const buttonColor = getButtonColor();

  // Get the appropriate item name based on test type
  const getItemName = () => {
    switch (testType) {
      case "TAT":
        return "Image Path";
      case "WAT":
        return "Word";
      case "SRT":
        return "Situation";
      default:
        return "Item";
    }
  };

  const itemName = getItemName();

  const handleAddItem = () => {
    setItems([...items, ""]);
  };

  const handleRemoveItem = (index: number) => {
    const newItems = [...items];
    newItems.splice(index, 1);
    setItems(newItems);
  };

  const handleItemChange = (index: number, value: string) => {
    const newItems = [...items];
    newItems[index] = value;
    setItems(newItems);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    try {
      // Validate form
      if (!setName.trim()) {
        throw new Error("Set name is required");
      }

      if (!description.trim()) {
        throw new Error("Description is required");
      }

      // Filter out empty items
      const validItems = items.filter((item) => item.trim() !== "");
      if (validItems.length === 0) {
        throw new Error(`At least one ${itemName.toLowerCase()} is required`);
      }

      // Create set data based on test type
      let setData;
      switch (testType) {
        case "TAT":
          setData = {
            name: setName,
            description,
            images: validItems,
          };
          break;
        case "WAT":
          setData = {
            name: setName,
            description,
            words: validItems,
          };
          break;
        case "SRT":
          setData = {
            name: setName,
            description,
            situations: validItems,
          };
          break;
        default:
          setData = {
            name: setName,
            description,
            items: validItems,
          };
      }

      // Call the onAddSet function with the set data
      await onAddSet(setData);

      // Reset form and close modal
      setSetName("");
      setDescription("");
      setItems([""]);
      onClose();
    } catch (err) {
      console.error("Error adding set:", err);
      setError(err instanceof Error ? err.message : "Failed to add set");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <AdminOnlyComponent>
      <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
          <div className="flex justify-between items-center px-6 py-4 border-b">
            <h2 className="text-xl font-semibold text-gray-900">
              Add New {testType} Set
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-500"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit} className="px-6 py-4">
            {error && (
              <div className="mb-4 bg-red-50 p-4 rounded-md">
                <p className="text-red-700">{error}</p>
              </div>
            )}

            <div className="mb-4">
              <label
                htmlFor="setName"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Set Name
              </label>
              <input
                type="text"
                id="setName"
                value={setName}
                onChange={(e) => setSetName(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="e.g., Set 3"
                disabled={isSubmitting}
              />
            </div>

            <div className="mb-4">
              <label
                htmlFor="description"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Description
              </label>
              <textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="Enter a description for this set"
                rows={3}
                disabled={isSubmitting}
              />
            </div>

            <div className="mb-4">
              <div className="flex justify-between items-center mb-2">
                <label className="block text-sm font-medium text-gray-700">
                  {itemName}s
                </label>
                <button
                  type="button"
                  onClick={handleAddItem}
                  className="text-sm text-indigo-600 hover:text-indigo-500"
                  disabled={isSubmitting}
                >
                  + Add {itemName}
                </button>
              </div>

              <div className="space-y-2">
                {items.map((item, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <input
                      type="text"
                      value={item}
                      onChange={(e) => handleItemChange(index, e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                      placeholder={`Enter ${itemName.toLowerCase()}`}
                      disabled={isSubmitting}
                    />
                    {items.length > 1 && (
                      <button
                        type="button"
                        onClick={() => handleRemoveItem(index)}
                        className="text-red-500 hover:text-red-700"
                        disabled={isSubmitting}
                      >
                        <XMarkIcon className="h-5 w-5" />
                      </button>
                    )}
                  </div>
                ))}
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <LoadingButton
                type="submit"
                className={`px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white ${buttonColor} focus:outline-none focus:ring-2 focus:ring-offset-2`}
                isLoading={isSubmitting}
                loadingText="Adding..."
              >
                Add Set
              </LoadingButton>
            </div>
          </form>
        </div>
      </div>
    </AdminOnlyComponent>
  );
}
