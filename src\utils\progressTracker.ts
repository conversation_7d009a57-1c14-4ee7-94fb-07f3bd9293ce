/**
 * Progress Tracking Utility
 * Tracks user completion progress across all test sections
 */

import { db } from "@/lib/firebase";
import { collection, query, where, getDocs } from "firebase/firestore";
import { TestType } from "@/services/responseService";

// Test section configurations
export interface SubSectionConfig {
  id: string;
  name: string;
  totalSets: number;
  type: "sets" | "categories" | "scenarios" | "topics";
  description: string;
}

export interface TestSectionConfig {
  id: string;
  name: string;
  description: string;
  color: string;
  subSections: SubSectionConfig[];
}

export const TEST_SECTIONS: TestSectionConfig[] = [
  {
    id: "to",
    name: "TO (Technical Officer)",
    description: "Psychological assessment tests",
    color: "blue",
    subSections: [
      {
        id: "srt",
        name: "SRT (Situation Reaction Test)",
        totalSets: 25,
        type: "sets",
        description: "Situation-based response tests",
      },
      {
        id: "wat",
        name: "WAT (Word Association Test)",
        totalSets: 12,
        type: "sets",
        description: "Word association responses",
      },
      {
        id: "tat",
        name: "TAT (Thematic Apperception Test)",
        totalSets: 15,
        type: "sets",
        description: "Picture story writing",
      },
      {
        id: "sdt",
        name: "SDT (Self Description Test)",
        totalSets: 1,
        type: "categories",
        description: "Self-description questions",
      },
    ],
  },
  {
    id: "gto",
    name: "GTO (Group Testing Officer)",
    description: "Group activities and leadership assessment",
    color: "green",
    subSections: [
      {
        id: "gd",
        name: "GD (Group Discussion)",
        totalSets: 32,
        type: "topics",
        description: "Group discussion topics",
      },
      {
        id: "gpe",
        name: "GPE (Group Planning Exercise)",
        totalSets: 5,
        type: "sets",
        description: "Planning exercise scenarios",
      },
      {
        id: "lecturette",
        name: "Lecturette",
        totalSets: 24,
        type: "topics",
        description: "Public speaking topics",
      },
    ],
  },
  {
    id: "io",
    name: "IO (Intelligence Officer)",
    description: "Interview practice sessions",
    color: "purple",
    subSections: [
      {
        id: "io",
        name: "Interview Questions",
        totalSets: 62,
        type: "categories",
        description: "50 questions + 6+6 groups",
      },
    ],
  },
  {
    id: "bc",
    name: "BC (Board Conference)",
    description: "Board interview questions",
    color: "indigo",
    subSections: [
      {
        id: "bc",
        name: "Board Questions",
        totalSets: 108,
        type: "categories",
        description: "Comprehensive board interview questions",
      },
    ],
  },
];

// Progress data interface
export interface UserProgress {
  userId: string;
  sectionProgress: SectionProgress[];
  totalCompleted: number;
  totalAvailable: number;
  overallPercentage: number;
  lastUpdated: Date;
}

export interface SubSectionProgress {
  subSectionId: string;
  subSectionName: string;
  completed: number;
  total: number;
  percentage: number;
  completedSets: string[];
  type: string;
}

export interface SectionProgress {
  sectionId: string;
  sectionName: string;
  completed: number;
  total: number;
  percentage: number;
  color: string;
  description: string;
  subSections: SubSectionProgress[];
}

/**
 * Get user progress across all test sections
 */
export const getUserProgress = async (
  userId: string
): Promise<UserProgress> => {
  try {
    const sectionProgress: SectionProgress[] = [];
    let totalCompleted = 0;
    let totalAvailable = 0;

    // Get progress for each test section
    for (const section of TEST_SECTIONS) {
      const progress = await getSectionProgress(userId, section);
      sectionProgress.push(progress);
      totalCompleted += progress.completed;
      totalAvailable += progress.total;
    }

    const overallPercentage =
      totalAvailable > 0
        ? Math.round((totalCompleted / totalAvailable) * 100)
        : 0;

    return {
      userId,
      sectionProgress,
      totalCompleted,
      totalAvailable,
      overallPercentage,
      lastUpdated: new Date(),
    };
  } catch (error) {
    console.error("Error getting user progress:", error);
    throw error;
  }
};

/**
 * Get progress for a specific test section
 */
export const getSectionProgress = async (
  userId: string,
  section: TestSectionConfig
): Promise<SectionProgress> => {
  try {
    const subSectionProgress: SubSectionProgress[] = [];
    let totalCompleted = 0;
    let totalAvailable = 0;

    // Get progress for each subsection
    for (const subSection of section.subSections) {
      const progress = await getSubSectionProgress(userId, subSection);
      subSectionProgress.push(progress);
      totalCompleted += progress.completed;
      totalAvailable += progress.total;
    }

    const percentage =
      totalAvailable > 0
        ? Math.round((totalCompleted / totalAvailable) * 100)
        : 0;

    return {
      sectionId: section.id,
      sectionName: section.name,
      completed: totalCompleted,
      total: totalAvailable,
      percentage,
      color: section.color,
      description: section.description,
      subSections: subSectionProgress,
    };
  } catch (error) {
    console.error(`Error getting progress for section ${section.id}:`, error);
    return {
      sectionId: section.id,
      sectionName: section.name,
      completed: 0,
      total: 0,
      percentage: 0,
      color: section.color,
      description: section.description,
      subSections: [],
    };
  }
};

/**
 * Mark a test session as completed in localStorage
 */
export const markTestCompleted = (
  testType: string,
  setName: string,
  userId?: string
) => {
  try {
    const storageKey = userId
      ? `progress_${userId}_${testType}`
      : `progress_${testType}`;
    const existingProgress = localStorage.getItem(storageKey);
    const completedSets = existingProgress ? JSON.parse(existingProgress) : [];

    if (!completedSets.includes(setName)) {
      completedSets.push(setName);
      localStorage.setItem(storageKey, JSON.stringify(completedSets));
      console.log(`Marked ${testType} - ${setName} as completed`);
    }
  } catch (error) {
    console.error("Error marking test as completed:", error);
  }
};

/**
 * Get completed tests from localStorage
 */
export const getCompletedTests = (
  testType: string,
  userId?: string
): string[] => {
  try {
    const storageKey = userId
      ? `progress_${userId}_${testType}`
      : `progress_${testType}`;
    const existingProgress = localStorage.getItem(storageKey);
    return existingProgress ? JSON.parse(existingProgress) : [];
  } catch (error) {
    console.error("Error getting completed tests:", error);
    return [];
  }
};

/**
 * Get completed tests based on actual recordings in localStorage
 */
export const getCompletedTestsFromRecordings = (
  testType: string,
  userId?: string
): string[] => {
  try {
    const completedSets: string[] = [];

    // Check the audio_recordings_index for saved recordings
    const recordingsIndex = localStorage.getItem("audio_recordings_index");
    if (recordingsIndex) {
      const recordings = JSON.parse(recordingsIndex);
      const testRecordings = recordings.filter(
        (recording: any) => recording.testType === testType.toUpperCase()
      );

      // Extract unique question IDs/categories for this test type
      const uniqueSets = new Set<string>();
      testRecordings.forEach((recording: any) => {
        if (recording.questionId) {
          // For IO and BC, extract category from questionId (format: "category-questionIndex")
          if (testType === "io" || testType === "bc") {
            const parts = recording.questionId.split("-");
            if (parts.length >= 2) {
              uniqueSets.add(parts[0]);
            }
          } else {
            // For GD and Lecturette, use the full questionId
            uniqueSets.add(recording.questionId);
          }
        }
      });

      completedSets.push(...Array.from(uniqueSets));
    }

    // Special handling for GPE which uses a different storage pattern
    if (testType === "gpe") {
      // GPE recordings are stored as "gpe-recordings-{setId}"
      // We need to check all possible set IDs
      for (let i = 1; i <= 5; i++) {
        const setId = `set${i}`;
        const gpeKey = `gpe-recordings-${setId}`;
        const gpeRecordings = localStorage.getItem(gpeKey);
        if (gpeRecordings) {
          const recordings = JSON.parse(gpeRecordings);
          // If there are any recordings for this set, consider it completed
          if (Object.keys(recordings).length > 0) {
            completedSets.push(setId);
          }
        }
      }
    }

    return completedSets;
  } catch (error) {
    console.error("Error getting completed tests from recordings:", error);
    return [];
  }
};

/**
 * Get progress for a specific subsection
 */
export const getSubSectionProgress = async (
  userId: string,
  subSection: SubSectionConfig
): Promise<SubSectionProgress> => {
  try {
    // Special handling for tests that use localStorage-based progress tracking
    if (
      subSection.id === "io" ||
      subSection.id === "bc" ||
      subSection.id === "gd" ||
      subSection.id === "gpe" ||
      subSection.id === "lecturette"
    ) {
      // Get completed tests from actual recordings in localStorage
      const completedSets = getCompletedTestsFromRecordings(
        subSection.id,
        userId
      );
      const completed = completedSets.length;
      const total = subSection.totalSets;
      const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;

      return {
        subSectionId: subSection.id,
        subSectionName: subSection.name,
        completed,
        total,
        percentage,
        completedSets,
        type: subSection.type,
      };
    }

    // Query responses for this user and test type (for Firebase-tracked tests)
    const responsesRef = collection(db, "responses");
    const q = query(
      responsesRef,
      where("userId", "==", userId),
      where("testType", "==", subSection.id)
    );

    const querySnapshot = await getDocs(q);
    const completedSets = new Set<string>();

    // Get unique set names that user has completed
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      if (data.setName) {
        completedSets.add(data.setName);
      }
    });

    const completed = completedSets.size;
    const total = subSection.totalSets;
    const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;

    return {
      subSectionId: subSection.id,
      subSectionName: subSection.name,
      completed,
      total,
      percentage,
      completedSets: Array.from(completedSets),
      type: subSection.type,
    };
  } catch (error) {
    console.error(
      `Error getting progress for subsection ${subSection.id}:`,
      error
    );
    return {
      subSectionId: subSection.id,
      subSectionName: subSection.name,
      completed: 0,
      total: subSection.totalSets,
      percentage: 0,
      completedSets: [],
      type: subSection.type,
    };
  }
};

/**
 * Get progress summary for quick display
 */
export const getProgressSummary = async (
  userId: string
): Promise<{
  totalTests: number;
  completedTests: number;
  percentage: number;
  recentActivity: string[];
}> => {
  try {
    const progress = await getUserProgress(userId);

    // Get recent activity (last 5 completed sets)
    const responsesRef = collection(db, "responses");
    const recentQuery = query(responsesRef, where("userId", "==", userId));

    const recentSnapshot = await getDocs(recentQuery);
    const recentActivity: string[] = [];

    // Sort by timestamp and get recent activities
    const recentDocs = recentSnapshot.docs
      .map((doc) => ({ ...doc.data(), id: doc.id }))
      .sort((a, b) => (b.timestamp || 0) - (a.timestamp || 0))
      .slice(0, 5);

    recentDocs.forEach((doc) => {
      const sectionName =
        TEST_SECTIONS.find((s) => s.id === doc.testType)?.name || doc.testType;
      recentActivity.push(`${sectionName} - ${doc.setName}`);
    });

    return {
      totalTests: progress.totalAvailable,
      completedTests: progress.totalCompleted,
      percentage: progress.overallPercentage,
      recentActivity,
    };
  } catch (error) {
    console.error("Error getting progress summary:", error);
    return {
      totalTests: 0,
      completedTests: 0,
      percentage: 0,
      recentActivity: [],
    };
  }
};

/**
 * Get color classes for progress indicators
 */
export const getProgressColorClasses = (percentage: number, color: string) => {
  const intensity = percentage >= 80 ? "600" : percentage >= 50 ? "500" : "400";

  return {
    bg: `bg-${color}-${intensity}`,
    text: `text-${color}-${intensity}`,
    border: `border-${color}-${intensity}`,
    ring: `ring-${color}-${intensity}`,
  };
};

/**
 * Format progress text
 */
export const formatProgressText = (
  completed: number,
  total: number,
  type: string
): string => {
  const typeText =
    type === "sets"
      ? "sets"
      : type === "categories"
      ? "categories"
      : type === "topics"
      ? "topics"
      : "scenarios";
  return `${completed} out of ${total} ${typeText}`;
};
