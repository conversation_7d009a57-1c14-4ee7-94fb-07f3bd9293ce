"use client";

import React from "react";
import Link from "next/link";
import Image from "next/image";
import { ForumTopic } from "@/types/forum";
import {
  ChatBubbleLeftRightIcon,
  ClockIcon,
  StarIcon,
} from "@heroicons/react/24/outline";

interface TopicCardProps {
  topic: ForumTopic;
}

export default function TopicCard({ topic }: TopicCardProps) {
  // Format date for display
  const formatDate = (date: Date | undefined) => {
    if (!date) return "Unknown date";

    // Check if the date is today
    const today = new Date();
    const isToday =
      date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear();

    if (isToday) {
      // Format as time if today
      return `Today at ${date.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      })}`;
    } else {
      // Format as date otherwise
      return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
      });
    }
  };

  return (
    <Link
      href={`/forum/topic/${topic.id}`}
      className="block hover:bg-gray-50 transition-colors duration-150"
    >
      <div className="p-4 sm:p-6">
        <div className="flex flex-col sm:flex-row sm:items-start">
          {/* Author avatar */}
          <div className="flex-shrink-0 mb-3 sm:mb-0 sm:mr-4">
            {topic.authorPhotoURL ? (
              <Image
                src={topic.authorPhotoURL}
                alt={topic.authorName}
                width={40}
                height={40}
                className="rounded-full"
              />
            ) : (
              <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center">
                <span className="text-gray-500 font-medium">
                  {topic.authorName.charAt(0).toUpperCase()}
                </span>
              </div>
            )}
          </div>

          {/* Topic content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center">
              {topic.isPinned && (
                <StarIcon className="h-4 w-4 text-indigo-600 mr-1" />
              )}
              <h3 className="text-lg font-semibold text-gray-900 truncate">
                {topic.title}
              </h3>
            </div>

            <p className="mt-1 text-sm text-gray-500 line-clamp-2">
              {topic.content}
            </p>

            {/* Tags */}
            {topic.tags && topic.tags.length > 0 && (
              <div className="mt-2 flex flex-wrap gap-1.5">
                {topic.tags.map((tag) => {
                  // Define tag colors based on tag type
                  let tagColor = "";
                  switch (tag) {
                    case "to":
                      tagColor = "bg-blue-100 text-blue-800";
                      break;
                    case "srt":
                      tagColor = "bg-blue-50 text-blue-700";
                      break;
                    case "wat":
                      tagColor = "bg-green-50 text-green-700";
                      break;
                    case "tat":
                      tagColor = "bg-red-50 text-red-700";
                      break;
                    case "sdt":
                      tagColor = "bg-amber-50 text-amber-700";
                      break;
                    case "gto":
                      tagColor = "bg-green-100 text-green-800";
                      break;
                    case "io":
                      tagColor = "bg-amber-100 text-amber-800";
                      break;
                    case "bc":
                      tagColor = "bg-purple-100 text-purple-800";
                      break;
                    case "general":
                      tagColor = "bg-gray-100 text-gray-800";
                      break;
                    case "help":
                      tagColor = "bg-purple-100 text-purple-800";
                      break;
                    case "feedback":
                      tagColor = "bg-red-100 text-red-800";
                      break;
                    default:
                      tagColor = "bg-indigo-100 text-indigo-800";
                  }

                  // Get tag display name
                  let tagName = tag.toUpperCase();
                  if (tag === "to") tagName = "TO";
                  if (tag === "gto") tagName = "GTO";
                  if (tag === "io") tagName = "IO";
                  if (tag === "bc") tagName = "BC";
                  if (tag === "general") tagName = "General";
                  if (tag === "help") tagName = "Help";
                  if (tag === "feedback") tagName = "Feedback";

                  return (
                    <span
                      key={tag}
                      className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${tagColor}`}
                    >
                      {tagName}
                    </span>
                  );
                })}
              </div>
            )}

            {/* Meta information */}
            <div className="mt-2 flex items-center text-sm text-gray-500">
              <span className="truncate">By {topic.authorName}</span>
              <span className="mx-1">•</span>
              <ClockIcon className="h-4 w-4 mr-1" />
              <span>{formatDate(topic.createdAt)}</span>
            </div>
          </div>

          {/* Stats */}
          <div className="mt-3 sm:mt-0 sm:ml-4 flex-shrink-0 flex flex-row sm:flex-col items-start sm:items-end">
            <div className="flex items-center text-sm text-gray-500">
              <ChatBubbleLeftRightIcon className="h-4 w-4 mr-1" />
              <span>{topic.replyCount} replies</span>
            </div>
            {topic.lastReplyAt && (
              <div className="ml-4 sm:ml-0 sm:mt-2 text-xs text-gray-500">
                Last reply {formatDate(topic.lastReplyAt)}
              </div>
            )}
          </div>
        </div>
      </div>
    </Link>
  );
}
