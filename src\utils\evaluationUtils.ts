// Military traits and values categories for response analysis
export interface TraitCategory {
  name: string;
  description: string;
  keywords: string[];
  positiveIndicators: string[];
  negativeIndicators: string[];
}

export interface TraitCategories {
  [key: string]: TraitCategory;
}

// Comprehensive military traits and values for evaluation
export const militaryTraitCategories: TraitCategories = {
  leadership: {
    name: "Leadership",
    description:
      "Demonstrates command presence, initiative, and ability to guide others",
    keywords: [
      "lead",
      "command",
      "direct",
      "guide",
      "initiative",
      "responsibility",
      "decision",
      "authority",
      "influence",
      "inspire",
      "motivate",
      "delegate",
      "coordinate",
      "organize",
      "plan",
      "strategy",
      "vision",
      "mission",
    ],
    positiveIndicators: [
      "Takes charge in crisis",
      "Provides clear direction",
      "Motivates team members",
      "Makes timely decisions",
      "Delegates effectively",
      "Leads by example",
      "Inspires confidence",
      "Takes responsibility",
      "Shows initiative",
    ],
    negativeIndicators: [
      "Avoids responsibility",
      "Indecisive",
      "Micromanages",
      "Lacks authority",
      "Fails to provide direction",
      "Blames others",
      "Passive approach",
    ],
  },

  discipline: {
    name: "Discipline & Order",
    description: "Shows self-control, follows rules, and maintains structure",
    keywords: [
      "discipline",
      "order",
      "protocol",
      "procedure",
      "regulation",
      "rule",
      "structure",
      "routine",
      "obedience",
      "compliance",
      "standard",
      "control",
      "restraint",
      "punctual",
      "precise",
      "methodical",
      "systematic",
    ],
    positiveIndicators: [
      "Follows established protocols",
      "Maintains self-control",
      "Respects chain of command",
      "Adheres to regulations",
      "Shows restraint",
      "Maintains order",
      "Follows instructions precisely",
      "Demonstrates consistency",
    ],
    negativeIndicators: [
      "Disregards rules",
      "Lacks self-control",
      "Challenges authority inappropriately",
      "Acts impulsively",
      "Inconsistent performance",
      "Disorganized approach",
    ],
  },

  integrity: {
    name: "Integrity & Ethics",
    description: "Demonstrates honesty, moral courage, and ethical behavior",
    keywords: [
      "integrity",
      "honesty",
      "ethics",
      "moral",
      "truth",
      "principle",
      "value",
      "fair",
      "just",
      "right",
      "wrong",
      "conscience",
      "ethical",
      "transparent",
      "accountable",
      "trustworthy",
      "honor",
      "duty",
    ],
    positiveIndicators: [
      "Reports honestly",
      "Stands up for what's right",
      "Takes responsibility for mistakes",
      "Maintains confidentiality",
      "Resists corruption",
      "Shows moral courage",
      "Demonstrates transparency",
      "Upholds ethical standards",
    ],
    negativeIndicators: [
      "Dishonest reporting",
      "Covers up mistakes",
      "Compromises ethics for convenience",
      "Lacks moral courage",
      "Engages in deception",
      "Betrays trust",
    ],
  },

  teamwork: {
    name: "Teamwork & Cooperation",
    description: "Works effectively with others toward common goals",
    keywords: [
      "team",
      "cooperate",
      "collaborate",
      "support",
      "assist",
      "help",
      "together",
      "unity",
      "collective",
      "group",
      "joint",
      "share",
      "contribute",
      "participate",
      "coordinate",
      "communicate",
      "cohesion",
      "synergy",
    ],
    positiveIndicators: [
      "Puts team goals first",
      "Supports teammates",
      "Communicates effectively",
      "Shares information",
      "Contributes to group efforts",
      "Builds team spirit",
      "Resolves conflicts constructively",
      "Promotes unity",
    ],
    negativeIndicators: [
      "Prioritizes self over team",
      "Withholds information",
      "Creates division",
      "Refuses to cooperate",
      "Undermines team efforts",
      "Isolates from group",
    ],
  },

  resilience: {
    name: "Resilience & Adaptability",
    description:
      "Demonstrates mental toughness and ability to adapt to changing situations",
    keywords: [
      "resilient",
      "adapt",
      "endure",
      "persist",
      "overcome",
      "flexible",
      "adjust",
      "recover",
      "bounce back",
      "withstand",
      "tough",
      "strong",
      "versatile",
      "resourceful",
      "improvise",
      "cope",
      "stress",
      "pressure",
    ],
    positiveIndicators: [
      "Persists through difficulties",
      "Adapts to changing situations",
      "Maintains composure under pressure",
      "Recovers quickly from setbacks",
      "Shows mental toughness",
      "Finds alternative solutions",
      "Remains effective in stressful conditions",
      "Improvises when needed",
    ],
    negativeIndicators: [
      "Breaks under pressure",
      "Rigid thinking",
      "Gives up easily",
      "Panics in crisis",
      "Unable to adapt",
      "Overwhelmed by challenges",
    ],
  },

  decisiveness: {
    name: "Decisiveness & Action",
    description: "Makes timely decisions and takes appropriate action",
    keywords: [
      "decide",
      "action",
      "resolve",
      "determine",
      "commit",
      "execute",
      "implement",
      "swift",
      "prompt",
      "immediate",
      "quick",
      "timely",
      "definite",
      "firm",
      "confident",
      "assertive",
      "proactive",
    ],
    positiveIndicators: [
      "Makes timely decisions",
      "Takes decisive action",
      "Shows confidence in choices",
      "Acts without unnecessary delay",
      "Commits to a course of action",
      "Implements solutions",
      "Takes initiative",
      "Responds promptly to situations",
    ],
    negativeIndicators: [
      "Hesitates excessively",
      "Avoids making decisions",
      "Second-guesses constantly",
      "Delays necessary action",
      "Lacks confidence in choices",
      "Passive approach",
    ],
  },

  tacticalThinking: {
    name: "Tactical Thinking",
    description: "Demonstrates strategic planning and situational awareness",
    keywords: [
      "tactical",
      "strategy",
      "plan",
      "analyze",
      "assess",
      "evaluate",
      "situational awareness",
      "objective",
      "mission",
      "intelligence",
      "reconnaissance",
      "security",
      "risk",
      "threat",
      "opportunity",
      "advantage",
      "position",
    ],
    positiveIndicators: [
      "Analyzes situations thoroughly",
      "Develops effective plans",
      "Maintains situational awareness",
      "Identifies risks and opportunities",
      "Considers multiple options",
      "Anticipates problems",
      "Secures tactical advantages",
      "Prioritizes objectives",
    ],
    negativeIndicators: [
      "Fails to plan ahead",
      "Misses important details",
      "Lacks situational awareness",
      "Overlooks risks",
      "Focuses on single option only",
      "Reactive rather than proactive",
    ],
  },

  communication: {
    name: "Communication Skills",
    description: "Effectively conveys and receives information",
    keywords: [
      "communicate",
      "express",
      "articulate",
      "inform",
      "report",
      "brief",
      "listen",
      "understand",
      "clear",
      "concise",
      "precise",
      "accurate",
      "message",
      "instruction",
      "feedback",
      "dialogue",
      "discussion",
    ],
    positiveIndicators: [
      "Communicates clearly and concisely",
      "Listens actively",
      "Provides accurate information",
      "Gives clear instructions",
      "Ensures understanding",
      "Reports effectively",
      "Maintains open communication channels",
      "Adapts communication style as needed",
    ],
    negativeIndicators: [
      "Unclear or confusing communication",
      "Poor listening skills",
      "Withholds important information",
      "Vague instructions",
      "Fails to verify understanding",
      "Ineffective reporting",
    ],
  },

  courage: {
    name: "Courage & Bravery",
    description:
      "Shows physical and moral courage in the face of danger or adversity",
    keywords: [
      "courage",
      "brave",
      "valor",
      "fearless",
      "bold",
      "daring",
      "heroic",
      "confident",
      "face danger",
      "confront",
      "stand up",
      "risk",
      "sacrifice",
      "protect",
      "defend",
      "challenge",
      "overcome fear",
    ],
    positiveIndicators: [
      "Acts despite personal risk",
      "Stands up for what's right",
      "Confronts difficult situations",
      "Protects others",
      "Faces danger",
      "Shows moral courage",
      "Overcomes fear",
      "Takes necessary risks",
    ],
    negativeIndicators: [
      "Avoids necessary risks",
      "Fails to act due to fear",
      "Abandons responsibilities",
      "Prioritizes personal safety inappropriately",
      "Lacks moral courage",
    ],
  },

  selflessness: {
    name: "Selflessness & Sacrifice",
    description: "Puts mission and others before self-interest",
    keywords: [
      "selfless",
      "sacrifice",
      "altruism",
      "service",
      "duty",
      "dedication",
      "commitment",
      "others first",
      "greater good",
      "mission",
      "team",
      "give",
      "help",
      "support",
      "protect",
      "serve",
      "volunteer",
    ],
    positiveIndicators: [
      "Puts mission before personal comfort",
      "Sacrifices for team",
      "Volunteers for difficult tasks",
      "Helps others without expectation",
      "Prioritizes group needs",
      "Shows dedication to service",
      "Takes on additional responsibilities",
      "Supports teammates",
    ],
    negativeIndicators: [
      "Prioritizes self-interest",
      "Avoids difficult tasks",
      "Unwilling to sacrifice",
      "Seeks personal gain",
      "Neglects team needs",
      "Minimal effort",
    ],
  },

  professionalKnowledge: {
    name: "Professional Knowledge",
    description: "Demonstrates technical competence and job knowledge",
    keywords: [
      "knowledge",
      "skill",
      "competence",
      "expertise",
      "proficient",
      "capable",
      "trained",
      "qualified",
      "experienced",
      "technical",
      "professional",
      "specialist",
      "understand",
      "know",
      "learn",
      "study",
      "practice",
      "develop",
    ],
    positiveIndicators: [
      "Shows technical expertise",
      "Applies knowledge effectively",
      "Continues professional development",
      "Shares knowledge with others",
      "Maintains current skills",
      "Demonstrates competence",
      "Seeks to improve capabilities",
      "Uses equipment properly",
    ],
    negativeIndicators: [
      "Lacks necessary knowledge",
      "Fails to develop skills",
      "Misuses equipment",
      "Applies techniques incorrectly",
      "Resists learning",
      "Outdated knowledge",
    ],
  },

  resourcefulness: {
    name: "Resourcefulness & Innovation",
    description: "Finds creative solutions with available resources",
    keywords: [
      "resourceful",
      "innovative",
      "creative",
      "improvise",
      "adapt",
      "solve",
      "alternative",
      "solution",
      "idea",
      "approach",
      "method",
      "technique",
      "improve",
      "enhance",
      "optimize",
      "efficient",
      "effective",
    ],
    positiveIndicators: [
      "Finds solutions with limited resources",
      "Develops innovative approaches",
      "Improvises effectively",
      "Creates alternatives",
      "Optimizes resource use",
      "Thinks outside conventional boundaries",
      "Improves existing methods",
      "Adapts tools for new purposes",
    ],
    negativeIndicators: [
      "Gives up when resources are limited",
      "Lacks creativity",
      "Relies only on standard procedures",
      "Inefficient resource use",
      "Fails to consider alternatives",
      "Rigid thinking",
    ],
  },
};

// Function to analyze responses based on military traits
export const analyzeResponses = (
  responses: string[],
  categories: TraitCategories = militaryTraitCategories
): Record<string, number> => {
  const categoryScores: Record<string, number> = {};

  // Initialize scores
  Object.keys(categories).forEach((category) => {
    categoryScores[category] = 0;
  });

  // Analyze each response
  responses.forEach((response) => {
    if (!response) return; // Skip empty responses

    const lowerResponse = response.toLowerCase();

    Object.entries(categories).forEach(([category, data]) => {
      // Check for keywords
      data.keywords.forEach((keyword) => {
        if (lowerResponse.includes(keyword.toLowerCase())) {
          categoryScores[category]++;
        }
      });

      // Check for positive indicators (phrases that might not be captured by keywords)
      data.positiveIndicators.forEach((indicator) => {
        const indicatorLower = indicator.toLowerCase();
        if (lowerResponse.includes(indicatorLower)) {
          categoryScores[category]++;
        }
      });

      // Check for negative indicators (subtract points)
      data.negativeIndicators.forEach((indicator) => {
        const indicatorLower = indicator.toLowerCase();
        if (lowerResponse.includes(indicatorLower)) {
          categoryScores[category]--;
        }
      });
    });
  });

  // Ensure no negative scores
  Object.keys(categoryScores).forEach((category) => {
    categoryScores[category] = Math.max(0, categoryScores[category]);
  });

  return categoryScores;
};

// Calculate percentage score for a category with more generous scoring
export const calculateCategoryScore = (
  analysis: Record<string, number>,
  category: string,
  responseCount: number
): number => {
  const score = analysis[category] || 0;
  // Assuming each response could potentially match 3 keywords/indicators on average
  const maxPossible = responseCount * 3;

  // Calculate base score
  let calculatedScore = Math.min(Math.round((score / maxPossible) * 100), 100);

  // Apply more generous scoring
  // Ensure minimum 20% if there are responses
  if (responseCount > 0 && calculatedScore < 20) {
    calculatedScore = 20;
  }

  // Boost scores based on response count
  if (responseCount >= 10) {
    // Add bonus points for having 10+ responses
    calculatedScore += 10;
  } else if (responseCount >= 5) {
    // Add smaller bonus for 5-9 responses
    calculatedScore += 5;
  }

  // Cap at 100%
  return Math.min(calculatedScore, 100);
};

// Get overall score based on all categories with more generous scoring
export const calculateOverallScore = (
  analysis: Record<string, number>,
  responseCount: number
): number => {
  let totalScore = 0;
  let count = 0;

  Object.keys(analysis).forEach((category) => {
    totalScore += calculateCategoryScore(analysis, category, responseCount);
    count++;
  });

  let overallScore = count > 0 ? Math.round(totalScore / count) : 0;

  // Apply more generous scoring for overall score
  // Ensure minimum 30% if there are 10+ responses
  if (responseCount >= 10 && overallScore < 30) {
    overallScore = 30;
  } else if (responseCount >= 5 && overallScore < 25) {
    // Ensure minimum 25% if there are 5-9 responses
    overallScore = 25;
  } else if (responseCount > 0 && overallScore < 20) {
    // Ensure minimum 20% if there are any responses
    overallScore = 20;
  }

  return overallScore;
};

// Get strength and improvement areas based on scores
export const getStrengthsAndImprovements = (
  analysis: Record<string, number>,
  responseCount: number,
  categories: TraitCategories = militaryTraitCategories
): { strengths: string[]; improvements: string[] } => {
  const categoryScores = Object.entries(analysis).map(([category]) => ({
    category,
    name: categories[category].name,
    score: calculateCategoryScore(analysis, category, responseCount),
  }));

  // Sort by score (descending)
  categoryScores.sort((a, b) => b.score - a.score);

  // Top 3 strengths (highest scores)
  const strengths = categoryScores
    .slice(0, 3)
    .filter((item) => item.score > 40) // Only include if score is decent
    .map((item) => item.name);

  // Bottom 3 areas for improvement (lowest scores)
  const improvements = categoryScores
    .slice(-3)
    .filter((item) => item.score < 70) // Only include if there's room for improvement
    .map((item) => item.name)
    .reverse(); // Show lowest score first

  return { strengths, improvements };
};

// Generate detailed feedback based on analysis with colorful language
export const generateFeedback = (
  analysis: Record<string, number>,
  responseCount: number,
  categories: TraitCategories = militaryTraitCategories
): string => {
  const { strengths, improvements } = getStrengthsAndImprovements(
    analysis,
    responseCount,
    categories
  );

  const overallScore = calculateOverallScore(analysis, responseCount);

  // Motivational opening statements based on score
  let feedback = "";

  if (overallScore >= 80) {
    feedback += "1. EXCEPTIONAL MILITARY POTENTIAL\n\n";
    feedback += `Your responses reveal an impressive military aptitude score of ${overallScore}%, placing you among those with outstanding potential for military service. Your thought patterns align remarkably well with the core values and mindset required in military environments.\n\n`;
  } else if (overallScore >= 60) {
    feedback += " STRONG MILITARY APTITUDE\n\n";
    feedback += `Your assessment indicates a solid military aptitude score of ${overallScore}%. This demonstrates a natural alignment with many key military values and traits that are essential for success in challenging environments.\n\n`;
  } else if (overallScore >= 40) {
    feedback += "1. DEVELOPING MILITARY POTENTIAL\n\n";
    feedback += `Your responses indicate a military aptitude score of ${overallScore}%. While this shows promising potential, there are specific areas where focused development could significantly enhance your military readiness and effectiveness.\n\n`;
  } else {
    feedback += " FOUNDATIONAL MILITARY APTITUDE\n\n";
    feedback += `Your assessment shows a military aptitude score of ${overallScore}%. This provides a foundation upon which you can build the essential traits and values that define military excellence. With dedicated effort, you can significantly enhance your military potential.\n\n`;
  }

  // Strengths section with colorful language
  if (strengths.length > 0) {
    feedback += " YOUR STANDOUT STRENGTHS\n";
    feedback +=
      "Your responses demonstrate particular excellence in these areas:\n";
    strengths.forEach((strength) => {
      feedback += `• ${strength}: You show natural aptitude in this critical military dimension.\n`;
    });
    feedback += "\n";
  }

  // Improvements section with constructive framing
  if (improvements.length > 0) {
    feedback += " YOUR GROWTH OPPORTUNITIES\n";
    feedback +=
      "Strategic focus in these areas could elevate your military potential:\n";
    improvements.forEach((area) => {
      feedback += `• ${area}: Developing this trait would significantly enhance your overall military effectiveness.\n`;
    });
    feedback += "\n";
  }

  // Personalized advice based on score ranges
  feedback += " PERSONALIZED INSIGHTS\n";
  if (overallScore >= 80) {
    feedback +=
      "Your responses reveal the mindset of someone with exceptional military potential. You demonstrate the rare combination of leadership, discipline, and adaptability that defines elite military personnel. Your thought patterns show a natural alignment with military values and decision-making frameworks.\n\n";
    feedback +=
      "While you show remarkable aptitude across multiple dimensions, even the most accomplished military professionals continuously refine their skills. Consider how you might further develop your identified growth areas to achieve even greater excellence.";
  } else if (overallScore >= 60) {
    feedback +=
      "Your responses indicate strong military potential with a solid foundation in key military traits. You demonstrate good judgment in challenging situations and show an understanding of the values that underpin military service.\n\n";
    feedback +=
      "To further enhance your military aptitude, focus on developing your identified growth areas through targeted practice and study. Consider seeking mentorship from experienced military personnel who can provide guidance on developing these specific traits.";
  } else if (overallScore >= 40) {
    feedback +=
      "Your responses show promising military potential with several strengths to build upon. You demonstrate understanding of some key military concepts and values, providing a foundation for further development.\n\n";
    feedback +=
      "To strengthen your military aptitude, consider deliberate practice in your growth areas. Reading military leadership books, participating in team-based activities, and seeking structured environments that emphasize discipline and order could significantly enhance your development.";
  } else {
    feedback +=
      "Your responses provide a starting point for developing military aptitude. Everyone begins their journey from different starting positions, and with focused effort, significant growth is absolutely possible.\n\n";
    feedback +=
      "Consider immersing yourself in environments and activities that cultivate military values. Team sports, structured organizational activities, and leadership opportunities can all help develop the traits valued in military contexts. Regular reflection on decision-making and values will accelerate your growth.";
  }

  // Add response count acknowledgment
  if (responseCount >= 10) {
    feedback +=
      "\n\n RESPONSE TRACKING\n" +
      "Your thorough completion of " +
      responseCount +
      " responses provided substantial data for this analysis, allowing for a comprehensive assessment of your military aptitude profile.";
  } else if (responseCount >= 5) {
    feedback +=
      "\n\n RESPONSE TRACKING\n" +
      "Your completion of " +
      responseCount +
      " responses provided good insight into your military aptitude profile. For even more detailed analysis in the future, completing more responses would be beneficial.";
  } else if (responseCount > 0) {
    feedback +=
      "\n\n RESPONSE TRACKING\n" +
      "This analysis is based on " +
      responseCount +
      " responses. For a more comprehensive assessment, we recommend completing additional responses in the future.";
  } else {
    feedback +=
      "\n\n RESPONSE TRACKING\n" +
      "No responses were provided. Please complete the test to receive a comprehensive assessment of your military aptitude profile.";
  }

  return feedback;
};
