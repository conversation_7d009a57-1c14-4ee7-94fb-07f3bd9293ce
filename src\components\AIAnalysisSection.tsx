"use client";

import { useState, useEffect } from "react";
import {
  AIAnalysisResult,
  analyzeResponsesWithDeepSeek,
  analyzeResponsesAsMilitaryPsychologist,
} from "@/services/aiService";
import { analyzeWATResponsesWithDeepSeek } from "@/services/watAnalysisService";
import { analyzeSRTResponsesWithDeepSeek } from "@/services/srtAnalysisService";
import { analyzeTATResponsesWithDeepSeek } from "@/services/tatAnalysisService";
import { analyzeSDTResponsesWithDeepSeek } from "@/services/sdtAnalysisService";
import {
  SparklesIcon,
  CheckCircleIcon,
  LockClosedIcon,
  TrashIcon,
} from "@heroicons/react/24/outline";
import { useAuth } from "@/context/GoogleAuthContext";
import { useRoleCheck } from "@/utils/authUtils";
import {
  getAIAnalysis,
  saveAIAnalysis,
  deleteAIAnalysis,
} from "@/services/aiAnalysisService";
import { TestType, getResponses } from "@/services/responseService";
import MilitaryPsychologistDisplay from "./MilitaryPsychologistDisplay";

interface AIAnalysisSectionProps {
  testType: string;
  responseTimestamp?: number; // Timestamp of the response this analysis is for
  setName: string; // Set name for the test
  responses?: string[]; // Optional direct responses array
  prompts?: string[]; // Optional prompts/questions that elicited the responses
}

export default function AIAnalysisSection({
  testType,
  responseTimestamp,
  setName,
  responses: directResponses,
  prompts: directPrompts,
}: AIAnalysisSectionProps) {
  const { user } = useAuth();
  const { canAccessAIAnalysis } = useRoleCheck();
  // Track both analyses separately
  const [deepseekAnalysis, setDeepseekAnalysis] =
    useState<AIAnalysisResult | null>(null);
  const [militaryAnalysis, setMilitaryAnalysis] =
    useState<AIAnalysisResult | null>(null);

  // Track saved state for both analyses
  const [isDeepseekSaved, setIsDeepseekSaved] = useState(false);
  const [isMilitarySaved, setIsMilitarySaved] = useState(false);

  const [isLoadingFromDB, setIsLoadingFromDB] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Track loading and deleting states for both analyses
  const [isDeepseekLoading, setIsDeepseekLoading] = useState(false);
  const [isMilitaryLoading, setIsMilitaryLoading] = useState(false);
  const [isDeepseekDeleting, setIsDeepseekDeleting] = useState(false);
  const [isMilitaryDeleting, setIsMilitaryDeleting] = useState(false);

  // Track delete confirmation for both analyses
  const [showDeepseekDeleteConfirm, setShowDeepseekDeleteConfirm] =
    useState(false);
  const [showMilitaryDeleteConfirm, setShowMilitaryDeleteConfirm] =
    useState(false);

  // Track expanded states for DeepSeek analysis
  const [isDeepseekResponsesExpanded, setIsDeepseekResponsesExpanded] =
    useState(false);
  const [isDeepseekFeedbackExpanded, setIsDeepseekFeedbackExpanded] =
    useState(false);

  // Load saved analyses when component mounts
  useEffect(() => {
    const loadSavedAnalyses = async () => {
      if (!user || !responseTimestamp || !canAccessAIAnalysis) {
        console.log(
          "Cannot load analyses: missing user, timestamp, or access rights"
        );
        setIsLoadingFromDB(false);
        return;
      }

      console.log(
        `Attempting to load analyses for user ${user.uid}, test ${testType}, set ${setName}, timestamp ${responseTimestamp}`
      );

      try {
        // Load military psychologist analysis
        console.log("Trying to load military psychologist analysis");
        let militaryAnalysisDoc = await getAIAnalysis(
          user.uid,
          testType as TestType,
          setName,
          responseTimestamp,
          "military-psychologist" // Military psychologist analyses
        );

        // Check if this is a military psychologist analysis
        if (
          militaryAnalysisDoc &&
          militaryAnalysisDoc.analysis.militaryPsychologist
        ) {
          console.log("Found military psychologist analysis in database");
          setMilitaryAnalysis(militaryAnalysisDoc.analysis);
          setIsMilitarySaved(true);
        } else if (militaryAnalysisDoc) {
          console.log(
            "Found 'google' analysis but it's not a military psychologist analysis"
          );
        } else {
          console.log("No military psychologist analysis found");
        }

        // Load DeepSeek analysis
        console.log("Trying to load Response  analysis");
        const deepseekAnalysisDoc = await getAIAnalysis(
          user.uid,
          testType as TestType,
          setName,
          responseTimestamp,
          "deepseek"
        );

        if (deepseekAnalysisDoc) {
          console.log("Found response analysis in database");

          // Add response quality assessment if missing
          try {
            console.log(
              "Attempting to add response quality assessment to existing response analysis"
            );

            // Import the addResponseQualityAssessment function
            const {
              addResponseQualityAssessment,
            } = require("@/services/aiService");

            console.log(
              "addResponseQualityAssessment function imported successfully"
            );

            // Add response quality assessment to the analysis
            const enhancedAnalysis = addResponseQualityAssessment(
              deepseekAnalysisDoc.analysis,
              directResponses || []
            );

            // Log whether responseQualityAssessment is present after adding it
            if (enhancedAnalysis.responseQualityAssessment) {
              console.log(
                "Response quality assessment added to saved response analysis:",
                Object.keys(enhancedAnalysis.responseQualityAssessment).length,
                "responses assessed"
              );
            } else {
              console.warn(
                "Failed to add response quality assessment to saved response analysis"
              );
            }

            setDeepseekAnalysis(enhancedAnalysis);
          } catch (error) {
            console.error("Error adding response quality assessment:", error);
            // If there's an error, still use the original analysis
            setDeepseekAnalysis(deepseekAnalysisDoc.analysis);
          }

          setIsDeepseekSaved(true);
        } else {
          console.log("No response analysis found");
        }
      } catch (err) {
        console.error("Error loading saved analyses:", err);
      } finally {
        setIsLoadingFromDB(false);
      }
    };

    loadSavedAnalyses();
  }, [user, testType, setName, responseTimestamp, canAccessAIAnalysis]);

  // Function to perform AI analysis
  const performAnalysis = async (
    provider: "deepseek" | "military-psychologist"
  ) => {
    if (!user || !responseTimestamp || !canAccessAIAnalysis) {
      return;
    }

    try {
      // Set the appropriate loading state based on provider
      if (provider === "deepseek") {
        setIsDeepseekLoading(true);
      } else {
        setIsMilitaryLoading(true);
      }

      setError(null);

      // Use direct responses if available, otherwise fetch from database
      let responsesToAnalyze: string[] = [];
      let promptsToUse: string[] | undefined;

      if (directResponses && directResponses.length > 0) {
        console.log(
          "Using direct responses provided to component:",
          directResponses.length
        );
        responsesToAnalyze = directResponses;
        promptsToUse = directPrompts;
      } else {
        // Get the user's responses from database
        console.log("Fetching responses from database");
        const responseData = await getResponses(
          user.uid,
          testType as TestType,
          setName
        );

        if (
          !responseData ||
          !responseData.responses ||
          responseData.responses.length === 0
        ) {
          setError("No responses found to analyze.");
          return;
        }

        responsesToAnalyze = responseData.responses;
        promptsToUse = responseData.questions;
      }

      // Validate responses
      if (responsesToAnalyze.length === 0) {
        setError("No responses found to analyze.");
        return;
      }

      console.log(
        `Analyzing ${responsesToAnalyze.length} responses with ${provider}`
      );

      // Perform the analysis based on the selected provider
      let result: AIAnalysisResult;
      if (provider === "military-psychologist") {
        result = await analyzeResponsesAsMilitaryPsychologist(
          responsesToAnalyze,
          testType,
          promptsToUse
        );
      } else {
        // Use specialized analysis services for WAT, SRT, TAT, and SDT tests to avoid JSON parsing issues
        if (testType === "WAT") {
          console.log("Using WAT-specific analysis service");
          result = await analyzeWATResponsesWithDeepSeek(
            responsesToAnalyze,
            promptsToUse
          );
        } else if (testType === "SRT") {
          console.log("Using SRT-specific analysis service");
          result = await analyzeSRTResponsesWithDeepSeek(
            responsesToAnalyze,
            promptsToUse
          );
        } else if (testType === "TAT") {
          console.log("Using TAT-specific analysis service");
          result = await analyzeTATResponsesWithDeepSeek(
            responsesToAnalyze,
            promptsToUse
          );
        } else if (testType === "SDT") {
          console.log("Using SDT-specific analysis service");
          result = await analyzeSDTResponsesWithDeepSeek(
            responsesToAnalyze,
            promptsToUse
          );
        } else {
          result = await analyzeResponsesWithDeepSeek(
            responsesToAnalyze,
            testType,
            promptsToUse
          );
        }
      }

      // Save the analysis
      if (result) {
        // Log whether responseQualityAssessment is present
        if (result.responseQualityAssessment) {
          console.log(
            "Response quality assessment is present in the result:",
            Object.keys(result.responseQualityAssessment).length,
            "responses assessed"
          );
        } else {
          console.warn(
            "Response quality assessment is NOT present in the result"
          );
        }

        // Update the appropriate state based on provider
        if (provider === "deepseek") {
          setDeepseekAnalysis(result);
        } else {
          setMilitaryAnalysis(result);
        }

        // Use the same provider name for storage
        const providerToSave = provider;

        // Save to database
        const saved = await saveAIAnalysis(
          user.uid,
          testType as TestType,
          setName,
          responseTimestamp,
          result,
          providerToSave
        );

        if (saved) {
          console.log(`Successfully saved ${provider} analysis to Firestore`);
          // Update the appropriate saved state based on provider
          if (provider === "deepseek") {
            setIsDeepseekSaved(true);
          } else {
            setIsMilitarySaved(true);
          }
        } else {
          console.error(`Failed to save ${provider} analysis to Firestore`);
        }
      }
    } catch (err) {
      console.error(`Error performing ${provider} analysis:`, err);
      setError(
        `Failed to analyze responses with ${provider}: ${
          err instanceof Error ? err.message : "Unknown error"
        }`
      );
    } finally {
      // Reset the appropriate loading state based on provider
      if (provider === "deepseek") {
        setIsDeepseekLoading(false);
      } else {
        setIsMilitaryLoading(false);
      }
    }
  };

  // Function to delete a specific analysis
  const handleDeleteAnalysis = async (
    provider: "deepseek" | "military-psychologist"
  ) => {
    if (!user || !responseTimestamp) return;

    // Check if the analysis exists
    const analysisExists =
      provider === "deepseek" ? deepseekAnalysis : militaryAnalysis;
    if (!analysisExists) return;

    try {
      // Set the appropriate deleting state
      if (provider === "deepseek") {
        setIsDeepseekDeleting(true);
      } else {
        setIsMilitaryDeleting(true);
      }

      // Use the same provider name for storage
      const providerToUse = provider;

      // Create the document ID that includes the AI provider
      const analysisId = `${user.uid}_${testType}_${setName}_${responseTimestamp}_${providerToUse}_analysis`;

      console.log(`Deleting ${provider} analysis with ID: ${analysisId}`);

      const success = await deleteAIAnalysis(
        analysisId,
        user.uid,
        testType as TestType,
        setName,
        responseTimestamp,
        providerToUse
      );

      if (success) {
        console.log(`Successfully deleted ${provider} analysis from Firestore`);
        // Clear the appropriate analysis
        if (provider === "deepseek") {
          setDeepseekAnalysis(null);
          setIsDeepseekSaved(false);
          setShowDeepseekDeleteConfirm(false);
        } else {
          setMilitaryAnalysis(null);
          setIsMilitarySaved(false);
          setShowMilitaryDeleteConfirm(false);
        }
      } else {
        console.error(`Failed to delete ${provider} analysis from Firestore`);
      }
    } catch (error) {
      console.error(`Error deleting ${provider} analysis:`, error);
    } finally {
      // Reset the appropriate deleting state
      if (provider === "deepseek") {
        setIsDeepseekDeleting(false);
      } else {
        setIsMilitaryDeleting(false);
      }
    }
  };

  // Log the DeepSeek analysis object when it changes
  useEffect(() => {
    if (deepseekAnalysis) {
      console.log(
        "Current response analysis object:",
        JSON.stringify(deepseekAnalysis)
      );
      console.log(
        "Has responseQualityAssessment:",
        deepseekAnalysis.responseQualityAssessment ? "Yes" : "No"
      );
      if (deepseekAnalysis.responseQualityAssessment) {
        console.log(
          "responseQualityAssessment entries:",
          Object.keys(deepseekAnalysis.responseQualityAssessment).length
        );
      }
    }
  }, [deepseekAnalysis]);

  // Log the Military Psychologist analysis object when it changes
  useEffect(() => {
    if (militaryAnalysis) {
      console.log(
        "Current Military Psychologist analysis object:",
        JSON.stringify(militaryAnalysis)
      );
      if (militaryAnalysis.militaryPsychologist) {
        console.log("Has militaryPsychologist data:", "Yes");
      }
    }
  }, [militaryAnalysis]);

  // Trait scores rendering removed as requested

  return (
    <div className="bg-white shadow rounded-lg p-4 sm:p-6 mt-6">
      <div className="flex flex-col space-y-4 mb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <SparklesIcon className="h-5 w-5 mr-2 text-blue-500" />
            <h2 className="text-xl font-bold text-gray-900">AI Analysis</h2>
          </div>
        </div>
      </div>

      {isLoadingFromDB && (
        <div className="flex flex-col items-center justify-center py-8">
          <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
          <p className="mt-4 text-gray-600 text-center">
            Loading saved analyses...
          </p>
        </div>
      )}

      {error && !(isDeepseekLoading || isMilitaryLoading) && (
        <div className="bg-red-100 border-2 border-red-300 rounded-md p-4 mt-4 text-center">
          <p className="text-red-800 font-medium text-lg">
            Error Analyzing Responses
          </p>
          <p className="text-red-700 mt-2">{error}</p>
        </div>
      )}

      {/* Military Psychologist Analysis Section */}
      <div className="mb-8 border-b pb-8">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4 space-y-3 sm:space-y-0">
          <div className="flex items-center">
            <h3 className="text-lg sm:text-xl font-bold text-green-700">
              Military Psychologist Analysis
            </h3>
            {isMilitarySaved && (
              <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                <CheckCircleIcon className="h-3 w-3 mr-1" />
                Saved
              </span>
            )}
          </div>

          {militaryAnalysis && !isMilitaryLoading ? (
            <div className="self-center sm:self-auto">
              {showMilitaryDeleteConfirm ? (
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() =>
                      handleDeleteAnalysis("military-psychologist")
                    }
                    disabled={isMilitaryDeleting}
                    className="text-red-600 hover:text-red-800 text-sm font-medium"
                  >
                    {isMilitaryDeleting ? "Deleting..." : "Confirm Delete"}
                  </button>
                  <button
                    onClick={() => setShowMilitaryDeleteConfirm(false)}
                    disabled={isMilitaryDeleting}
                    className="text-gray-600 hover:text-gray-800 text-sm font-medium"
                  >
                    Cancel
                  </button>
                </div>
              ) : (
                <button
                  onClick={() => setShowMilitaryDeleteConfirm(true)}
                  className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 w-full sm:w-auto justify-center sm:justify-start"
                >
                  <TrashIcon className="h-4 w-4 mr-1" />
                  Delete Analysis
                </button>
              )}
            </div>
          ) : !isMilitaryLoading && canAccessAIAnalysis ? (
            <button
              onClick={() => performAnalysis("military-psychologist")}
              disabled={isMilitaryLoading}
              className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed w-full sm:w-auto"
            >
              <SparklesIcon className="h-5 w-5 mr-2" />
              Generate Military Psychologist Analysis
            </button>
          ) : null}
        </div>

        {isMilitaryLoading ? (
          <div className="flex flex-col items-center justify-center py-8">
            <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-green-500"></div>
            <p className="mt-4 text-gray-600 text-center">
              Generating Military Psychologist analysis...
            </p>
          </div>
        ) : militaryAnalysis ? (
          <MilitaryPsychologistDisplay
            analysis={{
              userId: user?.uid || "",
              testType: testType as TestType,
              setName,
              responseTimestamp: responseTimestamp || 0,
              analysis: militaryAnalysis,
              createdAt: null,
              isAIGenerated: true,
              aiProvider: "military-psychologist",
              docId: `${user?.uid}_${testType}_${setName}_${responseTimestamp}_military-psychologist_analysis`,
            }}
            isExpanded={false}
            onDelete={() => handleDeleteAnalysis("military-psychologist")}
          />
        ) : !canAccessAIAnalysis ? (
          <div className="bg-blue-50 border border-blue-200 rounded-md p-4 sm:p-6 text-center">
            <LockClosedIcon className="h-8 w-8 mx-auto text-gray-500 mb-2" />
            <p className="text-gray-700 mb-4 text-sm sm:text-base">
              You don't have access to the AI analysis feature. Please contact
              an administrator to request access.
            </p>
          </div>
        ) : (
          <div className="bg-green-50 border border-green-200 rounded-md p-4 sm:p-6 text-center">
            <SparklesIcon className="h-8 w-8 mx-auto text-green-500 mb-2" />
            <p className="text-green-700 mb-4 text-sm sm:text-base">
              Generate a Military Psychologist analysis to get professional
              insights into your military aptitude.
            </p>
          </div>
        )}
      </div>
      {/* DeepSeek Analysis Section */}
      <div className=" ">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4 space-y-3 sm:space-y-0">
          <div className="flex items-center">
            <h3 className="text-lg sm:text-xl font-bold text-indigo-700">
              Response Quality Analysis
            </h3>
            {isDeepseekSaved && (
              <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                <CheckCircleIcon className="h-3 w-3 mr-1" />
                Saved
              </span>
            )}
          </div>

          {deepseekAnalysis && !isDeepseekLoading ? (
            <div className="self-center sm:self-auto">
              {showDeepseekDeleteConfirm ? (
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleDeleteAnalysis("deepseek")}
                    disabled={isDeepseekDeleting}
                    className="text-red-600 hover:text-red-800 text-sm font-medium"
                  >
                    {isDeepseekDeleting ? "Deleting..." : "Confirm Delete"}
                  </button>
                  <button
                    onClick={() => setShowDeepseekDeleteConfirm(false)}
                    disabled={isDeepseekDeleting}
                    className="text-gray-600 hover:text-gray-800 text-sm font-medium"
                  >
                    Cancel
                  </button>
                </div>
              ) : (
                <button
                  onClick={() => setShowDeepseekDeleteConfirm(true)}
                  className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 w-full sm:w-auto justify-center sm:justify-start"
                >
                  <TrashIcon className="h-4 w-4 mr-1" />
                  Delete Analysis
                </button>
              )}
            </div>
          ) : !isDeepseekLoading && canAccessAIAnalysis ? (
            <button
              onClick={() => performAnalysis("deepseek")}
              disabled={isDeepseekLoading}
              className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed w-full sm:w-auto"
            >
              <SparklesIcon className="h-5 w-5 mr-2" />
              Generate Response Analysis
            </button>
          ) : null}
        </div>

        {isDeepseekLoading ? (
          <div className="flex flex-col items-center justify-center py-8">
            <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-indigo-500"></div>
            <p className="mt-4 text-gray-600 text-center">
              Generating Response analysis...
            </p>
          </div>
        ) : deepseekAnalysis ? (
          <div className="space-y-6">
            {/* Overall Score */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
              <h3 className="text-lg font-medium text-gray-900 text-center sm:text-left">
                Overall Military Aptitude
              </h3>
              <div className="text-2xl font-bold text-indigo-600 text-center sm:text-right">
                {Math.round(deepseekAnalysis.overallScore / 10)}/10
              </div>
            </div>

            {/* Progress bar for overall score */}
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div
                className="bg-indigo-600 h-3 rounded-full"
                style={{ width: `${deepseekAnalysis.overallScore}%` }}
              ></div>
            </div>

            {/* Stack layout for Response Quality Assessment and Detailed Feedback */}
            <div className="mt-6 flex flex-col gap-6">
              {/* Response Quality Assessment */}
              {deepseekAnalysis.responseQualityAssessment &&
                Object.keys(deepseekAnalysis.responseQualityAssessment).length >
                  0 && (
                  <div className="w-full">
                    <h3 className="text-lg font-medium text-gray-900 mb-3 text-center sm:text-left">
                      Response Quality Assessment
                    </h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                      {Object.entries(
                        deepseekAnalysis.responseQualityAssessment
                      )
                        // Limit to first 3 responses if not expanded
                        .slice(0, isDeepseekResponsesExpanded ? undefined : 3)
                        .map(([index, assessment]) => (
                          <div
                            key={index}
                            className={`border rounded-md p-3 sm:p-4 ${
                              assessment.quality === "Good"
                                ? "border-green-200 bg-green-50"
                                : assessment.quality === "Moderate"
                                ? "border-amber-200 bg-amber-50"
                                : "border-red-200 bg-red-50"
                            }`}
                          >
                            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-2 space-y-1 sm:space-y-0">
                              <div className="flex items-center justify-center sm:justify-start">
                                <span className="text-sm font-medium mr-2">
                                  Response {parseInt(index) + 1}:
                                </span>
                                <span
                                  className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                    assessment.quality === "Good"
                                      ? "bg-green-100 text-green-800"
                                      : assessment.quality === "Moderate"
                                      ? "bg-amber-100 text-amber-800"
                                      : "bg-red-100 text-red-800"
                                  }`}
                                >
                                  {assessment.quality}
                                </span>
                              </div>
                            </div>

                            <div className="text-sm text-gray-700 mb-2 italic text-center sm:text-left">
                              "{assessment.response}..."
                            </div>

                            {(assessment.quality === "Bad" ||
                              assessment.quality === "Moderate") &&
                              assessment.improvementSuggestions && (
                                <div className="mt-2">
                                  <h4 className="text-sm font-medium text-gray-900 mb-1 text-center sm:text-left">
                                    Improvement Suggestions:
                                  </h4>
                                  <ul className="list-disc pl-5 space-y-1 text-sm text-left">
                                    {assessment.improvementSuggestions.map(
                                      (suggestion, i) => (
                                        <li
                                          key={i}
                                          className={`${
                                            assessment.quality === "Bad"
                                              ? "text-red-700"
                                              : "text-amber-700"
                                          }`}
                                        >
                                          {suggestion}
                                        </li>
                                      )
                                    )}
                                  </ul>
                                </div>
                              )}
                          </div>
                        ))}
                    </div>

                    {/* Show More/Less Button */}
                    {deepseekAnalysis.responseQualityAssessment &&
                      Object.keys(deepseekAnalysis.responseQualityAssessment)
                        .length > 3 && (
                        <div className="mt-4 text-center">
                          <button
                            onClick={() =>
                              setIsDeepseekResponsesExpanded(
                                !isDeepseekResponsesExpanded
                              )
                            }
                            className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 w-full sm:w-auto"
                          >
                            {isDeepseekResponsesExpanded ? (
                              <>
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  className="h-5 w-5 mr-2"
                                  viewBox="0 0 20 20"
                                  fill="currentColor"
                                >
                                  <path
                                    fillRule="evenodd"
                                    d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z"
                                    clipRule="evenodd"
                                  />
                                </svg>
                                Show Fewer Responses
                              </>
                            ) : (
                              <>
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  className="h-5 w-5 mr-2"
                                  viewBox="0 0 20 20"
                                  fill="currentColor"
                                >
                                  <path
                                    fillRule="evenodd"
                                    d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                    clipRule="evenodd"
                                  />
                                </svg>
                                Show All{" "}
                                {
                                  Object.keys(
                                    deepseekAnalysis.responseQualityAssessment
                                  ).length
                                }{" "}
                                Responses
                              </>
                            )}
                          </button>
                        </div>
                      )}
                  </div>
                )}

              {/* Detailed Feedback */}
              {deepseekAnalysis.detailedFeedback && (
                <div className="w-full">
                  <h3 className="text-lg font-medium text-gray-900 mb-2 text-center sm:text-left">
                    Detailed Feedback
                  </h3>
                  <div className="bg-gray-50 rounded-md p-3 sm:p-4 h-full">
                    {deepseekAnalysis.detailedFeedback &&
                    typeof deepseekAnalysis.detailedFeedback === "string" &&
                    !deepseekAnalysis.detailedFeedback.includes(
                      "Error processing DeepSeek response"
                    ) ? (
                      <p className="text-gray-700 whitespace-pre-line text-sm sm:text-base">
                        {isDeepseekFeedbackExpanded
                          ? deepseekAnalysis.detailedFeedback
                          : deepseekAnalysis.detailedFeedback.length > 300
                          ? `${deepseekAnalysis.detailedFeedback.substring(
                              0,
                              300
                            )}...`
                          : deepseekAnalysis.detailedFeedback}
                      </p>
                    ) : (
                      <div className="p-3 sm:p-4 bg-white rounded-md border border-gray-200">
                        <h4 className="text-lg font-medium text-gray-900 mb-2 text-center sm:text-left">
                          Detailed Analysis
                        </h4>
                        <p className="text-gray-700 mb-4 text-sm sm:text-base text-center sm:text-left">
                          Based on your responses, here is a comprehensive
                          analysis of your military aptitude:
                        </p>
                        <ul className="list-disc pl-5 space-y-2 text-gray-700 text-sm sm:text-base text-left">
                          <li>
                            Your responses show a basic understanding of
                            military concepts and principles.
                          </li>
                          <li>
                            Consider providing more detailed examples in your
                            answers to demonstrate deeper knowledge.
                          </li>
                          <li>
                            Focus on incorporating military context and
                            protocols in your future responses.
                          </li>
                          <li>
                            Your overall approach shows potential, but needs
                            more specific military terminology and reasoning.
                          </li>
                        </ul>
                        <p className="mt-4 text-gray-700 text-sm sm:text-base text-center sm:text-left">
                          Continue to develop your understanding of military
                          scenarios and decision-making processes to improve
                          your aptitude score.
                        </p>
                      </div>
                    )}
                  </div>

                  {/* Show More/Less Button for Detailed Feedback */}
                  {deepseekAnalysis.detailedFeedback &&
                    typeof deepseekAnalysis.detailedFeedback === "string" &&
                    !deepseekAnalysis.detailedFeedback.includes(
                      "Error processing DeepSeek response"
                    ) &&
                    deepseekAnalysis.detailedFeedback.length > 300 && (
                      <div className="mt-4 text-center">
                        <button
                          onClick={() =>
                            setIsDeepseekFeedbackExpanded(
                              !isDeepseekFeedbackExpanded
                            )
                          }
                          className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 w-full sm:w-auto"
                        >
                          {isDeepseekFeedbackExpanded ? (
                            <>
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                className="h-5 w-5 mr-2"
                                viewBox="0 0 20 20"
                                fill="currentColor"
                              >
                                <path
                                  fillRule="evenodd"
                                  d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z"
                                  clipRule="evenodd"
                                />
                              </svg>
                              Show Summary
                            </>
                          ) : (
                            <>
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                className="h-5 w-5 mr-2"
                                viewBox="0 0 20 20"
                                fill="currentColor"
                              >
                                <path
                                  fillRule="evenodd"
                                  d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                  clipRule="evenodd"
                                />
                              </svg>
                              Read Full Analysis
                            </>
                          )}
                        </button>
                      </div>
                    )}
                </div>
              )}
            </div>

            {/* AI Generated Notice */}
            <div className="text-xs text-gray-500 mt-4 flex items-center">
              <SparklesIcon className="h-3 w-3 mr-1" />
              {deepseekAnalysis.isAIGenerated
                ? `This analysis was generated using Response Quality and is provided as a free service. ${
                    isDeepseekSaved
                      ? "This analysis is saved to your profile."
                      : ""
                  } Although we try our best to make this app accurate, we are using AI for analyzing the response so sometimes the information might be inaccurate.`
                : `This response was generated using AI technology. Although we try our best to make this app accurate, we are using AI for analyzing the response so sometimes the information might be inaccurate.`}
            </div>
          </div>
        ) : !canAccessAIAnalysis ? (
          <div className="bg-blue-50 border border-blue-200 rounded-md p-4 sm:p-6 text-center">
            <LockClosedIcon className="h-8 w-8 mx-auto text-gray-500 mb-2" />
            <p className="text-gray-700 mb-4 text-sm sm:text-base">
              You don't have access to the AI analysis feature. Please contact
              an administrator to request access.
            </p>
          </div>
        ) : (
          <div className="bg-indigo-50 border border-indigo-200 rounded-md p-4 sm:p-6 text-center">
            <SparklesIcon className="h-8 w-8 mx-auto text-indigo-500 mb-2" />
            <p className="text-indigo-700 mb-4 text-sm sm:text-base">
              Generate a Response Quality analysis to get detailed response
              quality assessment and feedback.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
