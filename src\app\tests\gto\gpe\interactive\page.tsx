"use client";

import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import {
  ArrowLeftIcon,
  MapIcon,
  ClockIcon,
  ArrowRightIcon,
  CheckIcon,
  InformationCircleIcon,
  PencilIcon,
  UserGroupIcon,
  PresentationChartBarIcon,
} from "@heroicons/react/24/outline";
import NavigationButton from "@/components/NavigationButton";
import SimpleButton from "@/components/SimpleButton";
import { gpeScenarios, GPEScenario, MapPoint } from "@/data/gpe-scenarios";

// Define the steps of the GPE process
const GPE_STEPS = [
  {
    id: "intro",
    title: "Introduction",
    description: "Overview of the Group Planning Exercise",
    icon: InformationCircleIcon,
  },
  {
    id: "narration",
    title: "GTO Narration",
    description: "The GTO explains the situation and color codes",
    icon: MapIcon,
  },
  {
    id: "story",
    title: "Story Reading",
    description: "5 minutes to read and understand the situation",
    icon: ClockIcon,
    duration: 5 * 60, // 5 minutes in seconds
  },
  {
    id: "solution",
    title: "Solution Planning",
    description: "10 minutes to write your solution",
    icon: PencilIcon,
    duration: 10 * 60, // 10 minutes in seconds
  },
  {
    id: "discussion",
    title: "Group Discussion",
    description: "20 minutes for group discussion",
    icon: UserGroupIcon,
    duration: 20 * 60, // 20 minutes in seconds
  },
  {
    id: "presentation",
    title: "Solution Presentation",
    description: "Present your solution using the map",
    icon: PresentationChartBarIcon,
  },
];

export default function InteractiveGPEPage() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(0);
  const [selectedScenario, setSelectedScenario] = useState<GPEScenario | null>(null);
  const [timeLeft, setTimeLeft] = useState(0);
  const [isTimerActive, setIsTimerActive] = useState(false);
  const [solution, setSolution] = useState("");
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const [showColorCodes, setShowColorCodes] = useState(false);

  // Select a random scenario on component mount
  useEffect(() => {
    const randomIndex = Math.floor(Math.random() * gpeScenarios.length);
    setSelectedScenario(gpeScenarios[randomIndex]);
  }, []);

  // Timer logic
  useEffect(() => {
    if (isTimerActive && timeLeft > 0) {
      timerRef.current = setTimeout(() => {
        setTimeLeft((prev) => prev - 1);
      }, 1000);
    } else if (timeLeft === 0 && isTimerActive) {
      setIsTimerActive(false);
      // Auto-advance to next step when timer ends
      if (currentStep < GPE_STEPS.length - 1) {
        handleNextStep();
      }
    }

    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, [isTimerActive, timeLeft, currentStep]);

  // Start timer for timed steps
  const startTimer = () => {
    const currentStepData = GPE_STEPS[currentStep];
    if (currentStepData.duration) {
      setTimeLeft(currentStepData.duration);
      setIsTimerActive(true);
    }
  };

  // Format time as MM:SS
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
  };

  // Handle next step button click
  const handleNextStep = () => {
    if (currentStep < GPE_STEPS.length - 1) {
      setCurrentStep((prev) => prev + 1);
      setIsTimerActive(false);
      
      // If the next step has a timer, initialize it
      const nextStepData = GPE_STEPS[currentStep + 1];
      if (nextStepData.duration) {
        setTimeLeft(nextStepData.duration);
      }
    }
  };

  // Handle previous step button click
  const handlePrevStep = () => {
    if (currentStep > 0) {
      setCurrentStep((prev) => prev - 1);
      setIsTimerActive(false);
    }
  };

  // Render the map component
  const renderMap = () => {
    if (!selectedScenario) return null;

    return (
      <div className="relative w-full h-96 bg-gray-100 border border-gray-300 rounded-lg overflow-hidden">
        {/* Map background */}
        <div className="absolute inset-0 bg-gray-200">
          {/* Map grid lines */}
          <div className="grid grid-cols-10 grid-rows-10 h-full w-full">
            {Array.from({ length: 10 }).map((_, i) => (
              <div key={`col-${i}`} className="border-r border-gray-300 h-full" />
            ))}
            {Array.from({ length: 10 }).map((_, i) => (
              <div key={`row-${i}`} className="border-b border-gray-300 w-full" />
            ))}
          </div>
        </div>

        {/* Map points */}
        {selectedScenario.mapPoints.map((point) => (
          <div
            key={point.id}
            className={`absolute w-6 h-6 transform -translate-x-1/2 -translate-y-1/2 rounded-full flex items-center justify-center ${
              point.type === 'obstacle' ? 'opacity-50' : 'border-2 border-white shadow-md'
            }`}
            style={{
              left: `${point.x}%`,
              top: `${point.y}%`,
              backgroundColor: point.color || '#000',
            }}
            title={point.description}
          >
            {point.label && (
              <span className="text-white font-bold text-xs">{point.label}</span>
            )}
          </div>
        ))}

        {/* Map legend */}
        <div className="absolute bottom-2 right-2 bg-white bg-opacity-90 p-2 rounded-md shadow-md">
          <button 
            onClick={() => setShowColorCodes(!showColorCodes)}
            className="text-xs font-medium text-gray-700 flex items-center"
          >
            <InformationCircleIcon className="h-4 w-4 mr-1" />
            {showColorCodes ? "Hide Color Codes" : "Show Color Codes"}
          </button>
          
          {showColorCodes && (
            <div className="mt-2 space-y-1">
              {Object.entries(selectedScenario.colorCodes).map(([key, value]) => (
                <div key={key} className="flex items-center text-xs">
                  <div 
                    className="w-3 h-3 rounded-full mr-1"
                    style={{ backgroundColor: value.color }}
                  />
                  <span className="text-gray-700">{value.description}</span>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    );
  };

  // Render content based on current step
  const renderStepContent = () => {
    if (!selectedScenario) return <div>Loading scenario...</div>;

    const currentStepData = GPE_STEPS[currentStep];

    switch (currentStepData.id) {
      case "intro":
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900">
              Welcome to the Interactive Group Planning Exercise
            </h2>
            <p className="text-gray-700">
              This simulation will guide you through the actual steps of a Group Planning Exercise (GPE) as conducted in military selection tests.
            </p>
            <div className="bg-green-50 p-6 rounded-lg">
              <h3 className="text-lg font-medium text-green-900 mb-4">
                The GPE process consists of the following steps:
              </h3>
              <ol className="list-decimal pl-5 space-y-2 text-green-800">
                <li>GTO narrates the situation and explains the color codes</li>
                <li>Candidates are given 5 minutes to read and understand the situation</li>
                <li>Candidates get 10 minutes to write their solution</li>
                <li>A 20-minute group discussion is conducted</li>
                <li>One candidate is nominated to present the group's solution</li>
              </ol>
            </div>
            <p className="text-gray-700">
              In this interactive simulation, you'll go through each of these steps with a realistic scenario. Click "Next" to begin.
            </p>
          </div>
        );

      case "narration":
        return (
          <div className="space-y-6">
            <div className="bg-amber-50 p-6 rounded-lg border border-amber-200">
              <div className="flex items-start">
                <div className="flex-shrink-0 mt-1">
                  <MapIcon className="h-6 w-6 text-amber-600" />
                </div>
                <div className="ml-3">
                  <h3 className="text-lg font-medium text-amber-800">
                    GTO Narration
                  </h3>
                  <p className="mt-2 text-amber-700">
                    "Candidates, I will now explain today's Group Planning Exercise. You will be presented with a scenario that requires careful planning and resource allocation. Pay close attention to the map, color codes, and situation details."
                  </p>
                  <p className="mt-2 text-amber-700">
                    "The map shows various points marked A, B, C, and D. Each represents a location with specific challenges. The color codes indicate different types of terrain or priority levels."
                  </p>
                  <p className="mt-2 text-amber-700">
                    "You will have 5 minutes to read the situation, followed by 10 minutes to write your solution, and then 20 minutes for group discussion."
                  </p>
                </div>
              </div>
            </div>

            <h3 className="text-xl font-semibold text-gray-900">
              {selectedScenario.title}
            </h3>
            
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-medium mb-2">Available Resources:</h4>
              <ul className="list-disc pl-5 space-y-1 text-gray-700">
                {selectedScenario.resources.map((resource) => (
                  <li key={resource.name}>
                    <span className="font-medium">{resource.name} (x{resource.quantity})</span>: {resource.description}
                  </li>
                ))}
              </ul>
            </div>

            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-medium mb-2">Time Constraints:</h4>
              <p className="text-gray-700">
                Current time: <span className="font-medium">{selectedScenario.timeConstraints.startTime}</span> hours
              </p>
              <p className="text-gray-700">
                Available until: <span className="font-medium">{selectedScenario.timeConstraints.endTime}</span> hours
              </p>
              <p className="text-gray-700">
                Total time available: <span className="font-medium">{selectedScenario.timeConstraints.totalAvailableHours}</span> hours
              </p>
            </div>

            {renderMap()}
          </div>
        );

      case "story":
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-gray-900">
                Scenario Details
              </h2>
              <div className="bg-green-100 text-green-800 px-4 py-2 rounded-full flex items-center">
                <ClockIcon className="h-5 w-5 mr-2" />
                <span className="font-mono font-medium">{formatTime(timeLeft)}</span>
              </div>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">
                {selectedScenario.title}
              </h3>
              <p className="text-gray-700 whitespace-pre-line">
                {selectedScenario.situation}
              </p>
            </div>

            {renderMap()}

            {!isTimerActive && (
              <div className="flex justify-center">
                <button
                  onClick={startTimer}
                  className="bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-6 rounded-lg shadow transition duration-200 flex items-center"
                >
                  <ClockIcon className="h-5 w-5 mr-2" />
                  Start 5-Minute Timer
                </button>
              </div>
            )}
          </div>
        );

      case "solution":
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-gray-900">
                Write Your Solution
              </h2>
              <div className="bg-green-100 text-green-800 px-4 py-2 rounded-full flex items-center">
                <ClockIcon className="h-5 w-5 mr-2" />
                <span className="font-mono font-medium">{formatTime(timeLeft)}</span>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                {renderMap()}
                
                <div className="mt-4 bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-medium mb-2">Available Resources:</h4>
                  <ul className="list-disc pl-5 space-y-1 text-gray-700 text-sm">
                    {selectedScenario.resources.map((resource) => (
                      <li key={resource.name}>
                        <span className="font-medium">{resource.name} (x{resource.quantity})</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              <div>
                <textarea
                  className="w-full h-64 p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  placeholder="Write your solution here. Consider resource allocation, prioritization, and time management..."
                  value={solution}
                  onChange={(e) => setSolution(e.target.value)}
                ></textarea>
                
                {selectedScenario.hints && (
                  <div className="mt-4 bg-amber-50 p-4 rounded-lg border border-amber-200">
                    <h4 className="font-medium mb-2 text-amber-800">Planning Hints:</h4>
                    <ul className="list-disc pl-5 space-y-1 text-amber-700 text-sm">
                      {selectedScenario.hints.map((hint, index) => (
                        <li key={index}>{hint}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>

            {!isTimerActive && (
              <div className="flex justify-center">
                <button
                  onClick={startTimer}
                  className="bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-6 rounded-lg shadow transition duration-200 flex items-center"
                >
                  <ClockIcon className="h-5 w-5 mr-2" />
                  Start 10-Minute Timer
                </button>
              </div>
            )}
          </div>
        );

      case "discussion":
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-gray-900">
                Group Discussion
              </h2>
              <div className="bg-green-100 text-green-800 px-4 py-2 rounded-full flex items-center">
                <ClockIcon className="h-5 w-5 mr-2" />
                <span className="font-mono font-medium">{formatTime(timeLeft)}</span>
              </div>
            </div>

            <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
              <div className="flex items-start">
                <div className="flex-shrink-0 mt-1">
                  <UserGroupIcon className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-3">
                  <h3 className="text-lg font-medium text-blue-800">
                    Discussion Guidelines
                  </h3>
                  <p className="mt-2 text-blue-700">
                    In a real GPE, this is where you would discuss your solution with the group. The goal is to arrive at a consensus and develop a comprehensive plan that addresses all aspects of the scenario.
                  </p>
                  <p className="mt-2 text-blue-700">
                    Key discussion points should include:
                  </p>
                  <ul className="list-disc pl-5 mt-2 space-y-1 text-blue-700">
                    <li>Prioritization of objectives</li>
                    <li>Resource allocation</li>
                    <li>Timeline and sequencing of actions</li>
                    <li>Contingency plans</li>
                    <li>Roles and responsibilities</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                {renderMap()}
              </div>
              <div>
                <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
                  <h4 className="font-medium mb-2">Your Solution:</h4>
                  <p className="text-gray-700 whitespace-pre-line">
                    {solution || "No solution written yet."}
                  </p>
                </div>
              </div>
            </div>

            {!isTimerActive && (
              <div className="flex justify-center">
                <button
                  onClick={startTimer}
                  className="bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-6 rounded-lg shadow transition duration-200 flex items-center"
                >
                  <ClockIcon className="h-5 w-5 mr-2" />
                  Start 20-Minute Timer
                </button>
              </div>
            )}
          </div>
        );

      case "presentation":
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900">
              Solution Presentation
            </h2>

            <div className="bg-amber-50 p-6 rounded-lg border border-amber-200">
              <div className="flex items-start">
                <div className="flex-shrink-0 mt-1">
                  <PresentationChartBarIcon className="h-6 w-6 text-amber-600" />
                </div>
                <div className="ml-3">
                  <h3 className="text-lg font-medium text-amber-800">
                    Presentation Guidelines
                  </h3>
                  <p className="mt-2 text-amber-700">
                    In a real GPE, one candidate would be nominated to present the group's solution to the GTO. The presentation should be clear, concise, and demonstrate effective planning.
                  </p>
                  <p className="mt-2 text-amber-700">
                    The presenter would use the map and a pointer stick to explain the plan, including:
                  </p>
                  <ul className="list-disc pl-5 mt-2 space-y-1 text-amber-700">
                    <li>The overall approach and strategy</li>
                    <li>How resources are allocated</li>
                    <li>The sequence of actions with timeline</li>
                    <li>How each objective will be addressed</li>
                    <li>Any contingency plans</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                {renderMap()}
              </div>
              <div>
                <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
                  <h4 className="font-medium mb-2">Final Solution:</h4>
                  <p className="text-gray-700 whitespace-pre-line">
                    {solution || "No solution provided."}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-green-50 p-6 rounded-lg">
              <h3 className="text-lg font-medium text-green-900 mb-4">
                Congratulations!
              </h3>
              <p className="text-green-800">
                You have completed the interactive Group Planning Exercise simulation. In a real GPE, the GTO would evaluate your performance based on:
              </p>
              <ul className="list-disc pl-5 mt-2 space-y-1 text-green-800">
                <li>Quality and practicality of your solution</li>
                <li>Contribution to the group discussion</li>
                <li>Leadership qualities demonstrated</li>
                <li>Ability to work as part of a team</li>
                <li>Communication and presentation skills</li>
              </ul>
            </div>
          </div>
        );

      default:
        return <div>Unknown step</div>;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="mx-auto max-w-5xl px-6">
        <NavigationButton
          href="/tests/gto/gpe"
          className="mb-8 inline-flex items-center text-gray-600 hover:text-gray-900"
        >
          <ArrowLeftIcon className="h-5 w-5 mr-2" />
          Back to GPE Overview
        </NavigationButton>

        <div className="bg-white rounded-2xl shadow-sm p-8 border-t-4 border-green-500">
          <div className="flex items-center space-x-3 mb-6">
            <div className="p-2 rounded-full bg-green-100">
              <MapIcon className="h-8 w-8 text-green-600" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900">
              Interactive Group Planning Exercise
            </h1>
          </div>

          {/* Progress steps */}
          <div className="mb-8">
            <div className="flex items-center justify-between w-full">
              {GPE_STEPS.map((step, index) => (
                <div
                  key={step.id}
                  className={`flex flex-col items-center ${
                    index === GPE_STEPS.length - 1 ? "" : "w-full"
                  }`}
                >
                  <div
                    className={`flex items-center justify-center w-10 h-10 rounded-full ${
                      index < currentStep
                        ? "bg-green-500 text-white"
                        : index === currentStep
                        ? "bg-green-100 text-green-600 ring-2 ring-green-500"
                        : "bg-gray-100 text-gray-400"
                    }`}
                  >
                    {index < currentStep ? (
                      <CheckIcon className="w-6 h-6" />
                    ) : (
                      <step.icon className="w-5 h-5" />
                    )}
                  </div>
                  <div className="text-xs mt-2 text-center hidden sm:block">
                    {step.title}
                  </div>
                  {index < GPE_STEPS.length - 1 && (
                    <div
                      className={`h-0.5 w-full mt-5 hidden sm:block ${
                        index < currentStep ? "bg-green-500" : "bg-gray-200"
                      }`}
                    ></div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Step content */}
          <div className="mb-8">{renderStepContent()}</div>

          {/* Navigation buttons */}
          <div className="flex justify-between mt-8">
            <SimpleButton
              onClick={handlePrevStep}
              className={`flex items-center ${
                currentStep === 0 ? "invisible" : ""
              }`}
            >
              <ArrowLeftIcon className="h-5 w-5 mr-2" />
              Previous
            </SimpleButton>

            <SimpleButton
              onClick={handleNextStep}
              className={`flex items-center ${
                currentStep === GPE_STEPS.length - 1 ? "invisible" : ""
              }`}
            >
              Next
              <ArrowRightIcon className="h-5 w-5 ml-2" />
            </SimpleButton>
          </div>
        </div>
      </div>
    </div>
  );
}
