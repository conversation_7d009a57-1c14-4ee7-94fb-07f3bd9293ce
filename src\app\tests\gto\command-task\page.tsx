"use client";

import { ArrowLeftIcon, CommandLineIcon } from "@heroicons/react/24/outline";
import NavigationButton from "@/components/NavigationButton";
import React from "react";

export default function CommandTaskPage() {
  const [showMoreTasks, setShowMoreTasks] = React.useState(false);

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="mx-auto max-w-4xl px-6">
        <NavigationButton
          href="/tests/gto"
          className="mb-8 inline-flex items-center text-gray-600 hover:text-gray-900"
        >
          <ArrowLeftIcon className="h-5 w-5 mr-2" />
          Back to GTO Test
        </NavigationButton>

        <div className="bg-white rounded-2xl shadow-sm p-8 border-t-4 border-emerald-500">
          <div className="flex items-center space-x-3 mb-6">
            <div className="p-2 rounded-full bg-emerald-100">
              <CommandLineIcon className="h-8 w-8 text-emerald-600" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900">
              Command Task (CT)
            </h1>
          </div>

          <div className="prose prose-lg max-w-none">
            <p className="text-lg text-gray-700 mb-6">
              The Command Task (CT) is a critical assessment that evaluates your
              leadership abilities by placing you in charge of a small team to
              accomplish a specific objective, testing your command skills,
              decision-making, and team management.
            </p>

            <h2 className="text-xl font-semibold text-gray-900 mt-8 mb-4">
              What is Command Task?
            </h2>
            <p>
              The Command Task (CT) is a unique assessment in the GTO series
              where, for the first time, a candidate is designated as a
              commander with positional power. Unlike the leaderless tasks (PGT,
              HGT, FGT), in the Command Task, you are given complete authority
              to lead your team in your own way, without anyone questioning or
              interfering with your decisions.
            </p>
            <p className="mt-2">
              Each candidate, in turn, is nominated as a commander for one task.
              You will select two team members from the group to assist you, and
              then you must lead them to complete a specific obstacle or task
              using the resources provided. The task is similar to those in PGT
              but may be circular or structured differently.
            </p>
            <p className="mt-2">
              The difficulty of the task assigned may vary based on the
              candidate's perceived capability - stronger candidates often
              receive more challenging tasks. You will have 15 minutes to
              complete your task, and your performance as a leader is the
              primary focus of this assessment.
            </p>

            <h2 className="text-xl font-semibold text-gray-900 mt-8 mb-4">
              Skills Evaluated
            </h2>
            <div className="bg-emerald-50 p-6 rounded-lg mb-8">
              <h3 className="text-lg font-medium text-emerald-900 mb-4">
                CT evaluates your:
              </h3>
              <ul className="list-disc pl-5 space-y-2 text-emerald-800">
                <li>Leadership abilities</li>
                <li>Command and control</li>
                <li>Decision-making under pressure</li>
                <li>Team management</li>
                <li>Problem-solving skills</li>
                <li>Resource allocation</li>
                <li>Adaptability to challenges</li>
                <li>Communication effectiveness</li>
              </ul>
            </div>

            <h2 className="text-xl font-semibold text-gray-900 mt-8 mb-4">
              Official Briefing for Command Task
            </h2>
            <div className="bg-emerald-50 p-6 rounded-lg mb-8 italic text-emerald-800">
              <p className="mb-3">
                "Gentleman, in your group tasks so far you have been working as
                equals, where each of you had the same say as to how the task
                could be done. At times, you might have felt differently to the
                plan of action adopted by your group. This task, called the
                command task, will afford an opportunity to you of doing the
                task in your own way.
              </p>
              <p className="mb-3">
                Each of you, in turn, will be nominated as a commander for one
                task. You will then do your task in the manner you want to
                without anyone else questioning or interfering you.
              </p>
              <p className="mb-3">
                The task will be an obstacle of the PGT with similar rules,
                except the group rule which is bit modified, for obvious
                reasons. You will be allowed 15 minutes each to complete your
                task.
              </p>
              <p className="mb-3">
                Each commander will have the choice of his own team from amongst
                the group members. Excluding the commander, the team will
                consist of two members.
              </p>
              <p>
                Now those of you who are chosen as teammates must do as the
                commander tells you to do whether you approve it or not. After
                all, the responsibility of doing the task is his and it is only
                fair that his wishes are carried out unquestioned."
              </p>
            </div>

            <h2 className="text-xl font-semibold text-gray-900 mt-8 mb-4">
              Command Task Format
            </h2>
            <div className="bg-gray-100 p-6 rounded-lg mb-8">
              <ol className="list-decimal pl-5 space-y-3">
                <li>
                  <strong>Team Selection:</strong> You will select 2 team
                  members from your group to assist you.
                </li>
                <li>
                  <strong>Task Assignment:</strong> The GTO will assign you a
                  specific task or obstacle to overcome, often based on your
                  perceived capability.
                </li>
                <li>
                  <strong>Resource Allocation:</strong> You will be provided
                  with limited resources (ropes, planks, balli, etc.) similar to
                  PGT.
                </li>
                <li>
                  <strong>Planning Time:</strong> You get a short time to plan
                  your approach and brief your team.
                </li>
                <li>
                  <strong>Execution:</strong> You must lead your team to
                  complete the task within the allotted time (15 minutes).
                </li>
                <li>
                  <strong>Stress Handling:</strong> The GTO may create stress by
                  denying or enabling certain options during the task.
                </li>
                <li>
                  <strong>Evaluation:</strong> The GTO observes your leadership
                  style, decision-making, and team management throughout.
                </li>
              </ol>
            </div>

            <h2 className="text-xl font-semibold text-gray-900 mt-8 mb-4">
              Command Task Examples
            </h2>
            <div className="bg-gray-100 p-6 rounded-lg mb-8">
              <p className="italic text-gray-600 mb-3">
                (Solve physical/logistical challenges with limited resources)
              </p>
              <ol className="list-decimal pl-5 space-y-4">
                <li className="pb-3 border-b border-gray-200">
                  <strong>Task 1:</strong>
                  <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-700">
                    <li>
                      <strong>Objective</strong>: Transport a 100 kg encrypted
                      server across a 4-meter-wide &quot;acid pool&quot;
                      (simulated).
                    </li>
                    <li>
                      <strong>Resources</strong>: 2 wooden planks (3m each), 4
                      sandbags, 1 pulley system.
                    </li>
                    <li>
                      <strong>Rules</strong>: No part of the equipment can touch
                      the ground.
                    </li>
                  </ul>
                </li>
                <li className="pb-3 border-b border-gray-200">
                  <strong>Task 2:</strong>
                  <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-700">
                    <li>
                      <strong>Objective</strong>: Retrieve a &quot;classified
                      document&quot; from a 3-meter-high platform.
                    </li>
                    <li>
                      <strong>Resources</strong>: 1 ladder (2m), 1 rope (10m), 1
                      hook.
                    </li>
                    <li>
                      <strong>Rules</strong>: Only one cadet can climb at a
                      time.
                    </li>
                  </ul>
                </li>
                <li className="pb-3 border-b border-gray-200">
                  <strong>Task 3:</strong>
                  <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-700">
                    <li>
                      <strong>Objective</strong>: Build a makeshift bridge over
                      a &quot;ravine&quot; using 2 logs and ropes to move a 50
                      kg crate.
                    </li>
                    <li>
                      <strong>Constraints</strong>: Logs cannot span the entire
                      gap (use ropes creatively).
                    </li>
                  </ul>
                </li>
                {showMoreTasks && (
                  <>
                    <li className="pb-3 border-b border-gray-200">
                      <strong>Task 4:</strong>
                      <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-700">
                        <li>
                          <strong>Objective</strong>: Transport a fragile piece
                          of communication equipment across a 5-meter-wide
                          &quot;lava pit&quot; (simulated).
                        </li>
                        <li>
                          <strong>Resources</strong>: 2 metal sheets (2m each),
                          4 ropes (5m each), 1 pulley system.
                        </li>
                        <li>
                          <strong>Rules</strong>: Equipment must not touch the
                          ground.
                        </li>
                      </ul>
                    </li>
                    <li className="pb-3 border-b border-gray-200">
                      <strong>Task 5:</strong>
                      <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-700">
                        <li>
                          <strong>Objective</strong>: Retrieve a
                          &quot;confidential document&quot; from a locked room
                          without using the door.
                        </li>
                        <li>
                          <strong>Resources</strong>: 1 ladder (3m), 1 rope
                          (10m), 1 crowbar, 1 window (1.5m above ground).
                        </li>
                        <li>
                          <strong>Rules</strong>: Only one cadet can enter the
                          room at a time.
                        </li>
                      </ul>
                    </li>
                    <li className="pb-3 border-b border-gray-200">
                      <strong>Task 6:</strong>
                      <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-700">
                        <li>
                          <strong>Objective</strong>: Build a makeshift shelter
                          to protect a &quot;wounded soldier&quot; from a
                          simulated storm.
                        </li>
                        <li>
                          <strong>Resources</strong>: 2 tarps (3m x 3m), 4 poles
                          (2m each), 1 rope (10m), 1 medical kit.
                        </li>
                        <li>
                          <strong>Constraints</strong>: Shelter must withstand
                          strong winds and rain.
                        </li>
                      </ul>
                    </li>
                    <li className="pb-3 border-b border-gray-200">
                      <strong>Task 7:</strong>
                      <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-700">
                        <li>
                          <strong>Objective</strong>: Cross a 6-meter-wide
                          &quot;river&quot; using limited resources to transport
                          a 50 kg supply crate.
                        </li>
                        <li>
                          <strong>Resources</strong>: 2 logs (3m each), 4 ropes
                          (5m each), 1 pulley system.
                        </li>
                        <li>
                          <strong>Rules</strong>: No part of the equipment can
                          touch the water.
                        </li>
                      </ul>
                    </li>
                    <li className="pb-3 border-b border-gray-200">
                      <strong>Task 8:</strong>
                      <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-700">
                        <li>
                          <strong>Objective</strong>: Defuse a simulated bomb
                          placed in a crowded market (represented by a model).
                        </li>
                        <li>
                          <strong>Resources</strong>: 1 bomb disposal kit, 1
                          mirror, 1 set of wire cutters, 1 timer.
                        </li>
                        <li>
                          <strong>Rules</strong>: Must be completed within 10
                          minutes.
                        </li>
                      </ul>
                    </li>
                    <li className="pb-3 border-b border-gray-200">
                      <strong>Task 9:</strong>
                      <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-700">
                        <li>
                          <strong>Objective</strong>: Navigate a
                          &quot;minefield&quot; to retrieve a &quot;critical
                          component&quot; for a communication device.
                        </li>
                        <li>
                          <strong>Resources</strong>: 1 metal detector, 1 map of
                          the minefield, 1 flag to mark safe paths.
                        </li>
                        <li>
                          <strong>Rules</strong>: Must avoid all
                          &quot;mines&quot; (marked areas).
                        </li>
                      </ul>
                    </li>
                    <li className="pb-3 border-b border-gray-200">
                      <strong>Task 10:</strong>
                      <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-700">
                        <li>
                          <strong>Objective</strong>: Construct a bridge over a
                          4-meter-wide &quot;gorge&quot; to transport a 70 kg
                          equipment box.
                        </li>
                        <li>
                          <strong>Resources</strong>: 2 planks (3m each), 4
                          ropes (5m each), 1 pulley system.
                        </li>
                        <li>
                          <strong>Rules</strong>: Bridge must support the weight
                          of the box and one cadet.
                        </li>
                      </ul>
                    </li>
                    <li className="pb-3 border-b border-gray-200">
                      <strong>Task 11:</strong>
                      <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-700">
                        <li>
                          <strong>Objective</strong>: Rescue a &quot;trapped
                          civilian&quot; from a collapsed building (simulated
                          structure).
                        </li>
                        <li>
                          <strong>Resources</strong>: 1 crowbar, 1 rope (10m), 1
                          stretcher, 1 medical kit.
                        </li>
                        <li>
                          <strong>Rules</strong>: Must stabilize the structure
                          before entering.
                        </li>
                      </ul>
                    </li>
                    <li className="pb-3 border-b border-gray-200">
                      <strong>Task 12:</strong>
                      <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-700">
                        <li>
                          <strong>Objective</strong>: Transport a 60 kg
                          &quot;ammunition box&quot; across a 5-meter-wide
                          &quot;chasm.&quot;
                        </li>
                        <li>
                          <strong>Resources</strong>: 2 ladders (3m each), 4
                          ropes (5m each), 1 pulley system.
                        </li>
                        <li>
                          <strong>Rules</strong>: Box must not touch the ground.
                        </li>
                      </ul>
                    </li>
                    <li className="pb-3 border-b border-gray-200">
                      <strong>Task 13:</strong>
                      <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-700">
                        <li>
                          <strong>Objective</strong>: Retrieve a &quot;sensitive
                          document&quot; from a high ledge in a simulated urban
                          environment.
                        </li>
                        <li>
                          <strong>Resources</strong>: 1 ladder (3m), 1 rope
                          (10m), 1 grappling hook.
                        </li>
                        <li>
                          <strong>Rules</strong>: Only one cadet can climb at a
                          time.
                        </li>
                      </ul>
                    </li>
                    <li className="pb-3 border-b border-gray-200">
                      <strong>Task 14:</strong>
                      <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-700">
                        <li>
                          <strong>Objective</strong>: Build a makeshift raft to
                          cross a &quot;flooded area&quot; and transport a 50 kg
                          supply crate.
                        </li>
                        <li>
                          <strong>Resources</strong>: 2 barrels, 4 planks (2m
                          each), 1 rope (10m).
                        </li>
                        <li>
                          <strong>Rules</strong>: Raft must float and support
                          the weight of the crate and one cadet.
                        </li>
                      </ul>
                    </li>
                    <li className="pb-3 border-b border-gray-200">
                      <strong>Task 15:</strong>
                      <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-700">
                        <li>
                          <strong>Objective</strong>: Defuse a simulated bomb in
                          a confined space (represented by a small room).
                        </li>
                        <li>
                          <strong>Resources</strong>: 1 bomb disposal kit, 1
                          mirror, 1 set of wire cutters, 1 timer.
                        </li>
                        <li>
                          <strong>Rules</strong>: Must be completed within 8
                          minutes.
                        </li>
                      </ul>
                    </li>
                    <li className="pb-3 border-b border-gray-200">
                      <strong>Task 16:</strong>
                      <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-700">
                        <li>
                          <strong>Objective</strong>: Navigate a &quot;dark
                          tunnel&quot; to retrieve a &quot;vital component&quot;
                          for a communication device.
                        </li>
                        <li>
                          <strong>Resources</strong>: 1 flashlight, 1 map of the
                          tunnel, 1 rope (10m).
                        </li>
                        <li>
                          <strong>Rules</strong>: Must avoid all obstacles
                          (marked areas).
                        </li>
                      </ul>
                    </li>
                    <li className="pb-3 border-b border-gray-200">
                      <strong>Task 17:</strong>
                      <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-700">
                        <li>
                          <strong>Objective</strong>: Construct a platform to
                          elevate a &quot;wounded soldier&quot; above a
                          &quot;flooded area.&quot;
                        </li>
                        <li>
                          <strong>Resources</strong>: 2 planks (3m each), 4
                          ropes (5m each), 1 tarp (3m x 3m).
                        </li>
                        <li>
                          <strong>Rules</strong>: Platform must be stable and
                          support the weight of the soldier.
                        </li>
                      </ul>
                    </li>
                    <li className="pb-3 border-b border-gray-200">
                      <strong>Task 18:</strong>
                      <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-700">
                        <li>
                          <strong>Objective</strong>: Transport a 70 kg
                          &quot;equipment box&quot; across a 6-meter-wide
                          &quot;ravine.&quot;
                        </li>
                        <li>
                          <strong>Resources</strong>: 2 logs (3m each), 4 ropes
                          (5m each), 1 pulley system.
                        </li>
                        <li>
                          <strong>Rules</strong>: Box must not touch the ground.
                        </li>
                      </ul>
                    </li>
                    <li className="pb-3 border-b border-gray-200">
                      <strong>Task 19:</strong>
                      <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-700">
                        <li>
                          <strong>Objective</strong>: Rescue a &quot;trapped
                          hiker&quot; from a steep cliff (simulated
                          environment).
                        </li>
                        <li>
                          <strong>Resources</strong>: 1 rope (15m), 1 harness, 1
                          carabiner, 1 medical kit.
                        </li>
                        <li>
                          <strong>Rules</strong>: Must secure the hiker before
                          descending.
                        </li>
                      </ul>
                    </li>
                    <li className="pb-3 border-b border-gray-200">
                      <strong>Task 20:</strong>
                      <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-700">
                        <li>
                          <strong>Objective</strong>: Build a makeshift barrier
                          to protect a &quot;vulnerable area&quot; from a
                          simulated attack.
                        </li>
                        <li>
                          <strong>Resources</strong>: 2 tarps (3m x 3m), 4 poles
                          (2m each), 1 rope (10m).
                        </li>
                        <li>
                          <strong>Rules</strong>: Barrier must withstand impacts
                          and cover the designated area.
                        </li>
                      </ul>
                    </li>
                    <li className="pb-3 border-b border-gray-200">
                      <strong>Task 21:</strong>
                      <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-700">
                        <li>
                          <strong>Objective</strong>: Cross a 5-meter-wide
                          &quot;acid pool&quot; to retrieve a &quot;critical
                          component&quot; for a communication device.
                        </li>
                        <li>
                          <strong>Resources</strong>: 2 planks (3m each), 4
                          ropes (5m each), 1 pulley system.
                        </li>
                        <li>
                          <strong>Rules</strong>: No part of the equipment can
                          touch the &quot;acid.&quot;
                        </li>
                      </ul>
                    </li>
                    <li className="pb-3 border-b border-gray-200">
                      <strong>Task 22:</strong>
                      <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-700">
                        <li>
                          <strong>Objective</strong>: Defuse a simulated bomb in
                          a crowded market (represented by a model) with limited
                          visibility.
                        </li>
                        <li>
                          <strong>Resources</strong>: 1 bomb disposal kit, 1
                          mirror, 1 set of wire cutters, 1 timer, 1 flashlight.
                        </li>
                        <li>
                          <strong>Rules</strong>: Must be completed within 10
                          minutes in low light conditions.
                        </li>
                      </ul>
                    </li>
                    <li className="pb-3 border-b border-gray-200">
                      <strong>Task 23:</strong>
                      <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-700">
                        <li>
                          <strong>Objective</strong>: Navigate a
                          &quot;minefield&quot; to retrieve a &quot;confidential
                          document&quot; from a locked building.
                        </li>
                        <li>
                          <strong>Resources</strong>: 1 metal detector, 1 map of
                          the minefield, 1 crowbar, 1 rope (10m).
                        </li>
                        <li>
                          <strong>Rules</strong>: Must avoid all
                          &quot;mines&quot; (marked areas) and enter the
                          building through a window.
                        </li>
                      </ul>
                    </li>
                    <li className="pb-3 border-b border-gray-200">
                      <strong>Task 24:</strong>
                      <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-700">
                        <li>
                          <strong>Objective</strong>: Construct a bridge over a
                          5-meter-wide &quot;river&quot; to transport a 60 kg
                          supply crate.
                        </li>
                        <li>
                          <strong>Resources</strong>: 2 logs (3m each), 4 ropes
                          (5m each), 1 pulley system.
                        </li>
                        <li>
                          <strong>Rules</strong>: Bridge must support the weight
                          of the crate and one cadet.
                        </li>
                      </ul>
                    </li>
                    <li className="pb-3 border-b border-gray-200">
                      <strong>Task 25:</strong>
                      <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-700">
                        <li>
                          <strong>Objective</strong>: Rescue a &quot;trapped
                          civilian&quot; from a simulated earthquake-damaged
                          building.
                        </li>
                        <li>
                          <strong>Resources</strong>: 1 crowbar, 1 rope (10m), 1
                          stretcher, 1 medical kit, 1 flashlight.
                        </li>
                        <li>
                          <strong>Rules</strong>: Must stabilize the structure
                          and navigate through dark, confined spaces.
                        </li>
                      </ul>
                    </li>
                  </>
                )}
              </ol>
            </div>

            <div className="flex justify-center my-8">
              <button
                onClick={() => setShowMoreTasks(!showMoreTasks)}
                className="bg-emerald-500 hover:bg-emerald-600 text-white font-semibold py-2 px-6 rounded-lg shadow transition duration-200"
              >
                {showMoreTasks ? "Show Less Tasks" : "View More Tasks"}
              </button>
            </div>

            <h2 className="text-xl font-semibold text-gray-900 mt-8 mb-4">
              Types of Command Tasks
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
              <div className="bg-gray-100 p-4 rounded-lg">
                <h3 className="font-medium mb-2">Obstacle Crossing</h3>
                <ul className="list-disc pl-5 space-y-1 text-gray-700">
                  <li>River/ditch crossing with limited resources</li>
                  <li>Wall scaling with team coordination</li>
                  <li>Navigating through restricted areas</li>
                  <li>Crossing simulated dangerous zones</li>
                </ul>
              </div>
              <div className="bg-gray-100 p-4 rounded-lg">
                <h3 className="font-medium mb-2">Construction Tasks</h3>
                <ul className="list-disc pl-5 space-y-1 text-gray-700">
                  <li>Building bridges with limited materials</li>
                  <li>Creating platforms or structures</li>
                  <li>Establishing pathways across obstacles</li>
                  <li>Constructing tools for specific purposes</li>
                </ul>
              </div>
              <div className="bg-gray-100 p-4 rounded-lg">
                <h3 className="font-medium mb-2">Transport Challenges</h3>
                <ul className="list-disc pl-5 space-y-1 text-gray-700">
                  <li>Moving heavy objects across obstacles</li>
                  <li>Transporting &quot;injured&quot; team members</li>
                  <li>Transferring materials without direct contact</li>
                  <li>Relocating resources through difficult terrain</li>
                </ul>
              </div>
              <div className="bg-gray-100 p-4 rounded-lg">
                <h3 className="font-medium mb-2">Problem-Solving Scenarios</h3>
                <ul className="list-disc pl-5 space-y-1 text-gray-700">
                  <li>Escaping from confined spaces</li>
                  <li>Retrieving objects from restricted areas</li>
                  <li>Overcoming complex multi-step challenges</li>
                  <li>Solving puzzles with physical components</li>
                </ul>
              </div>
            </div>

            <h2 className="text-xl font-semibold text-gray-900 mt-8 mb-4">
              Key Aspects of Command Task
            </h2>
            <div className="bg-emerald-50 p-6 rounded-lg mb-8">
              <ul className="list-disc pl-5 space-y-3 text-emerald-800">
                <li>
                  <strong>Positional Power:</strong> Unlike leaderless tasks,
                  you have 100% authority as the commander - your team must
                  follow your instructions without questioning.
                </li>
                <li>
                  <strong>Task Difficulty:</strong> The difficulty level of your
                  task may depend on your perceived capability - stronger
                  candidates often receive more challenging tasks.
                </li>
                <li>
                  <strong>Task Sequence:</strong> Commanders are selected in any
                  sequence, not necessarily in a predetermined order.
                </li>
                <li>
                  <strong>Task Structure:</strong> Similar to PGT obstacles but
                  may be circular or structured differently.
                </li>
                <li>
                  <strong>Time Constraint:</strong> You have exactly 15 minutes
                  to complete your task.
                </li>
                <li>
                  <strong>Stress Management:</strong> The GTO may deliberately
                  create stress by denying or enabling certain options during
                  your task.
                </li>
                <li>
                  <strong>Idea Creation:</strong> Your ability to generate ideas
                  is considered more important than perfect execution.
                </li>
              </ul>
            </div>

            <h2 className="text-xl font-semibold text-gray-900 mt-8 mb-4">
              Leadership Qualities Evaluated
            </h2>
            <div className="bg-emerald-50 p-6 rounded-lg mb-8">
              <ul className="list-disc pl-5 space-y-3 text-emerald-800">
                <li>
                  <strong>Clear communication:</strong> Giving precise
                  instructions and ensuring team members understand their roles.
                </li>
                <li>
                  <strong>Quick decision making:</strong> Assessing situations
                  rapidly and making effective choices under time pressure.
                </li>
                <li>
                  <strong>Team motivation:</strong> Encouraging team members and
                  maintaining positive morale throughout the task.
                </li>
                <li>
                  <strong>Resource allocation:</strong> Using available
                  materials and team members&apos; strengths optimally.
                </li>
                <li>
                  <strong>Problem resolution:</strong> Finding solutions to
                  unexpected challenges that arise during execution.
                </li>
                <li>
                  <strong>Adaptability:</strong> Adjusting plans when initial
                  approaches don&apos;t work as expected.
                </li>
                <li>
                  <strong>Initiative:</strong> Taking charge confidently and
                  leading by example.
                </li>
                <li>
                  <strong>Feedback reception:</strong> Being open to suggestions
                  from team members while maintaining authority.
                </li>
                <li>
                  <strong>Structure analysis:</strong> Ability to analyze
                  obstacles and their components effectively.
                </li>
                <li>
                  <strong>Stress handling:</strong> Maintaining composure and
                  effectiveness under pressure.
                </li>
                <li>
                  <strong>Idea generation:</strong> Creating innovative
                  solutions to overcome obstacles.
                </li>
              </ul>
            </div>

            <h2 className="text-xl font-semibold text-gray-900 mt-8 mb-4">
              Effective Command Approach
            </h2>
            <div className="bg-gray-100 p-6 rounded-lg mb-8">
              <h3 className="font-medium mb-3">
                A successful Command Task typically follows this structure:
              </h3>
              <ol className="list-decimal pl-5 space-y-2">
                <li>
                  <strong>Assessment:</strong> Quickly analyze the task,
                  available resources, and team members&apos; capabilities.
                </li>
                <li>
                  <strong>Planning:</strong> Develop a clear, step-by-step plan
                  that utilizes resources efficiently.
                </li>
                <li>
                  <strong>Briefing:</strong> Clearly explain the plan to your
                  team, ensuring everyone understands their roles.
                </li>
                <li>
                  <strong>Delegation:</strong> Assign specific responsibilities
                  based on team members&apos; strengths.
                </li>
                <li>
                  <strong>Execution:</strong> Oversee the implementation of the
                  plan, providing guidance as needed.
                </li>
                <li>
                  <strong>Adaptation:</strong> Adjust the approach if obstacles
                  arise or the initial plan proves ineffective.
                </li>
                <li>
                  <strong>Encouragement:</strong> Motivate team members
                  throughout, especially when facing challenges.
                </li>
                <li>
                  <strong>Completion:</strong> Ensure the task is fully
                  completed according to requirements.
                </li>
              </ol>
            </div>

            <h2 className="text-xl font-semibold text-gray-900 mt-8 mb-4">
              Common Mistakes to Avoid
            </h2>
            <div className="bg-red-50 p-6 rounded-lg mb-8">
              <ul className="list-disc pl-5 space-y-2 text-red-800">
                <li>Being indecisive or taking too long to formulate a plan</li>
                <li>
                  Failing to clearly communicate instructions to team members
                </li>
                <li>Micromanaging instead of delegating appropriately</li>
                <li>Ignoring input or suggestions from team members</li>
                <li>Becoming frustrated or negative when facing setbacks</li>
                <li>
                  Focusing on personal performance rather than team success
                </li>
                <li>Sticking rigidly to a failing plan instead of adapting</li>
                <li>
                  Neglecting safety considerations in pursuit of task completion
                </li>
                <li>Taking a passive role instead of actively leading</li>
              </ul>
            </div>

            <div className="bg-gray-100 p-6 rounded-lg mt-10">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                Preparation Strategy
              </h2>
              <p className="mb-4">
                Success in the Command Task requires a balance of leadership
                skills, problem-solving abilities, and effective team
                management.
              </p>
              <ol className="list-decimal pl-5 space-y-2">
                <li>
                  Practice giving clear, concise instructions in various
                  scenarios
                </li>
                <li>
                  Develop your ability to think quickly and make decisions under
                  pressure
                </li>
                <li>
                  Work on your problem-solving skills, especially with limited
                  resources
                </li>
                <li>
                  Learn to assess people&apos;s strengths and delegate
                  accordingly
                </li>
                <li>
                  Improve your ability to adapt plans when facing unexpected
                  challenges
                </li>
                <li>Build your confidence in taking charge of situations</li>
                <li>
                  Practice motivational techniques to encourage team members
                </li>
              </ol>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
