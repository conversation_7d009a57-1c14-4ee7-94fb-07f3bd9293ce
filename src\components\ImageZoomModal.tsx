"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import { XMarkIcon, MagnifyingGlassIcon } from "@heroicons/react/24/outline";

interface ImageZoomModalProps {
  isOpen: boolean;
  onClose: () => void;
  imageSrc: string;
  imageAlt: string;
  title?: string;
}

export default function ImageZoomModal({
  isOpen,
  onClose,
  imageSrc,
  imageAlt,
  title,
}: ImageZoomModalProps) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Prevent right-click context menu
  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();
    return false;
  };

  // Prevent propagation when clicking inside the modal content
  const handleContentClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  if (!isOpen || !isClient) return null;

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-white bg-opacity-98 transition-opacity duration-300"
      onClick={onClose}
      onContextMenu={handleContextMenu}
    >
      <div
        className="relative max-w-5xl max-h-[90vh] w-full mx-4 p-4"
        onClick={handleContentClick}
      >
        <button
          className="absolute top-2 right-2 z-10 bg-green-100 rounded-full p-2 shadow-md hover:bg-green-200 transition-colors"
          onClick={onClose}
        >
          <XMarkIcon className="h-6 w-6 text-green-700" />
        </button>

        {title && (
          <h2 className="text-gray-800 text-2xl font-bold text-center mb-4">
            {title}
          </h2>
        )}

        <div className="relative w-full h-[75vh] bg-white rounded-lg overflow-hidden border-2 border-green-200 shadow-lg">
          <div className="absolute inset-0 bg-white">
            <Image
              src={imageSrc}
              alt={imageAlt}
              fill
              className="object-contain p-2"
              priority
              sizes="(max-width: 768px) 100vw, 80vw"
              onContextMenu={handleContextMenu}
              draggable={false}
              style={{ pointerEvents: "none" }}
              onError={(e) => {
                // Fallback for missing images
                const target = e.target as HTMLImageElement;
                target.src = "/images/military-404.png";
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

// Wrapper component to add zoom functionality to any image
export function ZoomableImage({
  src,
  alt,
  title,
  className,
  width,
  height,
  ...props
}: {
  src: string;
  alt: string;
  title?: string;
  className?: string;
  width?: number;
  height?: number;
  [key: string]: any;
}) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const openModal = () => {
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  // Prevent right-click context menu
  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();
    return false;
  };

  return (
    <>
      <div
        className={`relative group cursor-zoom-in hover:shadow-md transition-shadow duration-200 ${
          className || ""
        }`}
        onClick={openModal}
        onContextMenu={handleContextMenu}
        style={{ cursor: "zoom-in" }}
      >
        <Image
          src={src}
          alt={alt}
          width={width || 800}
          height={height || 450}
          className="object-cover"
          draggable={false}
          style={{ pointerEvents: "none" }}
          {...props}
        />

        {isClient && (
          <div className="absolute inset-0 bg-transparent transition-all duration-200 flex items-center justify-center">
            <div className="opacity-0 group-hover:opacity-100 transition-all duration-200 bg-white bg-opacity-90 rounded-full p-2 shadow-sm">
              <MagnifyingGlassIcon className="h-6 w-6 text-green-600" />
            </div>
          </div>
        )}
      </div>

      <ImageZoomModal
        isOpen={isModalOpen}
        onClose={closeModal}
        imageSrc={src}
        imageAlt={alt}
        title={title}
      />
    </>
  );
}
