"use client";

import { useRouter } from "next/navigation";
import NavigationButton from "@/components/NavigationButton";
import SimpleButton from "@/components/SimpleButton";

import {
  ArrowLeftIcon,
  ClipboardDocumentListIcon,
} from "@heroicons/react/24/outline";

export default function PIQLanding() {
  const router = useRouter();

  const handleStartPIQ = () => {
    router.push("/tests/to/piq/practice/form");
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="mx-auto max-w-3xl px-6">
        <SimpleButton
          onClick={() => router.push("/tests/to")}
          className="mb-8 flex items-center text-gray-600 hover:text-gray-900"
        >
          <ArrowLeftIcon className="h-5 w-5 mr-2" />
          Back to TO Test
        </SimpleButton>

        <div className="bg-white rounded-2xl shadow-sm p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">
            Personal Information Questionnaire (PIQ)
          </h1>

          <div className="space-y-6 text-gray-700">
            <div>
              <h2 className="text-xl font-semibold mb-4">About PIQ:</h2>
              <div className="space-y-4">
                <p>
                  The Personal Information Questionnaire (PIQ) is a crucial
                  document that provides comprehensive information about your
                  background, education, and personal details. Please follow
                  these guidelines:
                </p>

                <div className="space-y-4">
                  <h3 className="font-semibold text-gray-900">
                    Important Points:
                  </h3>
                  <ul className="list-disc list-inside space-y-2">
                    <li>Fill all sections completely and accurately</li>
                    <li>
                      Use CAPITAL LETTERS for your name as per your certificates
                    </li>
                    <li>
                      Provide detailed information about your education and
                      achievements
                    </li>
                    <li>
                      Include all relevant extracurricular activities and
                      positions held
                    </li>
                    <li>Be honest and precise with dates and numbers</li>
                  </ul>
                </div>

                <div className="space-y-4">
                  <h3 className="font-semibold text-gray-900">
                    Form Sections:
                  </h3>
                  <ul className="list-disc list-inside space-y-2">
                    <li>Personal Details and Residence Information</li>
                    <li>Demographic Information</li>
                    <li>Parent/Guardian and Siblings Details</li>
                    <li>Educational Record</li>
                    <li>Physical Details and Occupation</li>
                    <li>NCC Training and Activities</li>
                    <li>Commission Details and Previous Interviews</li>
                  </ul>
                </div>

                <div className="mt-8 p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <h3 className="font-semibold text-blue-800 mb-2">
                    What to Expect:
                  </h3>
                  <ul className="list-disc list-inside space-y-2 text-blue-700">
                    <li>Step-by-step form with all required sections</li>
                    <li>Ability to save your progress and return later</li>
                    <li>Option to download your completed PIQ as a PDF</li>
                    <li>Guidance on proper formatting and requirements</li>
                  </ul>
                </div>
              </div>
            </div>

            <SimpleButton
              onClick={handleStartPIQ}
              className="group relative overflow-hidden rounded-xl border border-purple-200 bg-white p-6 shadow-sm transition-all hover:border-purple-500 hover:shadow-md w-full text-left mt-8"
            >
              <div className="flex items-start space-x-4 mb-4">
                <div className="rounded-lg bg-purple-50 p-3 text-purple-600 transition-colors group-hover:bg-purple-100">
                  <ClipboardDocumentListIcon className="h-6 w-6" />
                </div>
                <div className="text-left">
                  <h3 className="text-lg font-semibold text-gray-900">
                    Personal Information Questionnaire
                  </h3>
                  <p className="mt-1 text-sm text-gray-600">
                    Complete your PIQ form. Your responses will be saved locally
                    and can be downloaded as a PDF.
                  </p>
                </div>
              </div>

              <div className="p-3 bg-yellow-50 rounded-lg border border-yellow-200 mb-4">
                <p className="text-sm text-yellow-700">
                  Take your time to understand the format and requirements. You
                  can save your progress and return later.
                </p>
              </div>

              <div className="flex justify-between items-center">
                <p className="text-sm font-medium text-purple-600">
                  Multiple sections to complete
                </p>
                <span className="text-white bg-purple-600 px-4 py-2 rounded-md font-medium transition-all group-hover:bg-purple-700">
                  Start PIQ Form →
                </span>
              </div>

              <div className="absolute inset-0 bg-gradient-to-r from-purple-50/0 via-purple-50/0 to-purple-50/0 opacity-0 transition-opacity group-hover:opacity-10" />
            </SimpleButton>
          </div>
        </div>
      </div>
    </div>
  );
}
