// Snake Race Obstacles Data Structure
// This file contains structured data for Snake Race (Group Obstacle Race) in GTO test

export interface Obstacle {
  id: number;
  name: string;
  description: string;
  instructions: string;
  image: string; // Path to the image
  tips: string[];
}

export const snakeRaceObstacles: Obstacle[] = [
  {
    id: 1,
    name: "Single Ramp",
    description:
      "A ramp that the group must climb up and jump down while carrying the snake.",
    instructions:
      "The group is expected to climb up the ramp and jump down carrying the load which must be held by at least 4 candidates while crossing over.",
    image: "/images/gto/snake-race/1.png",
    tips: [
      "Maintain momentum while approaching the ramp",
      "Coordinate your movements with team members",
      "Ensure the snake is held securely by at least 4 members",
      "Land together to maintain formation",
    ],
  },
  {
    id: 2,
    name: "Figure of Eight",
    description:
      "A pattern where the group must navigate in a figure-8 formation while carrying the snake.",
    instructions:
      "Group is expected to make a figure of 8 through the obstacle. The snake is also expected to make a figure of 8.",
    image: "/images/gto/snake-race/2.png",
    tips: [
      "Maintain clear communication during turns",
      "Move in a coordinated manner to maintain the snake's shape",
      "Practice smooth transitions between direction changes",
      "Keep pace consistent throughout the pattern",
    ],
  },
  {
    id: 3,
    name: "Spider Web",
    description:
      "A web-like structure made of ropes that the group must navigate through.",
    instructions:
      "Group has to simply cross over using web made of ropes while carrying the snake.",
    image: "/images/gto/snake-race/3.png",
    tips: [
      "Move carefully to avoid getting tangled in the web",
      "Help teammates navigate through difficult sections",
      "Maintain grip on the snake throughout the obstacle",
      "Communicate clearly about web openings and pathways",
    ],
  },
  {
    id: 4,
    name: "Double Wall",
    description:
      "Two walls connected by a wooden log that the group must cross over.",
    instructions:
      "Two walls are connected by wooden log and group is expected to walk up the log to cross over while carrying the snake.",
    image: "/images/gto/snake-race/4.png",
    tips: [
      "Balance is crucial - move slowly and deliberately",
      "Support team members who are struggling with balance",
      "Keep the snake secure and balanced during crossing",
      "Maintain focus on the log, not on the ground below",
    ],
  },
  {
    id: 5,
    name: "Single Wall",
    description:
      "A higher wall that requires teamwork to climb over while carrying the snake.",
    instructions:
      "Wall is higher than the double wall. Heavier team members should be sent up by clasping hands by two candidates, and at least two must remain on the wall to pull up others.",
    image: "/images/gto/snake-race/5.png",
    tips: [
      "Form a human ladder to help teammates climb",
      "Stronger members should assist others in climbing",
      "Ensure the snake is passed carefully over the wall",
      "Maintain at least two people on top to help others",
    ],
  },
  {
    id: 6,
    name: "Giant Slide",
    description:
      "A large slide that the group must climb up using narrow steps and then slide down.",
    instructions:
      "It is like a slide and group is to go up using narrow steps carrying the load, then slide down together.",
    image: "/images/gto/snake-race/6.png",
    tips: [
      "Climb carefully using the narrow steps",
      "Maintain formation during the slide down",
      "Keep the snake secure throughout the obstacle",
      "Coordinate speed during descent to stay together",
    ],
  },
];

// Rules for Snake Race
export const snakeRaceRules = [
  "Once picked up, snake cannot touch the ground until finish",
  "Snake cannot be shortened or folded",
  "Snake must take the same course as the candidates",
  "While running, snake must be held by all group members and at least by 3 while negotiating obstacles",
  "On completing one obstacle, complete team must move to next and not individually",
  "Color rule applicable (don't touch red portions of obstacles)",
  "For rule violation, GTO can impose penalties",
];

// Tips for success
export const successTips = [
  "Be enthusiastic and keep cheering team members",
  "If fit, take initiative and help others",
  "Take responsibility for carrying the snake",
  "Think of the group and not individual - team wins",
  "If rules are violated, own up when asked and repeat obstacle if required",
  "Everyone gets tired - don't give up",
  "Maintain clear communication throughout the race",
  "Support struggling team members",
];

// Helper function to get an obstacle by ID
export const getObstacleById = (id: number): Obstacle | undefined => {
  return snakeRaceObstacles.find((obstacle) => obstacle.id === id);
};
