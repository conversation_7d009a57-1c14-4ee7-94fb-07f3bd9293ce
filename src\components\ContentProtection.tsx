"use client";

import { useEffect } from "react";
import { initContentProtection } from "@/utils/contentProtection";
import { addDevToolsWarning, isDevToolsOpen } from "@/utils/devToolsDetection";

export default function ContentProtection() {
  useEffect(() => {
    // Initialize content protection when component mounts
    initContentProtection();

    // Add developer tools detection and warning
    addDevToolsWarning();

    // Check periodically if developer tools are open
    const checkInterval = setInterval(() => {
      if (isDevToolsOpen()) {
        console.warn("Developer tools detected. This site is protected.");
      }
    }, 1000);

    return () => {
      // Cleanup
      clearInterval(checkInterval);
    };
  }, []);

  return null; // This component doesn't render anything
}
