"use client";

import { useRouter } from "next/navigation";
import SimpleButton from "@/components/SimpleButton";
import { useLoading } from "@/context/LoadingContext";
import { useAuth } from "@/context/GoogleAuthContext";
import {
  ArrowLeftIcon,
  DocumentArrowDownIcon,
} from "@heroicons/react/24/outline";
import { use } from "react";
import { useState, useEffect } from "react";
import SimplePasswordProtectedDownload from "@/components/SimplePasswordProtectedDownload";
import {
  getResponses,
  saveResponses,
  TestResponse,
} from "@/services/responseService";
import AIAnalysisSection from "@/components/AIAnalysisSection";
export default function SDTResults({
  params,
}: {
  params: Promise<{ set: string }>;
}) {
  const router = useRouter();
  const { startLoading } = useLoading();
  const { user } = useAuth();
  const { set } = use(params);
  const setName = decodeURIComponent(set);
  const [responses, setResponses] = useState<string[]>([]);
  const [questions, setQuestions] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);
  const [generationStatus, setGenerationStatus] = useState("");
  const [isSaving, setIsSaving] = useState(false);
  const [currentTimestamp, setCurrentTimestamp] = useState<number>(Date.now());
  const [selectedHistoricalResponse, setSelectedHistoricalResponse] =
    useState<TestResponse | null>(null);

  // Handle selecting a historical response
  const handleSelectHistoricalResponse = (response: TestResponse) => {
    setSelectedHistoricalResponse(response);

    if (response.responses) {
      setResponses(response.responses);

      if (response.questions) {
        setQuestions(response.questions);
      }

      // Update the current timestamp for AI analysis
      if (response.timestamp) {
        setCurrentTimestamp(response.timestamp);
        console.log(
          "Updated timestamp for historical response:",
          response.timestamp
        );
      }
    }
  };

  // Load responses and questions from Firestore or localStorage
  useEffect(() => {
    const fetchData = async () => {
      try {
        let responseData = null;
        let parsedResponses: string[] = [];
        let parsedQuestions: string[] = [];

        // Try to get responses from Firestore if user is logged in
        if (user) {
          try {
            responseData = await getResponses(user.uid, "sdt", setName);

            if (responseData && responseData.responses) {
              parsedResponses = responseData.responses;
              if (responseData.questions) {
                parsedQuestions = responseData.questions;
              }
              console.log("Loaded responses from Firestore:", parsedResponses);

              // Set the current timestamp to the response timestamp for AI analysis
              if (responseData.timestamp) {
                setCurrentTimestamp(responseData.timestamp);
                console.log(
                  "Using response timestamp for AI analysis:",
                  responseData.timestamp
                );
              }
            }
          } catch (error) {
            console.error("Error fetching from Firestore:", error);
            // Continue to try localStorage
          }
        }

        // If no data from Firestore, try localStorage
        if (!responseData) {
          // Try set-specific keys first
          let storedResponses = localStorage.getItem(
            `sdt_responses_${setName}`
          );
          let storedQuestions = localStorage.getItem(
            `sdt_questions_${setName}`
          );

          // Fall back to generic keys for backward compatibility
          if (!storedResponses) {
            storedResponses = localStorage.getItem("sdt_responses");
            storedQuestions = localStorage.getItem("sdt_questions");
            console.log("Using generic localStorage keys as fallback");
          } else {
            console.log("Using set-specific localStorage keys");
          }

          const storedSet = localStorage.getItem("sdt_current_set");

          if (!storedResponses || !storedQuestions || storedSet !== setName) {
            throw new Error(
              "No saved responses found or set mismatch. Please complete the test first."
            );
          }

          parsedResponses = JSON.parse(storedResponses);
          parsedQuestions = JSON.parse(storedQuestions);

          // If user is logged in and we got responses from localStorage but not Firestore,
          // save them to Firestore, but check if they were already saved recently
          if (user && parsedResponses.length > 0 && !responseData) {
            // Check if we have a recent save timestamp in localStorage
            const lastSaveTimestamp = localStorage.getItem(
              `sdt_${setName}_last_save_timestamp`
            );
            const currentTime = new Date().getTime();
            const fiveMinutesAgo = currentTime - 5 * 60 * 1000; // 5 minutes in milliseconds

            // Only save if we don't have a timestamp or it's older than 5 minutes
            if (
              !lastSaveTimestamp ||
              parseInt(lastSaveTimestamp) < fiveMinutesAgo
            ) {
              setIsSaving(true);
              try {
                const timestamp = await saveResponses(
                  user.uid,
                  "sdt",
                  setName,
                  parsedResponses,
                  parsedQuestions
                );
                console.log(
                  "Saved responses to Firestore from results page, timestamp:",
                  timestamp
                );
                // Update the last save timestamp in localStorage
                localStorage.setItem(
                  `sdt_${setName}_last_save_timestamp`,
                  timestamp.toString()
                );

                // Update the current timestamp for AI analysis
                setCurrentTimestamp(timestamp);
                console.log(
                  "Updated current timestamp for AI analysis:",
                  timestamp
                );
              } catch (error) {
                console.error("Error saving responses to Firestore:", error);
              } finally {
                setIsSaving(false);
              }
            } else {
              console.log(
                "Skipping save to Firestore - responses were saved recently"
              );
            }
          }
        }

        setResponses(parsedResponses);
        setQuestions(parsedQuestions);
        setLoading(false);
      } catch (err) {
        setError(
          err instanceof Error ? err.message : "An unknown error occurred"
        );
        setLoading(false);
      }
    };

    fetchData();
  }, [setName, user]);

  // Function to generate and download PDF
  const downloadPDF = async () => {
    try {
      setIsGeneratingPDF(true);
      setGenerationStatus("Preparing PDF...");

      // Import the PDF generator dynamically to reduce initial load time
      const { generateSDTPDF } = await import("@/utils/sdtPdfGenerator");

      // Generate PDF
      const blob = await generateSDTPDF(setName, responses, questions);

      // Create download link
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `SDT_${setName}_Results.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      setIsGeneratingPDF(false);
    } catch (error) {
      console.error("Error generating PDF:", error);
      setGenerationStatus("Error generating PDF. Please try again.");
      setIsGeneratingPDF(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="mx-auto max-w-3xl px-6 text-center">
          <p className="text-gray-600">Loading results...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="mx-auto max-w-3xl px-6">
          <div className="bg-white rounded-2xl shadow-sm p-8 text-center">
            <h2 className="text-2xl font-bold text-red-600 mb-4">Error</h2>
            <p className="text-gray-600 mb-8">{error}</p>
            <SimpleButton
              onClick={() => router.push("/tests/to/sdt/practice")}
              className="inline-flex justify-center rounded-md bg-amber-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-amber-700"
            >
              Return to Practice Sets
            </SimpleButton>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="mx-auto max-w-4xl px-6">
        <SimpleButton
          onClick={() => router.push("/tests/to")}
          className="mb-8 flex items-center text-gray-600 hover:text-gray-900"
        >
          <ArrowLeftIcon className="h-5 w-5 mr-2" />
          Back to TO Test
        </SimpleButton>

        <div className="space-y-8">
          {/* Results Header */}
          <div className="bg-white rounded-2xl shadow-sm p-4 sm:p-8">
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-4 sm:mb-6 text-center sm:text-left">
              Self Description Test - Results
            </h1>
            <p className="text-gray-600 text-center sm:text-left">
              You have completed the Self Description Test. Your responses have
              been recorded.
            </p>
          </div>

          {/* PDF Download Section */}
          <div className="bg-gradient-to-r from-amber-500 to-amber-600 rounded-2xl shadow-lg p-4 sm:p-8 text-white">
            <div className="flex flex-col sm:flex-row items-center sm:items-start">
              <div className="flex-1">
                <h2 className="text-xl sm:text-2xl font-bold mb-4 text-center sm:text-left">
                  Download Your Responses
                </h2>
                <p className="mb-4 text-center sm:text-left">
                  Get a PDF copy of your responses for offline review:
                </p>
                <ul className="space-y-2 mb-6 text-sm sm:text-base">
                  <li className="flex items-center">
                    <span className="mr-2 text-yellow-300">✓</span> Your
                    complete responses to all questions
                  </li>
                  <li className="flex items-center">
                    <span className="mr-2 text-yellow-300">✓</span> Convenient
                    PDF format for offline review
                  </li>
                  <li className="flex items-center">
                    <span className="mr-2 text-yellow-300">✓</span> Organized
                    presentation of questions and answers
                  </li>
                  <li className="flex items-center">
                    <span className="mr-2 text-yellow-300">✓</span> Printable
                    format for your records
                  </li>
                </ul>
                <div className="flex justify-center sm:justify-start">
                  <SimplePasswordProtectedDownload
                    onDownload={downloadPDF}
                    isLoading={isGeneratingPDF}
                    statusMessage={generationStatus}
                    correctPassword="Balaram1020"
                    buttonClassName="inline-flex items-center justify-center rounded-md bg-white px-4 sm:px-6 py-2 sm:py-3 text-sm font-semibold text-amber-600 shadow-sm hover:bg-gray-100 w-full sm:w-auto"
                  >
                    <DocumentArrowDownIcon className="h-5 w-5 mr-2" />
                    Download Responses PDF
                  </SimplePasswordProtectedDownload>
                </div>
              </div>
              <div className="hidden sm:block md:w-1/4 mt-4 sm:mt-0">
                <div className="rounded-full bg-white/20 p-6 backdrop-blur-sm">
                  <div className="text-center">
                    <div className="text-4xl font-bold mb-1">100%</div>
                    <div className="text-sm">Free</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Summary Section */}
          <div className="bg-white rounded-2xl shadow-sm p-4 sm:p-8">
            <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-4 sm:mb-6 text-center sm:text-left">
              Test Summary
            </h2>
            <p className="text-gray-600 mb-6 text-center sm:text-left">
              You have completed the Self Description Test. Your responses have
              been recorded.
            </p>

            {selectedHistoricalResponse && (
              <div className="bg-amber-50 p-4 rounded-lg mb-4">
                <p className="text-amber-800 font-medium text-center sm:text-left">
                  Viewing historical attempt from{" "}
                  {selectedHistoricalResponse.timestamp
                    ? new Date(
                        selectedHistoricalResponse.timestamp
                      ).toLocaleString()
                    : "an earlier date"}
                </p>
                <div className="flex justify-center sm:justify-start">
                  <button
                    onClick={() => window.location.reload()}
                    className="mt-2 text-amber-600 text-sm font-medium hover:text-amber-800"
                  >
                    Return to latest attempt
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* AI Analysis Section */}
          <AIAnalysisSection
            responses={responses.filter((r) => r && r.trim() !== "")}
            testType="SDT"
            prompts={questions}
            setName={set}
            responseTimestamp={currentTimestamp}
          />

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row justify-between space-y-4 sm:space-y-0">
            <SimpleButton
              href={`/tests/to/sdt/practice/${encodeURIComponent(
                setName
              )}/test`}
              className="inline-flex justify-center rounded-md bg-amber-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-amber-700 w-full sm:w-auto"
            >
              Retry Test
            </SimpleButton>
            <SimpleButton
              href="/tests/to"
              className="inline-flex justify-center rounded-md bg-white px-4 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 w-full sm:w-auto"
            >
              Go Back
            </SimpleButton>
          </div>
        </div>
      </div>
    </div>
  );
}
