'use client';

import { useEffect } from 'react';
import Link from 'next/link';
import { ArrowLeftIcon } from "@heroicons/react/24/outline";

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Application error:', error);
  }, [error]);

  return (
    <div className="min-h-screen bg-gray-100 py-12 flex flex-col items-center justify-center">
      <div className="mx-auto max-w-3xl px-6 text-center">
        <div className="bg-white rounded-2xl shadow-md p-8 border-t-4 border-red-600">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Something went wrong
          </h1>

          <div className="bg-gray-100 p-4 rounded-lg border-l-4 border-amber-500 mb-6">
            <p className="text-xl font-medium text-gray-800 italic">
              "Mission temporarily compromised. Standing by for new orders."
            </p>
          </div>

          <p className="text-lg text-gray-700 mb-8">
            We apologize for the inconvenience. Our technical team has been notified.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={reset}
              className="inline-flex items-center justify-center rounded-md bg-indigo-700 px-6 py-3 text-base font-semibold text-white shadow-sm hover:bg-indigo-600 transition-colors"
            >
              Try again
            </button>

            <Link
              href="/"
              className="inline-flex items-center justify-center rounded-md bg-gray-200 px-6 py-3 text-base font-semibold text-gray-800 shadow-sm hover:bg-gray-300 transition-colors"
            >
              <ArrowLeftIcon className="h-5 w-5 mr-2" />
              Return to Home
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
