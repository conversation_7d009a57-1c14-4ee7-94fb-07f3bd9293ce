"use client";

import { useRouter } from "next/navigation";
import SimpleButton from "@/components/SimpleButton";
import { ArrowLeftIcon, BookOpenIcon } from "@heroicons/react/24/outline";
import { use } from "react";
import { sdtQuestionSets } from "@/data/sdt-questions";

export default function SDTInstructions({
  params,
}: {
  params: Promise<{ set: string }>;
}) {
  const router = useRouter();
  const { set } = use(params);
  const setName = decodeURIComponent(set);

  // Using SimpleButton with href instead of onClick for better performance
  const testUrl = `/tests/to/sdt/practice/${encodeURIComponent(setName)}/test`;

  // Get the question count
  const questionCount = sdtQuestionSets[setName]?.questions.length || 5;

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="mx-auto max-w-3xl px-6">
        <SimpleButton
          onClick={() => router.push("/tests/to")}
          className="mb-8 flex items-center text-gray-600 hover:text-gray-900"
        >
          <ArrowLeftIcon className="h-5 w-5 mr-2" />
          Back to TO Tests
        </SimpleButton>

        <div className="bg-white rounded-2xl shadow-sm p-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
            <h1 className="text-3xl font-bold text-gray-900">
              Self Description Test - Instructions
            </h1>
            <SimpleButton
              onClick={() => router.push("/tests/to/sdt/sample-answers")}
              className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-amber-600 hover:bg-amber-700 transition-colors"
            >
              <BookOpenIcon className="h-5 w-5 mr-2" />
              View Sample Answers
            </SimpleButton>
          </div>

          <div className="space-y-6">
            <div className="bg-amber-50 p-6 rounded-lg">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                About This Test
              </h2>
              <p className="text-gray-800 mb-4">
                The Self Description Test (SDT) evaluates how you perceive
                yourself and how others perceive you.
              </p>
              <ul className="list-disc list-inside space-y-2 text-gray-800">
                <li>Total questions: {questionCount}</li>
                <li>Time limit: 15 minutes</li>
                <li>All questions will be displayed at once</li>
                <li>Answer honestly and thoroughly</li>
                <li>There are no right or wrong answers</li>
                <li>
                  Your responses reveal your self-awareness and interpersonal
                  understanding
                </li>
              </ul>
            </div>

            <div className="bg-gray-100 p-6 rounded-lg">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                Tips for Success
              </h2>
              <ul className="list-disc list-inside space-y-2 text-gray-800">
                <li>Be honest and authentic in your responses</li>
                <li>Provide specific examples when possible</li>
                <li>Demonstrate self-awareness and maturity</li>
                <li>
                  Show your ability to reflect on your strengths and weaknesses
                </li>
                <li>Articulate your thoughts clearly and concisely</li>
                <li>Manage your time effectively across all 5 questions</li>
              </ul>
            </div>

            <div className="bg-amber-100 p-6 rounded-lg">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                Questions You'll Answer
              </h2>
              <ol className="list-decimal list-inside space-y-2 text-gray-800">
                <li>What do your parents think of you?</li>
                <li>What do your teachers/employers think of you?</li>
                <li>What do your friends and colleagues think of you?</li>
                <li>What do you think about yourself?</li>
                <li>
                  What kind of person you would like to become or what
                  improvements you want to bring in yourself?
                </li>
              </ol>
            </div>

            <div className="text-center mt-8">
              <SimpleButton
                href={testUrl}
                className="inline-flex justify-center rounded-md bg-amber-600 px-6 py-3 text-base font-semibold text-white shadow-sm hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2"
              >
                Start Practice
              </SimpleButton>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
