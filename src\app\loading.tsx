"use client";

import { useEffect, useState, useMemo } from "react";

export default function Loading() {
  const [loadingText, setLoadingText] = useState("Preparing mission...");

  // Military-themed loading messages
  const loadingMessages = useMemo(
    () => [
      "Preparing mission...",
      "Gathering intelligence...",
      "Securing perimeter...",
      "Establishing command post...",
      "Analyzing terrain...",
      "Checking equipment...",
      "Briefing in progress...",
      "Awaiting orders...",
      "Deploying assets...",
      "Coordinating operations...",
    ],
    []
  );

  useEffect(() => {
    // Change the loading message every 2 seconds
    const interval = setInterval(() => {
      const randomIndex = Math.floor(Math.random() * loadingMessages.length);
      setLoadingText(loadingMessages[randomIndex]);
    }, 2000);

    return () => clearInterval(interval);
  }, [loadingMessages]);

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center">
      <div className="max-w-md w-full mx-auto px-4">
        <div className="bg-white rounded-2xl shadow-md p-8 border-t-4 border-indigo-700">
          <div className="flex justify-center mb-6">
            {/* Military-style loading animation */}
            <div className="relative w-24 h-24">
              <div className="absolute inset-0 border-4 border-gray-200 rounded-full"></div>
              <div className="absolute inset-0 border-4 border-t-indigo-700 border-r-transparent border-b-transparent border-l-transparent rounded-full animate-spin"></div>
              <div
                className="absolute inset-2 border-4 border-t-transparent border-r-amber-500 border-b-transparent border-l-transparent rounded-full animate-spin"
                style={{
                  animationDirection: "reverse",
                  animationDuration: "1.5s",
                }}
              ></div>
              <div
                className="absolute inset-4 border-4 border-t-transparent border-r-transparent border-b-green-600 border-l-transparent rounded-full animate-spin"
                style={{ animationDuration: "3s" }}
              ></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-3 h-3 bg-indigo-700 rounded-full"></div>
              </div>
            </div>
          </div>

          <h2 className="text-xl font-bold text-center text-gray-900 mb-2">
            {loadingText}
          </h2>

          <div className="w-full bg-gray-200 rounded-full h-2.5 mb-6">
            <div className="bg-indigo-700 h-2.5 rounded-full animate-pulse"></div>
          </div>

          <p className="text-sm text-center text-gray-600">
            Please stand by while we prepare your content.
            <br />
            This operation requires military-grade patience.
          </p>
        </div>

        <div className="mt-6 flex items-center justify-center">
          <div className="h-px w-12 bg-gray-300"></div>
          <p className="mx-4 text-xs text-gray-500">
            MILITARY SELECTION TEST PREP
          </p>
          <div className="h-px w-12 bg-gray-300"></div>
        </div>
      </div>
    </div>
  );
}
