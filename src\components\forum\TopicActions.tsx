"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/context/GoogleAuthContext";
import { deleteTopic } from "@/services/forumService";
import { PencilIcon, TrashIcon } from "@heroicons/react/24/outline";
import ReportButton from "./ReportButton";

interface TopicActionsProps {
  topicId: string;
  authorId: string;
  authorName: string;
}

export default function TopicActions({
  topicId,
  authorId,
  authorName,
}: TopicActionsProps) {
  const { user } = useAuth();
  const router = useRouter();
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showConfirmDelete, setShowConfirmDelete] = useState(false);

  // Check if the current user is the author
  const isAuthor = user?.uid === authorId;

  // Handle edit button click
  const handleEdit = () => {
    router.push(`/forum/edit/${topicId}`);
  };

  // Handle delete button click
  const handleDelete = async () => {
    try {
      setIsDeleting(true);
      setError(null);

      if (!user) {
        throw new Error("You must be logged in to delete a topic.");
      }

      // Delete the topic
      await deleteTopic(topicId, user.uid);

      // Redirect to the forum page
      router.push("/forum");
    } catch (error) {
      console.error("Error deleting topic:", error);
      setError(
        error instanceof Error
          ? error.message
          : "An error occurred while deleting the topic. Please try again."
      );
      setShowConfirmDelete(false);
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <div className="mt-4 flex justify-end">
      {isAuthor ? (
        // Author actions: Edit and Delete
        <div className="flex space-x-4">
          {error && (
            <div className="text-sm text-red-600 mr-4">{error}</div>
          )}
          
          <button
            type="button"
            onClick={handleEdit}
            className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800"
          >
            <PencilIcon className="h-4 w-4 mr-1" />
            Edit
          </button>
          
          {showConfirmDelete ? (
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">Are you sure?</span>
              <button
                type="button"
                onClick={handleDelete}
                disabled={isDeleting}
                className="inline-flex items-center text-sm text-white bg-red-600 hover:bg-red-700 px-2 py-1 rounded"
              >
                {isDeleting ? "Deleting..." : "Yes, Delete"}
              </button>
              <button
                type="button"
                onClick={() => setShowConfirmDelete(false)}
                className="inline-flex items-center text-sm text-gray-600 hover:text-gray-800 px-2 py-1 rounded border border-gray-300"
              >
                Cancel
              </button>
            </div>
          ) : (
            <button
              type="button"
              onClick={() => setShowConfirmDelete(true)}
              className="inline-flex items-center text-sm text-red-600 hover:text-red-800"
            >
              <TrashIcon className="h-4 w-4 mr-1" />
              Delete
            </button>
          )}
        </div>
      ) : (
        // Non-author action: Report
        <ReportButton
          contentId={topicId}
          contentType="topic"
          authorName={authorName}
        />
      )}
    </div>
  );
}
