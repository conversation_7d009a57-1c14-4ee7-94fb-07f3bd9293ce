"use client";

import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import {
  ArrowLeftIcon,
  FlagIcon,
  ClockIcon,
  CheckCircleIcon,
  ArrowRightIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import NavigationButton from "@/components/NavigationButton";
import {
  snakeRaceObstacles,
  snakeRaceRules,
} from "@/data/snake-race-obstacles";

export default function SnakeRaceInteractivePage() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState<
    "intro" | "course" | "results"
  >("intro");
  const [completedObstacles, setCompletedObstacles] = useState<number[]>([]);
  const [timeRemaining, setTimeRemaining] = useState(240); // 4 minutes in seconds
  const [isTimerRunning, setIsTimerRunning] = useState(false);
  const [teamCoordination, setTeamCoordination] = useState(100); // Team coordination score (0-100)
  const [feedback, setFeedback] = useState("");
  const [currentObstacle, setCurrentObstacle] = useState<number | null>(null);
  const [showObstacleDetails, setShowObstacleDetails] = useState(false);

  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // Start timer when course begins
  useEffect(() => {
    if (isTimerRunning && timeRemaining > 0) {
      timerRef.current = setInterval(() => {
        setTimeRemaining((prev) => {
          if (prev <= 1) {
            clearInterval(timerRef.current as NodeJS.Timeout);
            setIsTimerRunning(false);
            setCurrentStep("results");
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [isTimerRunning, timeRemaining]);

  // Format time as MM:SS
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  };

  // Handle obstacle completion
  const handleObstacleComplete = (id: number) => {
    if (completedObstacles.includes(id)) {
      return; // Already completed
    }

    // Reduce team coordination slightly for each obstacle (simulating fatigue)
    setTeamCoordination((prev) =>
      Math.max(prev - Math.floor(Math.random() * 10 + 5), 0)
    );
    setCompletedObstacles([...completedObstacles, id]);
    setCurrentObstacle(null);
  };

  // Calculate final results and feedback
  const calculateResults = () => {
    const obstaclePercentage =
      (completedObstacles.length / snakeRaceObstacles.length) * 100;
    const timePercentage = (timeRemaining / 240) * 100;
    const overallScore =
      obstaclePercentage * 0.5 + teamCoordination * 0.3 + timePercentage * 0.2;

    let feedbackText = "";

    if (overallScore >= 80) {
      feedbackText =
        "Excellent performance! Your team demonstrated outstanding coordination, speed, and obstacle navigation skills.";
    } else if (overallScore >= 60) {
      feedbackText =
        "Good job! Your team showed solid teamwork and decent obstacle navigation, though there's room for improvement.";
    } else if (overallScore >= 40) {
      feedbackText =
        "Fair attempt. Your team completed several obstacles but could improve coordination and efficiency.";
    } else {
      feedbackText =
        "Your team needs more practice. Focus on improving coordination, communication, and obstacle navigation strategies.";
    }

    setFeedback(feedbackText);
  };

  // Start the course
  const startCourse = () => {
    setCurrentStep("course");
    setIsTimerRunning(true);
  };

  // End the course
  const endCourse = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
    setIsTimerRunning(false);
    calculateResults();
    setCurrentStep("results");
  };

  // Select an obstacle to attempt
  const selectObstacle = (id: number) => {
    setCurrentObstacle(id);
    setShowObstacleDetails(true);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="mx-auto max-w-4xl px-6">
        <NavigationButton
          href="/tests/gto/snake-race"
          className="mb-8 inline-flex items-center text-gray-600 hover:text-gray-900"
        >
          <ArrowLeftIcon className="h-5 w-5 mr-2" />
          Back to Snake Race
        </NavigationButton>

        {currentStep === "intro" && (
          <div className="bg-white rounded-2xl shadow-sm p-8 border-t-4 border-red-500">
            <div className="flex items-center space-x-3 mb-6">
              <div className="p-2 rounded-full bg-red-100">
                <FlagIcon className="h-8 w-8 text-red-600" />
              </div>
              <h1 className="text-3xl font-bold text-gray-900">
                Snake Race Simulation
              </h1>
            </div>

            <div className="prose prose-lg max-w-none">
              <p className="text-lg text-gray-700 mb-6">
                This is a simulation of the Snake Race course. You'll lead your
                team through 6 obstacles while maintaining coordination and
                speed. Your goal is to complete all obstacles before time runs
                out.
              </p>

              <div className="bg-red-50 p-6 rounded-lg mb-8">
                <h3 className="text-lg font-medium text-red-900 mb-4">
                  Instructions:
                </h3>
                <ol className="list-decimal pl-5 space-y-2 text-red-800">
                  <li>
                    You have 4 minutes (240 seconds) to complete all obstacles
                  </li>
                  <li>
                    Your team must navigate through each obstacle while carrying
                    the snake
                  </li>
                  <li>Team coordination is crucial for success</li>
                  <li>The snake must not touch the ground at any point</li>
                  <li>All team members must stay with the snake throughout</li>
                  <li>Complete all obstacles as quickly as possible</li>
                </ol>
              </div>

              <div className="bg-gray-100 p-6 rounded-lg mb-8">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Snake Race Rules:
                </h3>
                <ul className="list-disc pl-5 space-y-2 text-gray-700">
                  {snakeRaceRules.map((rule, index) => (
                    <li key={index}>{rule}</li>
                  ))}
                </ul>
              </div>

              <div className="flex justify-center mt-8">
                <button
                  onClick={startCourse}
                  className="bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-6 rounded-lg shadow-md transition duration-200"
                >
                  Start Race
                </button>
              </div>
            </div>
          </div>
        )}

        {currentStep === "course" && (
          <div className="bg-white rounded-2xl shadow-sm p-8 border-t-4 border-red-500">
            <div className="flex justify-between items-center mb-6">
              <h1 className="text-2xl font-bold text-gray-900">
                Snake Race Course
              </h1>
              <div className="flex items-center space-x-4">
                <div className="bg-red-100 text-red-800 px-4 py-2 rounded-lg flex items-center">
                  <ClockIcon className="h-5 w-5 mr-2" />
                  <span className="font-mono font-bold">
                    {formatTime(timeRemaining)}
                  </span>
                </div>
                <div className="bg-blue-100 text-blue-800 px-4 py-2 rounded-lg">
                  <span className="font-bold">
                    Coordination: {teamCoordination}%
                  </span>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
              {snakeRaceObstacles.map((obstacle) => (
                <button
                  key={obstacle.id}
                  onClick={() => selectObstacle(obstacle.id)}
                  className={`rounded-lg border-2 transition-all overflow-hidden shadow-sm hover:shadow-md ${
                    completedObstacles.includes(obstacle.id)
                      ? "border-green-500"
                      : "border-gray-200 hover:border-red-300"
                  }`}
                  disabled={completedObstacles.includes(obstacle.id)}
                >
                  <div className="relative h-48 w-full bg-gray-100 rounded-t-lg">
                    <Image
                      src={obstacle.image}
                      alt={`${obstacle.name} obstacle`}
                      fill
                      className="object-contain"
                      unoptimized
                    />
                    {completedObstacles.includes(obstacle.id) && (
                      <div className="absolute inset-0 bg-green-500 bg-opacity-20 flex items-center justify-center">
                        <CheckCircleIcon className="h-8 w-8 text-white" />
                      </div>
                    )}
                  </div>
                  <div className="p-3 text-center">
                    <div className="text-sm font-medium mb-2">
                      {obstacle.id}. {obstacle.name}
                    </div>
                    <div
                      className={`text-xs font-medium px-2 py-1 rounded ${
                        completedObstacles.includes(obstacle.id)
                          ? "bg-green-100 text-green-800"
                          : "bg-red-100 text-red-800"
                      }`}
                    >
                      {completedObstacles.includes(obstacle.id)
                        ? "Completed"
                        : "Not Completed"}
                    </div>
                  </div>
                </button>
              ))}
            </div>

            {showObstacleDetails && currentObstacle && (
              <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
                <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                  <div className="p-6">
                    {snakeRaceObstacles
                      .filter((o) => o.id === currentObstacle)
                      .map((obstacle) => (
                        <div key={obstacle.id}>
                          <div className="flex justify-between items-start mb-4">
                            <h2 className="text-2xl font-bold text-gray-900">
                              {obstacle.id}. {obstacle.name}
                            </h2>
                            <button
                              onClick={() => setShowObstacleDetails(false)}
                              className="text-gray-500 hover:text-gray-700"
                            >
                              <XMarkIcon className="h-6 w-6" />
                            </button>
                          </div>
                          <div className="relative h-64 w-full mb-4 bg-gray-100 rounded-lg overflow-hidden flex items-center justify-center">
                            <Image
                              src={obstacle.image}
                              alt={`${obstacle.name} obstacle`}
                              fill
                              className="object-contain"
                              unoptimized
                            />
                          </div>
                          <div className="mb-4">
                            <h3 className="font-semibold text-gray-900 mb-2">
                              Description:
                            </h3>
                            <p className="text-gray-700">
                              {obstacle.description}
                            </p>
                          </div>
                          <div className="mb-4">
                            <h3 className="font-semibold text-gray-900 mb-2">
                              Instructions:
                            </h3>
                            <p className="text-gray-700">
                              {obstacle.instructions}
                            </p>
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900 mb-2">
                              Tips:
                            </h3>
                            <ul className="list-disc pl-5 space-y-1 text-gray-700">
                              {obstacle.tips.map((tip, index) => (
                                <li key={index}>{tip}</li>
                              ))}
                            </ul>
                          </div>
                          <div className="mt-6 flex justify-center">
                            <button
                              onClick={() => {
                                handleObstacleComplete(obstacle.id);
                                setShowObstacleDetails(false);
                              }}
                              className="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-200"
                            >
                              Complete Obstacle
                            </button>
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              </div>
            )}

            <div className="flex justify-center">
              <button
                onClick={endCourse}
                className="bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-6 rounded-lg shadow-md transition duration-200"
              >
                End Race
              </button>
            </div>
          </div>
        )}

        {currentStep === "results" && (
          <div className="bg-white rounded-2xl shadow-sm p-8 border-t-4 border-red-500">
            <div className="flex items-center space-x-3 mb-6">
              <div className="p-2 rounded-full bg-red-100">
                <CheckCircleIcon className="h-8 w-8 text-red-600" />
              </div>
              <h1 className="text-3xl font-bold text-gray-900">Race Results</h1>
            </div>

            <div className="prose prose-lg max-w-none">
              <div className="bg-gray-100 p-6 rounded-lg mb-8">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  Team Performance
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <p className="text-gray-700 mb-2">
                      <strong>Obstacles Completed:</strong>{" "}
                      {completedObstacles.length} of {snakeRaceObstacles.length}
                    </p>
                    <p className="text-gray-700 mb-2">
                      <strong>Time Used:</strong>{" "}
                      {formatTime(240 - timeRemaining)} of 04:00
                    </p>
                    <p className="text-gray-700 mb-2">
                      <strong>Final Team Coordination:</strong>{" "}
                      {teamCoordination}%
                    </p>
                  </div>
                  <div>
                    <h4 className="font-medium mb-2">Obstacles Completed:</h4>
                    <div className="flex flex-wrap gap-2">
                      {completedObstacles.length > 0 ? (
                        completedObstacles.map((id, index) => (
                          <div
                            key={index}
                            className="bg-green-200 text-green-800 px-3 py-1 rounded-full font-medium"
                          >
                            {id}
                          </div>
                        ))
                      ) : (
                        <p className="text-gray-500">No obstacles completed</p>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-red-50 p-6 rounded-lg mb-8">
                <h3 className="text-xl font-semibold text-red-900 mb-4">
                  Feedback
                </h3>
                <p className="text-red-800">{feedback}</p>
              </div>

              <div className="flex justify-center space-x-4 mt-8">
                <button
                  onClick={() => {
                    setCurrentStep("intro");
                    setCompletedObstacles([]);
                    setTeamCoordination(100);
                    setTimeRemaining(240);
                    setCurrentObstacle(null);
                  }}
                  className="bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-6 rounded-lg shadow-md transition duration-200"
                >
                  Try Again
                </button>
                <button
                  onClick={() => router.push("/tests/gto/snake-race")}
                  className="bg-gray-600 hover:bg-gray-700 text-white font-bold py-3 px-6 rounded-lg shadow-md transition duration-200"
                >
                  Return to Guide
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
