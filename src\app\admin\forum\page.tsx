"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useAuth } from "@/context/GoogleAuthContext";
import { useRoleCheck } from "@/utils/authUtils";
import {
  collection,
  query,
  where,
  orderBy,
  limit,
  getDocs,
  doc,
  updateDoc,
  deleteDoc,
  Timestamp,
} from "firebase/firestore";
import { db } from "@/lib/firebase";
import { ForumTopic, ContentReport, ReportStatus } from "@/types/forum";
import {
  ArrowLeftIcon,
  FlagIcon,
  TrashIcon,
  LockClosedIcon,
  EyeSlashIcon,
  CheckCircleIcon,
  ExclamationCircleIcon,
  ChatBubbleLeftRightIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import ConfirmationModal from "@/components/ConfirmationModal";

export default function AdminForumPage() {
  const { user, loading } = useAuth();
  const { isAdmin } = useRoleCheck();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<"topics" | "reports">("reports");
  const [topics, setTopics] = useState<ForumTopic[]>([]);
  const [reports, setReports] = useState<ContentReport[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [confirmAction, setConfirmAction] = useState<{
    isOpen: boolean;
    title: string;
    message: string;
    action: () => Promise<void>;
  }>({
    isOpen: false,
    title: "",
    message: "",
    action: async () => {},
  });

  // Load data when the component mounts
  useEffect(() => {
    if (!loading) {
      if (!user || !isAdmin) {
        // Redirect to login if not authenticated or not admin
        router.push("/login");
      } else {
        loadData();
      }
    }
  }, [user, loading, isAdmin, activeTab]);

  // Load data based on active tab
  const loadData = async () => {
    try {
      setIsLoading(true);
      if (activeTab === "topics") {
        await loadTopics();
      } else {
        await loadReports();
      }
    } catch (error) {
      console.error(`Error loading ${activeTab}:`, error);
    } finally {
      setIsLoading(false);
    }
  };

  // Load topics from Firestore
  const loadTopics = async () => {
    const topicsQuery = query(
      collection(db, "forumTopics"),
      orderBy("createdAt", "desc"),
      limit(50)
    );

    const topicsSnapshot = await getDocs(topicsQuery);
    const topicsList: ForumTopic[] = [];

    topicsSnapshot.forEach((doc) => {
      const data = doc.data();
      topicsList.push({
        id: doc.id,
        ...data,
        createdAt: data.createdAt?.toDate(),
        updatedAt: data.updatedAt?.toDate(),
        lastReplyAt: data.lastReplyAt?.toDate(),
      } as ForumTopic);
    });

    setTopics(topicsList);
  };

  // Load reports from Firestore
  const loadReports = async () => {
    const reportsQuery = query(
      collection(db, "contentReports"),
      orderBy("createdAt", "desc"),
      limit(50)
    );

    const reportsSnapshot = await getDocs(reportsQuery);
    const reportsList: ContentReport[] = [];

    reportsSnapshot.forEach((doc) => {
      const data = doc.data();
      reportsList.push({
        id: doc.id,
        ...data,
        createdAt: data.createdAt?.toDate(),
        reviewedAt: data.reviewedAt?.toDate(),
      } as ContentReport);
    });

    setReports(reportsList);
  };

  // Handle topic actions
  const handleTopicAction = async (
    topicId: string,
    action: "lock" | "hide" | "delete"
  ) => {
    try {
      const topicRef = doc(db, "forumTopics", topicId);

      if (action === "delete") {
        setConfirmAction({
          isOpen: true,
          title: "Delete Topic",
          message:
            "Are you sure you want to delete this topic? This action cannot be undone.",
          action: async () => {
            await deleteDoc(topicRef);
            await loadTopics();
          },
        });
      } else if (action === "lock") {
        setConfirmAction({
          isOpen: true,
          title: "Lock Topic",
          message:
            "Are you sure you want to lock this topic? Users will no longer be able to reply.",
          action: async () => {
            await updateDoc(topicRef, {
              status: "locked",
              updatedAt: Timestamp.now(),
            });
            await loadTopics();
          },
        });
      } else if (action === "hide") {
        setConfirmAction({
          isOpen: true,
          title: "Hide Topic",
          message:
            "Are you sure you want to hide this topic? It will no longer be visible to users.",
          action: async () => {
            await updateDoc(topicRef, {
              status: "hidden",
              updatedAt: Timestamp.now(),
            });
            await loadTopics();
          },
        });
      }
    } catch (error) {
      console.error(`Error performing action on topic ${topicId}:`, error);
    }
  };

  // Handle report actions
  const handleReportAction = async (
    reportId: string,
    action: "dismiss" | "action"
  ) => {
    try {
      const reportRef = doc(db, "contentReports", reportId);

      if (action === "dismiss") {
        setConfirmAction({
          isOpen: true,
          title: "Dismiss Report",
          message:
            "Are you sure you want to dismiss this report? It will be marked as reviewed with no action taken.",
          action: async () => {
            await updateDoc(reportRef, {
              status: "dismissed",
              reviewedBy: user?.uid,
              reviewedAt: Timestamp.now(),
            });
            await loadReports();
          },
        });
      } else if (action === "action") {
        setConfirmAction({
          isOpen: true,
          title: "Take Action",
          message:
            "Are you sure you want to take action on this report? The reported content will be hidden.",
          action: async () => {
            // Get the report to determine content type and ID
            const reportDoc = reports.find((r) => r.id === reportId);

            if (reportDoc) {
              // Update the report status
              await updateDoc(reportRef, {
                status: "actioned",
                reviewedBy: user?.uid,
                reviewedAt: Timestamp.now(),
                actionTaken: "Content hidden",
              });

              // Hide the reported content
              if (reportDoc.contentType === "topic") {
                const topicRef = doc(db, "forumTopics", reportDoc.contentId);
                await updateDoc(topicRef, {
                  status: "hidden",
                  updatedAt: Timestamp.now(),
                });
              } else if (reportDoc.contentType === "reply") {
                const replyRef = doc(db, "forumReplies", reportDoc.contentId);
                await updateDoc(replyRef, {
                  status: "hidden",
                  updatedAt: Timestamp.now(),
                });
              }

              await loadReports();
            }
          },
        });
      }
    } catch (error) {
      console.error(`Error performing action on report ${reportId}:`, error);
    }
  };

  // Format date for display
  const formatDate = (date: Date | undefined) => {
    if (!date) return "Unknown date";
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Get status badge color
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800";
      case "locked":
        return "bg-yellow-100 text-yellow-800";
      case "hidden":
        return "bg-red-100 text-red-800";
      case "deleted":
        return "bg-gray-100 text-gray-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "reviewed":
        return "bg-blue-100 text-blue-800";
      case "dismissed":
        return "bg-gray-100 text-gray-800";
      case "actioned":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="mx-auto max-w-7xl px-6">
          <div className="text-center">
            <h1 className="text-3xl font-bold tracking-tight text-gray-900">
              Loading...
            </h1>
          </div>
        </div>
      </div>
    );
  }

  if (!user || !isAdmin) {
    return null; // Will redirect in useEffect
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="mx-auto max-w-7xl px-6">
        <div className="flex items-center justify-between mb-8">
          <div>
            <Link
              href="/profile"
              className="inline-flex items-center text-sm font-medium text-indigo-600 hover:text-indigo-500 mb-2"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-1" />
              Back to Profile
            </Link>
            <h1 className="text-2xl font-bold tracking-tight text-gray-900">
              Forum Management
            </h1>
          </div>
          <Link
            href="/forum"
            className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            View Forum
          </Link>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200 mb-6">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab("reports")}
              className={`${
                activeTab === "reports"
                  ? "border-indigo-500 text-indigo-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
            >
              <FlagIcon
                className={`${
                  activeTab === "reports" ? "text-indigo-500" : "text-gray-400"
                } -ml-0.5 mr-2 h-5 w-5 inline-block`}
              />
              Reported Content
            </button>
            <button
              onClick={() => setActiveTab("topics")}
              className={`${
                activeTab === "topics"
                  ? "border-indigo-500 text-indigo-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
            >
              <ChatBubbleLeftRightIcon
                className={`${
                  activeTab === "topics" ? "text-indigo-500" : "text-gray-400"
                } -ml-0.5 mr-2 h-5 w-5 inline-block`}
              />
              All Topics
            </button>
          </nav>
        </div>

        {/* Content based on active tab */}
        {activeTab === "topics" ? (
          <div className="bg-white shadow overflow-hidden sm:rounded-md">
            <ul className="divide-y divide-gray-200">
              {topics.length === 0 && !isLoading ? (
                <li className="px-6 py-4 text-center text-gray-500">
                  No topics found.
                </li>
              ) : (
                topics.map((topic) => (
                  <li key={topic.id} className="px-6 py-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <Link
                          href={`/forum/topic/${topic.id}`}
                          className="text-lg font-medium text-indigo-600 hover:text-indigo-500"
                        >
                          {topic.title}
                        </Link>
                        <div className="mt-1 flex items-center text-sm text-gray-500">
                          <span>By {topic.authorName}</span>
                          <span className="mx-1">•</span>
                          <span>{formatDate(topic.createdAt)}</span>
                          <span className="mx-1">•</span>
                          <span
                            className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeColor(
                              topic.status
                            )}`}
                          >
                            {topic.status.charAt(0).toUpperCase() +
                              topic.status.slice(1)}
                          </span>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleTopicAction(topic.id, "lock")}
                          className="p-2 text-gray-400 hover:text-yellow-500"
                          title="Lock Topic"
                        >
                          <LockClosedIcon className="h-5 w-5" />
                        </button>
                        <button
                          onClick={() => handleTopicAction(topic.id, "hide")}
                          className="p-2 text-gray-400 hover:text-red-500"
                          title="Hide Topic"
                        >
                          <EyeSlashIcon className="h-5 w-5" />
                        </button>
                        <button
                          onClick={() => handleTopicAction(topic.id, "delete")}
                          className="p-2 text-gray-400 hover:text-red-700"
                          title="Delete Topic"
                        >
                          <TrashIcon className="h-5 w-5" />
                        </button>
                      </div>
                    </div>
                  </li>
                ))
              )}
              {isLoading && (
                <li className="px-6 py-4 text-center text-gray-500">
                  Loading topics...
                </li>
              )}
            </ul>
          </div>
        ) : (
          <div className="bg-white shadow overflow-hidden sm:rounded-md">
            <ul className="divide-y divide-gray-200">
              {reports.length === 0 && !isLoading ? (
                <li className="px-6 py-4 text-center text-gray-500">
                  No reports found.
                </li>
              ) : (
                reports.map((report) => (
                  <li key={report.id} className="px-6 py-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="text-lg font-medium text-gray-900">
                          Reported {report.contentType}{" "}
                          <Link
                            href={
                              report.contentType === "topic"
                                ? `/forum/topic/${report.contentId}`
                                : `/forum/topic/${
                                    report.contentId.split("_")[0]
                                  }#${report.contentId}`
                            }
                            className="text-indigo-600 hover:text-indigo-500"
                          >
                            (View Content)
                          </Link>
                        </div>
                        <div className="mt-1 text-sm text-gray-500">
                          <p>
                            <strong>Reason:</strong>{" "}
                            {report.reason
                              .replace(/_/g, " ")
                              .replace(/\b\w/g, (l) => l.toUpperCase())}
                          </p>
                          {report.description && (
                            <p className="mt-1">
                              <strong>Description:</strong> {report.description}
                            </p>
                          )}
                        </div>
                        <div className="mt-2 flex items-center text-sm text-gray-500">
                          <span>Reported by {report.reporterName}</span>
                          <span className="mx-1">•</span>
                          <span>{formatDate(report.createdAt)}</span>
                          <span className="mx-1">•</span>
                          <span
                            className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeColor(
                              report.status
                            )}`}
                          >
                            {report.status.charAt(0).toUpperCase() +
                              report.status.slice(1)}
                          </span>
                        </div>
                      </div>
                      {report.status === "pending" && (
                        <div className="flex space-x-2">
                          <button
                            onClick={() =>
                              handleReportAction(report.id, "dismiss")
                            }
                            className="p-2 text-gray-400 hover:text-gray-700"
                            title="Dismiss Report"
                          >
                            <XMarkIcon className="h-5 w-5" />
                          </button>
                          <button
                            onClick={() =>
                              handleReportAction(report.id, "action")
                            }
                            className="p-2 text-gray-400 hover:text-green-500"
                            title="Take Action"
                          >
                            <CheckCircleIcon className="h-5 w-5" />
                          </button>
                        </div>
                      )}
                    </div>
                  </li>
                ))
              )}
              {isLoading && (
                <li className="px-6 py-4 text-center text-gray-500">
                  Loading reports...
                </li>
              )}
            </ul>
          </div>
        )}
      </div>

      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={confirmAction.isOpen}
        onClose={() => setConfirmAction({ ...confirmAction, isOpen: false })}
        onConfirm={async () => {
          await confirmAction.action();
          setConfirmAction({ ...confirmAction, isOpen: false });
        }}
        title={confirmAction.title}
        message={confirmAction.message}
        confirmButtonText="Confirm"
        cancelButtonText="Cancel"
        confirmButtonColor="red"
      />
    </div>
  );
}
