"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import {
  ArrowLeftIcon,
  BookOpenIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  CheckCircleIcon,
} from "@heroicons/react/24/outline";
import SimpleButton from "@/components/SimpleButton";
import {
  srtSampleAnswers,
  paginateSRTAnswers,
  getUniqueQualities,
} from "@/data/srt_sample_answers";

export default function SRTSampleAnswers() {
  const router = useRouter();
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [expandedItems, setExpandedItems] = useState<number[]>([]);
  const [selectedQuality, setSelectedQuality] = useState<string>("All");
  const [searchTerm, setSearchTerm] = useState("");
  const itemsPerPage = 30;

  // Get unique qualities
  const qualities = ["All", ...getUniqueQualities()];

  // Filter by search term and quality
  const filteredAnswers = srtSampleAnswers.filter(
    (item) =>
      (selectedQuality === "All" ||
        item.responses.some((response) =>
          response.qualities
            .split(",")
            .map((q) => {
              // Normalize quality for comparison (same as in filterByQuality function)
              const trimmed = q.trim();
              return (
                trimmed.charAt(0).toUpperCase() + trimmed.slice(1).toLowerCase()
              );
            })
            .includes(selectedQuality)
        )) &&
      (item.situation.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.responses.some(
          (r) =>
            r.response.toLowerCase().includes(searchTerm.toLowerCase()) ||
            r.qualities.toLowerCase().includes(searchTerm.toLowerCase())
        ))
  );

  // Get paginated data
  const paginatedData = paginateSRTAnswers(
    filteredAnswers,
    currentPage,
    itemsPerPage
  );

  // Toggle accordion item - only one open at a time
  const toggleItem = (index: number) => {
    if (expandedItems.includes(index)) {
      // Close the current item
      setExpandedItems([]);
    } else {
      // Close any open items and open only the clicked one
      setExpandedItems([index]);
    }
  };

  // Handle page navigation
  const handleNextPage = () => {
    if (currentPage < paginatedData.totalPages) {
      setCurrentPage(currentPage + 1);
      setExpandedItems([]);
      // Scroll to top of the page
      window.scrollTo({ top: 0, behavior: "smooth" });
    }
  };

  const handlePreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
      setExpandedItems([]);
      // Scroll to top of the page
      window.scrollTo({ top: 0, behavior: "smooth" });
    }
  };

  // Clear search
  const clearSearch = () => {
    setSearchTerm("");
    setExpandedItems([]);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-6 sm:py-12">
      <div className="mx-auto max-w-4xl px-4 sm:px-6">
        <SimpleButton
          onClick={() => router.push("/tests/to/srt/practice")}
          className="mb-4 sm:mb-8 flex items-center text-gray-600 hover:text-gray-900"
        >
          <ArrowLeftIcon className="h-5 w-5 mr-2" />
          <span className="text-sm sm:text-base">Back to SRT Practice</span>
        </SimpleButton>

        <div className="bg-white rounded-xl sm:rounded-2xl shadow-sm p-4 sm:p-6 md:p-8">
          <div className="flex flex-col sm:flex-row sm:items-center mb-4 sm:mb-6">
            <BookOpenIcon className="h-7 w-7 sm:h-8 sm:w-8 text-blue-600 mb-2 sm:mb-0 sm:mr-3" />
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">
              SRT Sample Answers
            </h1>
          </div>

          <div className="mb-6 sm:mb-8 text-gray-700">
            <p className="mb-4 text-sm sm:text-base">
              This page provides sample responses for common SRT situations,
              organized by officer-like qualities. Each situation includes
              multiple possible responses and highlights the specific qualities
              demonstrated in each response.
            </p>
            <div className="bg-blue-50 p-3 sm:p-4 rounded-lg border border-blue-200 mb-4">
              <h3 className="font-semibold text-blue-800 mb-2 text-sm sm:text-base">
                How to use these samples:
              </h3>
              <ul className="list-disc list-inside space-y-1 text-blue-800 text-xs sm:text-sm">
                <li>Study the different approaches to the same situation</li>
                <li>
                  Notice how each response demonstrates specific officer-like
                  qualities
                </li>
                <li>
                  Observe the concise and direct nature of effective responses
                </li>
                <li>Use these as inspiration, not answers to memorize</li>
              </ul>
            </div>

            {/* Return to Practice Button */}
            <div className="flex justify-center sm:justify-end mb-4">
              <SimpleButton
                onClick={() => router.push("/tests/to/srt/practice")}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center"
              >
                <ArrowLeftIcon className="h-4 w-4 mr-2" />
                Return to SRT Practice
              </SimpleButton>
            </div>
          </div>

          {/* Search and filter */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            {/* Quality filter */}
            <div className="w-full sm:w-1/3">
              <label
                htmlFor="quality-filter"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Filter by Quality
              </label>
              <select
                id="quality-filter"
                value={selectedQuality}
                onChange={(e) => {
                  setSelectedQuality(e.target.value);
                  setCurrentPage(1);
                  setExpandedItems([]);
                }}
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              >
                {qualities.map((quality) => (
                  <option key={quality} value={quality}>
                    {quality}
                  </option>
                ))}
              </select>
            </div>

            {/* Search box */}
            <div className="w-full sm:w-2/3">
              <label
                htmlFor="search"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Search Situations
              </label>
              <div className="relative">
                <input
                  type="text"
                  id="search"
                  placeholder="Search for situations, responses, or qualities..."
                  value={searchTerm}
                  onChange={(e) => {
                    setSearchTerm(e.target.value);
                    setCurrentPage(1);
                  }}
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 pr-10 sm:text-sm"
                />
                {searchTerm && (
                  <button
                    onClick={clearSearch}
                    className="absolute right-2 sm:right-3 inset-y-0 my-auto h-7 sm:h-8 px-2 sm:px-3 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded text-xs sm:text-sm font-medium"
                  >
                    Clear
                  </button>
                )}
              </div>
            </div>
          </div>

          {/* Sample answers accordion */}
          {paginatedData.items.length === 0 ? (
            <div className="text-center py-12 border-t">
              <p className="text-gray-600 text-lg">
                No results found. Try a different search term or quality.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {paginatedData.items.map((item, index) => (
                <div key={index} className="border rounded-lg overflow-hidden">
                  <button
                    onClick={() => toggleItem(index)}
                    className="w-full flex justify-between items-center p-4 bg-blue-50 hover:bg-blue-100 transition-colors text-left"
                  >
                    <div>
                      <div className="flex flex-wrap gap-1 mb-2">
                        {/* Extract all unique qualities from this item's responses */}
                        {[
                          ...new Set(
                            item.responses.flatMap((r) =>
                              r.qualities.split(",").map((q) => {
                                // Normalize quality for display
                                const trimmed = q.trim();
                                return (
                                  trimmed.charAt(0).toUpperCase() +
                                  trimmed.slice(1).toLowerCase()
                                );
                              })
                            )
                          ),
                        ].map((quality, qIndex) => (
                          <span
                            key={qIndex}
                            className="inline-block px-2 py-1 text-xs font-medium bg-blue-200 text-blue-800 rounded"
                          >
                            {quality}
                          </span>
                        ))}
                      </div>
                      <h3 className="font-medium text-gray-900">
                        {item.situation}
                      </h3>
                    </div>
                    {expandedItems.includes(index) ? (
                      <ChevronUpIcon className="h-5 w-5 text-blue-500 flex-shrink-0" />
                    ) : (
                      <ChevronDownIcon className="h-5 w-5 text-blue-500 flex-shrink-0" />
                    )}
                  </button>
                  {expandedItems.includes(index) && (
                    <div className="p-4 bg-white border-t">
                      <div className="space-y-4">
                        {item.responses.map((response, responseIndex) => (
                          <div
                            key={responseIndex}
                            className="border-b pb-4 last:border-b-0 last:pb-0"
                          >
                            <div className="flex items-start">
                              <div className="bg-blue-100 text-blue-800 rounded-full p-1 mr-3 mt-1">
                                <CheckCircleIcon className="h-4 w-4" />
                              </div>
                              <div>
                                <h4 className="font-medium text-gray-800 mb-1">
                                  Response {responseIndex + 1}:
                                </h4>
                                <p className="text-gray-700 mb-2">
                                  {response.response}
                                </p>
                                <div className="flex items-center">
                                  <span className="text-xs font-medium text-gray-500 mr-2">
                                    Qualities Shown:
                                  </span>
                                  <span className="text-xs font-medium text-blue-600">
                                    {response.qualities}
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}

          {/* Pagination controls */}
          {paginatedData.totalPages > 1 && (
            <div className="flex justify-center items-center space-x-4 mt-8 pt-4 border-t">
              <button
                onClick={handlePreviousPage}
                disabled={currentPage === 1}
                className={`inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium ${
                  currentPage === 1
                    ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                    : "bg-white text-gray-700 hover:bg-gray-50"
                }`}
              >
                <ChevronLeftIcon className="h-5 w-5 mr-1" />
                Previous
              </button>
              <div className="text-sm text-gray-700">
                Page {currentPage} of {paginatedData.totalPages}
              </div>
              <button
                onClick={handleNextPage}
                disabled={currentPage === paginatedData.totalPages}
                className={`inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium ${
                  currentPage === paginatedData.totalPages
                    ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                    : "bg-white text-gray-700 hover:bg-gray-50"
                }`}
              >
                Next
                <ChevronRightIcon className="h-5 w-5 ml-1" />
              </button>
            </div>
          )}

          {/* Tips section */}
          <div className="mt-8 sm:mt-12 bg-gray-100 p-4 sm:p-6 rounded-md sm:rounded-lg">
            <h2 className="text-lg sm:text-xl font-bold text-gray-900 mb-3 sm:mb-4">
              Tips for Effective SRT Responses
            </h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
              <div>
                <h3 className="font-semibold text-gray-800 mb-2 text-sm sm:text-base">
                  Do:
                </h3>
                <ul className="list-disc list-inside space-y-1 text-gray-700 text-xs sm:text-sm">
                  <li>Keep responses concise and to the point</li>
                  <li>Include both immediate actions and follow-up steps</li>
                  <li>Demonstrate officer-like qualities in your approach</li>
                  <li>Show resourcefulness and initiative</li>
                  <li>Consider both practical and ethical dimensions</li>
                </ul>
              </div>
              <div className="mt-3 sm:mt-0">
                <h3 className="font-semibold text-gray-800 mb-2 text-sm sm:text-base">
                  Don't:
                </h3>
                <ul className="list-disc list-inside space-y-1 text-gray-700 text-xs sm:text-sm">
                  <li>Write overly complex or lengthy responses</li>
                  <li>
                    Focus only on immediate actions without follow-through
                  </li>
                  <li>Provide unrealistic or impractical solutions</li>
                  <li>Demonstrate impulsive or emotional decision-making</li>
                  <li>Ignore the ethical dimensions of situations</li>
                </ul>
              </div>
            </div>

            {/* Bottom Return to Practice Button */}
            <div className="flex justify-center mt-6">
              <SimpleButton
                onClick={() => router.push("/tests/to/srt/practice")}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center"
              >
                <ArrowLeftIcon className="h-4 w-4 mr-2" />
                Return to SRT Practice
              </SimpleButton>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
