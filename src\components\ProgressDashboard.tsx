"use client";

import React, { useState, useEffect } from "react";
import { useAuth } from "@/context/GoogleAuthContext";
import {
  getUserProgress,
  UserProgress,
  SectionProgress,
  SubSectionProgress,
  formatProgressText,
} from "@/utils/progressTracker";
import {
  ChartBarIcon,
  TrophyIcon,
  ClockIcon,
  CheckCircleIcon,
  ArrowTrendingUpIcon,
  ChevronDownIcon,
  ChevronRightIcon,
} from "@heroicons/react/24/outline";

interface ProgressDashboardProps {
  className?: string;
  showHeader?: boolean;
}

export default function ProgressDashboard({
  className = "",
  showHeader = true,
}: ProgressDashboardProps) {
  const { user } = useAuth();
  const [progress, setProgress] = useState<UserProgress | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expandedSections, setExpandedSections] = useState<Set<string>>(
    new Set()
  );

  useEffect(() => {
    const fetchProgress = async () => {
      if (!user) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const userProgress = await getUserProgress(user.uid);
        setProgress(userProgress);
        setError(null);
      } catch (err) {
        console.error("Error fetching progress:", err);
        setError("Failed to load progress data");
      } finally {
        setLoading(false);
      }
    };

    fetchProgress();
  }, [user]);

  const toggleSection = (sectionId: string) => {
    setExpandedSections((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(sectionId)) {
        newSet.delete(sectionId);
      } else {
        newSet.add(sectionId);
      }
      return newSet;
    });
  };

  if (!user) {
    return (
      <div
        className={`bg-blue-50 border border-blue-200 rounded-lg p-6 ${className}`}
      >
        <p className="text-blue-800 text-center">
          Please sign in to view your progress
        </p>
      </div>
    );
  }

  if (loading) {
    return (
      <div
        className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}
      >
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[...Array(7)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div
        className={`bg-red-50 border border-red-200 rounded-lg p-6 ${className}`}
      >
        <p className="text-red-800 text-center">{error}</p>
      </div>
    );
  }

  if (!progress) {
    return null;
  }

  const getColorClasses = (color: string, percentage: number) => {
    const intensity =
      percentage >= 80 ? "600" : percentage >= 50 ? "500" : "400";
    return {
      bg: `bg-${color}-${intensity}`,
      text: `text-${color}-${intensity}`,
      bgLight: `bg-${color}-50`,
      border: `border-${color}-200`,
    };
  };

  return (
    <div
      className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}
    >
      {showHeader && (
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-100 rounded-full">
                <ChartBarIcon className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">
                  Your Progress
                </h2>
                <p className="text-sm text-gray-500">
                  Track your completion across all test sections
                </p>
              </div>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-gray-900">
                {progress.overallPercentage}%
              </div>
              <div className="text-sm text-gray-500">
                {progress.totalCompleted} / {progress.totalAvailable} completed
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="p-6">
        {/* Overall Progress Bar */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">
              Overall Progress
            </span>
            <span className="text-sm text-gray-500">
              {progress.overallPercentage}%
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div
              className="bg-blue-600 h-3 rounded-full transition-all duration-300"
              style={{ width: `${progress.overallPercentage}%` }}
            ></div>
          </div>
        </div>

        {/* Accordion Section Progress */}
        <div className="space-y-3">
          {progress.sectionProgress.map((section: SectionProgress) => {
            const colors = getColorClasses(section.color, section.percentage);
            const isExpanded = expandedSections.has(section.sectionId);

            return (
              <div
                key={section.sectionId}
                className="border border-gray-200 rounded-lg overflow-hidden"
              >
                {/* Section Header - Clickable */}
                <button
                  onClick={() => toggleSection(section.sectionId)}
                  className={`w-full p-4 text-left hover:bg-gray-50 transition-colors ${colors.bgLight}`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center space-x-2">
                        {isExpanded ? (
                          <ChevronDownIcon className="h-5 w-5 text-gray-500" />
                        ) : (
                          <ChevronRightIcon className="h-5 w-5 text-gray-500" />
                        )}
                        <div
                          className={`p-1.5 rounded-full ${colors.bg
                            .replace("400", "100")
                            .replace("500", "100")
                            .replace("600", "100")}`}
                        >
                          {section.percentage === 100 ? (
                            <CheckCircleIcon
                              className={`h-4 w-4 ${colors.text}`}
                            />
                          ) : (
                            <ArrowTrendingUpIcon
                              className={`h-4 w-4 ${colors.text}`}
                            />
                          )}
                        </div>
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900">
                          {section.sectionName}
                        </h3>
                        <p className="text-sm text-gray-600">
                          {section.description}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className={`text-lg font-bold ${colors.text}`}>
                        {section.percentage}%
                      </div>
                      <div className="text-xs text-gray-500">
                        {section.completed}/{section.total}
                      </div>
                    </div>
                  </div>

                  {/* Section Progress Bar */}
                  <div className="mt-3">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-300 ${colors.bg}`}
                        style={{ width: `${section.percentage}%` }}
                      ></div>
                    </div>
                  </div>
                </button>

                {/* Expanded Subsections */}
                {isExpanded && (
                  <div className="border-t border-gray-200 bg-white">
                    <div className="p-4 space-y-3">
                      {section.subSections.map(
                        (subSection: SubSectionProgress) => (
                          <div
                            key={subSection.subSectionId}
                            className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                          >
                            <div className="flex items-center space-x-3">
                              <div className="w-2 h-2 rounded-full bg-gray-400"></div>
                              <div>
                                <h4 className="font-medium text-gray-900">
                                  {subSection.subSectionName}
                                </h4>
                                <p className="text-sm text-gray-600">
                                  {formatProgressText(
                                    subSection.completed,
                                    subSection.total,
                                    subSection.type
                                  )}
                                </p>
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="text-sm font-semibold text-gray-900">
                                {subSection.percentage}%
                              </div>
                              <div className="text-xs text-gray-500">
                                {subSection.completed}/{subSection.total}
                              </div>
                            </div>
                          </div>
                        )
                      )}
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </div>

        {/* Achievement Badges */}
        {progress.overallPercentage > 0 && (
          <div className="mt-6 pt-6 border-t border-gray-200">
            <h3 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
              <TrophyIcon className="h-4 w-4 mr-2" />
              Achievements
            </h3>
            <div className="flex flex-wrap gap-2">
              {progress.overallPercentage >= 25 && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Getting Started
                </span>
              )}
              {progress.overallPercentage >= 50 && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  Halfway There
                </span>
              )}
              {progress.overallPercentage >= 75 && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                  Almost Complete
                </span>
              )}
              {progress.overallPercentage === 100 && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                  <TrophyIcon className="h-3 w-3 mr-1" />
                  Master Achiever
                </span>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
