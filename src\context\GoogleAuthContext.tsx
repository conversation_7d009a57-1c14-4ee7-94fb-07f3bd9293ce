"use client";

import React, { createContext, useContext, useEffect, useState } from "react";
import {
  User,
  signOut,
  onAuthStateChanged,
  GoogleAuthProvider as FirebaseGoogleAuthProvider,
  signInWithPopup,
  updateProfile,
} from "firebase/auth";
import { auth, db } from "@/lib/firebase";
import { doc, setDoc, getDoc, serverTimestamp } from "firebase/firestore";
import { useRouter } from "next/navigation";
import { checkAdminStatus, checkAIAccessStatus } from "@/utils/adminUtils";

interface AuthContextType {
  user: User | null;
  loading: boolean;
  signInWithGoogle: () => Promise<void>;
  logout: () => Promise<void>;
  updateUserProfile: (displayName: string) => Promise<void>;
  isAdmin: boolean;
  canAccessAIAnalysis: boolean;
  userRoles: string[];
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  loading: true,
  signInWithGoogle: async () => {},
  logout: async () => {},
  updateUserProfile: async () => {},
  isAdmin: false,
  canAccessAIAnalysis: false,
  userRoles: [],
});

export const useAuth = () => useContext(AuthContext);

export const GoogleAuthProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [isAdmin, setIsAdmin] = useState(false);
  const [canAccessAIAnalysis, setCanAccessAIAnalysis] = useState(false);
  const [userRoles, setUserRoles] = useState<string[]>([]);
  const router = useRouter();

  useEffect(() => {
    // Check if auth is available (it might be null during SSG/SSR)
    if (!auth) {
      setLoading(false);
      return;
    }

    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      setUser(user);

      // Check for admin status and AI access in Firestore if user is logged in
      if (user && db) {
        try {
          // Use the utility functions to check admin status
          const adminStatus = await checkAdminStatus(user.uid);
          setIsAdmin(adminStatus);

          // Check if user can access AI analysis
          const aiAccessStatus = await checkAIAccessStatus(user.uid);
          setCanAccessAIAnalysis(aiAccessStatus);

          // Get user data for roles
          const userRef = doc(db, "users", user.uid);
          const userDoc = await getDoc(userRef);

          if (userDoc.exists()) {
            const userData = userDoc.data();
            // Get user roles if they exist
            const roles = userData.roles || [];
            setUserRoles(roles);
          } else {
            setUserRoles([]);
          }
        } catch (error) {
          console.error("Error fetching user status:", error);
          setIsAdmin(false);
          setCanAccessAIAnalysis(false);
          setUserRoles([]);
        }
      } else {
        setIsAdmin(false);
        setCanAccessAIAnalysis(false);
        setUserRoles([]);
      }

      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  const signInWithGoogle = async () => {
    try {
      // Check if auth is available
      if (!auth || !db) {
        console.error("Firebase auth or Firestore not initialized");
        return;
      }

      const provider = new FirebaseGoogleAuthProvider();
      const result = await signInWithPopup(auth, provider);
      const user = result.user;

      // Create or update user document in Firestore
      const userRef = doc(db, "users", user.uid);
      const userDoc = await getDoc(userRef);

      if (!userDoc.exists()) {
        // Determine if the user should be an admin based on email
        const shouldBeAdmin = user.email?.includes("admin") || false;

        // Create new user document
        await setDoc(userRef, {
          name: user.displayName,
          email: user.email,
          photoURL: user.photoURL,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
          isAdmin: shouldBeAdmin, // Set initial admin status based on email
          roles: shouldBeAdmin ? ["user", "admin"] : ["user"], // Add admin role if needed
        });

        // User created with admin status based on email
      } else {
        // Update existing user document
        await setDoc(
          userRef,
          {
            updatedAt: serverTimestamp(),
            lastLogin: serverTimestamp(),
          },
          { merge: true }
        );
      }

      router.push("/profile");
    } catch (error) {
      console.error("Google sign-in error:", error);
      throw error;
    }
  };

  const logout = async () => {
    try {
      // Check if auth is available
      if (!auth) {
        console.error("Firebase auth not initialized");
        router.push("/");
        return;
      }

      await signOut(auth);
      router.push("/");
    } catch (error) {
      console.error("Logout error:", error);
      throw error;
    }
  };

  const updateUserProfile = async (displayName: string) => {
    try {
      // Check if auth and db are available
      if (!auth || !db) {
        console.error("Firebase auth or Firestore not initialized");
        return;
      }

      if (!auth.currentUser) {
        throw new Error("No user is currently logged in");
      }

      // Update Firebase Auth profile
      await updateProfile(auth.currentUser, { displayName });

      // Update Firestore user document
      const userRef = doc(db, "users", auth.currentUser.uid);
      await setDoc(
        userRef,
        {
          name: displayName,
          updatedAt: serverTimestamp(),
        },
        { merge: true }
      );

      // Update local user state to reflect changes immediately
      setUser({ ...auth.currentUser });
    } catch (error) {
      console.error("Update profile error:", error);
      throw error;
    }
  };

  const value = {
    user,
    loading,
    signInWithGoogle,
    logout,
    updateUserProfile,
    isAdmin,
    canAccessAIAnalysis,
    userRoles,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
