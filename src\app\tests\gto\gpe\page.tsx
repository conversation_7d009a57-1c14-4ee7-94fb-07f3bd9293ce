"use client";

import { ArrowLeftIcon, MapIcon } from "@heroicons/react/24/outline";
import Link from "next/link";
import NavigationButton from "@/components/NavigationButton";
import React from "react";

export default function GroupPlanningExercisePage() {
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="mx-auto max-w-4xl px-6">
        <NavigationButton
          href="/tests/gto"
          className="mb-8 inline-flex items-center text-gray-600 hover:text-gray-900"
        >
          <ArrowLeftIcon className="h-5 w-5 mr-2" />
          Back to GTO Test
        </NavigationButton>

        <div className="bg-white rounded-2xl shadow-sm p-8 border-t-4 border-green-500">
          <div className="flex items-center space-x-3 mb-6">
            <div className="p-2 rounded-full bg-green-100">
              <MapIcon className="h-8 w-8 text-green-600" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900">
              Group Planning Exercise (GPE)
            </h1>
          </div>

          <div className="flex justify-center mb-10">
            <Link
              href="/tests/gto/gpe/sets"
              className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-4 px-10 rounded-lg shadow-lg transition duration-200 flex items-center text-xl animate-pulse"
            >
              <MapIcon className="h-7 w-7 mr-3" />
              Try Interactive GPE Simulation
            </Link>
          </div>

          <div className="prose prose-lg max-w-none">
            <p className="text-lg text-gray-700 mb-6">
              The Group Planning Exercise (GPE) is a critical component of the
              GTO test that evaluates your ability to analyze situations, plan
              effectively, and work collaboratively to solve complex problems.
            </p>

            <h2 className="text-xl font-semibold text-gray-900 mt-8 mb-4">
              What is Group Planning Exercise?
            </h2>
            <p>
              In this round, the authorities divide the candidates into multiple
              groups and provide them with a particular situation. The
              candidates are given a time frame of 30 minutes to find a possible
              solution to the situation. In the end, the groups are asked to
              nominate a candidate who narrates the solution to the authorities.
            </p>

            <h2 className="text-xl font-semibold text-gray-900 mt-8 mb-4">
              How is GPE Conducted?
            </h2>
            <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm mb-6">
              <ol className="list-decimal pl-5 space-y-4">
                <li className="text-gray-700">
                  <strong>GTO Narration:</strong> The GTO shows an outdoor field
                  with the help of a model and narrates the situation,
                  explaining the color codes, distances, and different figures
                  on the map.
                </li>
                <li className="text-gray-700">
                  <strong>Story Reading:</strong> The GTO provides each
                  candidate with a story page containing the scenario details
                  for five minutes of individual reading.
                </li>
                <li className="text-gray-700">
                  <strong>Solution Planning:</strong> Candidates are given 10
                  minutes to write their solutions to the different problems
                  presented in the scenario.
                </li>
                <li className="text-gray-700">
                  <strong>Group Discussion:</strong> A 20-minute group
                  discussion is conducted where candidates share and debate
                  their solutions to arrive at a consensus.
                </li>
                <li className="text-gray-700">
                  <strong>Presentation:</strong> One candidate is nominated from
                  the group to represent the group's solution. This candidate
                  narrates the solution to the GTO using a stick and the map.
                </li>
                <li className="text-gray-700">
                  <strong>Q&A:</strong> The GTO might ask questions or request
                  clarification about the presented solution.
                </li>
              </ol>
              <div className="mt-4 bg-blue-50 p-4 rounded-lg">
                <p className="text-blue-700 text-sm">
                  <strong>Note:</strong> Our interactive GPE simulation allows
                  you to experience this process in a realistic way. Try it by
                  clicking the "Try Interactive GPE Simulation" button at the
                  top of this page. You'll be able to select from multiple
                  scenario sets.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
