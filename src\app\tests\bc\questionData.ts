import {
  UserIcon,
  NewspaperIcon,
  HomeIcon,
  AcademicCapIcon,
  TrophyIcon,
  ShieldCheckIcon,
  BuildingLibraryIcon,
  UserCircleIcon,
  GlobeAsiaAustraliaIcon,
  CalculatorIcon,
  ScaleIcon,
  MapIcon,
  BanknotesIcon,
  PuzzlePieceIcon,
} from "@heroicons/react/24/outline";

export const questionCategories = [
  {
    id: "personal",
    name: "Personal Introduction & Etiquette",
    icon: UserIcon,
    questions: [
      "Greet the officer in Nepali/English. Why did you choose this language?",
      "Describe your journey to this interview venue today.",
      "What is the first thing you noticed when entering this room?",
      "Introduce yourself in one minute, highlighting your relevant skills and aspirations.",
      "What are your first impressions of the interview panel?",
      "How do you handle nervousness or stress, particularly when presenting technical information?",
    ],
  },
  {
    id: "current-affairs",
    name: "Current Affairs & News",
    icon: NewspaperIcon,
    questions: [
      "Summarize today's front-page news in The Himalayan Times. Are there any stories related to technology, cybersecurity, or defense?",
      "Explain the recent protest by teachers/doctors in Nepal. Could technology or data analysis have played a role in understanding or mitigating the issues?",
      "Detail the outcomes of the latest SAARC summit. Were there any discussions on regional tech collaboration or cybersecurity threats?",
      "What is Nepal's stance on the Israel-Palestine conflict? How is technology being used by both sides in the conflict (e.g., drones, cyber warfare)?",
      "Discuss Nepal-India relations in the last six months. Are there any reported incidents of cyber espionage or technological competition between them?",
      "How has climate change impacted Nepal's agriculture recently? Can technology, like remote sensing or data analytics, help in monitoring or addressing these impacts?",
      "Name three key decisions from Nepal's latest federal budget. Were there any allocations for technology infrastructure or digital transformation?",
      "What are the major challenges facing Nepal currently? How can technology help address one of these challenges (e.g., governance, infrastructure)?",
      "Discuss the recent activities of major international powers in South Asia. How significant is technological competition (e.g., 5G, AI) in their strategies?",
      "What is Nepal's role in BIMSTEC? Are there any tech-related cooperation initiatives within BIMSTEC?",
      "How has technology changed how people access news in Nepal? What are the implications for information security and disinformation?",
      "What is your opinion on the current political stability in Nepal? How might technology be used to enhance transparency or citizen participation?",
    ],
  },

  {
    id: "family",
    name: "Family & Lifestyle",
    icon: HomeIcon,
    questions: [
      "How has your family influenced your decision to join the Army? Have they supported your interest in computer engineering and the military?",
      "Describe your sibling's career and how it inspires you. Do they work in a technical field?",
      "What is your monthly expense on groceries? Break it down.",
      "How do you manage time between studies, family duties, and potentially working on personal projects?",
      "Which family member taught you resilience? Give an example, perhaps related to overcoming a difficulty in your studies or projects.",
      "Describe a typical day in your life, including time spent on technical activities.",
      "What are your family's values, and how do they align with Army values?",
      "How would your family cope with your potential deployment, knowing your role might involve sensitive technical work?",
    ],
  },
  {
    id: "academics",
    name: "Academics & Education",
    icon: AcademicCapIcon,
    questions: [
      "Why did you choose Computer Engineering? Link your interest to potential roles or needs within the Army.",
      "Which academic failure (perhaps in a programming course or difficult subject) taught you the most?",
      "How would you improve Nepal's technical education system, particularly in areas relevant to defense?",
      "Explain a complex concept from your favorite Computer Engineering subject in simple terms.",
      "How do you stay updated on the latest trends and technologies in Computer Engineering?",
      "What is the most valuable technical skill you learned during your education?",
      "How important is continuous learning in the Army, especially with rapidly evolving technology?",
      "Describe a significant project you worked on during your degree. What was its purpose, your role, and the outcome?",
      "What programming languages are you proficient in, and which do you prefer for different types of tasks?",
      "Are you familiar with concepts like data structures, algorithms, or operating systems? How might these be relevant in a military context?",
    ],
  },
  {
    id: "sports",
    name: "Sports & Physical Fitness",
    icon: TrophyIcon,
    questions: [
      "Which sport best prepares you for the Army? Why?",
      "Detail the rules of kabaddi and its relevance to teamwork.",
      "How do sports build leadership? Use an example.",
      "What's your fitness routine? How does it align with Army standards?",
      "What are the physical requirements for joining the Nepal Army?",
      "How do you stay motivated to maintain physical fitness?",
      "Have you ever participated in competitive sports? Describe your experience and how you handled pressure.",
    ],
  },
  {
    id: "army",
    name: "Nepal Army-Specific",
    icon: ShieldCheckIcon,
    questions: [
      "What distinguishes Nepal Army from other professions? How can your technical skills be uniquely applied here?",
      "Explain UN peacekeeping missions Nepal has participated in. How might technology, like communication systems or surveillance, support these missions?",
      "How would you handle a soldier disobeying orders, particularly in a situation involving critical equipment or data?",
      'What does "सेवा सुरक्षा सुखराम" mean to you, and how does the role of technology fit into this motto?',
      "Describe the Army's role in disaster management. How can technology (communication networks, data analysis, mapping) enhance this role?",
      "Why do you want to join the Nepal Army, specifically leveraging your Computer Engineering skills?",
      "What is the history and significance of the Nepal Army? How has technology played a role in its modernization?",
      "What are the core values of the Nepal Army, and how do you see your technical work upholding these values?",
      "What is the rank structure of the Nepal Army?",
      "Describe the different branches or services within the Nepal Army. Which ones do you believe would benefit most from your technical expertise?",
      "How do you see yourself contributing to the Nepal Army using your engineering background?",
      "What sacrifices are you prepared to make for the country by joining the Army, including potentially working in challenging environments with limited technical resources?",
      "What challenges do you anticipate facing in the Army, particularly as a technical professional?",
      "How does the Army contribute to national unity? How can technology aid in communication and coordination across different units?",
    ],
  },
  {
    id: "government",
    name: "Government & Politics",
    icon: BuildingLibraryIcon,
    questions: [
      "Compare Nepal's federal system with India's. How might technology impact governance at different levels?",
      "Roles of the President vs. Prime Minister in Nepal. How might technology support their functions or communication?",
      "Who are Nepal's current Home and Defense Ministers?",
      'What is the "VVIP" protocol in Nepal? How is technology used for their security and communication?',
      "Explain the Judiciary's structure in Nepal. Could technology improve the efficiency or transparency of the judicial process?",
      "What is the function of the Parliament in Nepal? How can technology facilitate legislative processes or public engagement?",
      "Who is the Commander-in-Chief of the Nepal Army?",
      "How does the government ensure civilian oversight of the Army? How might technology be used to enhance accountability and transparency?",
    ],
  },
  {
    id: "leadership",
    name: "Leadership & Situational",
    icon: UserCircleIcon,
    questions: [
      "Describe a time you led a team under pressure, perhaps during a challenging coding project or technical assignment.",
      "How would you motivate a demoralized technical unit facing a difficult or repetitive task?",
      "If a superior gave an unethical order, perhaps involving misuse of technical capabilities or data, what would you do?",
      "Share an example of your problem-solving skill when debugging code or resolving a technical issue.",
      "Describe a time you had to make a difficult decision related to a technical project with limited information.",
      "How do you prioritize technical tasks when faced with multiple demands and deadlines?",
      "Describe a situation where you had to adapt to unexpected changes in project requirements or available technology.",
      "How do you handle conflict within a technical team, perhaps due to differing opinions on implementation approaches?",
    ],
  },
  {
    id: "general",
    name: "General Knowledge",
    icon: GlobeAsiaAustraliaIcon,
    questions: [
      "Name the seven provinces and their capitals. How is technology being used for development or communication within these provinces?",
      "Recent infrastructure projects under PM Pushpa Kamal Dahal. Have any involved significant technology components (e.g., digital infrastructure, smart cities)?",
      'What is the "Koshi Treaty" between Nepal and India? Are there any modern technological considerations related to river management or data sharing?',
      "Key outcomes of COP28 relevant to Nepal. How can technology contribute to Nepal's climate change mitigation or adaptation efforts?",
      "Explain the Bangladesh 2023 elections and protests. How was social media or other technology used by different groups?",
      "Name three major rivers in Nepal and their significance. Could technology help in monitoring water levels or predicting floods?",
      "What is the significance of Sagarmatha (Mount Everest) to Nepal? How is technology used in mountaineering or environmental monitoring in the Everest region?",
      "Name the countries bordering Nepal and their capitals. Are there any cross-border technological collaborations or security concerns?",
      "What are some major historical events that shaped modern Nepal? How has technology influenced Nepalese society over time?",
      "Who are some national heroes of Nepal? Do any have a connection to innovation or strategic thinking that can be related to technology?",
    ],
  },
  {
    id: "math",
    name: "Mathematics & Analytical",
    icon: CalculatorIcon,
    questions: [
      "Calculate the area of a trapezoidal room (provide dimensions).",
      "If 10 soldiers need rations for 15 days, how long for 25 soldiers?",
      "Budgeting: Plan a meal for 50 people with Rs. 20,000.",
      "Solve a simple logical reasoning puzzle (interviewer provides).",
      "Analyze a given scenario (e.g., a network security issue or a data analysis problem) and suggest a course of action.",
      "Explain a complex mathematical concept (like a specific algorithm or data structure efficiency) in simple terms.",
      "How do you approach problem-solving from an analytical and systematic perspective, similar to debugging code?",
    ],
  },
  {
    id: "ethical",
    name: "Ethical & Hypothetical",
    icon: ScaleIcon,
    questions: [
      "If you saw a colleague misusing official technical resources for personal gain, how would you react?",
      "How would you handle a conflict between two squad members, perhaps related to differing technical approaches or sharing equipment?",
      "What would you do if a senior officer publicly criticized your technical work unfairly?",
      "Imagine you are responsible for maintaining a critical communication system in a remote area. What challenges might you face, and how would you address them?",
      "If faced with a choice between following an order that seems technically unsound and your professional judgment, what factors would you consider?",
      "How important is data privacy and security in a military context?",
    ],
  },
  {
    id: "geography",
    name: "Geography & Culture",
    icon: MapIcon,
    questions: [
      "Best hiking routes in Annapurna vs. Langtang.",
      "Cultural significance of Lumbini.",
      "How does Nepal's geography affect national security, and how can technology help overcome geographical challenges (e.g., communication in remote areas)?",
      "Describe the cultural diversity of Nepal. How can technology be used to bridge communication gaps or preserve cultural heritage?",
      "Name some major festivals celebrated in Nepal.",
      "How do the Himalayas influence Nepal's climate and agriculture? Can technology help in adapting to these conditions?",
      "Describe the geographical divisions of Nepal (Terai, Hills, Mountains). How do these affect infrastructure development and technology deployment?",
    ],
  },
  {
    id: "economics",
    name: "Economics",
    icon: BanknotesIcon,
    questions: [
      "Current price of rice, lentils, and onions in Kathmandu.",
      "How does remittance impact Nepal's economy?",
      "What are the major sectors of Nepal's economy? How is technology impacting these sectors?",
      "Discuss the impact of tourism on Nepal's economy. How can technology enhance the tourism experience?",
      "How has Nepal's trade relationship with India and China evolved in recent years? What role does technology play in these relationships?",
      "What economic challenges does Nepal face as a landlocked country? How can digital technologies help overcome these challenges?",
      "Explain Nepal's current inflation rate and its impact on the average citizen. How might digital financial services help manage this impact?",
    ],
  },
  {
    id: "miscellaneous",
    name: "Miscellaneous",
    icon: PuzzlePieceIcon,
    questions: [
      'Define "rachhya sachib" (Chief Secretary of Defence) and their role. How might a technical advisor support this role?',
      "What qualities make a successful Army officer? How can technical expertise complement these qualities?",
      "How do you stay updated on military trends, particularly those related to technology (e.g., drones, AI, cyber warfare)?",
      "Why should the Army invest more heavily in technology and technical personnel like yourself?",
      "Do you have any questions for us, perhaps about the specific technical roles available or the Army's technology strategy?",
      "How do you define success in both your technical career and potential Army career?",
      "What are your long-term career goals, and how does joining the Nepal Army fit into them, especially from a technical perspective?",
      "What is the most challenging technical problem you've ever solved? Describe your approach.",
    ],
  },
];
