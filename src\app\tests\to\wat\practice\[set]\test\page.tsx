"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import { useRouter } from "next/navigation";
import NavigationButton from "@/components/NavigationButton";
import { use } from "react";
import {
  PlayIcon,
  PauseIcon,
  ArrowRightIcon,
  CheckIcon,
} from "@heroicons/react/24/outline";
import { wordSets } from "@/data/wat-sets";
import { saveResponses } from "@/services/responseService";
import { useAuth } from "@/context/GoogleAuthContext";
import { useLoading } from "@/context/LoadingContext";
// We're using a custom timer implementation instead of the TestTimer component

export default function WATTest({
  params,
}: {
  params: Promise<{ set: string }>;
}) {
  const router = useRouter();
  const { startLoading } = useLoading();
  const { user } = useAuth();
  const [currentWordIndex, setCurrentWordIndex] = useState(0);
  const [timeLeft, setTimeLeft] = useState(15);
  const timerEndRef = useRef<number | null>(null); // Store the end time as a ref
  const [isPaused, setIsPaused] = useState(false);
  const [response, setResponse] = useState("");
  const { set } = use(params);
  const setName = decodeURIComponent(set);
  const words = wordSets[setName]?.words || [];
  const [responses, setResponses] = useState<string[]>(() =>
    Array(words.length).fill("")
  );
  const [isSaving, setIsSaving] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Keep a stable reference to the latest responses
  const responsesRef = useRef(responses);
  useEffect(() => {
    responsesRef.current = responses;
  }, [responses]);

  // Handle next word only (navigation)
  const handleNext = useCallback(() => {
    const newResponses = [...responsesRef.current];
    newResponses[currentWordIndex] = response;
    setResponses(newResponses);

    if (currentWordIndex < words.length - 1) {
      setCurrentWordIndex((prev) => prev + 1);
      setTimeLeft(15);
      setResponse("");
      // Reset timer for next word
      timerEndRef.current = Date.now() + 15 * 1000;
    }
  }, [currentWordIndex, response, words.length]);

  // Handle final submission
  const handleSubmit = useCallback(async () => {
    if (isSubmitting) return;
    setIsSubmitting(true);

    // Stop the timer by setting isPaused to true and clearing the timer end reference
    setIsPaused(true);
    timerEndRef.current = null;

    console.log("Submitting final responses...");

    // Save the current response first
    const newResponses = [...responsesRef.current];
    newResponses[currentWordIndex] = response;
    setResponses(newResponses);

    console.log("Storing responses:", newResponses);

    // Save to localStorage with set-specific key
    localStorage.setItem(
      `wat_responses_${setName}`,
      JSON.stringify(newResponses)
    );
    localStorage.setItem("wat_current_set", setName);

    // Save to cloud storage if user is logged in
    if (user) {
      setIsSaving(true);
      try {
        const timestamp = await saveResponses(
          user.uid,
          "wat",
          setName,
          newResponses
        );
        console.log("Saved responses to Firestore, timestamp:", timestamp);
        // Save the timestamp to localStorage to prevent duplicate saves
        localStorage.setItem(
          `wat_${setName}_last_save_timestamp`,
          timestamp.toString()
        );
      } catch (error) {
        console.error("Error saving responses to cloud:", error);
        // Continue anyway since we have localStorage backup
      } finally {
        setIsSaving(false);
      }
    }

    startLoading();
    router.push(`/tests/to/wat/practice/${set}/results`);
  }, [
    currentWordIndex,
    response,
    router,
    set,
    setName,
    user,
    startLoading,
    isSubmitting,
  ]);

  // Custom timer implementation that won't reset on re-renders
  useEffect(() => {
    // Don't run the timer if paused
    if (isPaused) {
      return;
    }

    // Initialize the timer end time if it's not set
    if (timerEndRef.current === null) {
      timerEndRef.current = Date.now() + 15 * 1000;
    }

    const intervalId = setInterval(() => {
      const now = Date.now();
      const endTime = timerEndRef.current as number;
      const remaining = Math.max(0, Math.floor((endTime - now) / 1000));

      setTimeLeft(remaining);

      if (remaining === 0) {
        clearInterval(intervalId);

        // If not on the last word, automatically move to the next one
        if (currentWordIndex < words.length - 1) {
          handleNext();
        }
        // If on the last word, just let the timer expire and wait for manual submission
      }
    }, 100);

    return () => clearInterval(intervalId);
  }, [isPaused, handleNext, currentWordIndex, words.length]);

  // Keyboard shortcut for navigation (not submission)
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (!isPaused && event.key === "Enter" && !event.shiftKey) {
        event.preventDefault();

        // Only use Enter key to navigate between words, not to submit at the end
        if (currentWordIndex < words.length - 1) {
          handleNext();
        }
      }
    };
    window.addEventListener("keydown", handleKeyPress);
    return () => window.removeEventListener("keydown", handleKeyPress);
  }, [isPaused, handleNext, currentWordIndex, words.length]);

  const pausedTimeRef = useRef<number>(0);

  const handlePause = () => {
    const newPausedState = !isPaused;
    setIsPaused(newPausedState);

    if (newPausedState) {
      // Pausing - store the remaining time
      pausedTimeRef.current = timeLeft;
    } else {
      // Resuming - recalculate the end time
      timerEndRef.current = Date.now() + pausedTimeRef.current * 1000;
    }
  };

  // We're using a custom timer implementation instead of the TestTimer component

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      {/* We're using a custom timer implementation */}
      <div className="mx-auto max-w-3xl px-6">
        <div className="bg-white rounded-2xl shadow-sm p-8">
          <div className="flex justify-between items-center mb-8">
            <div className="text-2xl font-bold text-gray-900">
              Word {currentWordIndex + 1} of {words.length}
            </div>
            <div className="text-2xl font-bold text-indigo-600">
              {timeLeft}s
            </div>
          </div>

          <div className="text-center mb-8">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              {words[currentWordIndex]}
            </h2>
          </div>

          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Your Response:
            </label>
            <textarea
              value={response}
              onChange={(e) => setResponse(e.target.value)}
              className="w-full h-32 rounded-md border border-gray-300 px-4 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              placeholder="Write your immediate reaction..."
            />
          </div>

          {currentWordIndex < words.length - 1 ? (
            <p className="text-sm text-gray-500 mb-4">
              Press Enter to move to the next word
            </p>
          ) : (
            <p className="text-sm text-gray-500 mb-4">
              Click Submit to complete the test and view your results
            </p>
          )}

          <div className="flex justify-between items-center">
            <button
              onClick={handlePause}
              className="rounded-md bg-gray-100 px-4 py-2 text-sm font-semibold text-gray-900 hover:bg-gray-200"
            >
              {isPaused ? (
                <>
                  <PlayIcon className="h-5 w-5 inline-block mr-2" />
                  Resume
                </>
              ) : (
                <>
                  <PauseIcon className="h-5 w-5 inline-block mr-2" />
                  Pause
                </>
              )}
            </button>

            {currentWordIndex < words.length - 1 ? (
              // Show Next button if not on the last word
              <button
                onClick={handleNext}
                className="inline-flex items-center justify-center rounded-md bg-green-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
              >
                Next Word
                <ArrowRightIcon className="h-4 w-4 ml-2" />
              </button>
            ) : (
              // Show Submit button on the last word
              <button
                onClick={handleSubmit}
                disabled={isSubmitting}
                className="inline-flex items-center justify-center rounded-md bg-green-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? "Submitting..." : "Submit Test"}
                <CheckIcon className="h-4 w-4 ml-2" />
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
