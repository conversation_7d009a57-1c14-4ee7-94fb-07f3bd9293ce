"use client";

import React, { useState } from "react";
import Link from "next/link";
import {
  CheckIcon,
  ChevronDownIcon,
  ChevronUpIcon,
} from "@heroicons/react/24/outline";

interface GuidelinesAcceptanceProps {
  accepted: boolean;
  onAcceptChange: (accepted: boolean) => void;
}

export default function GuidelinesAcceptance({
  accepted,
  onAcceptChange,
}: GuidelinesAcceptanceProps) {
  const [expanded, setExpanded] = useState(false);

  return (
    <div className="bg-gray-50 rounded-md p-4 sm:p-5 border border-gray-200">
      <div className="flex justify-between items-center">
        <h3 className="text-sm font-medium text-gray-900">
          Community Guidelines
        </h3>
        <button
          type="button"
          onClick={() => setExpanded(!expanded)}
          className="text-gray-500 hover:text-gray-700"
        >
          {expanded ? (
            <ChevronUpIcon className="h-5 w-5" />
          ) : (
            <ChevronDownIcon className="h-5 w-5" />
          )}
        </button>
      </div>

      {expanded && (
        <div className="mt-4 text-sm text-gray-600 space-y-3">
          <p className="px-1">
            Our community guidelines are designed to ensure a respectful and
            productive environment for all users. By posting in this forum, you
            agree to:
          </p>
          <ul className="list-disc pl-6 space-y-2">
            <li>
              <strong>Be respectful:</strong> Treat others with respect and
              courtesy, even in disagreement.
            </li>
            <li>
              <strong>No hate speech or harassment:</strong> Content that
              promotes hatred, harassment, or discrimination is not allowed.
            </li>
            <li>
              <strong>No exam cheating:</strong> Do not share actual exam
              questions, answers, or materials that would constitute cheating.
            </li>
            <li>
              <strong>Protect privacy:</strong> Do not share personally
              identifiable information about yourself or others.
            </li>
            <li>
              <strong>No explicit content:</strong> Do not post explicit,
              inappropriate, or offensive content.
            </li>
            <li>
              <strong>Stay on topic:</strong> Keep discussions relevant to
              military selection and preparation.
            </li>
            <li>
              <strong>No spam:</strong> Do not post promotional content,
              advertisements, or spam.
            </li>
          </ul>
          <p>
            Violations of these guidelines may result in content removal,
            temporary suspension, or permanent banning from the forum.
          </p>
          <p>
            <Link
              href="/forum/guidelines"
              className="text-indigo-600 hover:text-indigo-500"
              target="_blank"
            >
              Read the full Community Guidelines
            </Link>
          </p>
        </div>
      )}

      <div className="mt-5 flex items-start">
        <div className="flex items-center h-6">
          <input
            id="guidelines"
            name="guidelines"
            type="checkbox"
            checked={accepted}
            onChange={(e) => onAcceptChange(e.target.checked)}
            className="h-5 w-5 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
          />
        </div>
        <div className="ml-3 text-sm">
          <label htmlFor="guidelines" className="font-medium text-gray-700">
            I have read and agree to follow the community guidelines
          </label>
        </div>
      </div>
    </div>
  );
}
