"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import {
  ArrowLeftIcon,
  BookOpenIcon,
  MagnifyingGlassIcon,
} from "@heroicons/react/24/outline";
import SimpleButton from "@/components/SimpleButton";
import { sampleAnswers } from "@/data/sample_answers_output";

// Sample answer data organized alphabetically

export default function WATSampleAnswers() {
  const router = useRouter();
  const [activeLetters, setActiveLetters] = useState<string[]>(["A"]);
  const [searchTerm, setSearchTerm] = useState("");

  // Set active letter (only one at a time)
  const setActiveLetter = (letter: string) => {
    setActiveLetters([letter]);
  };

  // Filter words based on search term
  const filteredSampleAnswers = Object.entries(sampleAnswers).reduce(
    (acc, [letter, words]) => {
      const filteredWords = words.filter(
        (item) =>
          item.word.toLowerCase().includes(searchTerm.toLowerCase()) ||
          item.association.toLowerCase().includes(searchTerm.toLowerCase())
      );
      if (filteredWords.length > 0) {
        acc[letter] = filteredWords;
      }
      return acc;
    },
    {} as Record<string, typeof sampleAnswers.A>
  );

  // If search is active, show all matching letters
  // Otherwise, only show the selected letter
  const displayLetters = searchTerm
    ? Object.keys(filteredSampleAnswers)
    : activeLetters;

  return (
    <div className="min-h-screen bg-gray-50 py-6 sm:py-12">
      <div className="mx-auto max-w-4xl px-4 sm:px-6">
        <SimpleButton
          href="/tests/to/wat/practice"
          className="mb-4 sm:mb-8 flex items-center text-gray-600 hover:text-gray-900"
        >
          <ArrowLeftIcon className="h-5 w-5 mr-2" />
          <span className="text-sm sm:text-base">Back to WAT Practice</span>
        </SimpleButton>

        <div className="bg-white rounded-xl sm:rounded-2xl shadow-sm p-4 sm:p-6 md:p-8">
          <div className="flex flex-col sm:flex-row sm:items-center mb-4 sm:mb-6">
            <BookOpenIcon className="h-7 w-7 sm:h-8 sm:w-8 text-amber-600 mb-2 sm:mb-0 sm:mr-3" />
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">
              WAT Sample Answers
            </h1>
          </div>

          <div className="mb-6 sm:mb-8 text-gray-700">
            <p className="mb-4 text-sm sm:text-base">
              This page provides sample associations for common WAT stimulus
              words, organized alphabetically. These examples demonstrate
              positive, contextually relevant associations that reflect
              qualities valued in military and leadership contexts.
            </p>

            {/* Special highlight for negative words */}
            <div className="bg-red-50 p-4 sm:p-6 rounded-lg border-2 border-red-200 mb-4">
              <h3 className="font-bold text-red-800 mb-3 text-base sm:text-lg flex items-center">
                <span className="bg-red-600 text-white px-2 py-1 rounded text-xs mr-2">
                  IMPORTANT
                </span>
                Negative Words Section - Critical for Exams
              </h3>
              <p className="text-red-700 mb-3 text-sm sm:text-base">
                The <strong>Negative Words</strong> section contains 100
                carefully selected negative stimulus words that frequently
                appear in WAT exams. These words are particularly challenging
                and require positive, constructive responses that demonstrate
                emotional maturity and leadership qualities.
              </p>
              <div className="bg-red-100 p-3 rounded border border-red-300">
                <p className="text-red-800 font-semibold text-xs sm:text-sm">
                  💡 Pro Tip: Master these negative word responses as they often
                  determine your psychological assessment score!
                </p>
              </div>
            </div>

            <div className="bg-amber-50 p-3 sm:p-4 rounded-lg border border-amber-200 mb-4">
              <h3 className="font-semibold text-amber-800 mb-2 text-sm sm:text-base">
                How to use these samples:
              </h3>
              <ul className="list-disc list-inside space-y-1 text-amber-800 text-xs sm:text-sm">
                <li>Study the pattern of positive, meaningful associations</li>
                <li>
                  Notice how associations reflect leadership and military values
                </li>
                <li>
                  Use these as inspiration, but develop your own authentic
                  responses
                </li>
                <li>
                  Remember that in the actual test, you should respond
                  spontaneously
                </li>
                <li className="font-semibold">
                  Pay special attention to the Negative Words section - these
                  are exam favorites!
                </li>
              </ul>
            </div>
          </div>

          {/* Search bar */}
          <div className="relative mb-4 sm:mb-6">
            <div className="absolute inset-y-0 left-0 pl-2 sm:pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search words or associations..."
              className="w-full text-sm sm:text-base pl-8 sm:pl-10 pr-16 sm:pr-24 py-2 sm:py-3 border border-gray-300 rounded-md sm:rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            {searchTerm && (
              <button
                onClick={() => setSearchTerm("")}
                className="absolute right-2 sm:right-3 inset-y-0 my-auto h-7 sm:h-8 px-2 sm:px-3 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded text-xs sm:text-sm font-medium"
              >
                Clear
              </button>
            )}
          </div>

          {/* Alphabet navigation */}
          <div className="flex flex-wrap gap-1 sm:gap-2 mb-4 sm:mb-6">
            {Object.keys(sampleAnswers).map((letter) => (
              <button
                key={letter}
                onClick={() => setActiveLetter(letter)}
                className={`px-2 sm:px-3 py-1 rounded text-xs sm:text-sm font-medium ${
                  letter === "NEGATIVE_WORDS"
                    ? activeLetters.includes(letter)
                      ? "bg-red-600 text-white border-2 border-red-700 shadow-lg"
                      : "bg-red-100 text-red-800 border-2 border-red-300 hover:bg-red-200 font-bold"
                    : activeLetters.includes(letter)
                    ? "bg-amber-600 text-white"
                    : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                }`}
              >
                {letter === "NEGATIVE_WORDS" ? "⚠️ NEGATIVE WORDS" : letter}
              </button>
            ))}
          </div>

          {/* Sample answers by letter */}
          <div className="space-y-6 sm:space-y-8">
            {Object.keys(filteredSampleAnswers).length === 0 && searchTerm ? (
              <div className="text-center py-6 sm:py-8 border-t">
                <p className="text-gray-600 text-base sm:text-lg">
                  No results found for &quot;{searchTerm}&quot;. Try a different
                  search term.
                </p>
              </div>
            ) : (
              Object.entries(filteredSampleAnswers).map(
                ([letter, words]) =>
                  displayLetters.includes(letter) && (
                    <div
                      key={letter}
                      className={`border-t pt-4 sm:pt-6 ${
                        letter === "NEGATIVE_WORDS"
                          ? "bg-red-50 border-red-200 rounded-lg p-4 sm:p-6"
                          : ""
                      }`}
                    >
                      <h2
                        className={`text-xl sm:text-2xl font-bold mb-3 sm:mb-4 ${
                          letter === "NEGATIVE_WORDS"
                            ? "text-red-700 flex items-center"
                            : "text-amber-700"
                        }`}
                      >
                        {letter === "NEGATIVE_WORDS" && (
                          <span className="bg-red-600 text-white px-2 py-1 rounded text-sm mr-3">
                            CRITICAL
                          </span>
                        )}
                        {letter === "NEGATIVE_WORDS"
                          ? "⚠️ NEGATIVE WORDS (100 Essential Words)"
                          : letter}
                      </h2>
                      {letter === "NEGATIVE_WORDS" && (
                        <div className="bg-red-100 p-3 rounded border border-red-300 mb-4">
                          <p className="text-red-800 text-xs sm:text-sm font-medium">
                            🎯 These 100 negative words are frequently tested in
                            WAT exams. Study how each negative word is
                            transformed into a positive, constructive response.
                          </p>
                        </div>
                      )}
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                        {words.map((item) => (
                          <div
                            key={item.word}
                            className={`border rounded-md sm:rounded-lg p-3 sm:p-4 transition-colors ${
                              letter === "NEGATIVE_WORDS"
                                ? "border-red-200 hover:border-red-400 hover:bg-red-100 bg-white"
                                : "border-gray-200 hover:border-amber-300 hover:bg-amber-50"
                            }`}
                          >
                            <h3
                              className={`font-bold text-sm sm:text-base ${
                                letter === "NEGATIVE_WORDS"
                                  ? "text-red-900"
                                  : "text-gray-900"
                              }`}
                            >
                              {item.word}
                            </h3>
                            <p
                              className={`text-xs sm:text-sm ${
                                letter === "NEGATIVE_WORDS"
                                  ? "text-red-700"
                                  : "text-gray-700"
                              }`}
                            >
                              {item.association}
                            </p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )
              )
            )}
          </div>

          {/* Tips section */}
          <div className="mt-8 sm:mt-12 space-y-6">
            {/* Special tips for negative words */}
            <div className="bg-red-50 p-4 sm:p-6 rounded-lg border-2 border-red-200">
              <h2 className="text-lg sm:text-xl font-bold text-red-800 mb-3 sm:mb-4 flex items-center">
                <span className="bg-red-600 text-white px-2 py-1 rounded text-xs mr-2">
                  STRATEGY
                </span>
                Mastering Negative Words in WAT
              </h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                <div>
                  <h3 className="font-semibold text-red-800 mb-2 text-sm sm:text-base">
                    Key Strategies for Negative Words:
                  </h3>
                  <ul className="list-disc list-inside space-y-1 text-red-700 text-xs sm:text-sm">
                    <li>Transform negative into positive context</li>
                    <li>Show problem-solving and resilience</li>
                    <li>Demonstrate emotional maturity</li>
                    <li>Focus on overcoming or learning from challenges</li>
                    <li>Highlight leadership qualities in adversity</li>
                  </ul>
                </div>
                <div>
                  <h3 className="font-semibold text-red-800 mb-2 text-sm sm:text-base">
                    Examples of Transformation:
                  </h3>
                  <ul className="list-disc list-inside space-y-1 text-red-700 text-xs sm:text-sm">
                    <li>
                      <strong>FEAR</strong> → "Courage overcomes fear"
                    </li>
                    <li>
                      <strong>FAILURE</strong> → "Learning opportunity for
                      growth"
                    </li>
                    <li>
                      <strong>CRISIS</strong> → "Tests leadership abilities"
                    </li>
                    <li>
                      <strong>WEAKNESS</strong> → "Identifies areas for
                      improvement"
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            {/* General tips */}
            <div className="bg-gray-100 p-4 sm:p-6 rounded-md sm:rounded-lg">
              <h2 className="text-lg sm:text-xl font-bold text-gray-900 mb-3 sm:mb-4">
                General Tips for Effective WAT Responses
              </h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                <div>
                  <h3 className="font-semibold text-gray-800 mb-2 text-sm sm:text-base">
                    Do:
                  </h3>
                  <ul className="list-disc list-inside space-y-1 text-gray-700 text-xs sm:text-sm">
                    <li>Respond with your first genuine association</li>
                    <li>Keep responses concise (under 10 words)</li>
                    <li>Show positive character traits when possible</li>
                    <li>Demonstrate emotional intelligence</li>
                    <li>Consider the military/leadership context</li>
                    <li className="font-semibold text-red-700">
                      Practice negative words extensively!
                    </li>
                  </ul>
                </div>
                <div className="mt-3 sm:mt-0">
                  <h3 className="font-semibold text-gray-800 mb-2 text-sm sm:text-base">
                    Don&apos;t:
                  </h3>
                  <ul className="list-disc list-inside space-y-1 text-gray-700 text-xs sm:text-sm">
                    <li>Overthink your responses</li>
                    <li>Use overly complex or technical language</li>
                    <li>Give unrelated or random associations</li>
                    <li>Repeat the same association pattern</li>
                    <li>
                      Try to memorize <strong>correct</strong> answers
                    </li>
                    <li className="font-semibold text-red-700">
                      Give purely negative responses to negative words
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
