"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import { useRouter } from "next/navigation";
import NavigationButton from "@/components/NavigationButton";
import LoadingButton from "@/components/LoadingButton";
import SimpleButton from "@/components/SimpleButton";
import { useLoading } from "@/context/LoadingContext";
import { useAuth } from "@/context/GoogleAuthContext";
import {
  ArrowLeftIcon,
  PlayIcon,
  PauseIcon,
  ArrowRightIcon,
  CheckIcon,
} from "@heroicons/react/24/outline";
import { use } from "react";
import { situationSets } from "@/data/situationsSets";
import { saveResponses } from "@/services/responseService";

export default function SRTTest({
  params,
}: {
  params: Promise<{ set: string }>;
}) {
  const router = useRouter();
  const { startLoading } = useLoading();
  const { user } = useAuth();
  const { set } = use(params);
  const [currentSituation, setCurrentSituation] = useState(0);
  const [timeLeft, setTimeLeft] = useState(30);
  const timerEndRef = useRef<number | null>(null);
  const [response, setResponse] = useState("");
  const [isActive, setIsActive] = useState(false);
  const [responses, setResponses] = useState<string[]>([]);
  const [isPaused, setIsPaused] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Get the current set data
  useEffect(() => {
    const setName = decodeURIComponent(set);

    // Check if the set exists
    if (situationSets[setName]) {
      // Initialize responses array based on the number of situations
      setResponses(Array(situationSets[setName].situations.length).fill(""));
      setLoading(false);
      return;
    }

    // If we get here, the set wasn't found
    setError(`Set "${setName}" not found. Please select a valid set.`);
    setLoading(false);
  }, [set]);

  // Get the current set
  const setName = decodeURIComponent(set);
  const currentSet = situationSets[setName];

  const handleNext = useCallback(() => {
    if (!currentSet || !currentSet.situations) return;

    // Save the current response
    const newResponses = [...responses];
    newResponses[currentSituation] = response;
    setResponses(newResponses);

    // Move to the next situation
    if (currentSituation < currentSet.situations.length - 1) {
      setCurrentSituation((prev) => prev + 1);
      setTimeLeft(30);
      setResponse("");
      // Reset timer for next situation
      timerEndRef.current = Date.now() + 30 * 1000;
    }
  }, [currentSituation, response, responses, currentSet]);

  // New function to handle final submission
  const handleSubmit = useCallback(async () => {
    if (isSubmitting) return;
    setIsSubmitting(true);

    // Stop the timer by setting isPaused to true and clearing the timer end reference
    setIsPaused(true);
    timerEndRef.current = null;

    console.log("Submitting final responses...");

    // Save the current response first
    const newResponses = [...responses];
    newResponses[currentSituation] = response;

    // Save to localStorage with set-specific key
    localStorage.setItem(
      `srt_responses_${setName}`,
      JSON.stringify(newResponses)
    );
    localStorage.setItem("srt_responses", JSON.stringify(newResponses)); // Keep for backward compatibility
    localStorage.setItem("srt_current_set", setName);

    // Save to cloud storage if user is logged in
    if (user) {
      setIsSaving(true);
      try {
        console.log("Saving responses to Firestore...");
        const timestamp = await saveResponses(
          user.uid,
          "srt",
          setName,
          newResponses
        );
        console.log("Saved responses to Firestore, timestamp:", timestamp);
        // Save the timestamp to localStorage
        localStorage.setItem(
          `srt_${setName}_last_save_timestamp`,
          timestamp.toString()
        );
      } catch (error) {
        console.error("Error saving responses to cloud:", error);
        // Continue anyway since we have localStorage backup
      } finally {
        setIsSaving(false);
      }
    }

    startLoading();
    router.push(`/tests/to/srt/practice/${set}/results`);
  }, [
    currentSituation,
    response,
    responses,
    router,
    set,
    setName,
    user,
    isSubmitting,
  ]);

  const handlePause = () => {
    setIsPaused(!isPaused);

    // If pausing, store the current time left
    // If resuming, recalculate the end time
    if (!isPaused) {
      // We're pausing
      timerEndRef.current = null;
    } else {
      // We're resuming
      timerEndRef.current = Date.now() + timeLeft * 1000;
    }
  };

  // Custom timer implementation that won't reset on re-renders
  useEffect(() => {
    let interval: NodeJS.Timeout;

    // Only run when active and not paused
    if (isActive && !isPaused) {
      // Initialize the timer end time if it's not set
      if (timerEndRef.current === null) {
        timerEndRef.current = Date.now() + 30 * 1000;
      }

      interval = setInterval(() => {
        const now = Date.now();
        const endTime = timerEndRef.current as number;
        const remaining = Math.max(0, Math.floor((endTime - now) / 1000));

        setTimeLeft(remaining);

        if (remaining === 0) {
          clearInterval(interval);

          // If not on the last situation, automatically move to the next one
          if (currentSituation < (currentSet?.situations?.length || 0) - 1) {
            handleNext();
          }
          // If on the last situation, just let the timer expire and wait for manual submission
        }
      }, 100); // Update more frequently to ensure smooth countdown
    }

    return () => clearInterval(interval);
  }, [isActive, isPaused, handleNext, currentSituation, currentSet]);

  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (isActive && !isPaused && event.key === "Enter" && !event.shiftKey) {
        event.preventDefault();

        // Only use Enter key to navigate between questions, not to submit at the end
        if (currentSituation < (currentSet?.situations?.length || 0) - 1) {
          handleNext();
        }
      }
    };

    window.addEventListener("keydown", handleKeyPress);
    return () => window.removeEventListener("keydown", handleKeyPress);
  }, [isActive, isPaused, handleNext, currentSituation, currentSet]);

  const handleStart = () => {
    setIsActive(true);
    // Initialize the timer when starting
    timerEndRef.current = Date.now() + 30 * 1000;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="mx-auto max-w-3xl px-6">
          <div className="bg-white rounded-2xl shadow-sm p-8">
            <h1 className="text-2xl font-bold text-gray-900">Loading...</h1>
            <p className="mt-4 text-gray-600">
              Please wait while we load the situation set.
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="mx-auto max-w-3xl px-6">
          <div className="bg-white rounded-2xl shadow-sm p-8">
            <h1 className="text-2xl font-bold text-gray-900">Error</h1>
            <p className="mt-4 text-gray-600">{error}</p>
            <SimpleButton
              onClick={() => router.push("/tests/to/srt/practice")}
              className="mt-6 inline-flex justify-center rounded-md bg-blue-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-700"
            >
              Back to Practice Sets
            </SimpleButton>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="mx-auto max-w-3xl px-6">
        <SimpleButton
          onClick={() => router.back()}
          className="mb-8 flex items-center text-gray-600 hover:text-gray-900"
        >
          <ArrowLeftIcon className="h-5 w-5 mr-2" />
          Back to Instructions
        </SimpleButton>

        <div className="bg-white rounded-2xl shadow-sm p-8">
          <div className="flex justify-between items-center mb-6">
            <div className="flex items-center space-x-4">
              {currentSet.icon && (
                <currentSet.icon className="h-8 w-8 text-indigo-600" />
              )}
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  Situation {currentSituation + 1}/
                  {currentSet.situations.length}
                </h1>
                <p className="text-sm text-gray-500">
                  {currentSet.description}
                </p>
              </div>
            </div>
            <div className="text-xl font-mono text-gray-500">
              Time: {String(timeLeft).padStart(2, "0")}s
            </div>
          </div>

          {!isActive ? (
            <div className="text-center py-12">
              <h2 className="text-xl font-semibold text-gray-500 mb-4">
                Ready to Begin {currentSet.description}?
              </h2>
              <p className="text-gray-600 mb-8">
                You will have 30 seconds for each situation. The timer will
                start when you click the button below.
              </p>
              <SimpleButton
                onClick={handleStart}
                className="inline-flex justify-center rounded-md bg-blue-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-700"
              >
                Start Test
              </SimpleButton>
            </div>
          ) : (
            <div className="space-y-6">
              <div className="bg-gray-50 p-6 rounded-lg">
                <p className="text-lg text-gray-900">
                  {currentSet.situations[currentSituation]}
                </p>
              </div>

              <div>
                <label htmlFor="response" className="sr-only">
                  Your response
                </label>
                <textarea
                  id="response"
                  name="response"
                  rows={4}
                  className="block w-full rounded-md border-0 py-2 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600"
                  placeholder="Type your response here..."
                  value={response}
                  onChange={(e) => setResponse(e.target.value)}
                  disabled={isPaused}
                ></textarea>
              </div>

              <div className="flex flex-col space-y-4">
                {currentSituation < currentSet.situations.length - 1 ? (
                  <p className="text-sm text-gray-500">
                    Press Enter to move to the next situation
                  </p>
                ) : (
                  <p className="text-sm text-gray-500">
                    Click Submit to complete the test and view your results
                  </p>
                )}

                <div className="flex justify-between items-center">
                  <SimpleButton
                    onClick={handlePause}
                    className="rounded-md bg-gray-100 px-4 py-2 text-sm font-semibold text-gray-900 hover:bg-gray-200"
                  >
                    {isPaused ? (
                      <>
                        <PlayIcon className="h-5 w-5 inline-block mr-2" />
                        Resume
                      </>
                    ) : (
                      <>
                        <PauseIcon className="h-5 w-5 inline-block mr-2" />
                        Pause
                      </>
                    )}
                  </SimpleButton>

                  {currentSituation < currentSet.situations.length - 1 ? (
                    // Show Next button if not on the last situation
                    <SimpleButton
                      onClick={handleNext}
                      className="inline-flex items-center justify-center rounded-md bg-blue-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    >
                      Next Situation
                      <ArrowRightIcon className="h-4 w-4 ml-2" />
                    </SimpleButton>
                  ) : (
                    // Show Submit button on the last situation
                    <LoadingButton
                      onClick={handleSubmit}
                      disabled={isSubmitting}
                      className="inline-flex items-center justify-center rounded-md bg-green-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                      loadingText="Submitting..."
                      isLoading={isSubmitting}
                    >
                      Submit Test
                      <CheckIcon className="h-4 w-4 ml-2" />
                    </LoadingButton>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
