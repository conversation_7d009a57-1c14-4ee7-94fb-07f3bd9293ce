"use client";

import { useRouter } from "next/navigation";
import SimpleButton from "@/components/SimpleButton";
import { useLoading } from "@/context/LoadingContext";
import {
  ArrowLeftIcon,
  DocumentArrowDownIcon,
  LockClosedIcon,
} from "@heroicons/react/24/outline";
import { use } from "react";
import { useState, useEffect } from "react";
import { tatSets } from "../../../../../../../data/tat-sets";
import { generateImprovedTATPDF } from "@/utils/improvedTatPdfGenerator";
import { militaryTraitCategories } from "@/utils/evaluationUtils";
import SimplePasswordProtectedDownload from "@/components/SimplePasswordProtectedDownload";
import { useAuth } from "@/context/GoogleAuthContext";
import {
  getResponses,
  saveResponses,
  TestResponse,
} from "@/services/responseService";
import AIAnalysisSection from "@/components/AIAnalysisSection";

// Add download function
const downloadPDF = async (
  setName: string,
  responses: string[],
  images: string[],
  setIsGeneratingPDF: (isGenerating: boolean) => void,
  setGenerationStatus: (status: string) => void
) => {
  try {
    // Show loading indicator
    setIsGeneratingPDF(true);
    setGenerationStatus("Starting PDF generation...");
    console.log("Generating PDF with images, please wait...");

    // Generate the PDF with our improved generator that handles status updates
    const blob = await generateImprovedTATPDF(
      setName,
      responses,
      images,
      setGenerationStatus
    );

    // Create a download link
    setGenerationStatus("PDF generated successfully! Downloading...");
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `TAT_Set_${setName}_Results.pdf`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Clean up the URL object
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error("Error generating PDF:", error);
    alert("There was an error generating the PDF. Please try again.");
  } finally {
    // Hide loading indicator
    setIsGeneratingPDF(false);
    setGenerationStatus("");
  }
};

export default function TATResults({
  params,
}: {
  params: Promise<{ set: string }>;
}) {
  const router = useRouter();
  const { startLoading } = useLoading();
  const { user } = useAuth();
  const { set } = use(params);
  const setName = decodeURIComponent(set);
  const [responses, setResponses] = useState<string[]>([]);
  // Removed overall score state as analysis is no longer included in PDFs
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);
  const [generationStatus, setGenerationStatus] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [currentTimestamp, setCurrentTimestamp] = useState<number>(Date.now());
  const [selectedHistoricalResponse, setSelectedHistoricalResponse] =
    useState<TestResponse | null>(null);
  const images = tatSets[setName]?.images || [];

  // Handle selecting a historical response
  const handleSelectHistoricalResponse = (response: TestResponse) => {
    setSelectedHistoricalResponse(response);

    if (response.responses) {
      // Ensure responses array is the same length as images array
      const paddedResponses = [...response.responses];
      while (paddedResponses.length < images.length) {
        paddedResponses.push("");
      }

      setResponses(paddedResponses);

      // Update the current timestamp for AI analysis
      if (response.timestamp) {
        setCurrentTimestamp(response.timestamp);
        console.log(
          "Updated timestamp for historical response:",
          response.timestamp
        );
      }
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        let responseData = null;
        let parsedResponses: string[] = [];

        // Try to get responses from Firestore if user is logged in
        if (user) {
          try {
            responseData = await getResponses(user.uid, "tat", setName);

            if (responseData && responseData.responses) {
              parsedResponses = responseData.responses;
              console.log("Loaded responses from Firestore:", parsedResponses);

              // Set the current timestamp to the response timestamp for AI analysis
              if (responseData.timestamp) {
                setCurrentTimestamp(responseData.timestamp);
                console.log(
                  "Using response timestamp for AI analysis:",
                  responseData.timestamp
                );
              }
            }
          } catch (error) {
            console.error("Error fetching from Firestore:", error);
            // Continue to try localStorage
          }
        }

        // If no data from Firestore, try localStorage
        if (!responseData) {
          // Try the set-specific key first
          const savedResponses = localStorage.getItem(
            `tat_responses_${setName}`
          );
          // Fall back to the generic key for backward compatibility
          const genericSavedResponses = localStorage.getItem("tat_responses");
          const storedSetName = localStorage.getItem("tat_current_set");

          if (savedResponses) {
            parsedResponses = JSON.parse(savedResponses);
            console.log(
              "Loaded responses from set-specific localStorage:",
              parsedResponses
            );
          } else if (genericSavedResponses && storedSetName === setName) {
            parsedResponses = JSON.parse(genericSavedResponses);
            console.log(
              "Loaded responses from generic localStorage:",
              parsedResponses
            );
          }
        }

        // Ensure responses array is the same length as images array
        const paddedResponses = [...parsedResponses];
        while (paddedResponses.length < images.length) {
          paddedResponses.push("");
        }

        setResponses(paddedResponses);

        // If user is logged in and we got responses from localStorage but not Firestore,
        // save them to Firestore, but check if they were already saved recently
        if (user && parsedResponses.length > 0 && !responseData) {
          // Check if we have a recent save timestamp in localStorage
          const lastSaveTimestamp = localStorage.getItem(
            `tat_${setName}_last_save_timestamp`
          );
          const currentTime = new Date().getTime();
          const fiveMinutesAgo = currentTime - 5 * 60 * 1000; // 5 minutes in milliseconds

          // Only save if we don't have a timestamp or it's older than 5 minutes
          if (
            !lastSaveTimestamp ||
            parseInt(lastSaveTimestamp) < fiveMinutesAgo
          ) {
            setIsSaving(true);
            try {
              const timestamp = await saveResponses(
                user.uid,
                "tat",
                setName,
                parsedResponses
              );
              console.log(
                "Saved responses to Firestore from results page, timestamp:",
                timestamp
              );
              // Update the last save timestamp in localStorage
              localStorage.setItem(
                `tat_${setName}_last_save_timestamp`,
                timestamp.toString()
              );

              // Update the current timestamp for AI analysis
              setCurrentTimestamp(timestamp);
              console.log(
                "Updated current timestamp for AI analysis:",
                timestamp
              );
            } catch (error) {
              console.error("Error saving responses to Firestore:", error);
            } finally {
              setIsSaving(false);
            }
          } else {
            console.log(
              "Skipping save to Firestore - responses were saved recently"
            );
          }
        }
      } catch (error) {
        console.error("Error in fetchData:", error);
        setError("Failed to load responses. Please try again.");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [images, user, setName]);

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="mx-auto max-w-4xl px-6">
        <SimpleButton
          onClick={() => router.push("/tests/to/tat/practice")}
          className="mb-8 flex items-center text-gray-600 hover:text-gray-900"
        >
          <ArrowLeftIcon className="h-5 w-5 mr-2" />
          Back to Practice Sets
        </SimpleButton>

        <div className="space-y-8">
          {/* Results Header */}
          <div className="bg-white rounded-2xl shadow-sm p-4 sm:p-8">
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-4 sm:mb-6 text-center sm:text-left">
              TAT Practice Set {setName} - Results
            </h1>
            <p className="text-gray-600 text-center sm:text-left">
              Below are your responses to each image in the Thematic
              Apperception Test.
            </p>
          </div>

          {/* PDF Download Section */}
          <div className="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-2xl shadow-lg p-4 sm:p-8 text-white">
            <div className="flex flex-col sm:flex-row items-center sm:items-start">
              <div className="flex-1">
                <h2 className="text-xl sm:text-2xl font-bold mb-4 text-center sm:text-left">
                  Download Your Responses
                </h2>
                <p className="mb-4 text-center sm:text-left">
                  Get a PDF copy of your responses for offline review:
                </p>
                <ul className="space-y-2 mb-6 text-sm sm:text-base">
                  <li className="flex items-center">
                    <span className="mr-2 text-yellow-300">✓</span> Your
                    complete responses to all TAT images
                  </li>
                  <li className="flex items-center">
                    <span className="mr-2 text-yellow-300">✓</span> Convenient
                    PDF format for offline review
                  </li>
                  <li className="flex items-center">
                    <span className="mr-2 text-yellow-300">✓</span> Organized
                    presentation with images and responses
                  </li>
                  <li className="flex items-center">
                    <span className="mr-2 text-yellow-300">✓</span> Printable
                    format for your records
                  </li>
                </ul>
                <div className="flex justify-center sm:justify-start">
                  <SimplePasswordProtectedDownload
                    onDownload={() =>
                      downloadPDF(
                        setName,
                        responses,
                        images,
                        setIsGeneratingPDF,
                        setGenerationStatus
                      )
                    }
                    buttonClassName="inline-flex justify-center rounded-md bg-white px-4 sm:px-6 py-2 sm:py-3 text-sm font-semibold text-indigo-600 shadow-sm hover:bg-gray-100 w-full sm:w-auto"
                    isLoading={isGeneratingPDF}
                    statusMessage={generationStatus}
                  >
                    <LockClosedIcon className="h-5 w-5 mr-1" />
                    <DocumentArrowDownIcon className="h-5 w-5 mr-2" />
                    Download Responses PDF
                  </SimplePasswordProtectedDownload>
                </div>
              </div>
              <div className="hidden sm:block md:w-1/4 mt-4 sm:mt-0">
                <div className="rounded-full bg-white/20 p-6 backdrop-blur-sm">
                  <div className="text-center">
                    <div className="text-4xl font-bold mb-1">100%</div>
                    <div className="text-sm">Free</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Summary Section */}
          <div className="bg-white rounded-2xl shadow-sm p-4 sm:p-8">
            <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-4 sm:mb-6 text-center sm:text-left">
              Test Summary
            </h2>
            <p className="text-gray-600 mb-6 text-center sm:text-left">
              You have responded to{" "}
              {responses.filter((r) => r && r.trim() !== "").length} out of{" "}
              {images.length} images. Use the download button above to view your
              complete responses.
            </p>

            {selectedHistoricalResponse && (
              <div className="bg-red-50 p-4 rounded-lg mb-4">
                <p className="text-red-800 font-medium text-center sm:text-left">
                  Viewing historical attempt from{" "}
                  {selectedHistoricalResponse.timestamp
                    ? new Date(
                        selectedHistoricalResponse.timestamp
                      ).toLocaleString()
                    : "an earlier date"}
                </p>
                <div className="flex justify-center sm:justify-start">
                  <button
                    onClick={() => window.location.reload()}
                    className="mt-2 text-red-600 text-sm font-medium hover:text-red-800"
                  >
                    Return to latest attempt
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* AI Analysis Section */}
          <AIAnalysisSection
            responses={responses.filter((r) => r && r.trim() !== "")}
            testType="TAT"
            setName={set}
            responseTimestamp={currentTimestamp}
          />

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row justify-between space-y-4 sm:space-y-0">
            <SimpleButton
              onClick={() => router.push(`/tests/to/tat/practice/${set}/test`)}
              className="inline-flex justify-center rounded-md bg-red-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-700 w-full sm:w-auto"
            >
              Retry Test
            </SimpleButton>
            <SimpleButton
              onClick={() => router.push("/tests/to/tat/practice")}
              className="inline-flex justify-center rounded-md bg-white px-4 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 w-full sm:w-auto"
            >
              Try Another Set
            </SimpleButton>
          </div>
        </div>
      </div>
    </div>
  );
}
