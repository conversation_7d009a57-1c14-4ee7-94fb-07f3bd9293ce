"use client";

import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import SimpleButton from "@/components/SimpleButton";
import { useLoading } from "@/context/LoadingContext";
import {
  ArrowLeftIcon,
  PlayIcon,
  BookOpenIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  MagnifyingGlassIcon,
} from "@heroicons/react/24/outline";
import { wordSets } from "@/data/wat-sets";
import SetManagementButton from "@/components/SetManagementButton";
import AddSetModal from "@/components/AddSetModal";
import { useAuth } from "@/context/GoogleAuthContext";
import { useRoleCheck } from "@/utils/authUtils";
import { addSet, getSets, SetData } from "@/services/setManagementService";

export default function WATPractice() {
  const router = useRouter();
  const { startLoading } = useLoading();
  const { user } = useAuth();
  const { isAdmin } = useRoleCheck();
  const [isAddSetModalOpen, setIsAddSetModalOpen] = useState(false);
  const [customSets, setCustomSets] = useState<SetData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load custom sets from Firestore
  useEffect(() => {
    const loadCustomSets = async () => {
      if (!user) {
        setIsLoading(false);
        return;
      }

      try {
        const sets = await getSets("wat");
        setCustomSets(sets);
      } catch (error) {
        console.error("Error loading custom WAT sets:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadCustomSets();
  }, [user]);

  const handleSetSelect = (setName: string) => {
    startLoading();
    router.push(`/tests/to/wat/practice/${setName}/instructions`);
  };

  const handleAddSet = async (setData: SetData) => {
    if (!user) return;
    setError(null);

    try {
      await addSet(user.uid, "wat", setData);

      // Reload custom sets
      const sets = await getSets("wat");
      setCustomSets(sets);
    } catch (error) {
      console.error("Error adding WAT set:", error);
      if (error instanceof Error) {
        setError(error.message);
      } else {
        setError("An unknown error occurred");
      }
      throw error;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-6 sm:py-12">
      <div className="mx-auto max-w-3xl px-4 sm:px-6">
        <SimpleButton
          onClick={() => router.back()}
          className="mb-4 sm:mb-8 flex items-center text-gray-600 hover:text-gray-900"
        >
          <ArrowLeftIcon className="h-5 w-5 mr-2" />
          <span className="text-sm sm:text-base">Back to TO Test</span>
        </SimpleButton>

        <div className="bg-white rounded-xl sm:rounded-2xl shadow-sm p-4 sm:p-6 md:p-8">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-4 sm:mb-6">
            Word Association Test Practice
          </h1>

          <div className="space-y-4 sm:space-y-6 text-gray-700">
            {/* Sample Answers Link */}
            <div className="bg-amber-50 p-3 sm:p-4 rounded-lg border border-amber-200 mb-4 sm:mb-6">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-0">
                <div className="flex items-start sm:items-center">
                  <BookOpenIcon className="h-5 w-5 sm:h-6 sm:w-6 text-amber-600 mr-2 sm:mr-3 mt-0.5 sm:mt-0 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-amber-800 mb-0.5 sm:mb-1 text-sm sm:text-base">
                      Need help with WAT responses?
                    </h3>
                    <p className="text-xs sm:text-sm text-amber-700">
                      View sample associations for common WAT stimulus words
                    </p>
                  </div>
                </div>
                <SimpleButton
                  onClick={() => router.push("/tests/to/wat/sample-answers")}
                  className="w-full sm:w-auto px-3 sm:px-4 py-1.5 sm:py-2 text-xs sm:text-sm bg-amber-600 text-white rounded hover:bg-amber-700 transition-colors text-center"
                >
                  View Sample Answers
                </SimpleButton>
              </div>
            </div>

            <div className="flex justify-between items-center mb-4 sm:mb-6">
              <h2 className="text-lg sm:text-xl font-semibold">
                Select Word Set:
              </h2>
              <SetManagementButton
                onClick={() => setIsAddSetModalOpen(true)}
                testType="WAT"
              />
            </div>

            {/* Display errors */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
                <p className="text-red-700">{error}</p>
              </div>
            )}

            {isLoading ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-green-500"></div>
              </div>
            ) : (
              <div className="grid grid-cols-1 gap-6">
                {/* Default sets from data file */}
                {Object.entries(wordSets).map(([setName, setData]) => (
                  <SimpleButton
                    key={setName}
                    onClick={() => handleSetSelect(setName)}
                    className="group relative overflow-hidden rounded-xl border border-gray-200 bg-white p-6 shadow-sm transition-all hover:border-green-500 hover:shadow-md w-full text-left"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-4">
                        <div className="rounded-lg bg-green-50 p-3 text-green-600 transition-colors group-hover:bg-green-100">
                          <setData.icon className="h-6 w-6" />
                        </div>
                        <div className="text-left">
                          <h3 className="text-lg font-semibold text-gray-900">
                            {setName}
                          </h3>
                          <p className="mt-1 text-sm text-gray-600">
                            {setData.description}
                          </p>
                          <p className="mt-2 text-sm font-medium text-green-600">
                            {setData.words.length} words
                          </p>
                        </div>
                      </div>
                      <div className="rounded-full bg-gray-50 p-2 text-gray-400 transition-colors group-hover:bg-green-50 group-hover:text-green-600">
                        <PlayIcon className="h-5 w-5" />
                      </div>
                    </div>
                    <div className="absolute inset-0 bg-gradient-to-r from-green-50/0 via-green-50/0 to-green-50/0 opacity-0 transition-opacity group-hover:opacity-10" />
                  </SimpleButton>
                ))}

                {/* Custom sets from Firestore */}
                {customSets.map((set) => (
                  <SimpleButton
                    key={set.id}
                    onClick={() => handleSetSelect(set.name)}
                    className="group relative overflow-hidden rounded-xl border border-gray-200 bg-white p-6 shadow-sm transition-all hover:border-green-500 hover:shadow-md w-full text-left"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-4">
                        <div className="rounded-lg bg-green-50 p-3 text-green-600 transition-colors group-hover:bg-green-100">
                          {/* Use a default icon for custom sets */}
                          <PlayIcon className="h-6 w-6" />
                        </div>
                        <div className="text-left">
                          <div className="flex items-center">
                            <h3 className="text-lg font-semibold text-gray-900">
                              {set.name}
                            </h3>
                            <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                              Custom
                            </span>
                          </div>
                          <p className="mt-1 text-sm text-gray-600">
                            {set.description}
                          </p>
                          <p className="mt-2 text-sm font-medium text-green-600">
                            {set.words?.length || 0} words
                          </p>
                        </div>
                      </div>
                      <div className="rounded-full bg-gray-50 p-2 text-gray-400 transition-colors group-hover:bg-green-50 group-hover:text-green-600">
                        <PlayIcon className="h-5 w-5" />
                      </div>
                    </div>
                    <div className="absolute inset-0 bg-gradient-to-r from-green-50/0 via-green-50/0 to-green-50/0 opacity-0 transition-opacity group-hover:opacity-10" />
                  </SimpleButton>
                ))}
              </div>
            )}
          </div>

          {/* Add Set Modal */}
          <AddSetModal
            testType="WAT"
            isOpen={isAddSetModalOpen}
            onClose={() => setIsAddSetModalOpen(false)}
            onAddSet={handleAddSet}
          />
        </div>
      </div>
    </div>
  );
}
