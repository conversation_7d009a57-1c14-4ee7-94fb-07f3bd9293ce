// Utility to replace all Next.js Image components with ProtectedImage components

import React from "react";
import Image, { ImageProps } from "next/image";
import ProtectedImage from "@/components/ProtectedImage";

// Define a type for props that might have children
interface PropsWithChildren {
  children?: React.ReactNode;
  [key: string]: unknown;
}

/**
 * Recursively replaces all Next.js Image components with ProtectedImage components
 * @param children React children to process
 * @returns Modified React children with protected images
 */
export const replaceImagesWithProtectedImages = (
  children: React.ReactNode
): React.ReactNode => {
  return React.Children.map(children, (child) => {
    if (!React.isValidElement(child)) {
      return child;
    }

    // Check if the child is an Image component
    if (child.type === Image) {
      // Replace with ProtectedImage, passing all props
      // We know these are ImageProps since the component is Image
      return React.createElement(ProtectedImage, {
        ...(child.props as ImageProps),
        showProtectionOverlay: true,
      });
    }

    // If the child has children, recursively process them
    const props = child.props as PropsWithChildren;
    if (props && props.children) {
      const newChildren = replaceImagesWithProtectedImages(props.children);

      // Clone the element with the new children
      return React.cloneElement(
        child,
        // Use a proper type for the props
        { ...props, children: newChildren } as React.HTMLAttributes<HTMLElement>
      );
    }

    // Return the child as is if it doesn't need processing
    return child;
  });
};
