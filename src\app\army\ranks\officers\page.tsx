"use client";

import Link from "next/link";
import Image from "next/image";
import { useState } from "react";
import {
  ArrowLeftIcon,
  XMarkIcon,
  MagnifyingGlassIcon,
} from "@heroicons/react/24/outline";

// Officer ranks data
const officerRanks = [
  {
    rank: "COAS महारथी (प्रधानसेनापती)",
    description:
      "The highest rank in the Nepal Army, equivalent to a four-star general. The Chief of Army Staff (COAS) commands the entire military force and reports directly to the President of Nepal.",
    responsibilities:
      "Strategic leadership of the entire Nepal Army and ultimate authority on military matters.",
    image: "/images/ranks/coas.png",
    category: "senior",
  },
  {
    rank: "Lieutenant General (रथी)",
    description:
      "Three-star general rank. Usually commands a corps or serves as the Chief of General Staff.",
    responsibilities: "Corps command and high-level strategic planning.",
    image: "/images/ranks/lieutenant_general.png",
    category: "senior",
  },
  {
    rank: "Major General (उप रथी)",
    description:
      "Two-star general rank. Typically commands a division or serves as head of a major military department.",
    responsibilities: "Division command and departmental leadership.",
    image: "/images/ranks/major_general.png",
    category: "senior",
  },
  {
    rank: "Brigadier General (सहायक रथी)",
    description:
      "One-star general rank. Usually commands a brigade or serves as a director of a military department.",
    responsibilities: "Brigade command and departmental direction.",
    image: "/images/ranks/brigadier_general.png",
    category: "senior",
  },
  {
    rank: "Colonel (महासेनानी)",
    description:
      "Senior field-grade officer rank. Typically commands a regiment or serves as a senior staff officer.",
    responsibilities: "Regimental command and senior staff duties.",
    image: "/images/ranks/colonel.png",
    category: "field",
  },
  {
    rank: "Lieutenant Colonel (प्रमुख सेनानी)",
    description:
      "Field-grade officer rank. Usually commands a battalion or serves as a staff officer at higher headquarters.",
    responsibilities: "Battalion command and staff planning.",
    image: "/images/ranks/lieutenant_colonel.png",
    category: "field",
  },
  {
    rank: "Major (सेनानी)",
    description:
      "Field-grade officer rank. Typically serves as a battalion executive officer, operations officer, or staff officer.",
    responsibilities: "Battalion operations and staff coordination.",
    image: "/images/ranks/major.png",
    category: "field",
  },
  {
    rank: "Captain (सह-सेनानी)",
    description:
      "Company-grade officer rank. Usually commands a company or serves as a staff officer at battalion level.",
    responsibilities: "Company command and battalion staff duties.",
    image: "/images/ranks/captain.png",
    category: "company",
  },
  {
    rank: "Lieutenant (उप सेनानी)",
    description:
      "Junior officer rank. Typically serves as a platoon leader or assistant staff officer.",
    responsibilities: "Platoon leadership and assistant staff duties.",
    image: "/images/ranks/lieutenant.png",
    category: "company",
  },
  {
    rank: "Second Lieutenant (सहायक सेनानी)",
    description:
      "Entry-level commissioned officer rank. Usually serves as a platoon leader under supervision or as an assistant staff officer.",
    responsibilities: "Junior platoon leadership and basic officer duties.",
    image: "/images/ranks/second_lieutenant.png",
    category: "company",
  },
  {
    rank: "Officer Cadet (अधिकृत क्याडेट)",
    description:
      "Pre-commissioned rank for officer trainees at the Nepali Military Academy. Not yet a commissioned officer but in training to become one.",
    responsibilities:
      "Training, learning military skills, and preparing for commissioning as an officer.",
    image: "",
    category: "training",
  },
];

// Category color mapping
const categoryColors = {
  senior: "bg-purple-500",
  field: "bg-teal-500",
  company: "bg-teal-500",
  training: "bg-white",
};

export default function OfficerRanksPage() {
  const [selectedRank, setSelectedRank] = useState<{
    image: string;
    title: string;
  } | null>(null);
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        {/* Navigation */}
        <div className="mb-8">
          <Link
            href="/army/ranks"
            className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-indigo-600"
          >
            <ArrowLeftIcon className="mr-2 h-4 w-4" />
            Back to Army Ranks
          </Link>
        </div>

        {/* Page Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl">
            Nepal Army Commissioned Officer Ranks
          </h1>
          <p className="mt-6 text-lg leading-8 text-gray-600 max-w-3xl mx-auto">
            Explore the hierarchy and responsibilities of commissioned officers
            in the Nepal Army, from Officer Cadet to General.
          </p>
        </div>

        {/* Timeline Container */}
        <div className="relative max-w-5xl mx-auto">
          {/* Vertical Center Line */}
          <div
            className="absolute left-1/2 top-0 bottom-0 w-0.5 bg-indigo-200 -translate-x-1/2"
            aria-hidden="true"
          ></div>

          {/* Timeline Items */}
          <div className="space-y-24">
            {officerRanks.map((rank, index) => (
              <div key={index} className="relative">
                {/* Center Square with Image - Always centered */}
                <div className="absolute left-1/2 top-0 -translate-x-1/2 z-20 group">
                  <div
                    className={`w-24 h-24 ${
                      categoryColors[
                        rank.category as keyof typeof categoryColors
                      ] || "bg-gray-400"
                    } rounded-lg flex items-center justify-center shadow-lg overflow-hidden border-4 border-white transition-all duration-300 cursor-pointer relative hover:scale-110 hover:shadow-xl`}
                    onClick={() => {
                      if (rank.image) {
                        setSelectedRank({
                          image: rank.image,
                          title: rank.rank,
                        });
                      }
                    }}
                  >
                    {/* Image inside square or empty for ranks without insignia */}
                    {rank.image ? (
                      <Image
                        src={rank.image}
                        alt={rank.rank}
                        width={96}
                        height={96}
                        className="object-contain w-full h-full p-1"
                        onError={(e) => {
                          // Fallback for missing images
                          const target = e.target as HTMLImageElement;
                          target.src = "/images/military-404.png";
                        }}
                      />
                    ) : (
                      <div className="flex items-center justify-center w-full h-full">
                        <span className="text-gray-500 text-xs text-center">
                          No insignia
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Tooltip - only show for ranks with images */}
                  {rank.image && (
                    <div className="absolute opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none whitespace-nowrap bg-gray-800 text-white text-xs rounded-md px-3 py-2 top-[100%] mt-2 left-1/2 -translate-x-1/2 shadow-lg flex items-center z-30 group-hover:translate-y-1">
                      <MagnifyingGlassIcon className="h-3 w-3 mr-1 flex-shrink-0" />
                      Click to view full image
                    </div>
                  )}
                </div>

                {/* Content - Alternating sides on desktop */}
                <div
                  className={`flex flex-col items-center md:items-start md:flex-row ${
                    index % 2 === 0 ? "md:justify-end" : "md:justify-start"
                  }`}
                >
                  <div
                    className={`mt-28 md:mt-0 md:w-4/12 p-4 ${
                      index % 2 === 0 ? "md:mr-28" : "md:ml-28"
                    }`}
                  >
                    <div
                      className={`bg-white shadow-lg p-6 border border-gray-200 hover:shadow-xl transition-shadow duration-300 relative
                      rounded-xl md:rounded-lg
                      ${
                        index % 2 === 0
                          ? "md:rounded-tl-none"
                          : "md:rounded-tr-none"
                      }`}
                    >
                      {/* Triangular pointed corner - desktop only */}
                      <div
                        className={`absolute w-0 h-0 hidden md:block
                        ${
                          index % 2 === 0
                            ? "top-0 left-0 border-t-0 border-r-[18px] border-b-[18px] border-l-0 border-r-transparent border-b-white -translate-x-[18px]"
                            : "top-0 right-0 border-t-0 border-r-0 border-b-[18px] border-l-[18px] border-l-transparent border-b-white translate-x-[18px]"
                        }`}
                      ></div>

                      {/* Border for the triangle */}
                      <div
                        className={`absolute w-0 h-0 hidden md:block
                        ${
                          index % 2 === 0
                            ? "top-0 left-0 border-t-0 border-r-[19px] border-b-[19px] border-l-0 border-r-transparent border-b-gray-200 -translate-x-[19px] z-[-1]"
                            : "top-0 right-0 border-t-0 border-r-0 border-b-[19px] border-l-[19px] border-l-transparent border-b-gray-200 translate-x-[19px] z-[-1]"
                        }`}
                      ></div>

                      <h3 className="text-lg font-semibold text-gray-800 mb-2">
                        {rank.rank}
                      </h3>
                      <p className="text-sm text-gray-600 leading-relaxed mb-4">
                        {rank.description}
                      </p>
                      <div className="bg-gray-50 p-3 rounded-lg">
                        <h4 className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-1">
                          Responsibilities
                        </h4>
                        <p className="text-sm text-gray-700">
                          {rank.responsibilities}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Additional Information Section */}
        <div className="mt-24 bg-white rounded-xl shadow-lg p-8 border border-gray-200 max-w-5xl mx-auto">
          <h2 className="text-2xl font-semibold text-gray-900 mb-6">
            Officer Career Progression
          </h2>
          <div className="grid gap-6 md:grid-cols-2">
            <div className="bg-indigo-50 rounded-xl p-6 border border-indigo-100">
              <h3 className="text-lg font-medium text-indigo-800 mb-3">
                Training & Education
              </h3>
              <p className="text-sm text-indigo-700 leading-relaxed">
                Nepal Army officers begin their careers at the Nepali Military
                Academy, followed by specialized training. Throughout their
                service, they attend various courses including the Army Command
                and Staff College for field-grade officers and the Army War
                College for senior officers.
              </p>
            </div>
            <div className="bg-amber-50 rounded-xl p-6 border border-amber-100">
              <h3 className="text-lg font-medium text-amber-800 mb-3">
                Promotion Timeline
              </h3>
              <p className="text-sm text-amber-700 leading-relaxed">
                Officers typically spend 3-4 years as Lieutenants, 4-5 years as
                Captains, 5-6 years as Majors, and 4-5 years as Lieutenant
                Colonels. Promotion to Colonel and above is highly competitive
                and based on merit, performance, and available vacancies.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Full-screen image modal */}
      {selectedRank && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-80 transition-opacity duration-300"
          onClick={() => setSelectedRank(null)}
        >
          <div className="relative max-w-5xl max-h-[90vh] w-full mx-4 p-4">
            <button
              className="absolute top-2 right-2 z-10 bg-white rounded-full p-2 shadow-lg hover:bg-gray-100 transition-colors"
              onClick={(e) => {
                e.stopPropagation();
                setSelectedRank(null);
              }}
            >
              <XMarkIcon className="h-6 w-6 text-gray-700" />
            </button>

            <h2 className="text-white text-2xl font-bold text-center mb-4">
              {selectedRank.title}
            </h2>

            <div className="relative w-full h-[75vh] bg-white rounded-lg overflow-hidden">
              <Image
                src={selectedRank.image}
                alt={selectedRank.title}
                fill
                className="object-contain p-4"
                priority
                sizes="(max-width: 768px) 100vw, 80vw"
                onError={(e) => {
                  // Fallback for missing images
                  const target = e.target as HTMLImageElement;
                  target.src = "/images/military-404.png";
                }}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
