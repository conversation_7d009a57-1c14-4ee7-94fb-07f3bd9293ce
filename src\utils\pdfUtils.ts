import { jsPDF } from "jspdf";
import "jspdf-autotable";
import { TraitCategories, militaryTraitCategories } from "./evaluationUtils";

// Helper function to add watermark and social links to all pages of a PDF
const addWatermark = (doc: jsPDF) => {
  const totalPages = doc.getNumberOfPages();

  // Save the current state
  const fontSize = doc.getFontSize();
  const textColor = doc.getTextColor();

  // Set watermark properties
  doc.setFontSize(10);
  doc.setTextColor(150, 150, 150); // Light gray color

  // Add watermark and links to each page
  for (let i = 1; i <= totalPages; i++) {
    doc.setPage(i);

    const pageWidth = doc.internal.pageSize.getWidth();
    const pageHeight = doc.internal.pageSize.getHeight();

    // Add a divider line
    doc.setDrawColor(200, 200, 200);
    doc.line(15, pageHeight - 15, pageWidth - 15, pageHeight - 15);

    // Add website name at the bottom right of the page
    doc.textWithLink(
      "balaramshiwakoti.com.np",
      pageWidth - 15,
      pageHeight - 10,
      {
        align: "right",
        url: "https://balaramshiwakoti.com.np",
      }
    );

    // Add Facebook link at the bottom left of the page
    doc.setTextColor(59, 89, 152); // Facebook blue color
    doc.setFontSize(12);
    doc.textWithLink("My Facebook", 15, pageHeight - 10, {
      url: "https://www.facebook.com/hercules.shiwakoti",
    });
    doc.setFontSize(10);
  }

  // Restore the original state
  doc.setFontSize(fontSize);
  doc.setTextColor(textColor);

  return doc;
};

export const generateTATPDF = (
  setName: string,
  responses: string[],
  images: string[]
) => {
  const doc = new jsPDF();

  // Add title
  doc.setFontSize(18);
  doc.text(`TAT Practice Set ${setName} - Results`, 14, 20);

  // Add date
  const today = new Date();
  const dateStr = today.toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
  doc.setFontSize(12);
  doc.setTextColor(100, 100, 100);
  doc.text(`Generated on: ${dateStr}`, 14, 35);

  // Add responses directly
  let yPos = 50;

  doc.setFontSize(16);
  doc.setTextColor(0, 0, 0);
  doc.text("Your Responses", 14, yPos);
  yPos += 10;

  for (let index = 0; index < images.length; index++) {
    // Start a new page for each image and response
    if (index > 0) {
      doc.addPage();
      yPos = 20;
    }

    doc.setFontSize(12);
    doc.setTextColor(0, 0, 0);
    doc.text(`${index + 1}. Image ${index + 1}`, 14, yPos);
    yPos += 10;

    // For blank images, add a placeholder
    if (images[index] === "blank") {
      doc.setDrawColor(200, 200, 200);
      doc.setFillColor(240, 240, 240);
      doc.rect(14, yPos, 180, 80, "F");

      doc.setTextColor(150, 150, 150);
      doc.setFontSize(14);
      doc.text("Blank Image", 104, yPos + 40, { align: "center" });
      doc.setTextColor(0, 0, 0);
      doc.setFontSize(10);

      yPos += 90;
    } else {
      // For non-blank images, add a placeholder text
      doc.setDrawColor(200, 200, 200);
      doc.setFillColor(240, 240, 240);
      doc.rect(14, yPos, 180, 80, "F");

      doc.setTextColor(100, 100, 100);
      doc.setFontSize(14);
      doc.text("Image Reference", 104, yPos + 40, { align: "center" });
      doc.setTextColor(0, 0, 0);
      doc.setFontSize(10);

      yPos += 90;
    }

    // Add the response
    doc.setFontSize(12);
    doc.setTextColor(0, 0, 0);
    doc.text("Your Story:", 14, yPos);
    yPos += 7;

    const response = responses[index] || "Not answered";
    const splitText = doc.splitTextToSize(response, 180);
    doc.text(splitText, 14, yPos);
    yPos += splitText.length * 5 + 15;
  }

  // Add watermark to all pages
  addWatermark(doc);

  return doc.output("blob");
};

export const generateSRTPDF = (
  setName: string,
  responses: string[],
  situations: string[],
  analysis: Record<string, number>,
  categories: TraitCategories
) => {
  const doc = new jsPDF();

  // Add title
  doc.setFontSize(18);
  doc.text(`SRT Practice ${setName} - Results`, 14, 20);

  // Add date
  const today = new Date();
  const dateStr = today.toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
  doc.setFontSize(12);
  doc.setTextColor(100, 100, 100);
  doc.text(`Generated on: ${dateStr}`, 14, 35);

  // Add responses directly
  let yPos = 50;

  doc.setFontSize(16);
  doc.setTextColor(0, 0, 0);
  doc.text("Your Responses", 14, yPos);
  yPos += 10;

  responses.forEach((response, index) => {
    if (yPos > 250) {
      doc.addPage();
      yPos = 20;
    }

    const situationIndex = index % situations.length;
    const situation = situations[situationIndex];

    doc.setFontSize(12);
    doc.setTextColor(0, 0, 0);
    doc.text(`${index + 1}. Situation:`, 14, yPos);
    yPos += 7;

    doc.setFontSize(10);
    doc.setTextColor(100, 100, 100);
    const situationLines = doc.splitTextToSize(situation, 180);
    doc.text(situationLines, 14, yPos);
    yPos += situationLines.length * 5 + 5;

    doc.setFontSize(10);
    doc.setTextColor(0, 0, 0);
    doc.text("Your Response:", 14, yPos);
    yPos += 7;

    const responseLines = doc.splitTextToSize(
      response || "No response provided",
      180
    );
    doc.text(responseLines, 14, yPos);
    yPos += responseLines.length * 5 + 15;
  });

  // Add watermark to all pages
  addWatermark(doc);

  return doc.output("blob");
};

export const generateWATPDF = (
  setName: string,
  responses: string[],
  words: string[]
) => {
  const doc = new jsPDF();

  // Add title
  doc.setFontSize(18);
  doc.text(`WAT Practice Set ${setName} - Results`, 14, 20);

  // Add date
  const today = new Date();
  const dateStr = today.toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
  doc.setFontSize(12);
  doc.setTextColor(100, 100, 100);
  doc.text(`Generated on: ${dateStr}`, 14, 35);

  // Add responses directly
  let yPos = 50;

  doc.setFontSize(16);
  doc.setTextColor(0, 0, 0);
  doc.text("Your Responses", 14, yPos);
  yPos += 10;

  words.forEach((word, index) => {
    if (yPos > 270) {
      doc.addPage();
      yPos = 20;
    }

    const response = responses[index] || "No response provided";
    doc.setFontSize(12);
    doc.text(`${index + 1}. ${word}:`, 14, yPos);
    yPos += 6;

    doc.setFontSize(10);
    const responseLines = doc.splitTextToSize(response, 170);
    doc.text(responseLines, 24, yPos);
    yPos += responseLines.length * 5 + 10;
  });

  // Add watermark to all pages
  addWatermark(doc);

  return doc.output("blob");
};
