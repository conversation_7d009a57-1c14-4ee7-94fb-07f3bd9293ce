"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useAuth } from "@/context/GoogleAuthContext";
import { createTopic } from "@/services/forumService";
import { checkRateLimit, resetRateLimit } from "@/utils/forumSecurity";
import { filterInappropriateContent } from "@/utils/contentModeration";
import GuidelinesAcceptance from "@/components/forum/GuidelinesAcceptance";
import {
  ArrowLeftIcon,
  ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";

// Main categories
const mainCategories = [
  { id: "to", name: "Technical Officer" },
  { id: "gto", name: "Group Testing Officer" },
  { id: "io", name: "Interviewing Officer" },
  { id: "bc", name: "Board Conference" },
  { id: "misc", name: "Miscellaneous" },
];

// TO subcategories
const toSubcategories = [
  { id: "srt", name: "SRT" },
  { id: "wat", name: "WAT" },
  { id: "tat", name: "T<PERSON>" },
  { id: "sdt", name: "SDT" },
  { id: "piq", name: "PIQ" },
];

// GTO subcategories
const gtoSubcategories = [
  { id: "gd", name: "GD" },
  { id: "gpe", name: "GPE" },
  { id: "lecturette", name: "Lecturette" },
  { id: "pgt", name: "PGT" },
  { id: "snake-race", name: "Snake Race" },
  { id: "hgt", name: "HGT" },
  { id: "individual-obstacles", name: "Individual Obstacles" },
  { id: "command-task", name: "CT" },
  { id: "fgt", name: "FGT" },
];

export default function CreateTopicPage() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [title, setTitle] = useState("");
  const [content, setContent] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedSubcategories, setSelectedSubcategories] = useState<string[]>(
    []
  );
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [guidelinesAccepted, setGuidelinesAccepted] = useState(false);

  // Check if the user is authenticated
  useEffect(() => {
    if (!loading && !user) {
      router.push("/login?redirect=/forum/create");
    }
  }, [user, loading, router]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    if (!title.trim()) {
      setError("Please enter a title for your topic.");
      return;
    }

    if (!content.trim()) {
      setError("Please enter content for your topic.");
      return;
    }

    if (!selectedCategory) {
      setError("Please select a category for your topic.");
      return;
    }

    // If TO is selected, require at least one subcategory
    if (selectedCategory === "to" && selectedSubcategories.length === 0) {
      setError("Please select at least one TO test type for your topic.");
      return;
    }

    if (!guidelinesAccepted) {
      setError("Please accept the community guidelines before posting.");
      return;
    }

    // Check rate limiting
    if (user && checkRateLimit(user.uid, "create_topic", 3, 300000)) {
      // 3 topics per 5 minutes
      setError(
        "You're posting too frequently. Please wait a few minutes before creating another topic."
      );
      return;
    }

    try {
      setIsSubmitting(true);
      setError(null);

      if (!user) {
        throw new Error("You must be logged in to create a topic.");
      }

      // Prepare tags array
      let tags: string[] = [];

      // Always add the main category
      if (selectedCategory) {
        tags.push(selectedCategory);
      }

      // If TO or GTO is selected, add the selected subcategories
      if (selectedCategory === "to" || selectedCategory === "gto") {
        tags = [...tags, ...selectedSubcategories];
      }

      // Create the topic
      const topicId = await createTopic(
        user.uid,
        user.displayName || "Anonymous",
        user.photoURL,
        title,
        content,
        tags
      );

      // Reset rate limit if there was an error
      resetRateLimit(user.uid, "create_topic");

      // Redirect to the new topic
      router.push(`/forum/topic/${topicId}`);
    } catch (error) {
      console.error("Error creating topic:", error);
      setError(
        error instanceof Error
          ? error.message
          : "An error occurred while creating your topic. Please try again."
      );
      setIsSubmitting(false);
    }
  };

  // Handle category selection
  const handleCategorySelect = (categoryId: string) => {
    // If selecting the same category, deselect it
    if (categoryId === selectedCategory) {
      setSelectedCategory(null);
      // Clear subcategories when deselecting TO or GTO
      if (categoryId === "to" || categoryId === "gto") {
        setSelectedSubcategories([]);
      }
    } else {
      setSelectedCategory(categoryId);
      // Clear subcategories when changing categories
      if (selectedCategory === "to" || selectedCategory === "gto") {
        setSelectedSubcategories([]);
      }
    }
  };

  // Handle subcategory selection
  const toggleSubcategory = (subcategoryId: string) => {
    setSelectedSubcategories((prev) =>
      prev.includes(subcategoryId)
        ? prev.filter((id) => id !== subcategoryId)
        : [...prev, subcategoryId]
    );
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="mx-auto max-w-3xl px-6">
          <div className="text-center">
            <h1 className="text-3xl font-bold tracking-tight text-gray-900">
              Loading...
            </h1>
          </div>
        </div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect in useEffect
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="mx-auto max-w-3xl px-6">
        <Link
          href="/forum"
          className="inline-flex items-center text-sm font-medium text-indigo-600 hover:text-indigo-500 mb-6"
        >
          <ArrowLeftIcon className="h-4 w-4 mr-1" />
          Back to Forum
        </Link>

        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="px-6 py-5 border-b border-gray-200">
            <h1 className="text-xl font-semibold text-gray-900">
              Create New Topic
            </h1>
          </div>

          <form onSubmit={handleSubmit} className="p-4 sm:p-6">
            {/* Error message */}
            {error && (
              <div className="mb-6 bg-red-50 border-l-4 border-red-400 p-4">
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-red-700">{error}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Title */}
            <div className="mb-6">
              <label
                htmlFor="title"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Title
              </label>
              <input
                type="text"
                id="title"
                name="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm px-4 py-3"
                placeholder="Enter a descriptive title for your topic"
                maxLength={100}
                required
              />
            </div>

            {/* Main Category */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Category (select one)
              </label>
              <div className="grid grid-cols-2 sm:flex sm:flex-wrap gap-3">
                {mainCategories.map((category) => (
                  <button
                    key={category.id}
                    type="button"
                    onClick={() => handleCategorySelect(category.id)}
                    className={`px-3 sm:px-4 py-2.5 rounded-md text-xs sm:text-sm font-medium transition-colors ${
                      selectedCategory === category.id
                        ? "bg-indigo-100 text-indigo-800 border-2 border-indigo-300"
                        : "bg-gray-100 text-gray-700 border border-gray-300 hover:bg-gray-200"
                    }`}
                  >
                    {category.name}
                  </button>
                ))}
              </div>
              <p className="mt-1 text-xs text-gray-500">
                Select the main category for your topic
              </p>
            </div>

            {/* TO Subcategories - only shown when TO is selected */}
            {selectedCategory === "to" && (
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  TO Test Type (select at least one)
                </label>
                <div className="grid grid-cols-2 sm:flex sm:flex-wrap gap-3 bg-blue-50 p-4 rounded-lg">
                  {toSubcategories.map((subcat) => (
                    <button
                      key={subcat.id}
                      type="button"
                      onClick={() => toggleSubcategory(subcat.id)}
                      className={`px-3 sm:px-4 py-2 rounded-full text-xs sm:text-sm font-medium transition-colors ${
                        selectedSubcategories.includes(subcat.id)
                          ? "bg-blue-100 text-blue-800 border border-blue-300"
                          : "bg-white text-blue-600 border border-blue-200 hover:bg-blue-50"
                      }`}
                    >
                      {subcat.name}
                    </button>
                  ))}
                </div>
                <p className="mt-1 text-xs text-gray-500">
                  Select which TO test types this topic relates to
                </p>
              </div>
            )}

            {/* GTO Subcategories - only shown when GTO is selected */}
            {selectedCategory === "gto" && (
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  GTO Task Type (select at least one)
                </label>
                <div className="grid grid-cols-2 sm:flex sm:flex-wrap gap-3 bg-green-50 p-4 rounded-lg">
                  {gtoSubcategories.map((subcat) => (
                    <button
                      key={subcat.id}
                      type="button"
                      onClick={() => toggleSubcategory(subcat.id)}
                      className={`px-3 sm:px-4 py-2 rounded-full text-xs sm:text-sm font-medium transition-colors ${
                        selectedSubcategories.includes(subcat.id)
                          ? "bg-green-100 text-green-800 border border-green-300"
                          : "bg-white text-green-600 border border-green-200 hover:bg-green-50"
                      }`}
                    >
                      {subcat.name}
                    </button>
                  ))}
                </div>
                <p className="mt-1 text-xs text-gray-500">
                  Select which GTO tasks this topic relates to
                </p>
              </div>
            )}

            {/* Content */}
            <div className="mb-6">
              <label
                htmlFor="content"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Content
              </label>
              <textarea
                id="content"
                name="content"
                value={content}
                onChange={(e) => setContent(e.target.value)}
                rows={8}
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm px-4 py-3"
                placeholder="Share your thoughts, questions, or information..."
                required
              ></textarea>
            </div>

            {/* Guidelines acceptance */}
            <GuidelinesAcceptance
              accepted={guidelinesAccepted}
              onAcceptChange={setGuidelinesAccepted}
            />

            {/* Submit button */}
            <div className="mt-8 flex flex-col sm:flex-row justify-end gap-3">
              <button
                type="button"
                onClick={() => router.push("/forum")}
                className="px-5 py-2.5 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-5 py-2.5 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:bg-indigo-400 disabled:cursor-not-allowed"
              >
                {isSubmitting ? "Creating..." : "Create Topic"}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
