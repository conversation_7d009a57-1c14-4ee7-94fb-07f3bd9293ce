"use client";

import Link from "next/link";
import {
  ArrowRightIcon,
  ClockIcon,
  UserGroupIcon,
  StarIcon,
  TrophyIcon,
} from "@heroicons/react/24/outline";

// Define the content sections for the grid - only include developed sections
const armySections = [
  {
    title: "Nepal Army History",
    description:
      "Learn about the rich history of Nepal Army from its founding to present day",
    href: "/army/history",
    icon: ClockIcon,
    bgColor: "bg-indigo-50",
    textColor: "text-indigo-700",
    borderColor: "border-indigo-200",
    hoverBg: "hover:bg-indigo-100",
    iconBg: "bg-indigo-100",
  },
  {
    title: "Officer Qualities",
    description:
      "Essential qualities for military leadership and success in Army interviews",
    href: "/army/officer-qualities",
    icon: StarIcon,
    bgColor: "bg-blue-50",
    textColor: "text-blue-700",
    borderColor: "border-blue-200",
    hoverBg: "hover:bg-blue-100",
    iconBg: "bg-blue-100",
  },
  {
    title: "Army Ranks",
    description:
      "Explore the complete hierarchy of ranks in the Nepal Army, from officers to NCOs",
    href: "/army/ranks",
    icon: UserGroupIcon,
    bgColor: "bg-amber-50",
    textColor: "text-amber-700",
    borderColor: "border-amber-200",
    hoverBg: "hover:bg-amber-100",
    iconBg: "bg-amber-100",
  },
  {
    title: "Modern Achievements",
    description:
      "Discover Nepal Army's contributions to global peacekeeping and humanitarian operations",
    href: "/army/achievements",
    icon: TrophyIcon,
    bgColor: "bg-emerald-50",
    textColor: "text-emerald-700",
    borderColor: "border-emerald-200",
    hoverBg: "hover:bg-emerald-100",
    iconBg: "bg-emerald-100",
  },
];

export default function ArmyPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        {/* Page Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl">
            About Nepal Army
          </h1>
          <p className="mt-6 text-lg leading-8 text-gray-600 max-w-3xl mx-auto">
            Explore the rich history, organizational structure, and essential
            officer qualities of the Nepal Army, one of the oldest military
            forces in the world.
          </p>
        </div>

        {/* Featured Sections - Large Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {armySections.map((section) => (
            <Link
              key={section.title}
              href={section.href}
              className={`group relative overflow-hidden rounded-2xl ${section.bgColor} border ${section.borderColor} shadow-sm hover:shadow-md transition-all duration-300 flex flex-col h-full`}
            >
              {/* Triangular pointed corner */}
              <div className="absolute top-0 right-0 w-0 h-0 border-t-[60px] border-r-[60px] border-b-0 border-l-0 border-t-white/20 border-r-transparent"></div>

              <div className="p-8 flex flex-col h-full">
                <div className="flex items-center mb-4">
                  <div
                    className={`p-3 rounded-xl ${section.iconBg} ${section.textColor} mr-4`}
                  >
                    <section.icon className="h-6 w-6" />
                  </div>
                  <h2 className={`text-xl font-semibold ${section.textColor}`}>
                    {section.title}
                  </h2>
                </div>

                <p className="text-gray-700 mb-6 flex-grow">
                  {section.description}
                </p>

                <div
                  className={`mt-auto flex items-center ${section.textColor} font-medium`}
                >
                  <span>Explore</span>
                  <ArrowRightIcon
                    className={`ml-2 h-5 w-5 transform transition-transform duration-300 group-hover:translate-x-1`}
                  />
                </div>
              </div>
            </Link>
          ))}
        </div>

        {/* Additional Information */}
        <div className="bg-white rounded-xl shadow-md p-8 border border-gray-200 mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">
            Nepal Army: Defenders of the Nation
          </h2>
          <p className="text-gray-600 mb-8 text-lg">
            The Nepal Army is one of the oldest military organizations in the
            world, with a history dating back to the 18th century. It has played
            a crucial role in the unification and defense of Nepal, and
            continues to serve the nation through various military and
            humanitarian operations.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-gray-50 rounded-xl p-6 border border-gray-200 hover:border-indigo-200 transition-colors duration-300">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                Mission
              </h3>
              <p className="text-gray-700">
                To defend the sovereignty, territorial integrity and
                independence of Nepal, and protect the lives and property of its
                citizens.
              </p>
            </div>
            <div className="bg-gray-50 rounded-xl p-6 border border-gray-200 hover:border-indigo-200 transition-colors duration-300">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                Vision
              </h3>
              <p className="text-gray-700">
                To be a modern, professional, and accountable military force
                capable of addressing contemporary security challenges.
              </p>
            </div>
          </div>
        </div>

        {/* Modern Achievements Section */}
        <div className="bg-white rounded-xl shadow-md p-8 border border-gray-200">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">
            Modern Achievements of Nepal Army
          </h2>
          <p className="text-gray-600 mb-8 text-lg">
            The Nepal Army has earned international recognition for its
            exceptional contributions to global peace, disaster response, and
            humanitarian operations. These achievements showcase Nepal's
            commitment to international cooperation and security.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            <div className="bg-blue-50 rounded-xl p-6 border border-blue-200 hover:border-blue-300 transition-colors duration-300">
              <h3 className="text-xl font-semibold text-blue-800 mb-3">
                #1 UN Peacekeeping Contributor
              </h3>
              <p className="text-gray-700">
                As of 2024, Nepal has become the largest troop-contributing
                nation to UN peacekeeping missions, with over 6,247 personnel
                serving in various conflict zones worldwide. Since 1958, nearly
                150,000 Nepali peacekeepers have served in 44 UN missions across
                the globe.
              </p>
            </div>
            <div className="bg-emerald-50 rounded-xl p-6 border border-emerald-200 hover:border-emerald-300 transition-colors duration-300">
              <h3 className="text-xl font-semibold text-emerald-800 mb-3">
                Disaster Response Excellence
              </h3>
              <p className="text-gray-700">
                During the devastating 2015 earthquake, the Nepal Army launched
                "Operation Sankatmochan," mobilizing over 66,000 personnel for
                search, rescue, and relief operations. The Army continues to
                lead national disaster response efforts with modern capabilities
                and specialized training.
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-amber-50 rounded-xl p-6 border border-amber-200 hover:border-amber-300 transition-colors duration-300">
              <h3 className="text-xl font-semibold text-amber-800 mb-3">
                International Peace Prize Medal
              </h3>
              <p className="text-gray-700">
                In recognition of their contributions to UN peacekeeping, 11,332
                Nepal Army personnel received the International Peace Prize
                Medal, acknowledging their service from 1958-1988 when the UN
                Peacekeeping Forces were awarded the Nobel Peace Prize.
              </p>
            </div>
            <div className="bg-purple-50 rounded-xl p-6 border border-purple-200 hover:border-purple-300 transition-colors duration-300">
              <h3 className="text-xl font-semibold text-purple-800 mb-3">
                Modern Military Capabilities
              </h3>
              <p className="text-gray-700">
                The Nepal Army has developed specialized capabilities in
                engineering, aviation, medical services, and disaster
                management. The Army also conducts international peacekeeping
                training at the Birendra Peace Operations Training Center in
                Panchkhal, Kavre.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
