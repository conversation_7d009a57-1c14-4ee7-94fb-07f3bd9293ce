"use client";

import { useState, ReactNode, useEffect } from "react";
import { LockClosedIcon, XMarkIcon } from "@heroicons/react/24/outline";
import { PDF_DOWNLOAD_PASSWORD } from "@/utils/constants";

interface PasswordProtectedDownloadProps {
  onDownload: () => void | Promise<void>;
  children: ReactNode;
  buttonClassName?: string;
  isLoading?: boolean;
  statusMessage?: string;
}

export default function PasswordProtectedDownload({
  onDownload,
  children,
  buttonClassName = "",
  isLoading = false,
  statusMessage = "",
}: PasswordProtectedDownloadProps) {
  // Use null for initial state to prevent hydration mismatch
  const [isModalOpen, setIsModalOpen] = useState<boolean | null>(null);
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");

  // Initialize state after component mounts to prevent hydration issues
  useEffect(() => {
    setIsModalOpen(false);
  }, []);

  const handleOpenModal = () => {
    setIsModalOpen(true);
    setPassword("");
    setError("");
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setPassword("");
    setError("");
  };

  const handleSubmit = (
    e: React.MouseEvent | React.KeyboardEvent | React.FormEvent
  ) => {
    // No need to prevent default since we're not in a form
    console.log("Submit button clicked, password:", password);

    if (password === PDF_DOWNLOAD_PASSWORD) {
      console.log("Password correct, proceeding with download");
      setIsModalOpen(false);
      setPassword("");
      setError("");

      // Call the download function
      onDownload();
    } else {
      console.log("Password incorrect");
      // Clear the password field and set error
      setPassword("");
      setError("Incorrect password. Please try again.");
    }
  };

  // Don't render modal during SSR or before client initialization
  if (isModalOpen === null) {
    return (
      <div className="relative">
        <button
          onClick={handleOpenModal}
          className={`${buttonClassName} cursor-pointer`}
        >
          {children}
        </button>
      </div>
    );
  }

  return (
    <>
      <div className="relative">
        <button
          onClick={handleOpenModal}
          className={`${buttonClassName} cursor-pointer`}
        >
          {children}
        </button>
      </div>

      {/* Password Modal - Only rendered on client side after hydration */}
      {isModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4 overflow-hidden">
            <div className="flex items-center justify-between bg-indigo-600 px-6 py-4">
              <h3 className="text-lg font-medium text-white flex items-center">
                <LockClosedIcon className="h-5 w-5 mr-2" />
                Password Protected
              </h3>
              <button
                onClick={handleCloseModal}
                className="text-white hover:text-gray-200 cursor-pointer"
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            </div>

            <div className="p-6">
              <div className="mb-4">
                <p className="text-gray-700 mb-4">
                  This content is password protected. Please enter the password
                  to download.
                </p>

                {error && (
                  <div className="mb-4 p-3 bg-red-200 border-2 border-red-500 rounded-md shadow-sm">
                    <p className="text-red-900 font-bold flex items-center">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5 mr-2 text-red-700"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                          clipRule="evenodd"
                        />
                      </svg>
                      {error}
                    </p>
                  </div>
                )}

                <label
                  htmlFor="password"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Password
                </label>
                <input
                  type="password"
                  id="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className={`w-full px-3 py-2 border ${
                    error ? "border-red-300" : "border-gray-300"
                  } rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-gray-900 bg-white`}
                  placeholder="Enter password"
                  style={{ color: "#000" }}
                  autoFocus
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      e.preventDefault();
                      handleSubmit(e as any);
                    }
                  }}
                />
              </div>

              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={handleCloseModal}
                  className="mr-3 inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 cursor-pointer"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  className="inline-flex justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 cursor-pointer"
                  onClick={(e) => handleSubmit(e as React.FormEvent)}
                >
                  Submit
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
