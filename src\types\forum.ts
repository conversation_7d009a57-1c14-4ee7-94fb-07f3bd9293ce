// Forum data types

// Topic status
export type TopicStatus = "active" | "locked" | "hidden" | "deleted";

// Report status
export type ReportStatus = "pending" | "reviewed" | "dismissed" | "actioned";

// Report reason
export type ReportReason =
  | "hate_speech"
  | "harassment"
  | "sharing_exam_content"
  | "personal_info"
  | "explicit_content"
  | "spam"
  | "other";

// Report reason display names
export const reportReasonDisplayNames: Record<ReportReason, string> = {
  hate_speech: "Hate Speech",
  harassment: "Harassment or Bullying",
  sharing_exam_content: "Sharing Exam Questions/Answers",
  personal_info: "Personal Identifiable Information",
  explicit_content: "Explicit or Inappropriate Content",
  spam: "Spam",
  other: "Other",
};

// Forum topic
export interface ForumTopic {
  id: string;
  title: string;
  content: string;
  authorId: string;
  authorName: string;
  authorPhotoURL?: string;
  createdAt: any; // Firestore timestamp
  updatedAt: any; // Firestore timestamp
  status: TopicStatus;
  replyCount: number;
  // viewCount has been removed
  lastReplyAt?: any; // Firestore timestamp
  lastReplyAuthorId?: string;
  lastReplyAuthorName?: string;
  isPinned: boolean;
  tags: string[];
}

// Forum reply
export interface ForumReply {
  id: string;
  topicId: string;
  content: string;
  authorId: string;
  authorName: string;
  authorPhotoURL?: string;
  createdAt: any; // Firestore timestamp
  updatedAt: any; // Firestore timestamp
  status: TopicStatus;
  isAnswer: boolean; // For marking a reply as the answer to a question
  parentReplyId?: string; // For nested replies
}

// Content report
export interface ContentReport {
  id: string;
  contentId: string; // ID of the topic or reply
  contentType: "topic" | "reply";
  reporterId: string;
  reporterName: string;
  reason: ReportReason;
  description: string;
  createdAt: any; // Firestore timestamp
  status: ReportStatus;
  reviewedBy?: string;
  reviewedAt?: any; // Firestore timestamp
  actionTaken?: string;
}

// User ban
export interface UserBan {
  userId: string;
  reason: string;
  bannedBy: string;
  createdAt: any; // Firestore timestamp
  expiresAt?: any; // Firestore timestamp, undefined for permanent bans
  isActive: boolean;
}

// Forum user with additional forum-specific properties
export interface ForumUser {
  id: string;
  name: string;
  photoURL?: string;
  postCount: number;
  replyCount: number;
  isBanned: boolean;
  banExpiresAt?: any; // Firestore timestamp
  lastPostAt?: any; // Firestore timestamp
  joinedAt: any; // Firestore timestamp
  reputation: number; // Simple reputation system
  isModerator: boolean;
}
