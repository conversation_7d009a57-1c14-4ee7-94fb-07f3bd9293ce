# Military Selection Test Preparation Website

A comprehensive web application designed to help students in Nepal prepare for military selection psychological tests (GunaMapak Test). The website provides detailed information and preparation resources for TO, IO, GTO, and BC tests.

## Features

- Detailed information about each test component
- Interactive navigation between test sections
- Responsive design for all devices
- Clean and professional user interface
- Comprehensive preparation guides

## Tech Stack

- Next.js 14
- TypeScript
- Tailwind CSS
- Headless UI
- Heroicons

## Getting Started

1. Clone the repository:

```bash
git clone <repository-url>
cd military-selection-prep
```

2. Install dependencies:

```bash
npm install
```

3. Run the development server:

```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Project Structure

```
src/
├── app/                    # Next.js app directory
│   ├── tests/             # Test-specific pages
│   │   ├── to/           # Technical Officer Test
│   │   ├── io/           # Interviewing Officer Test
│   │   ├── gto/          # Group Testing Officer Test
│   │   └── bc/           # Board Conference
│   ├── layout.tsx        # Root layout
│   └── page.tsx          # Homepage
├── components/            # Reusable components
│   └── Navigation.tsx    # Navigation component
└── styles/               # Global styles
```

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Next.js team for the amazing framework
- Tailwind CSS for the utility-first CSS framework
- Headless UI for accessible components
- Heroicons for the beautiful icons
