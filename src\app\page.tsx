"use client";

import Link from "next/link";
import Image from "next/image";
import { ArrowRightIcon } from "@heroicons/react/24/outline";
import { useAuth } from "@/context/GoogleAuthContext";
import ProgressSummary from "@/components/ProgressSummary";
const testCategories = [
  {
    name: "Technical Officer (TO)",
    description:
      "Psychological assessment tests to evaluate your personality, decision-making, and leadership potential.",
    href: "/tests/to",
    color: "primary",
    modules: "4 Modules",
  },
  {
    name: "Group Testing Officer (GTO)",
    description:
      "Group tasks and leadership assessment to evaluate teamwork, initiative, and problem-solving abilities.",
    href: "/tests/gto",
    color: "secondary",
    modules: "9 Tasks",
  },
  {
    name: "Interviewing Officer (IO)",
    description:
      "Communication and personality assessment to evaluate your suitability for military leadership roles.",
    href: "/tests/io",
    color: "accent",
    modules: "5 Sections",
  },
  {
    name: "Board Conference (BC)",
    description:
      "Final assessment and selection process that evaluates your overall performance and potential.",
    href: "/tests/bc",
    color: "neutral",
    modules: "3 Stages",
  },
];

export default function Home() {
  const { user } = useAuth();

  return (
    <div className="relative isolate overflow-hidden bg-gray-100 min-h-screen">
      {/* Hero section with military image */}
      <div className="relative h-[60vh] w-full">
        <Image
          src="/images/military-hero.jpg" // Add this image to your public folder
          alt="Military officers in training"
          fill
          className="object-cover brightness-75"
          priority
        />
        <div className="absolute inset-0 bg-gradient-to-r from-gray-900/70 to-gray-900/30 flex items-center">
          <div className="mx-auto max-w-7xl px-6 lg:px-8">
            <div className="max-w-2xl">
              <h1
                className="text-4xl font-bold tracking-tight text-white sm:text-5xl lg:text-6xl"
                style={{ color: "white !important" }}
              >
                Military Selection Test Preparation
              </h1>
              <p
                className="mt-6 text-lg leading-8 text-white"
                style={{ color: "white !important" }}
              >
                Excel in Your Service to the Nation
              </p>
              <div className="mt-10 flex items-center">
                <Link
                  href="/tests/to"
                  className="rounded-md bg-indigo-700 px-6 py-3 text-lg font-semibold text-white shadow-sm hover:bg-indigo-600 focus-visible:outline focus-visible:outline-offset-2 focus-visible:outline-indigo-600 inline-flex items-center cursor-pointer allow-select"
                  style={{
                    textDecoration: "none",
                    position: "relative",
                    zIndex: 9999,
                  }}
                >
                  Get Started
                  <ArrowRightIcon className="ml-2 h-5 w-5" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Progress Summary for Signed Users */}
      {user && (
        <div className="mx-auto max-w-7xl px-6 pt-16 pb-8 lg:px-8">
          <ProgressSummary />
        </div>
      )}

      {/* Test Categories Grid */}
      <div
        id="test-categories"
        className={`mx-auto max-w-7xl px-6 ${user ? "py-8" : "py-16"} lg:px-8`}
      >
        <h2 className="text-3xl font-bold tracking-tight text-gray-900 mb-8 text-center">
          Test Categories
        </h2>
        <p className="mt-2 text-lg leading-8 text-gray-700 text-center max-w-2xl mx-auto mb-12">
          Select a test category to learn more about its components and
          preparation strategies.
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {testCategories.map((test) => (
            <Link
              key={test.name}
              href={test.href}
              className="bg-white rounded-lg shadow-md overflow-hidden border border-gray-200 transition-all hover:shadow-lg hover:border-indigo-300 flex flex-col h-full"
            >
              <div
                className={`h-3 ${
                  test.color === "primary"
                    ? "bg-indigo-700"
                    : test.color === "secondary"
                    ? "bg-green-700"
                    : test.color === "accent"
                    ? "bg-amber-600"
                    : "bg-gray-700"
                }`}
              ></div>
              <div className="p-8 flex flex-col h-full">
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h3 className="text-xl font-bold text-gray-900">
                      {test.name}
                    </h3>
                    <p className="mt-2 text-gray-700">{test.description}</p>
                  </div>
                  <span
                    className={`inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ${
                      test.color === "primary"
                        ? "bg-indigo-100 text-indigo-800"
                        : test.color === "secondary"
                        ? "bg-green-100 text-green-800"
                        : test.color === "accent"
                        ? "bg-amber-100 text-amber-800"
                        : "bg-gray-100 text-gray-800"
                    }`}
                  >
                    {test.modules}
                  </span>
                </div>
                <div className="mt-auto pt-4">
                  <span
                    className={`inline-flex items-center font-medium ${
                      test.color === "primary"
                        ? "text-indigo-700"
                        : test.color === "secondary"
                        ? "text-green-700"
                        : test.color === "accent"
                        ? "text-amber-700"
                        : "text-gray-700"
                    }`}
                  >
                    Begin Preparation
                    <ArrowRightIcon className="ml-1 h-4 w-4" />
                  </span>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>

      {/* Forum Section */}
      <div id="forum-section" className="mx-auto max-w-7xl px-6 pb-16">
        <div className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl shadow-md p-8 border border-indigo-100 overflow-hidden relative">
          <div className="absolute right-0 top-0 h-full w-1/3 opacity-10">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="currentColor"
              className="w-full h-full text-indigo-700"
            >
              <path
                fillRule="evenodd"
                d="M4.804 21.644A6.707 6.707 0 006 21.75a6.721 6.721 0 006.75-6.75 6.721 6.721 0 00-6.75-6.75 6.721 6.721 0 00-6.75 6.75c0 1.093.26 2.125.724 3.043l-1.267 1.267a.75.75 0 00.144 1.154l2.25 1.5a.75.75 0 00.4.11h2.25a.75.75 0 00.75-.75v-2.25a.75.75 0 00-.11-.4l-1.5-2.25a.75.75 0 00-1.154-.144l-1.083 1.083zm9.696-3.644a7.5 7.5 0 00-7.5-7.5 7.5 7.5 0 00-7.5 7.5 7.5 7.5 0 007.5 7.5 7.5 7.5 0 007.5-7.5z"
                clipRule="evenodd"
              />
              <path d="M19.5 7.5a1.5 1.5 0 100-3 1.5 1.5 0 000 3z" />
            </svg>
          </div>

          <div className="flex flex-col md:flex-row items-center">
            <div className="md:w-2/3 md:pr-8">
              <h2 className="text-2xl font-bold text-indigo-800 mb-4">
                Community Forum
              </h2>
              <p className="text-indigo-700 mb-6">
                Join our community forum to connect with fellow candidates,
                share experiences, ask questions, and get valuable insights
                about military selection tests.
              </p>

              <div className="space-y-4">
                <div className="flex items-start">
                  <div className="flex-shrink-0 h-6 w-6 rounded-full bg-indigo-100 flex items-center justify-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                      className="w-4 h-4 text-indigo-600"
                    >
                      <path d="M10 9a3 3 0 100-6 3 3 0 000 6zM6 8a2 2 0 11-4 0 2 2 0 014 0zM1.49 15.326a.78.78 0 01-.358-.442 3 3 0 014.308-3.516 6.484 6.484 0 00-1.905 3.959c-.023.222-.014.442.025.654a4.97 4.97 0 01-2.07-.655zM16.44 15.98a4.97 4.97 0 002.07-.654.78.78 0 00.357-.442 3 3 0 00-4.308-3.517 6.484 6.484 0 011.907 3.96 2.32 2.32 0 01-.026.654zM18 8a2 2 0 11-4 0 2 2 0 014 0zM5.304 16.19a.844.844 0 01-.277-.71 5 5 0 019.947 0 .843.843 0 01-.277.71A6.975 6.975 0 0110 18a6.974 6.974 0 01-4.696-1.81z" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-indigo-800">
                      Connect with Peers
                    </h3>
                    <p className="text-xs text-indigo-600">
                      Share experiences and learn from others preparing for
                      military selection.
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="flex-shrink-0 h-6 w-6 rounded-full bg-indigo-100 flex items-center justify-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                      className="w-4 h-4 text-indigo-600"
                    >
                      <path
                        fillRule="evenodd"
                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-indigo-800">
                      Get Expert Advice
                    </h3>
                    <p className="text-xs text-indigo-600">
                      Ask questions and receive guidance from experienced
                      members.
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="flex-shrink-0 h-6 w-6 rounded-full bg-indigo-100 flex items-center justify-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                      className="w-4 h-4 text-indigo-600"
                    >
                      <path d="M15.98 1.804A1 1 0 0017 2.8V5h-1V4.25a.25.25 0 00-.25-.25H7.5v2.264a2 2 0 01-1.857 1.98l-.143.01v1.992l.143.01a2 2 0 011.857 1.98V13.5h8.25a.25.25 0 00.25-.25V12h1v2.19a1 1 0 01-1.02.996H7.5v2.264a2 2 0 01-1.857 1.98l-.143.01v1.992l.143.01A2 2 0 017.5 23.432v.818a1 1 0 01-1.02.996H3.98a1 1 0 01-1.02-.996v-2.19a1 1 0 011.02-.996h2.5v-3.182a1 1 0 01-1.02-.996v-2.19a1 1 0 011.02-.996h2.5V7.818a1 1 0 01-1.02-.996V4.632a1 1 0 011.02-.996h2.5V1.804z" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-indigo-800">
                      Discuss Test Strategies
                    </h3>
                    <p className="text-xs text-indigo-600">
                      Exchange preparation techniques and effective study
                      methods.
                    </p>
                  </div>
                </div>
              </div>

              <div className="mt-8">
                <Link
                  href="/forum"
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Visit Forum
                  <ArrowRightIcon className="ml-2 h-5 w-5" />
                </Link>
              </div>
            </div>

            <div className="md:w-1/3 mt-8 md:mt-0">
              <div className="bg-white rounded-lg shadow-md p-4 border border-gray-200">
                <h3 className="text-sm font-medium text-gray-900 mb-3">
                  Recent Discussions
                </h3>
                <ul className="space-y-3">
                  <li className="border-b border-gray-100 pb-2">
                    <Link
                      href="/forum"
                      className="block hover:bg-gray-50 rounded p-1 -m-1"
                    >
                      <p className="text-xs font-medium text-indigo-600">
                        SRT Preparation Tips
                      </p>
                      <p className="text-xs text-gray-500">
                        Started by Ramesh • 12 replies
                      </p>
                    </Link>
                  </li>
                  <li className="border-b border-gray-100 pb-2">
                    <Link
                      href="/forum"
                      className="block hover:bg-gray-50 rounded p-1 -m-1"
                    >
                      <p className="text-xs font-medium text-indigo-600">
                        How to improve WAT responses?
                      </p>
                      <p className="text-xs text-gray-500">
                        Started by Sita • 8 replies
                      </p>
                    </Link>
                  </li>
                  <li className="border-b border-gray-100 pb-2">
                    <Link
                      href="/forum"
                      className="block hover:bg-gray-50 rounded p-1 -m-1"
                    >
                      <p className="text-xs font-medium text-indigo-600">
                        GTO tasks preparation
                      </p>
                      <p className="text-xs text-gray-500">
                        Started by Hari • 15 replies
                      </p>
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/forum"
                      className="block hover:bg-gray-50 rounded p-1 -m-1"
                    >
                      <p className="text-xs font-medium text-indigo-600">
                        Interview experience sharing
                      </p>
                      <p className="text-xs text-gray-500">
                        Started by Gita • 20 replies
                      </p>
                    </Link>
                  </li>
                </ul>
                <div className="mt-3 text-right">
                  <Link
                    href="/forum"
                    className="text-xs font-medium text-indigo-600 hover:text-indigo-500"
                  >
                    View all discussions →
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Nepal Army Rank Structure Section */}
      <div id="rank-structure" className="mx-auto max-w-7xl px-6 pb-16">
        <div className="bg-white rounded-xl shadow-md p-8 border border-gray-200">
          <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
            Nepal Army Rank Structure
          </h2>
          <p className="text-gray-600 mb-8 max-w-3xl mx-auto text-center">
            Understanding military ranks is essential for anyone preparing for a
            career in the armed forces. Below is the complete hierarchy of ranks
            in the Nepal Army.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Commissioned Officers */}
            <div className="bg-purple-50 rounded-xl p-6 border border-purple-100">
              <h3 className="text-lg font-medium text-purple-800 mb-3">
                Commissioned Officers
              </h3>
              <ul className="space-y-2">
                <li className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-purple-500 mr-2"></div>
                  <span className="text-sm text-purple-700">
                    COAS महारथी (प्रधानसेनापती)
                  </span>
                </li>
                <li className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-purple-500 mr-2"></div>
                  <span className="text-sm text-purple-700">
                    Lieutenant General (रथी)
                  </span>
                </li>
                <li className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-purple-500 mr-2"></div>
                  <span className="text-sm text-purple-700">
                    Major General (उप रथी)
                  </span>
                </li>
                <li className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-purple-500 mr-2"></div>
                  <span className="text-sm text-purple-700">
                    Brigadier General (सहायक रथी)
                  </span>
                </li>
                <li className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-teal-500 mr-2"></div>
                  <span className="text-sm text-teal-700">
                    Colonel (महासेनानी)
                  </span>
                </li>
                <li className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-teal-500 mr-2"></div>
                  <span className="text-sm text-teal-700">
                    Lieutenant Colonel (प्रमुख सेनानी)
                  </span>
                </li>
                <li className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-teal-500 mr-2"></div>
                  <span className="text-sm text-teal-700">Major (सेनानी)</span>
                </li>
                <li className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-teal-500 mr-2"></div>
                  <span className="text-sm text-teal-700">
                    Captain (सह-सेनानी)
                  </span>
                </li>
                <li className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-teal-500 mr-2"></div>
                  <span className="text-sm text-teal-700">
                    Lieutenant (उप सेनानी)
                  </span>
                </li>
                <li className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-teal-500 mr-2"></div>
                  <span className="text-sm text-teal-700">
                    Second Lieutenant (सहायक सेनानी)
                  </span>
                </li>
              </ul>
              <div className="mt-4">
                <Link
                  href="/army/ranks/officers"
                  className="text-sm font-medium text-purple-700 hover:text-purple-900 flex items-center"
                >
                  View detailed ranks
                  <ArrowRightIcon className="h-4 w-4 ml-1" />
                </Link>
              </div>
            </div>

            {/* Non-Commissioned Officers */}
            <div className="bg-teal-50 rounded-xl p-6 border border-teal-100">
              <h3 className="text-lg font-medium text-teal-800 mb-3">
                Non-Commissioned Officers
              </h3>
              <ul className="space-y-2">
                <li className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-purple-500 mr-2"></div>
                  <span className="text-sm text-purple-700">
                    Subedar Major (सुवेदार मेजर)
                  </span>
                </li>
                <li className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-purple-500 mr-2"></div>
                  <span className="text-sm text-purple-700">
                    Warrant Officer First Class (जमदार प्रथम श्रेणी)
                  </span>
                </li>
                <li className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-purple-500 mr-2"></div>
                  <span className="text-sm text-purple-700">
                    Warrant Officer Second Class (जमदार द्वितीय श्रेणी)
                  </span>
                </li>
                <li className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-teal-500 mr-2"></div>
                  <span className="text-sm text-teal-700">
                    Sergeant (हुद्दा)
                  </span>
                </li>
                <li className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-teal-500 mr-2"></div>
                  <span className="text-sm text-teal-700">
                    Corporal (अमल्दार)
                  </span>
                </li>
                <li className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-teal-500 mr-2"></div>
                  <span className="text-sm text-teal-700">
                    Lance Corporal (प्यूठ)
                  </span>
                </li>
              </ul>
              <div className="mt-4">
                <Link
                  href="/army/ranks/non-commissioned-officers"
                  className="text-sm font-medium text-teal-700 hover:text-teal-900 flex items-center"
                >
                  View detailed ranks
                  <ArrowRightIcon className="h-4 w-4 ml-1" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Nepal Army Historical Timeline Section */}
      <div id="historical-timeline" className="mx-auto max-w-7xl px-6 pb-24">
        <div className="bg-white rounded-xl shadow-md p-8 border border-gray-200">
          <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
            Nepal Army Historical Timeline
          </h2>
          <p className="text-gray-600 mb-8 max-w-3xl mx-auto text-center">
            The Nepal Army has a rich history spanning several centuries,
            playing a crucial role in the formation and defense of the nation.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Formation & Early History */}
            <div className="bg-green-50 rounded-xl p-6 border border-green-100">
              <h3 className="text-lg font-medium text-green-800 mb-3">
                Formation & Early History
              </h3>
              <ul className="space-y-3">
                <li>
                  <div className="flex items-center mb-1">
                    <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                    <span className="text-sm font-semibold text-green-700">
                      १६१६ (1559 AD)
                    </span>
                  </div>
                  <p className="text-sm text-green-700 ml-5">
                    Gorkha Kingdom founded by Drabya Shah
                  </p>
                </li>
                <li>
                  <div className="flex items-center mb-1">
                    <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                    <span className="text-sm font-semibold text-green-700">
                      १८०१-१८२६ (1744-1769 AD)
                    </span>
                  </div>
                  <p className="text-sm text-green-700 ml-5">
                    King Prithvi Narayan Shah begins unification campaign
                  </p>
                </li>
                <li>
                  <div className="flex items-center mb-1">
                    <div className="w-3 h-3 rounded-full bg-red-500 mr-2"></div>
                    <span className="text-sm font-semibold text-red-700">
                      १८७१-१८७३ (1814-1816 AD)
                    </span>
                  </div>
                  <p className="text-sm text-red-700 ml-5">
                    Anglo-Nepalese War and Sugauli Treaty
                  </p>
                </li>
                <li>
                  <div className="flex items-center mb-1">
                    <div className="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
                    <span className="text-sm font-semibold text-blue-700">
                      १९०३ (1846 AD)
                    </span>
                  </div>
                  <p className="text-sm text-blue-700 ml-5">
                    Kot Massacre & beginning of Rana regime
                  </p>
                </li>
              </ul>
              <div className="mt-4">
                <Link
                  href="/army/history"
                  className="text-sm font-medium text-green-700 hover:text-green-900 flex items-center"
                >
                  View complete timeline
                  <ArrowRightIcon className="h-4 w-4 ml-1" />
                </Link>
              </div>
            </div>

            {/* Modern Era */}
            <div className="bg-amber-50 rounded-xl p-6 border border-amber-100">
              <h3 className="text-lg font-medium text-amber-800 mb-3">
                Modern Era & International Role
              </h3>
              <ul className="space-y-3">
                <li>
                  <div className="flex items-center mb-1">
                    <div className="w-3 h-3 rounded-full bg-amber-500 mr-2"></div>
                    <span className="text-sm font-semibold text-amber-700">
                      १९७१-२००२ (1914-1945 AD)
                    </span>
                  </div>
                  <p className="text-sm text-amber-700 ml-5">
                    Gurkha participation in World Wars I and II
                  </p>
                </li>
                <li>
                  <div className="flex items-center mb-1">
                    <div className="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
                    <span className="text-sm font-semibold text-blue-700">
                      २००८ (1951 AD)
                    </span>
                  </div>
                  <p className="text-sm text-blue-700 ml-5">
                    End of Rana regime & formation of Royal Nepali Army
                  </p>
                </li>
                <li>
                  <div className="flex items-center mb-1">
                    <div className="w-3 h-3 rounded-full bg-amber-500 mr-2"></div>
                    <span className="text-sm font-semibold text-amber-700">
                      २०१५ (1958 AD)
                    </span>
                  </div>
                  <p className="text-sm text-amber-700 ml-5">
                    First UN Peacekeeping Mission to Lebanon
                  </p>
                </li>
                <li>
                  <div className="flex items-center mb-1">
                    <div className="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
                    <span className="text-sm font-semibold text-blue-700">
                      २०६५ (2008 AD)
                    </span>
                  </div>
                  <p className="text-sm text-blue-700 ml-5">
                    Republic declared & renamed as Nepal Army
                  </p>
                </li>
                <li>
                  <div className="flex items-center mb-1">
                    <div className="w-3 h-3 rounded-full bg-amber-500 mr-2"></div>
                    <span className="text-sm font-semibold text-amber-700">
                      २०८१ (2024 AD)
                    </span>
                  </div>
                  <p className="text-sm text-amber-700 ml-5">
                    Top UN Peacekeeping contributor globally
                  </p>
                </li>
              </ul>
              <div className="mt-4">
                <Link
                  href="/army/history"
                  className="text-sm font-medium text-amber-700 hover:text-amber-900 flex items-center"
                >
                  View complete timeline
                  <ArrowRightIcon className="h-4 w-4 ml-1" />
                </Link>
              </div>
            </div>
          </div>

          <div className="mt-8 bg-indigo-50 rounded-xl p-6 border border-indigo-100 max-w-3xl mx-auto">
            <h3 className="text-lg font-medium text-indigo-800 mb-3 text-center">
              Gurkha Heritage
            </h3>
            <p className="text-sm text-indigo-700 leading-relaxed text-center">
              The legendary discipline and valor of Nepali soldiers (Gurkhas)
              earned global renown, forming a core part of British and Indian
              army traditions, symbolized by their motto: &ldquo;Kaphar hunu
              bhanda marnu ramro&rdquo; (Better to die than be a coward).
            </p>
          </div>
        </div>
      </div>

      {/* About section */}
      <div id="about" className="mx-auto max-w-7xl px-6 pb-16">
        <div className="mx-auto max-w-3xl">
          <div className="rounded-lg bg-white p-8 shadow-md border border-gray-200">
            <h2 className="text-2xl font-bold tracking-tight text-gray-900 mb-4">
              About Military Psychological Testing
            </h2>
            <div className="prose prose-indigo text-gray-700">
              <p>
                Military{" "}
                <span
                  className="text-indigo-700 font-medium"
                  style={{ color: "#4338ca !important" }}
                >
                  psychological testing
                </span>{" "}
                and{" "}
                <span
                  className="text-indigo-700 font-medium"
                  style={{ color: "#4338ca !important" }}
                >
                  assessments
                </span>{" "}
                are vital in military life. It not only supports in{" "}
                <span
                  className="text-indigo-700 font-medium"
                  style={{ color: "#4338ca !important" }}
                >
                  training
                </span>
                ,{" "}
                <span
                  className="text-indigo-700 font-medium"
                  style={{ color: "#4338ca !important" }}
                >
                  selection
                </span>
                ,{" "}
                <span
                  className="text-indigo-700 font-medium"
                  style={{ color: "#4338ca !important" }}
                >
                  placement
                </span>
                , and{" "}
                <span
                  className="text-indigo-700 font-medium"
                  style={{ color: "#4338ca !important" }}
                >
                  promotion
                </span>{" "}
                purposes but also assists in the{" "}
                <span
                  className="text-indigo-700 font-medium"
                  style={{ color: "#4338ca !important" }}
                >
                  diagnosis
                </span>{" "}
                and{" "}
                <span
                  className="text-indigo-700 font-medium"
                  style={{ color: "#4338ca !important" }}
                >
                  identification
                </span>{" "}
                of candidates with mental problems and trauma that could pose a
                great risk.
              </p>
              <p>
                In 1905, the contemporary psychological testing procedure
                formally came into existence in{" "}
                <span
                  className="text-indigo-700 font-medium"
                  style={{ color: "#4338ca !important" }}
                >
                  France
                </span>
                . Similarly, it adopted the test for assessing and examining
                soldiers during{" "}
                <span
                  className="text-indigo-700 font-medium"
                  style={{ color: "#4338ca !important" }}
                >
                  WW II
                </span>{" "}
                for the selection of potential human resources for making war
                successful. After WW II,{" "}
                <span
                  className="text-indigo-700 font-medium"
                  style={{ color: "#4338ca !important" }}
                >
                  India
                </span>{" "}
                and{" "}
                <span
                  className="text-indigo-700 font-medium"
                  style={{ color: "#4338ca !important" }}
                >
                  Pakistan
                </span>{" "}
                began military psychological testing for the armed forces.
              </p>
              <p>
                Similarly, in the{" "}
                <span
                  className="text-indigo-700 font-medium"
                  style={{ color: "#4338ca !important" }}
                >
                  Nepali Army
                </span>
                , the present psychological selection system and the procedure
                has been continuing for more than{" "}
                <span
                  className="text-indigo-700 font-medium"
                  style={{ color: "#4338ca !important" }}
                >
                  3 decades
                </span>
                . Therefore, a review of the present psychological system and
                procedure is urgent.
              </p>
              <p className="text-sm text-gray-600 mt-4 italic">
                Reference: Lt Col. (Rtd) Phatya Bahadur Khadka, MPhil/PhD
                Scholar at Department of Gender Studies
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
