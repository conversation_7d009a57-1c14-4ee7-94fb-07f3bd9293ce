/**
 * Content moderation utilities for the forum
 * 
 * This file contains functions for filtering and moderating user-generated content
 * to prevent inappropriate material from being posted.
 */

// List of patterns to check for hate speech
const hatePatterns = [
  /\b(hate|hating|hated)\b/i,
  /\b(racist|racism|racial slur)\b/i,
  /\b(sexist|sexism)\b/i,
  /\b(homophobic|homophobia)\b/i,
  /\b(transphobic|transphobia)\b/i,
  /\b(bigot|bigotry)\b/i,
  /\b(discriminat(e|ion|ory))\b/i,
];

// List of patterns to check for harassment
const harassmentPatterns = [
  /\b(harass|harassment)\b/i,
  /\b(bully|bullying)\b/i,
  /\b(threaten|threatening|threat)\b/i,
  /\b(stalk|stalking)\b/i,
  /\b(intimidate|intimidation)\b/i,
];

// List of patterns to check for exam sharing
const examSharingPatterns = [
  /\b(exam (question|answer)s?)\b/i,
  /\b(test (question|answer)s?)\b/i,
  /\b(assessment (question|answer)s?)\b/i,
  /\b(cheat sheet)\b/i,
  /\b(leaked (exam|test|question)s?)\b/i,
];

// List of patterns to check for personal information
const personalInfoPatterns = [
  /\b(\d{3}[-\.\s]??\d{3}[-\.\s]??\d{4})\b/i, // Phone numbers
  /\b([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,})\b/i, // Email addresses
  /\b(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\b/i, // IP addresses
  /\b(\d{12,19})\b/i, // Potential credit card numbers
  /\b(address|street|avenue|road|lane)\b/i, // Address indicators
];

// List of patterns to check for explicit content
const explicitContentPatterns = [
  /\b(explicit|pornographic|obscene)\b/i,
  /\b(nsfw|not safe for work)\b/i,
  /\b(adult content)\b/i,
];

// List of common spam patterns
const spamPatterns = [
  /\b(buy now|click here|limited time|act now)\b/i,
  /\b(free|discount|sale|offer)\b/i,
  /\b(earn money|make money|cash)\b/i,
  /\b(lottery|prize|winner|won)\b/i,
  /\b(viagra|cialis|medication)\b/i,
  /\b(casino|gambling|bet)\b/i,
  /\b(https?:\/\/)\b/i, // URLs (will need refinement for legitimate links)
];

// Combine all patterns for a comprehensive check
const allInappropriatePatterns = [
  ...hatePatterns,
  ...harassmentPatterns,
  ...examSharingPatterns,
  ...personalInfoPatterns,
  ...explicitContentPatterns,
  ...spamPatterns,
];

/**
 * Check if content contains inappropriate patterns
 * @param content The content to check
 * @returns Object with flags for different types of inappropriate content
 */
export const checkInappropriateContent = (content: string): {
  containsHateSpeech: boolean;
  containsHarassment: boolean;
  containsExamSharing: boolean;
  containsPersonalInfo: boolean;
  containsExplicitContent: boolean;
  containsSpam: boolean;
  isInappropriate: boolean;
} => {
  const containsHateSpeech = hatePatterns.some(pattern => pattern.test(content));
  const containsHarassment = harassmentPatterns.some(pattern => pattern.test(content));
  const containsExamSharing = examSharingPatterns.some(pattern => pattern.test(content));
  const containsPersonalInfo = personalInfoPatterns.some(pattern => pattern.test(content));
  const containsExplicitContent = explicitContentPatterns.some(pattern => pattern.test(content));
  const containsSpam = spamPatterns.some(pattern => pattern.test(content));

  const isInappropriate = 
    containsHateSpeech || 
    containsHarassment || 
    containsExamSharing || 
    containsPersonalInfo || 
    containsExplicitContent || 
    containsSpam;

  return {
    containsHateSpeech,
    containsHarassment,
    containsExamSharing,
    containsPersonalInfo,
    containsExplicitContent,
    containsSpam,
    isInappropriate,
  };
};

/**
 * Filter inappropriate content from text
 * This is a simple implementation that could be replaced with a more sophisticated API-based solution
 * @param content The content to filter
 * @returns Filtered content with inappropriate parts removed or replaced
 */
export const filterInappropriateContent = async (content: string): Promise<string> => {
  // Check for inappropriate content
  const check = checkInappropriateContent(content);

  // If no inappropriate content is found, return the original content
  if (!check.isInappropriate) {
    return content;
  }

  // For now, we'll use a simple approach of replacing detected patterns
  // In a production environment, you might want to use a more sophisticated API
  let filteredContent = content;

  // Replace personal information with [REDACTED]
  if (check.containsPersonalInfo) {
    personalInfoPatterns.forEach(pattern => {
      filteredContent = filteredContent.replace(pattern, '[PERSONAL INFO REDACTED]');
    });
  }

  // Replace exam sharing content with [REDACTED]
  if (check.containsExamSharing) {
    examSharingPatterns.forEach(pattern => {
      filteredContent = filteredContent.replace(pattern, '[EXAM CONTENT REDACTED]');
    });
  }

  // Replace explicit content with [REDACTED]
  if (check.containsExplicitContent) {
    explicitContentPatterns.forEach(pattern => {
      filteredContent = filteredContent.replace(pattern, '[INAPPROPRIATE CONTENT REDACTED]');
    });
  }

  // For hate speech and harassment, we'll be more cautious and throw an error
  if (check.containsHateSpeech || check.containsHarassment) {
    throw new Error('Your post contains content that violates our community guidelines. Please revise your post to remove any hate speech or harassment.');
  }

  // For spam, we'll also throw an error
  if (check.containsSpam) {
    throw new Error('Your post appears to contain spam or promotional content, which is not allowed in our forum. Please revise your post.');
  }

  return filteredContent;
};

/**
 * Sanitize HTML to prevent XSS attacks
 * @param html The HTML to sanitize
 * @returns Sanitized HTML
 */
export const sanitizeHtml = (html: string): string => {
  // Replace < and > with their HTML entities
  return html
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;');
};

/**
 * Check if a user is posting too frequently (rate limiting)
 * @param userId The user ID to check
 * @param contentType The type of content being posted (topic or reply)
 * @returns Promise resolving to a boolean indicating if the user is posting too frequently
 */
export const isPostingTooFrequently = async (
  userId: string,
  contentType: 'topic' | 'reply'
): Promise<boolean> => {
  // This would typically involve checking the user's recent post history
  // For now, we'll return false (not posting too frequently)
  return false;
};
