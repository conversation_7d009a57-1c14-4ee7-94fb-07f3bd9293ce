"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/context/GoogleAuthContext";
import { useRoleCheck } from "@/utils/authUtils";
import {
  getAllAIAnalyses,
  AIAnalysisDocument,
  updateAnalysisWithCulturalFitAlgorithm,
} from "@/services/aiAnalysisService";

export default function UpdateCulturalFitPage() {
  const { user } = useAuth();
  const { isAdmin } = useRoleCheck();
  const [analyses, setAnalyses] = useState<AIAnalysisDocument[]>([]);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [selectedAnalyses, setSelectedAnalyses] = useState<string[]>([]);
  const [updateResults, setUpdateResults] = useState<{
    [key: string]: { success: boolean; message: string };
  }>({});

  useEffect(() => {
    const fetchAnalyses = async () => {
      if (user) {
        setLoading(true);
        try {
          const allAnalyses = await getAllAIAnalyses(user.uid);
          // Filter for military psychologist analyses only
          const militaryAnalyses = allAnalyses.filter(
            (analysis) => analysis.aiProvider === "military-psychologist"
          );
          setAnalyses(militaryAnalyses);
        } catch (error) {
          console.error("Error fetching analyses:", error);
        } finally {
          setLoading(false);
        }
      }
    };

    fetchAnalyses();
  }, [user]);

  const handleSelectAll = () => {
    if (selectedAnalyses.length === analyses.length) {
      setSelectedAnalyses([]);
    } else {
      setSelectedAnalyses(
        analyses.filter((a) => a.docId).map((a) => a.docId as string)
      );
    }
  };

  const handleSelectAnalysis = (docId: string) => {
    if (selectedAnalyses.includes(docId)) {
      setSelectedAnalyses(selectedAnalyses.filter((id) => id !== docId));
    } else {
      setSelectedAnalyses([...selectedAnalyses, docId]);
    }
  };

  const handleUpdateSelected = async () => {
    if (selectedAnalyses.length === 0) return;

    setUpdating(true);
    setUpdateResults({});

    const results: { [key: string]: { success: boolean; message: string } } = {};

    for (const docId of selectedAnalyses) {
      try {
        const analysis = analyses.find((a) => a.docId === docId);
        if (!analysis) {
          results[docId] = {
            success: false,
            message: "Analysis not found",
          };
          continue;
        }

        const success = await updateAnalysisWithCulturalFitAlgorithm(
          docId,
          analysis.userId,
          analysis.testType,
          analysis.setName,
          analysis.responseTimestamp,
          analysis.aiProvider as "military-psychologist"
        );

        results[docId] = {
          success,
          message: success
            ? "Successfully updated with Cultural Fit Algorithm data"
            : "Failed to update",
        };
      } catch (error) {
        results[docId] = {
          success: false,
          message: `Error: ${error instanceof Error ? error.message : String(error)}`,
        };
      }
    }

    setUpdateResults(results);
    setUpdating(false);
  };

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-4">Please sign in</h1>
        <p>You need to be signed in to access this page.</p>
      </div>
    );
  }

  if (!isAdmin) {
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-4">Admin Access Required</h1>
        <p>You need to be an admin to access this page.</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-4">
        Update Analyses with Cultural Fit Algorithm
      </h1>
      <p className="mb-4">
        This utility allows you to update existing Military Psychologist analyses
        with the new Cultural Fit Algorithm data.
      </p>

      {loading ? (
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
          <p className="ml-4">Loading analyses...</p>
        </div>
      ) : (
        <>
          <div className="mb-4 flex justify-between items-center">
            <button
              onClick={handleSelectAll}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              {selectedAnalyses.length === analyses.length
                ? "Deselect All"
                : "Select All"}
            </button>
            <button
              onClick={handleUpdateSelected}
              disabled={selectedAnalyses.length === 0 || updating}
              className={`px-4 py-2 rounded ${
                selectedAnalyses.length === 0 || updating
                  ? "bg-gray-300 cursor-not-allowed"
                  : "bg-green-500 text-white hover:bg-green-600"
              }`}
            >
              {updating
                ? "Updating..."
                : `Update Selected (${selectedAnalyses.length})`}
            </button>
          </div>

          {Object.keys(updateResults).length > 0 && (
            <div className="mb-4 p-4 border rounded">
              <h2 className="text-lg font-bold mb-2">Update Results</h2>
              <ul className="space-y-2">
                {Object.entries(updateResults).map(([docId, result]) => (
                  <li
                    key={docId}
                    className={`p-2 rounded ${
                      result.success ? "bg-green-100" : "bg-red-100"
                    }`}
                  >
                    <span className="font-medium">{docId}:</span>{" "}
                    {result.message}
                  </li>
                ))}
              </ul>
            </div>
          )}

          <div className="overflow-x-auto">
            <table className="min-w-full bg-white border">
              <thead>
                <tr className="bg-gray-100">
                  <th className="py-2 px-4 border">Select</th>
                  <th className="py-2 px-4 border">Test Type</th>
                  <th className="py-2 px-4 border">Set Name</th>
                  <th className="py-2 px-4 border">Date</th>
                  <th className="py-2 px-4 border">Status</th>
                </tr>
              </thead>
              <tbody>
                {analyses.length === 0 ? (
                  <tr>
                    <td colSpan={5} className="py-4 px-4 text-center border">
                      No analyses found
                    </td>
                  </tr>
                ) : (
                  analyses.map((analysis) => (
                    <tr key={analysis.docId} className="hover:bg-gray-50">
                      <td className="py-2 px-4 border text-center">
                        <input
                          type="checkbox"
                          checked={selectedAnalyses.includes(
                            analysis.docId as string
                          )}
                          onChange={() =>
                            handleSelectAnalysis(analysis.docId as string)
                          }
                          className="h-5 w-5"
                        />
                      </td>
                      <td className="py-2 px-4 border">
                        {analysis.testType.toUpperCase()}
                      </td>
                      <td className="py-2 px-4 border">{analysis.setName}</td>
                      <td className="py-2 px-4 border">
                        {new Date(
                          analysis.responseTimestamp
                        ).toLocaleDateString()}
                      </td>
                      <td className="py-2 px-4 border">
                        {analysis.analysis.militaryPsychologist
                          ?.culturalFitAlgorithm ? (
                          <span className="text-green-600">
                            Already has Cultural Fit Algorithm
                          </span>
                        ) : (
                          <span className="text-red-600">
                            Needs Cultural Fit Algorithm
                          </span>
                        )}
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </>
      )}
    </div>
  );
}
