import { jsPDF } from "jspdf";
import "jspdf-autotable";
import { militaryTraitCategories } from "./evaluationUtils";

// Helper function to add watermark and social links to all pages of a PDF
const addWatermark = (doc: jsPDF) => {
  const totalPages = doc.getNumberOfPages();

  // Save the current state
  const fontSize = doc.getFontSize();
  const textColor = doc.getTextColor();

  // Set watermark properties
  doc.setFontSize(10);
  doc.setTextColor(150, 150, 150); // Light gray color

  // Add watermark and links to each page
  for (let i = 1; i <= totalPages; i++) {
    doc.setPage(i);

    const pageWidth = doc.internal.pageSize.getWidth();
    const pageHeight = doc.internal.pageSize.getHeight();

    // Add a divider line
    doc.setDrawColor(200, 200, 200);
    doc.line(15, pageHeight - 15, pageWidth - 15, pageHeight - 15);

    // Add website name at the bottom right of the page
    doc.textWithLink(
      "balaramshiwakoti.com.np",
      pageWidth - 15,
      pageHeight - 10,
      {
        align: "right",
        url: "https://balaramshiwakoti.com.np",
      }
    );

    // Add Facebook link at the bottom left of the page
    doc.setTextColor(59, 89, 152); // Facebook blue color
    doc.setFontSize(12);
    doc.textWithLink("My Facebook", 15, pageHeight - 10, {
      url: "https://www.facebook.com/hercules.shiwakoti",
    });
    doc.setFontSize(10);
  }

  // Restore the original state
  doc.setFontSize(fontSize);
  doc.setTextColor(textColor);

  return doc;
};

/**
 * Loads an image from a URL and returns a Promise that resolves with the image data
 * @param url The URL of the image to load
 * @returns Promise that resolves with the loaded image
 */
const loadImage = (url: string): Promise<HTMLImageElement> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = "Anonymous"; // Enable CORS if the image is from another domain

    img.onload = () => {
      resolve(img);
    };

    img.onerror = (error) => {
      reject(new Error(`Failed to load image from ${url}: ${error}`));
    };

    img.src = url;
  });
};

/**
 * Converts an image URL to a base64 data URL that can be used with jsPDF
 * @param url The URL of the image to convert
 * @returns Promise that resolves with the base64 data URL
 */
const imageUrlToBase64 = async (url: string): Promise<string> => {
  try {
    console.log(`Converting image to base64: ${url}`);

    // For blank images, return an empty string
    if (url === "blank") {
      console.log("Blank image detected, skipping conversion");
      return "";
    }

    // Handle relative URLs by prepending the base URL if needed
    const fullUrl = url.startsWith("/")
      ? `${window.location.origin}${url}`
      : url;

    console.log(`Full URL: ${fullUrl}`);

    const img = await loadImage(fullUrl);
    console.log(`Image loaded successfully: ${img.width}x${img.height}`);

    // Create a canvas to draw the image
    const canvas = document.createElement("canvas");
    canvas.width = img.width;
    canvas.height = img.height;

    // Draw the image on the canvas
    const ctx = canvas.getContext("2d");
    if (!ctx) {
      throw new Error("Failed to get canvas context");
    }

    ctx.drawImage(img, 0, 0);

    // Get the data URL from the canvas
    const dataUrl = canvas.toDataURL("image/jpeg");
    console.log(
      `Image converted to base64 successfully (length: ${dataUrl.length})`
    );
    return dataUrl;
  } catch (error) {
    console.error("Error converting image to base64:", error);
    return "";
  }
};

/**
 * Generate a PDF with TAT test results including images
 */
export const generateTATPDFWithImages = async (
  setName: string,
  responses: string[],
  images: string[]
): Promise<Blob> => {
  console.log(`Generating TAT PDF for set: ${setName}`);
  console.log(`Number of images: ${images.length}`);
  console.log(`Images:`, images);

  // Create a new PDF document
  const doc = new jsPDF();

  // Add title
  doc.setFontSize(18);
  doc.text(`TAT Practice Set ${setName} - Results`, 14, 20);

  // Add date
  const today = new Date();
  const dateStr = today.toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
  doc.setFontSize(12);
  doc.setTextColor(100, 100, 100);
  doc.text(`Generated on: ${dateStr}`, 14, 35);

  // Add responses directly
  let yPos = 50;

  doc.setFontSize(16);
  doc.setTextColor(0, 0, 0);
  doc.text("Your Responses", 14, yPos);
  yPos += 10;

  // Add responses with images
  for (let index = 0; index < images.length; index++) {
    // Start a new page for each image and response after the first one
    if (index > 0) {
      doc.addPage();
      yPos = 20;
    }

    doc.setFontSize(14);
    doc.setTextColor(0, 0, 0);
    doc.text(`Image ${index + 1} and Your Response`, 14, yPos);
    yPos += 15;

    // Try to add the image if it's not a blank image
    if (images[index] !== "blank") {
      try {
        console.log(`Processing image ${index + 1}: ${images[index]}`);

        // Convert image to base64
        const imgData = await imageUrlToBase64(images[index]);

        if (imgData) {
          console.log(
            `Successfully converted image ${index + 1} to base64, adding to PDF`
          );

          // Calculate image dimensions to fit within the page while maintaining aspect ratio
          const imgWidth = 180; // Maximum width
          const imgHeight = 100; // Maximum height

          doc.addImage(
            imgData,
            "JPEG",
            14,
            yPos,
            imgWidth,
            imgHeight,
            undefined,
            "MEDIUM"
          );
          console.log(`Image ${index + 1} added to PDF successfully`);
          yPos += imgHeight + 15;
        } else {
          console.log(
            `Image conversion failed for image ${index + 1}, adding placeholder`
          );

          // If image conversion failed, add a placeholder
          doc.setDrawColor(200, 200, 200);
          doc.setFillColor(240, 240, 240);
          doc.rect(14, yPos, 180, 80, "F");

          doc.setTextColor(100, 100, 100);
          doc.setFontSize(14);
          doc.text("Image could not be displayed", 104, yPos + 40, {
            align: "center",
          });
          doc.setTextColor(0, 0, 0);
          doc.setFontSize(10);

          yPos += 90;
        }
      } catch (error) {
        console.error(`Error adding image ${index + 1} to PDF:`, error);
        console.log(`Image URL that failed: ${images[index]}`);

        // Add a placeholder if there was an error
        doc.setDrawColor(200, 200, 200);
        doc.setFillColor(240, 240, 240);
        doc.rect(14, yPos, 180, 80, "F");

        doc.setTextColor(231, 76, 60); // Red color for error
        doc.setFontSize(14);
        doc.text("Image could not be displayed", 104, yPos + 40, {
          align: "center",
        });
        doc.setTextColor(0, 0, 0);
        doc.setFontSize(10);

        yPos += 90;
      }
    } else {
      console.log(`Processing blank image ${index + 1}`);

      // For blank images, add a placeholder
      doc.setDrawColor(200, 200, 200);
      doc.setFillColor(240, 240, 240);
      doc.rect(14, yPos, 180, 80, "F");

      doc.setTextColor(150, 150, 150);
      doc.setFontSize(14);
      doc.text("Blank Image", 104, yPos + 40, { align: "center" });
      doc.setTextColor(0, 0, 0);
      doc.setFontSize(10);

      yPos += 90;
    }

    // Add the response
    doc.setFontSize(12);
    doc.setTextColor(0, 0, 0);
    doc.text("Your Story:", 14, yPos);
    yPos += 7;

    const response = responses[index] || "Not answered";
    const splitText = doc.splitTextToSize(response, 180);
    doc.text(splitText, 14, yPos);
  }

  // Add watermark to all pages
  addWatermark(doc);

  // Return the PDF as a blob
  return doc.output("blob");
};
