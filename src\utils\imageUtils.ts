/**
 * Utility functions for handling images
 */

/**
 * Loads an image from a URL and returns a Promise that resolves with the image data
 * @param url The URL of the image to load
 * @returns Promise that resolves with the loaded image
 */
export const loadImage = (url: string): Promise<HTMLImageElement> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'Anonymous'; // Enable CORS if the image is from another domain
    
    img.onload = () => {
      resolve(img);
    };
    
    img.onerror = (error) => {
      reject(new Error(`Failed to load image from ${url}: ${error}`));
    };
    
    img.src = url;
  });
};

/**
 * Converts an image URL to a base64 data URL that can be used with jsPDF
 * @param url The URL of the image to convert
 * @returns Promise that resolves with the base64 data URL
 */
export const imageUrlToBase64 = async (url: string): Promise<string> => {
  try {
    // For blank images, return an empty string
    if (url === "blank") {
      return "";
    }
    
    // Handle relative URLs by prepending the base URL if needed
    const fullUrl = url.startsWith('/') 
      ? `${window.location.origin}${url}` 
      : url;
    
    const img = await loadImage(fullUrl);
    
    // Create a canvas to draw the image
    const canvas = document.createElement('canvas');
    canvas.width = img.width;
    canvas.height = img.height;
    
    // Draw the image on the canvas
    const ctx = canvas.getContext('2d');
    if (!ctx) {
      throw new Error('Failed to get canvas context');
    }
    
    ctx.drawImage(img, 0, 0);
    
    // Get the data URL from the canvas
    const dataUrl = canvas.toDataURL('image/jpeg');
    return dataUrl;
  } catch (error) {
    console.error('Error converting image to base64:', error);
    return '';
  }
};

/**
 * Loads multiple images from URLs and returns a Promise that resolves with an array of base64 data URLs
 * @param urls Array of image URLs to load
 * @returns Promise that resolves with an array of base64 data URLs
 */
export const loadImagesAsBase64 = async (urls: string[]): Promise<string[]> => {
  try {
    const promises = urls.map(url => imageUrlToBase64(url));
    return await Promise.all(promises);
  } catch (error) {
    console.error('Error loading images as base64:', error);
    return [];
  }
};
