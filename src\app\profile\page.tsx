"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/context/GoogleAuthContext";
import { useRoleCheck } from "@/utils/authUtils";
import Image from "next/image";
import {
  getAllUserResponses,
  TestResponse,
  deleteResponse,
} from "@/services/responseService";
import Link from "next/link";
import ConfirmationModal from "@/components/ConfirmationModal";
import ProfileResponseGrid from "@/components/ProfileResponseGrid";
import {
  SparklesIcon,
  ShieldCheckIcon,
  UserIcon,
  LockOpenIcon,
  LockClosedIcon,
  UserGroupIcon,
  ChatBubbleLeftRightIcon,
  MicrophoneIcon,
} from "@heroicons/react/24/outline";
import ProgressDashboard from "@/components/ProgressDashboard";

export default function Profile() {
  const { user, logout, loading } = useAuth();
  const { isAdmin, canAccessAIAnalysis } = useRoleCheck();
  const router = useRouter();
  const [responses, setResponses] = useState<TestResponse[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDeleting, setIsDeleting] = useState(false);

  // Modal states
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [responseToDelete, setResponseToDelete] = useState<TestResponse | null>(
    null
  );

  // Navigation state for ProfileResponseGrid
  const [navigationState, setNavigationState] = useState<{
    currentView: "main" | "section" | "subsection" | "set";
    selectedSection: string | null;
    selectedSubsection: string | null;
    selectedSet: string | null;
  }>({
    currentView: "main",
    selectedSection: null,
    selectedSubsection: null,
    selectedSet: null,
  });

  useEffect(() => {
    // Redirect if not logged in
    if (!loading && !user) {
      router.push("/login");
    }
  }, [user, loading, router]);

  useEffect(() => {
    const fetchResponses = async () => {
      if (user) {
        try {
          console.log("Fetching responses for profile page...");
          const userResponses = await getAllUserResponses(user.uid);
          console.log(
            `Received ${userResponses.length} responses from service`
          );
          setResponses(userResponses);
        } catch (error) {
          console.error("Error fetching responses:", error);
        } finally {
          setIsLoading(false);
        }
      }
    };

    if (user) {
      fetchResponses();
    }
  }, [user]);

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  // Function to refresh responses after deletion
  const refreshResponses = async () => {
    if (user) {
      setIsLoading(true);
      try {
        const userResponses = await getAllUserResponses(user.uid);
        setResponses(userResponses);
      } catch (error) {
        console.error("Error fetching responses:", error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  // Handle deleting a single response
  const handleDeleteResponse = async () => {
    if (!responseToDelete || !responseToDelete.docId || !user) return;

    setIsDeleting(true);
    try {
      await deleteResponse(responseToDelete.docId);
      await refreshResponses();
    } catch (error) {
      console.error("Error deleting response:", error);
    } finally {
      setIsDeleting(false);
      setResponseToDelete(null);
      setShowDeleteModal(false);
    }
  };

  if (loading || !user) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Loading...
          </h2>
        </div>
      </div>
    );
  }

  // Add debugging for responses
  console.log(`Profile page: Processing ${responses.length} total responses`);

  // Log response details for debugging
  responses.forEach((response) => {
    console.log(
      `Response: ${response.testType} - ${response.setName} (${
        response.timestamp || "no timestamp"
      })`
    );
  });

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white shadow overflow-hidden sm:rounded-lg">
          <div className="px-4 py-5 sm:px-6 flex flex-col sm:flex-row sm:justify-between sm:items-center">
            <div className="flex flex-col sm:flex-row sm:items-center mb-4 sm:mb-0">
              <div className="relative flex justify-center sm:justify-start mb-3 sm:mb-0">
                {user?.photoURL ? (
                  <div className="sm:mr-4 relative h-16 w-16 rounded-full overflow-hidden">
                    <Image
                      src={user.photoURL}
                      alt={user.displayName || "User"}
                      width={64}
                      height={64}
                      className="object-cover"
                    />
                  </div>
                ) : (
                  <div className="sm:mr-4 h-16 w-16 rounded-full bg-indigo-600 flex items-center justify-center text-white text-xl font-medium">
                    {user?.displayName?.charAt(0) ||
                      user?.email?.charAt(0) ||
                      "?"}
                  </div>
                )}

                {/* Admin badge on profile picture */}
                {isAdmin && (
                  <div className="absolute -top-1 -right-1 bg-purple-600 rounded-full p-1 border-2 border-white shadow-md">
                    <ShieldCheckIcon className="h-5 w-5 text-white" />
                  </div>
                )}
              </div>
              <div className="text-center sm:text-left">
                <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-2">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    User Profile
                  </h3>
                  {isAdmin && (
                    <span className="inline-flex items-center justify-center px-3 py-1 mt-2 sm:mt-0 rounded-full text-xs font-medium bg-purple-100 text-purple-800 border border-purple-200 shadow-sm">
                      <ShieldCheckIcon className="h-4 w-4 mr-1" />
                      ADMINISTRATOR
                    </span>
                  )}
                </div>
                <p className="mt-1 max-w-2xl text-sm text-gray-500">
                  Your personal information and test history
                </p>
              </div>
            </div>
            <button
              onClick={handleLogout}
              className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 w-full sm:w-auto"
            >
              Log out
            </button>
          </div>
          <div className="border-t border-gray-200">
            <dl>
              <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">Full name</dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                  {user.displayName || "Not set"}
                </dd>
              </div>
              <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">
                  Email address
                </dt>
                <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                  {user.email}
                </dd>
              </div>
              <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt className="text-sm font-medium text-gray-500">
                  Account type
                </dt>
                <dd className="mt-1 text-sm sm:mt-0 sm:col-span-2">
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                    <div className="flex flex-col space-y-2 mb-3 sm:mb-0">
                      {isAdmin ? (
                        <span className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-purple-100 text-purple-800">
                          <ShieldCheckIcon className="h-4 w-4 mr-1.5" />
                          Administrator Account
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-gray-100 text-gray-800">
                          <UserIcon className="h-4 w-4 mr-1.5" />
                          Standard User Account
                        </span>
                      )}

                      {/* Show AI Access badge */}
                      {canAccessAIAnalysis && (
                        <span className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-800">
                          <SparklesIcon className="h-4 w-4 mr-1.5" />
                          AI Analysis Access
                        </span>
                      )}
                    </div>

                    {/* Admin badge */}
                    {isAdmin && (
                      <span className="inline-flex items-center justify-center px-3 py-1 border border-purple-300 shadow-sm text-sm font-medium rounded-md text-purple-700 bg-white w-full sm:w-auto">
                        <ShieldCheckIcon className="h-4 w-4 mr-1.5" />
                        Administrator
                      </span>
                    )}
                  </div>
                </dd>
              </div>
            </dl>
          </div>
        </div>

        {/* Progress Dashboard Section */}
        <div className="mt-8">
          <ProgressDashboard />
        </div>

        {/* Test History Section - Full Width */}
        <div className="mt-8">
          <div className="bg-white shadow overflow-hidden sm:rounded-lg">
            <div className="px-4 py-5 sm:px-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900">
                Test History
              </h3>
              <p className="mt-1 max-w-2xl text-sm text-gray-500">
                Your previous test attempts
              </p>
            </div>
            <div className="border-t border-gray-200 px-4 py-5 sm:p-6">
              {isLoading ? (
                <div className="text-center">
                  <p className="text-sm text-gray-500">
                    Loading your test history...
                  </p>
                </div>
              ) : responses.length === 0 ? (
                <div className="text-center">
                  <p className="text-sm text-gray-500">
                    You haven't taken any tests yet.
                  </p>
                </div>
              ) : (
                <div className="flex flex-col space-y-4">
                  <ProfileResponseGrid
                    responses={responses}
                    onDeleteResponse={(response, navState) => {
                      setResponseToDelete(response);
                      if (navState) {
                        setNavigationState(navState);
                      }
                      setShowDeleteModal(true);
                    }}
                    isDeleting={isDeleting}
                    initialNavigationState={navigationState}
                  />
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Audio Recordings Section */}
        <div className="mt-8">
          <div className="bg-white shadow overflow-hidden sm:rounded-lg">
            <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
              <div className="flex items-center">
                <MicrophoneIcon className="h-6 w-6 text-indigo-500 mr-2" />
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Audio Recordings
                </h3>
              </div>
              <Link
                href="/recordings"
                className="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                View All
              </Link>
            </div>
            <div className="border-t border-gray-200 px-4 py-5 sm:p-6">
              <p className="text-sm text-gray-500 mb-4">
                Manage your saved audio recordings from practice sessions.
                Download recordings or save them to browser storage for future
                reference.
              </p>
              <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4">
                <Link
                  href="/recordings"
                  className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  <MicrophoneIcon className="h-5 w-5 mr-2" />
                  Manage Recordings
                </Link>
                <Link
                  href="/tests/io"
                  className="inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  <MicrophoneIcon className="h-5 w-5 mr-2 text-indigo-500" />
                  Start Recording Session
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Admin Panel Section - Only visible to admins */}
        {isAdmin && (
          <div className="mt-8">
            <div className="bg-white shadow overflow-hidden sm:rounded-lg">
              <div className="px-4 py-5 sm:px-6">
                <div className="flex items-center">
                  <ShieldCheckIcon className="h-6 w-6 text-purple-600 mr-2" />
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    Admin Panel
                  </h3>
                </div>
                <p className="mt-1 max-w-2xl text-sm text-gray-500">
                  Manage users and forum content
                </p>
              </div>
              <div className="border-t border-gray-200 px-4 py-5 sm:p-6">
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <Link
                    href="/admin/users"
                    className="relative block p-6 border border-purple-200 rounded-lg shadow-sm hover:border-purple-300 hover:shadow-md transition-all"
                  >
                    <div className="flex items-center">
                      <div className="flex-shrink-0 bg-purple-100 rounded-md p-3">
                        <UserGroupIcon className="h-6 w-6 text-purple-600" />
                      </div>
                      <div className="ml-4">
                        <h4 className="text-lg font-medium text-gray-900">
                          User Management
                        </h4>
                        <p className="mt-1 text-sm text-gray-500">
                          Manage users and assign admin privileges
                        </p>
                      </div>
                    </div>
                  </Link>

                  <Link
                    href="/admin/forum"
                    className="relative block p-6 border border-indigo-200 rounded-lg shadow-sm hover:border-indigo-300 hover:shadow-md transition-all"
                  >
                    <div className="flex items-center">
                      <div className="flex-shrink-0 bg-indigo-100 rounded-md p-3">
                        <ChatBubbleLeftRightIcon className="h-6 w-6 text-indigo-600" />
                      </div>
                      <div className="ml-4">
                        <h4 className="text-lg font-medium text-gray-900">
                          Forum Management
                        </h4>
                        <p className="mt-1 text-sm text-gray-500">
                          Moderate forum content and manage reported posts
                        </p>
                      </div>
                    </div>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* AI Analyses Section - Only visible to users with AI access, placed at bottom */}
        {canAccessAIAnalysis && (
          <div className="mt-8">
            <div className="bg-white shadow overflow-hidden sm:rounded-lg">
              <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
                <div className="flex items-center">
                  <SparklesIcon className="h-6 w-6 text-blue-500 mr-2" />
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    AI Analyses
                  </h3>
                </div>
                <Link
                  href="/profile/ai-analyses"
                  className="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  View All
                </Link>
              </div>
              <div className="border-t border-gray-200 px-4 py-5 sm:p-6">
                <p className="text-sm text-gray-500 mb-4">
                  Access your saved AI analyses from all tests. These analyses
                  provide insights into your military aptitude based on your
                  test responses.
                </p>
                <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4">
                  <Link
                    href="/profile/ai-analyses"
                    className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    <SparklesIcon className="h-5 w-5 mr-2" />
                    View AI Analyses
                  </Link>
                  <Link
                    href="/tests"
                    className="inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    <SparklesIcon className="h-5 w-5 mr-2 text-blue-500" />
                    Create New Analysis
                  </Link>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Delete Single Response Modal */}
      <ConfirmationModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={handleDeleteResponse}
        title="Delete Response"
        message={`Are you sure you want to delete this response for ${responseToDelete?.testType?.toUpperCase()} - ${
          responseToDelete?.setName
        }? This action cannot be undone.`}
        confirmButtonText="Delete"
        confirmButtonColor="red"
      />
    </div>
  );
}
