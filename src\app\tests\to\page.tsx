"use client";

import { Tab } from "@headlessui/react";
import {
  PhotoIcon,
  DocumentTextIcon,
  PencilIcon,
  ChatBubbleLeftRightIcon,
  ClipboardDocumentListIcon,
} from "@heroicons/react/24/outline";
import Link from "next/link";
import NavigationButton from "@/components/NavigationButton";

const components = [
  {
    name: "SRT",
    description: "Situation Reaction Test",
    icon: DocumentTextIcon,
    practiceLink: "/tests/to/srt/practice",
    content: (
      <div className="space-y-4 ">
        <p className="text-gray-800">
          The SRT evaluates your ability to organize thoughts logically and
          coherently in multiple situations .
        </p>
        <div className="bg-gray-100 p-4 rounded-lg">
          <h4 className="font-semibold mb-2 text-gray-900">Test Format:</h4>
          <ul className="list-disc list-inside space-y-1 text-gray-800">
            <li>Total 60 Situations</li>
            <li>30 seconds per situation</li>
            <li>30 minutes total duration</li>
          </ul>
        </div>
        <div className="bg-indigo-50 p-4 rounded-lg">
          <h4 className="font-semibold mb-2 text-gray-900">
            Key Skills Tested:
          </h4>
          <ul className="list-disc list-inside space-y-1 text-gray-800">
            <li>Logical reasoning</li>
            <li>Comprehension</li>
            <li>Attention to detail</li>
            <li>Language proficiency</li>
          </ul>
        </div>
      </div>
    ),
  },
  {
    name: "WAT",
    description: "Word Association Test",
    icon: PencilIcon,
    practiceLink: "/tests/to/wat/practice",
    content: (
      <div className="space-y-4">
        <p className="text-gray-800">
          The WAT assesses your spontaneous responses and personality traits
          through word associations.
        </p>
        <div className="bg-gray-100 p-4 rounded-lg">
          <h4 className="font-semibold mb-2 text-gray-900">Test Format:</h4>
          <ul className="list-disc list-inside space-y-1 text-gray-800">
            <li>60 stimulus words</li>
            <li>15 seconds per word</li>
            <li>Write the first word that comes to mind</li>
            <li>No right or wrong answers</li>
          </ul>
        </div>
        <div className="bg-indigo-50 p-4 rounded-lg">
          <h4 className="font-semibold mb-2 text-gray-900">
            Personality Indicators:
          </h4>
          <ul className="list-disc list-inside space-y-1 text-gray-800">
            <li>Response patterns</li>
            <li>Emotional reactions</li>
            <li>Thought processes</li>
            <li>Psychological traits</li>
          </ul>
        </div>
      </div>
    ),
  },
  {
    name: "TAT",
    description: "Thematic Apperception Test (Most Important)",
    icon: PhotoIcon,
    practiceLink: "/tests/to/tat/practice",
    content: (
      <div className="space-y-4">
        {/* Highlighted importance banner */}
        <div className="bg-red-100 border-l-4 border-red-500 p-4 rounded-lg mb-4">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-red-500"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v3.586L7.707 9.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 10.586V7z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                Critical Assessment Component
              </h3>
              <div className="mt-1 text-sm text-red-700">
                <p>
                  TAT is the most important psychological assessment in military
                  selection, providing deep insights into personality,
                  leadership potential, and decision-making under pressure.
                </p>
              </div>
            </div>
          </div>
        </div>

        <p className="text-gray-800 font-medium">
          The TAT assesses your personality through story creation based on
          ambiguous pictures, revealing unconscious thoughts, motivations, and
          psychological traits crucial for military leadership roles.
        </p>

        <div className="bg-gray-100 p-4 rounded-lg">
          <h4 className="font-semibold mb-2 text-gray-900">Test Format:</h4>
          <ul className="list-disc list-inside space-y-1 text-gray-800">
            <li>12 pictures shown sequentially</li>
            <li>4 minutes per picture</li>
            <li>Write a story for each picture</li>
            <li>Include past, present, and future in stories</li>
          </ul>
        </div>

        <div className="bg-amber-50 p-4 rounded-lg">
          <h4 className="font-semibold mb-2 text-gray-900">Story Elements:</h4>
          <ul className="list-disc list-inside space-y-1 text-gray-800">
            <li>Character development</li>
            <li>Conflict resolution</li>
            <li>Emotional themes</li>
            <li>Narrative structure</li>
          </ul>
        </div>

        <div className="bg-red-50 p-4 rounded-lg">
          <h4 className="font-semibold mb-2 text-gray-900">
            Military Significance:
          </h4>
          <ul className="list-disc list-inside space-y-1 text-gray-800">
            <li>Reveals leadership potential and command abilities</li>
            <li>Assesses decision-making under pressure</li>
            <li>Evaluates emotional resilience and stress management</li>
            <li>
              Identifies problem-solving approaches and strategic thinking
            </li>
          </ul>
        </div>
      </div>
    ),
  },
  {
    name: "SDT",
    description: "Self description Test",
    icon: ChatBubbleLeftRightIcon,
    practiceLink: "/tests/to/sdt/practice/SelfDescription/instructions",
    content: (
      <div className="space-y-4">
        <p className="text-gray-800">
          The SDT evaluates your overall understanding of yourself and your
          personality.
        </p>
        <div className="bg-gray-100 p-4 rounded-lg">
          <h4 className="font-semibold mb-2 text-gray-900">Test Format:</h4>
          <ul className="list-disc list-inside space-y-1 text-gray-800">
            <li>5 questions to be completed in 15 minutes</li>
            <li>All questions are displayed at once</li>
            <li>
              Questions focus on self-perception and others' perception of you
            </li>
          </ul>
        </div>
        <div className="bg-amber-50 p-4 rounded-lg">
          <h4 className="font-semibold mb-2 text-gray-900">Questions:</h4>
          <ol className="list-decimal list-inside space-y-1 text-gray-800">
            <li>What do your parents think of you?</li>
            <li>What do your teachers/employers think of you?</li>
            <li>What do your friends and colleagues think of you?</li>
            <li>What do you think about yourself?</li>
            <li>
              What kind of person you would like to become or what improvements
              you want to bring in yourself?
            </li>
          </ol>
        </div>
        <div className="bg-green-50 p-4 rounded-lg">
          <h4 className="font-semibold mb-2 text-gray-900">
            Assessment Criteria:
          </h4>
          <ul className="list-disc list-inside space-y-1 text-gray-800">
            <li>Situational Awareness</li>
            <li>Self Awareness</li>
          </ul>
        </div>
      </div>
    ),
  },
  {
    name: "PIQ",
    description: "Personal Information Questionnaire",
    icon: ClipboardDocumentListIcon,
    practiceLink: "/tests/to/piq",
    content: (
      <div className="space-y-4">
        <p className="text-gray-800">
          The PIQ is a comprehensive form that collects detailed information
          about your background, education, and personal details.
        </p>
        <div className="bg-gray-100 p-4 rounded-lg">
          <h4 className="font-semibold mb-2 text-gray-900">Form Sections:</h4>
          <ul className="list-disc list-inside space-y-1 text-gray-800">
            <li>Personal and residence information</li>
            <li>Educational background</li>
            <li>Family details</li>
            <li>Extracurricular activities</li>
            <li>Previous interview experiences</li>
          </ul>
        </div>
        <div className="bg-purple-50 p-4 rounded-lg">
          <h4 className="font-semibold mb-2 text-gray-900">Importance:</h4>
          <ul className="list-disc list-inside space-y-1 text-gray-800">
            <li>Forms the basis for your interview</li>
            <li>Helps assessors understand your background</li>
            <li>Reveals your attention to detail</li>
            <li>Demonstrates your communication skills</li>
          </ul>
        </div>
      </div>
    ),
  },
];

export default function TOPage() {
  return (
    <div className="bg-white py-16 sm:py-24">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-2xl text-center">
          <h2 className="text-2xl font-bold tracking-tight text-gray-900 sm:text-3xl lg:text-4xl">
            Technical Officer Test
          </h2>
          <p className="mt-2 text-base leading-7 text-gray-800 sm:text-lg sm:leading-8">
            Comprehensive psychological assessment tests to evaluate your
            personality, decision-making, and leadership potential.
          </p>
        </div>
        <div className="mx-auto mt-12 max-w-2xl lg:mx-0 lg:max-w-none">
          <Tab.Group>
            <Tab.List className="flex flex-col gap-2 sm:flex-row sm:space-x-1 sm:rounded-xl sm:bg-gray-100 sm:p-1">
              {components.map((component) => (
                <Tab
                  key={component.name}
                  className={({ selected }) =>
                    `w-full rounded-lg py-2.5 text-sm font-medium leading-5 ring-white ring-opacity-60 ring-offset-2 ${
                      component.name === "TAT"
                        ? `${
                            selected
                              ? "bg-red-600 text-white shadow-lg border-2 border-red-700"
                              : "text-red-800 bg-red-100 hover:bg-red-200 border-2 border-red-300"
                          } ring-offset-red-400 font-bold`
                        : component.name === "SRT"
                        ? `${
                            selected
                              ? "bg-blue-600 text-white shadow-lg border-2 border-blue-700"
                              : "text-blue-800 bg-blue-50 hover:bg-blue-100 border-2 border-blue-200"
                          } ring-offset-blue-400`
                        : component.name === "WAT"
                        ? `${
                            selected
                              ? "bg-green-600 text-white shadow-lg border-2 border-green-700"
                              : "text-green-800 bg-green-50 hover:bg-green-100 border-2 border-green-200"
                          } ring-offset-green-400`
                        : component.name === "SDT"
                        ? `${
                            selected
                              ? "bg-amber-600 text-white shadow-lg border-2 border-amber-700"
                              : "text-amber-800 bg-amber-50 hover:bg-amber-100 border-2 border-amber-200"
                          } ring-offset-amber-400`
                        : component.name === "PIQ"
                        ? `${
                            selected
                              ? "bg-purple-600 text-white shadow-lg border-2 border-purple-700"
                              : "text-purple-800 bg-purple-50 hover:bg-purple-100 border-2 border-purple-200"
                          } ring-offset-purple-400`
                        : `${
                            selected
                              ? "bg-indigo-700 text-white shadow"
                              : "text-gray-800 hover:bg-gray-200"
                          } ring-offset-indigo-400`
                    } focus:outline-none focus:ring-2`
                  }
                >
                  <div className="flex items-center justify-center cursor-pointer">
                    <component.icon
                      className={`h-5 w-5 mr-2 ${
                        component.name === "TAT"
                          ? "text-red-500"
                          : component.name === "SRT"
                          ? "text-blue-500"
                          : component.name === "WAT"
                          ? "text-green-500"
                          : component.name === "SDT"
                          ? "text-amber-500"
                          : component.name === "PIQ"
                          ? "text-purple-500"
                          : ""
                      }`}
                    />
                    {component.name}
                    {component.name === "TAT" && (
                      <span className="ml-1 inline-flex items-center rounded-full bg-red-100 px-2 py-0.5 text-xs font-medium text-red-800">
                        Critical
                      </span>
                    )}
                  </div>
                </Tab>
              ))}
            </Tab.List>
            <Tab.Panels className="mt-8">
              {components.map((component, idx) => (
                <Tab.Panel
                  key={idx}
                  className={`rounded-xl p-6 ${
                    component.name === "TAT"
                      ? "bg-white ring-2 ring-red-300 shadow-md border-t-4 border-red-500"
                      : component.name === "SRT"
                      ? "bg-white ring-2 ring-blue-300 shadow-md border-t-4 border-blue-500"
                      : component.name === "WAT"
                      ? "bg-white ring-2 ring-green-300 shadow-md border-t-4 border-green-500"
                      : component.name === "SDT"
                      ? "bg-white ring-2 ring-amber-300 shadow-md border-t-4 border-amber-500"
                      : component.name === "PIQ"
                      ? "bg-white ring-2 ring-purple-300 shadow-md border-t-4 border-purple-500"
                      : "bg-white ring-1 ring-gray-200 shadow-sm"
                  }`}
                >
                  {/* Improved mobile responsiveness for header section */}
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 gap-3">
                    <div>
                      <h3 className="text-lg font-medium leading-6 text-gray-900">
                        {component.name} - {component.description}
                      </h3>
                    </div>
                    <NavigationButton
                      href={component.practiceLink}
                      className={`inline-flex items-center justify-center rounded-md px-4 py-2 text-sm font-medium text-white shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 whitespace-nowrap ${
                        component.name === "TAT"
                          ? "bg-red-600 hover:bg-red-700 focus:ring-red-500 focus:ring-offset-red-200 animate-pulse"
                          : component.name === "SRT"
                          ? "bg-blue-600 hover:bg-blue-700 focus:ring-blue-500 focus:ring-offset-blue-200"
                          : component.name === "WAT"
                          ? "bg-green-600 hover:bg-green-700 focus:ring-green-500 focus:ring-offset-green-200"
                          : component.name === "SDT"
                          ? "bg-amber-600 hover:bg-amber-700 focus:ring-amber-500 focus:ring-offset-amber-200"
                          : component.name === "PIQ"
                          ? "bg-purple-600 hover:bg-purple-700 focus:ring-purple-500 focus:ring-offset-purple-200"
                          : "bg-indigo-600 hover:bg-indigo-700 focus:ring-indigo-500 focus:ring-offset-2"
                      }`}
                    >
                      {component.name === "TAT" ? (
                        <>
                          <span className="mr-1">⭐</span> Practice Now{" "}
                          <span className="ml-1">⭐</span>
                        </>
                      ) : (
                        <>Practice Now</>
                      )}
                    </NavigationButton>
                  </div>
                  <div className="mt-2">{component.content}</div>
                </Tab.Panel>
              ))}
            </Tab.Panels>
          </Tab.Group>
        </div>
      </div>
    </div>
  );
}
