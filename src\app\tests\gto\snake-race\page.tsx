"use client";

import {
  ArrowLeftIcon,
  FlagIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import Link from "next/link";
import NavigationButton from "@/components/NavigationButton";
import Image from "next/image";
import {
  snakeRaceRules,
  successTips,
  snakeRaceObstacles,
} from "@/data/snake-race-obstacles";
import { useState } from "react";

export default function SnakeRacePage() {
  const [selectedObstacle, setSelectedObstacle] = useState<number | null>(null);

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="mx-auto max-w-4xl px-6">
        <NavigationButton
          href="/tests/gto"
          className="mb-8 inline-flex items-center text-gray-600 hover:text-gray-900"
        >
          <ArrowLeftIcon className="h-5 w-5 mr-2" />
          Back to GTO Test
        </NavigationButton>

        <div className="bg-white rounded-2xl shadow-sm p-8 border-t-4 border-red-500">
          <div className="flex items-center space-x-3 mb-6">
            <div className="p-2 rounded-full bg-red-100">
              <FlagIcon className="h-8 w-8 text-red-600" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900">Snake Race</h1>
          </div>

          <div className="flex justify-center mb-10">
            <Link
              href="/tests/gto/snake-race/interactive"
              className="bg-red-500 hover:bg-red-600 text-white font-bold py-4 px-10 rounded-lg shadow-lg transition duration-200 flex items-center text-xl animate-pulse"
            >
              <FlagIcon className="h-7 w-7 mr-3" />
              Try Interactive Snake Race Simulation
            </Link>
          </div>

          <div className="prose prose-lg max-w-none">
            <p className="text-lg text-gray-700 mb-6">
              The Snake Race is an exciting and physically demanding group
              activity that tests your teamwork, coordination, competitive
              spirit, and physical fitness in a high-pressure environment.
            </p>

            <h2 className="text-xl font-semibold text-gray-900 mt-8 mb-4">
              What is Snake Race?
            </h2>
            <p>
              Snake Race is a task where different groups participate in a
              competitive race. In this task, the candidates are asked to
              compete in a race in a tent that is rolled up and appears like a
              snake. The teams must move together in perfect coordination while
              maintaining speed to outpace other teams.
            </p>

            <h2 className="text-xl font-semibold text-gray-900 mt-8 mb-4">
              Skills Evaluated
            </h2>
            <div className="bg-red-50 p-6 rounded-lg mb-8">
              <h3 className="text-lg font-medium text-red-900 mb-4">
                This task evaluates your:
              </h3>
              <ul className="list-disc pl-5 space-y-2 text-red-800">
                <li>Physical fitness and endurance</li>
                <li>Team spirit and cooperation</li>
                <li>Competitive spirit</li>
                <li>Ability to work under pressure</li>
                <li>Leadership in competitive situations</li>
                <li>Coordination and synchronization</li>
                <li>Adaptability to physical constraints</li>
                <li>Resilience and determination</li>
              </ul>
            </div>

            <h2 className="text-xl font-semibold text-gray-900 mt-8 mb-4">
              How Snake Race Works
            </h2>
            <div className="bg-gray-100 p-6 rounded-lg mb-8">
              <ol className="list-decimal pl-5 space-y-3">
                <li>
                  <strong>Team Formation:</strong> Candidates are divided into
                  teams, typically of 8-10 members.
                </li>
                <li>
                  <strong>Equipment:</strong> Each team is provided with a
                  canvas tube or tent (the &quot;snake&quot;).
                </li>
                <li>
                  <strong>Position:</strong> Team members position themselves
                  inside the canvas tube with their heads outside, holding onto
                  the tube with their hands.
                </li>
                <li>
                  <strong>Course:</strong> A race course is set up with 6
                  obstacles that teams must navigate through while carrying the
                  snake.
                </li>
                <li>
                  <strong>Race:</strong> On the signal, all teams must race
                  through the obstacles while maintaining their formation inside
                  the snake.
                </li>
                <li>
                  <strong>Coordination:</strong> Success depends on all members
                  moving in perfect synchronization through each obstacle.
                </li>
                <li>
                  <strong>Winning:</strong> The first team to cross the finish
                  line with all members still properly positioned in the snake
                  wins.
                </li>
              </ol>
            </div>

            <h2 className="text-xl font-semibold text-gray-900 mt-8 mb-4">
              Snake Race Rules
            </h2>
            <div className="bg-red-50 p-6 rounded-lg mb-8">
              <ul className="list-disc pl-5 space-y-2 text-red-800">
                {snakeRaceRules.map((rule, index) => (
                  <li key={index}>{rule}</li>
                ))}
              </ul>
            </div>

            <h2 className="text-xl font-semibold text-gray-900 mt-8 mb-4">
              The 6 Snake Race Obstacles
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
              {snakeRaceObstacles.map((obstacle) => (
                <div
                  key={obstacle.id}
                  className="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer overflow-hidden"
                  onClick={() => setSelectedObstacle(obstacle.id)}
                >
                  <div className="relative h-48 w-full bg-gray-100">
                    <Image
                      src={obstacle.image}
                      alt={`${obstacle.name} obstacle`}
                      fill
                      className="object-contain"
                      unoptimized
                    />
                  </div>
                  <div className="p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-semibold text-gray-900">
                        {obstacle.id}. {obstacle.name}
                      </h3>
                    </div>
                    <p className="text-sm text-gray-600 line-clamp-2">
                      {obstacle.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>

            {selectedObstacle && (
              <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
                <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                  <div className="p-6">
                    {snakeRaceObstacles
                      .filter((o) => o.id === selectedObstacle)
                      .map((obstacle) => (
                        <div key={obstacle.id}>
                          <div className="flex justify-between items-start mb-4">
                            <h2 className="text-2xl font-bold text-gray-900">
                              {obstacle.id}. {obstacle.name}
                            </h2>
                            <button
                              onClick={() => setSelectedObstacle(null)}
                              className="text-gray-500 hover:text-gray-700"
                            >
                              <XMarkIcon className="h-6 w-6" />
                            </button>
                          </div>
                          <div className="relative h-64 w-full mb-4 bg-gray-100 rounded-lg overflow-hidden flex items-center justify-center">
                            <Image
                              src={obstacle.image}
                              alt={`${obstacle.name} obstacle`}
                              fill
                              className="object-contain"
                              unoptimized
                            />
                          </div>
                          <div className="mb-4">
                            <h3 className="font-semibold text-gray-900 mb-2">
                              Description:
                            </h3>
                            <p className="text-gray-700">
                              {obstacle.description}
                            </p>
                          </div>
                          <div className="mb-4">
                            <h3 className="font-semibold text-gray-900 mb-2">
                              Instructions:
                            </h3>
                            <p className="text-gray-700">
                              {obstacle.instructions}
                            </p>
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900 mb-2">
                              Tips:
                            </h3>
                            <ul className="list-disc pl-5 space-y-1 text-gray-700">
                              {obstacle.tips.map((tip, index) => (
                                <li key={index}>{tip}</li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              </div>
            )}

            <h2 className="text-xl font-semibold text-gray-900 mt-8 mb-4">
              Challenges of Snake Race
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
              <div className="bg-gray-100 p-4 rounded-lg">
                <h3 className="font-medium mb-2">Physical Challenges</h3>
                <ul className="list-disc pl-5 space-y-1 text-gray-700">
                  <li>Running while constrained in the canvas</li>
                  <li>Maintaining pace with team members</li>
                  <li>Enduring physical discomfort</li>
                  <li>Breathing coordination while running</li>
                </ul>
              </div>
              <div className="bg-gray-100 p-4 rounded-lg">
                <h3 className="font-medium mb-2">Coordination Challenges</h3>
                <ul className="list-disc pl-5 space-y-1 text-gray-700">
                  <li>Synchronizing movements with teammates</li>
                  <li>Navigating turns and obstacles</li>
                  <li>Maintaining formation throughout</li>
                  <li>Adjusting to different running speeds</li>
                </ul>
              </div>
              <div className="bg-gray-100 p-4 rounded-lg">
                <h3 className="font-medium mb-2">Team Challenges</h3>
                <ul className="list-disc pl-5 space-y-1 text-gray-700">
                  <li>Effective communication while running</li>
                  <li>Supporting struggling team members</li>
                  <li>Maintaining morale under pressure</li>
                  <li>Balancing competition with cooperation</li>
                </ul>
              </div>
              <div className="bg-gray-100 p-4 rounded-lg">
                <h3 className="font-medium mb-2">Leadership Challenges</h3>
                <ul className="list-disc pl-5 space-y-1 text-gray-700">
                  <li>Directing the team effectively</li>
                  <li>Making quick decisions during the race</li>
                  <li>Motivating team members</li>
                  <li>Adapting strategy as needed</li>
                </ul>
              </div>
            </div>

            <h2 className="text-xl font-semibold text-gray-900 mt-8 mb-4">
              Preparation Tips
            </h2>
            <div className="bg-red-50 p-6 rounded-lg mb-8">
              <ul className="list-disc pl-5 space-y-3 text-red-800">
                <li>
                  <strong>Regular physical training:</strong> Build your
                  cardiovascular endurance and stamina through running,
                  swimming, or other aerobic exercises.
                </li>
                <li>
                  <strong>Team building exercises:</strong> Practice activities
                  that require coordination and synchronization with others.
                </li>
                <li>
                  <strong>Obstacle course practice:</strong> Familiarize
                  yourself with navigating physical challenges while maintaining
                  speed.
                </li>
                <li>
                  <strong>Endurance training:</strong> Work on your ability to
                  maintain effort over time, even when fatigued.
                </li>
                <li>
                  <strong>Strategy planning:</strong> Think about effective ways
                  to communicate and coordinate during the race.
                </li>
                <li>
                  <strong>Core strength:</strong> Develop your core muscles to
                  help maintain posture and stability during the race.
                </li>
                <li>
                  <strong>Breathing techniques:</strong> Practice controlled
                  breathing while exercising to maximize oxygen efficiency.
                </li>
                <li>
                  <strong>Mental preparation:</strong> Develop resilience and
                  the ability to push through discomfort.
                </li>
              </ul>
            </div>

            <h2 className="text-xl font-semibold text-gray-900 mt-8 mb-4">
              Tips for Success
            </h2>
            <div className="bg-red-50 p-6 rounded-lg mb-8">
              <ul className="list-disc pl-5 space-y-2 text-red-800">
                {successTips.map((tip, index) => (
                  <li key={index}>{tip}</li>
                ))}
              </ul>
            </div>

            <h2 className="text-xl font-semibold text-gray-900 mt-8 mb-4">
              Common Mistakes to Avoid
            </h2>
            <div className="bg-red-50 p-6 rounded-lg mb-8">
              <ul className="list-disc pl-5 space-y-2 text-red-800">
                <li>Starting too fast and burning out quickly</li>
                <li>Failing to communicate effectively with teammates</li>
                <li>Focusing only on speed at the expense of coordination</li>
                <li>Not adapting to the pace of slower team members</li>
                <li>
                  Becoming frustrated or negative when facing difficulties
                </li>
                <li>Neglecting proper positioning within the snake</li>
                <li>
                  Ignoring team strategy in favor of individual performance
                </li>
                <li>Giving up when physically challenged</li>
              </ul>
            </div>

            <div className="bg-gray-100 p-6 rounded-lg mt-10">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                Key Takeaways
              </h2>
              <p className="mb-4">
                The Snake Race is more than just a physical challenge—it&apos;s
                a comprehensive test of your ability to function as part of a
                team under pressure while maintaining competitive drive and
                physical performance.
              </p>
              <p>
                Success in this task demonstrates your potential to contribute
                to military operations that require coordinated group effort,
                physical fitness, and the ability to perform under challenging
                conditions.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
