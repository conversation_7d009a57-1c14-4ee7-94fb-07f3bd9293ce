"use client";

import { useState, useEffect, useRef } from "react";
import { useParams, useRouter } from "next/navigation";
import Image from "next/image";
import {
  ArrowLeftIcon,
  DocumentTextIcon,
  PlayIcon,
  StopIcon,
} from "@heroicons/react/24/outline";
import NavigationButton from "@/components/NavigationButton";
import { gpeScenarios, GPEScenario } from "@/data/gpe-scenarios";
import AudioRecordingManager from "@/components/AudioRecordingManager";

export default function GPEResultsPage() {
  const params = useParams();
  const router = useRouter();
  const setId = params.setId as string;

  const [selectedScenario, setSelectedScenario] = useState<GPEScenario | null>(
    null
  );
  const [solution, setSolution] = useState("");
  const [recordings, setRecordings] = useState<
    Record<string, { url: string; duration: number; blob: Blob }>
  >({});
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentPlayingKey, setCurrentPlayingKey] = useState<string | null>(
    null
  );

  const audioPlayerRef = useRef<HTMLAudioElement | null>(null);

  // Load the scenario based on the setId
  useEffect(() => {
    // Extract the set number from the setId (e.g., "set1" -> 1)
    const setNumber = parseInt(setId.replace("set", ""), 10);

    if (
      !isNaN(setNumber) &&
      setNumber > 0 &&
      setNumber <= gpeScenarios.length
    ) {
      // Scenarios are 0-indexed in the array, but set IDs are 1-indexed
      setSelectedScenario(gpeScenarios[setNumber - 1]);
    } else {
      // Handle invalid set ID
      router.push("/tests/gto/gpe/sets");
    }

    // Load solution from localStorage
    const storedSolution = localStorage.getItem(`gpe-solution-${setId}`);
    if (storedSolution) {
      setSolution(storedSolution);
    }

    // Load recordings from localStorage
    const storedRecordings = localStorage.getItem(`gpe-recordings-${setId}`);
    if (storedRecordings) {
      try {
        const parsedRecordings = JSON.parse(storedRecordings);
        // Ensure each recording has the proper structure
        const validRecordings: Record<
          string,
          { url: string; duration: number; blob: Blob }
        > = {};

        Object.entries(parsedRecordings).forEach(
          ([key, recording]: [string, any]) => {
            if (
              recording &&
              typeof recording === "object" &&
              recording.url &&
              typeof recording.duration === "number"
            ) {
              validRecordings[key] = {
                url: recording.url,
                duration: recording.duration,
                blob: recording.blob || new Blob([], { type: "audio/webm" }), // Fallback blob
              };
            }
          }
        );

        setRecordings(validRecordings);
      } catch (error) {
        console.error("Error parsing recordings:", error);
        setRecordings({});
      }
    }
  }, [setId, router]);

  // Format time as MM:SS
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  };

  // Function to play recording
  const playRecording = (recordingKey: string) => {
    // If already playing this recording, stop it
    if (isPlaying && currentPlayingKey === recordingKey) {
      stopPlayback();
      return;
    }

    // If another recording is playing, stop it first
    if (isPlaying && audioPlayerRef.current) {
      stopPlayback();
    }

    // Create and play the new audio
    const audio = new Audio(recordings[recordingKey]?.url);
    audioPlayerRef.current = audio;

    // Set up event listeners
    audio.addEventListener("ended", () => {
      setIsPlaying(false);
      setCurrentPlayingKey(null);
      audioPlayerRef.current = null;
    });

    // Start playback
    audio
      .play()
      .then(() => {
        setIsPlaying(true);
        setCurrentPlayingKey(recordingKey);
      })
      .catch((error) => {
        console.error("Error playing audio:", error);
        setIsPlaying(false);
        setCurrentPlayingKey(null);
        audioPlayerRef.current = null;
      });
  };

  // Function to stop audio playback
  const stopPlayback = () => {
    if (audioPlayerRef.current) {
      audioPlayerRef.current.pause();
      audioPlayerRef.current.currentTime = 0;
      audioPlayerRef.current = null;
    }
    setIsPlaying(false);
    setCurrentPlayingKey(null);
  };

  // Render the map for the current scenario
  const renderMap = () => {
    if (!selectedScenario) return null;

    // Get the image number from the setId (e.g., "set1" -> 1)
    const setNumber = parseInt(setId.replace("set", ""), 10);
    const imagePath = `/images/gto/gpe/img${setNumber}.png`;

    return (
      <div className="mt-6 mb-8 relative">
        <div className="aspect-w-16 aspect-h-9 rounded-lg overflow-hidden shadow-lg">
          <Image
            src={imagePath}
            alt={`Scenario map for ${selectedScenario.title}`}
            width={800}
            height={450}
            className="object-cover"
          />
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="mx-auto max-w-5xl px-6">
        <button
          onClick={() => {
            // Scroll to top of the page
            window.scrollTo({ top: 0, behavior: "smooth" });
            // Navigate back to the test page
            router.push(`/tests/gto/gpe/${setId}/test`);
          }}
          className="mb-8 inline-flex items-center text-gray-600 hover:text-gray-900"
        >
          <ArrowLeftIcon className="h-5 w-5 mr-2" />
          Back to Test
        </button>

        <div className="bg-white rounded-2xl shadow-sm p-8 border-t-4 border-green-500">
          <div className="flex items-center space-x-3 mb-6">
            <div className="p-2 rounded-full bg-green-100">
              <DocumentTextIcon className="h-8 w-8 text-green-600" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900">
              {selectedScenario
                ? `${selectedScenario.title} Results`
                : "GPE Results"}
              <span className="ml-2 text-lg font-normal text-gray-500">
                (Set {setId.replace("set", "")})
              </span>
            </h1>
          </div>

          {/* Content */}
          <div className="mb-8 space-y-8">
            <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                Scenario Details
              </h2>

              {renderMap()}

              <div className="prose prose-gray max-w-none mt-6">
                <h3 className="text-xl font-semibold mb-2">Situation</h3>
                <p className="whitespace-pre-wrap">
                  {selectedScenario?.situation}
                </p>

                <div className="mt-4 bg-amber-50 p-4 rounded-lg border border-amber-200">
                  <h4 className="font-medium mb-2 text-amber-800">
                    Time Constraints:
                  </h4>
                  <ul className="list-disc pl-5 space-y-1 text-amber-700">
                    <li>
                      Start Time: {selectedScenario?.timeConstraints.startTime}
                    </li>
                    <li>
                      End Time: {selectedScenario?.timeConstraints.endTime}
                    </li>
                    <li>
                      Total Available Hours:{" "}
                      {selectedScenario?.timeConstraints.totalAvailableHours}
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                Your Solution
              </h2>
              <div className="bg-gray-50 p-4 rounded-lg border border-gray-200 min-h-[100px] whitespace-pre-wrap">
                {solution || "No solution provided."}
              </div>
            </div>
          </div>

          {/* Recordings Section */}
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Your Recordings
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.entries(recordings).length > 0 ? (
                Object.entries(recordings).map(([key, recording]) => (
                  <div
                    key={key}
                    className="bg-gray-50 p-4 rounded-lg border border-gray-200"
                  >
                    <h3 className="font-medium mb-2">
                      {key === "discussion"
                        ? "Group Discussion"
                        : "Final Presentation"}
                    </h3>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">
                        Duration: {formatTime(recording.duration)}
                      </span>
                      <button
                        onClick={() => playRecording(key)}
                        className={`inline-flex items-center px-3 py-1 border text-sm font-medium rounded-md ${
                          isPlaying && currentPlayingKey === key
                            ? "border-red-300 text-red-700 bg-red-50 hover:bg-red-100"
                            : "border-indigo-300 text-indigo-700 bg-indigo-50 hover:bg-indigo-100"
                        }`}
                      >
                        {isPlaying && currentPlayingKey === key ? (
                          <>
                            <StopIcon className="h-4 w-4 mr-1" />
                            Stop
                          </>
                        ) : (
                          <>
                            <PlayIcon className="h-4 w-4 mr-1" />
                            Play
                          </>
                        )}
                      </button>
                    </div>
                  </div>
                ))
              ) : (
                <div className="col-span-2 bg-gray-50 p-4 rounded-lg border border-gray-200 text-center text-gray-500">
                  No recordings available.
                </div>
              )}
            </div>
          </div>

          {/* Audio Recording Manager */}
          <div className="mt-12">
            <AudioRecordingManager
              recordings={recordings}
              testType="GPE"
              isPlaying={isPlaying}
              currentPlayingKey={currentPlayingKey}
              onPlay={playRecording}
              onStop={stopPlayback}
              className="bg-gray-50 rounded-xl p-6 border border-gray-200"
            />
          </div>

          {/* Practice Again Button */}
          <div className="flex justify-center mt-8">
            <button
              onClick={() => {
                // Scroll to top of the page
                window.scrollTo({ top: 0, behavior: "smooth" });
                // Navigate back to the test page
                router.push(`/tests/gto/gpe/${setId}/test`);
              }}
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-green-600 hover:bg-green-700 shadow-sm"
            >
              <PlayIcon className="h-5 w-5 mr-2" />
              Practice Again
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
