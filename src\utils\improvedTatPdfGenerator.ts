import { jsPDF } from "jspdf";
import "jspdf-autotable";
import { militaryTraitCategories } from "./evaluationUtils";

// Helper function to add watermark and social links to all pages of a PDF
const addWatermark = (doc: jsPDF) => {
  const totalPages = doc.getNumberOfPages();

  // Save the current state
  const fontSize = doc.getFontSize();
  const textColor = doc.getTextColor();

  // Set watermark properties
  doc.setFontSize(10);
  doc.setTextColor(150, 150, 150); // Light gray color

  // Add watermark and links to each page
  for (let i = 1; i <= totalPages; i++) {
    doc.setPage(i);

    const pageWidth = doc.internal.pageSize.getWidth();
    const pageHeight = doc.internal.pageSize.getHeight();

    // Add a divider line
    doc.setDrawColor(200, 200, 200);
    doc.line(15, pageHeight - 15, pageWidth - 15, pageHeight - 15);

    // Add website name at the bottom right of the page
    doc.textWithLink(
      "balaramshiwakoti.com.np",
      pageWidth - 15,
      pageHeight - 10,
      {
        align: "right",
        url: "https://balaramshiwakoti.com.np",
      }
    );

    // Add Facebook link at the bottom left of the page
    doc.setTextColor(59, 89, 152); // Facebook blue color
    doc.setFontSize(12);
    doc.textWithLink("My Facebook", 15, pageHeight - 10, {
      url: "https://www.facebook.com/hercules.shiwakoti",
    });
    doc.setFontSize(10);
  }

  // Restore the original state
  doc.setFontSize(fontSize);
  doc.setTextColor(textColor);

  return doc;
};

/**
 * Generate a PDF with TAT test results including images
 * This version uses a more reliable approach to handle images
 */
export const generateImprovedTATPDF = async (
  setName: string,
  responses: string[],
  images: string[],
  updateStatus: (status: string) => void
): Promise<Blob> => {
  // Create a new PDF document
  const doc = new jsPDF();

  updateStatus("Creating PDF document...");

  // Add title
  doc.setFontSize(18);
  doc.text(`TAT Practice Set ${setName} - Results`, 14, 20);

  // Add date
  const today = new Date();
  const dateStr = today.toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
  doc.setFontSize(12);
  doc.setTextColor(100, 100, 100);
  doc.text(`Generated on: ${dateStr}`, 14, 35);

  // Initialize yPos for positioning elements
  let yPos = 50;

  doc.setFontSize(16);
  doc.setTextColor(0, 0, 0);
  doc.text("Your Responses", 14, yPos);
  yPos += 10;

  updateStatus("Adding responses and images...");

  // Add responses with images
  for (let index = 0; index < images.length; index++) {
    updateStatus(`Processing image ${index + 1} of ${images.length}...`);

    // Start a new page for each image and response after the first one
    if (index > 0) {
      doc.addPage();
      yPos = 20;
    }

    doc.setFontSize(14);
    doc.setTextColor(0, 0, 0);
    doc.text(`Image ${index + 1} and Your Response`, 14, yPos);
    yPos += 15;

    // Try to add the image if it's not a blank image
    if (images[index] !== "blank") {
      try {
        // For non-blank images, add a placeholder first
        doc.setDrawColor(200, 200, 200);
        doc.setFillColor(240, 240, 240);
        doc.rect(14, yPos, 180, 80, "F");

        doc.setTextColor(100, 100, 100);
        doc.setFontSize(14);
        doc.text("Loading image...", 104, yPos + 40, { align: "center" });

        // Try to load the image directly using jsPDF's addImage
        // This is a more direct approach that might work better
        try {
          // Handle relative URLs by prepending the base URL if needed
          const fullUrl = images[index].startsWith("/")
            ? `${window.location.origin}${images[index]}`
            : images[index];

          // Add the image directly to the PDF
          doc.addImage(fullUrl, "JPEG", 14, yPos, 180, 80, undefined, "MEDIUM");
        } catch (directImageError) {
          console.warn(
            `Direct image loading failed for image ${index + 1}:`,
            directImageError
          );

          // If direct loading fails, keep the placeholder
          doc.setTextColor(100, 100, 100);
          doc.setFontSize(10);
          doc.text("Image could not be displayed", 104, yPos + 50, {
            align: "center",
          });
        }

        yPos += 90;
      } catch (error) {
        console.error(`Error adding image ${index + 1} to PDF:`, error);

        // Add a placeholder if there was an error
        doc.setDrawColor(200, 200, 200);
        doc.setFillColor(240, 240, 240);
        doc.rect(14, yPos, 180, 80, "F");

        doc.setTextColor(231, 76, 60); // Red color for error
        doc.setFontSize(14);
        doc.text("Image could not be displayed", 104, yPos + 40, {
          align: "center",
        });
        doc.setTextColor(0, 0, 0);
        doc.setFontSize(10);

        yPos += 90;
      }
    } else {
      // For blank images, add a placeholder
      doc.setDrawColor(200, 200, 200);
      doc.setFillColor(240, 240, 240);
      doc.rect(14, yPos, 180, 80, "F");

      doc.setTextColor(150, 150, 150);
      doc.setFontSize(14);
      doc.text("Blank Image", 104, yPos + 40, { align: "center" });
      doc.setTextColor(0, 0, 0);
      doc.setFontSize(10);

      yPos += 90;
    }

    // Add the response
    doc.setFontSize(12);
    doc.setTextColor(0, 0, 0);
    doc.text("Your Story:", 14, yPos);
    yPos += 7;

    const response = responses[index] || "Not answered";
    const splitText = doc.splitTextToSize(response, 180);
    doc.text(splitText, 14, yPos);
  }

  updateStatus("Adding watermark and finalizing PDF...");

  // Add watermark to all pages
  addWatermark(doc);

  // Return the PDF as a blob
  return doc.output("blob");
};
