/**
 * Utility functions for managing audio recordings
 * Handles download, storage, and management of audio recordings
 */

export interface AudioRecording {
  url: string;
  duration: number;
  blob: Blob;
  timestamp?: string;
  testType?: string;
  questionId?: string;
}

export interface StoredAudioRecording {
  id: string;
  name: string;
  duration: number;
  timestamp: string;
  testType: string;
  questionId: string;
  size: number;
  mimeType: string;
}

/**
 * Download an audio recording as a file
 */
export const downloadAudioRecording = (
  recording: AudioRecording,
  filename: string
): void => {
  try {
    // Create a download link
    const url = URL.createObjectURL(recording.blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = filename;
    
    // Trigger download
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // Clean up the URL object
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error("Error downloading audio recording:", error);
    throw new Error("Failed to download recording");
  }
};

/**
 * Generate a filename for an audio recording
 */
export const generateAudioFilename = (
  testType: string,
  questionId: string,
  timestamp?: string
): string => {
  const date = timestamp ? new Date(timestamp) : new Date();
  const dateStr = date.toISOString().split('T')[0]; // YYYY-MM-DD
  const timeStr = date.toTimeString().split(' ')[0].replace(/:/g, '-'); // HH-MM-SS
  
  return `${testType}_${questionId}_${dateStr}_${timeStr}.webm`;
};

/**
 * Convert blob to base64 for storage
 */
export const blobToBase64 = (blob: Blob): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      const result = reader.result as string;
      // Remove the data URL prefix to get just the base64 data
      const base64 = result.split(',')[1];
      resolve(base64);
    };
    reader.onerror = reject;
    reader.readAsDataURL(blob);
  });
};

/**
 * Convert base64 back to blob
 */
export const base64ToBlob = (base64: string, mimeType: string = 'audio/webm'): Blob => {
  const byteCharacters = atob(base64);
  const byteNumbers = new Array(byteCharacters.length);
  
  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i);
  }
  
  const byteArray = new Uint8Array(byteNumbers);
  return new Blob([byteArray], { type: mimeType });
};

/**
 * Save recording to localStorage
 */
export const saveRecordingToStorage = async (
  recording: AudioRecording,
  testType: string,
  questionId: string
): Promise<string> => {
  try {
    const timestamp = new Date().toISOString();
    const id = `${testType}_${questionId}_${timestamp}`;
    
    // Convert blob to base64 for storage
    const base64Data = await blobToBase64(recording.blob);
    
    const storedRecording: StoredAudioRecording & { data: string } = {
      id,
      name: generateAudioFilename(testType, questionId, timestamp),
      duration: recording.duration,
      timestamp,
      testType,
      questionId,
      size: recording.blob.size,
      mimeType: recording.blob.type,
      data: base64Data
    };
    
    // Save to localStorage
    localStorage.setItem(`audio_recording_${id}`, JSON.stringify(storedRecording));
    
    // Update the recordings index
    const existingIndex = localStorage.getItem('audio_recordings_index');
    const index: StoredAudioRecording[] = existingIndex ? JSON.parse(existingIndex) : [];
    
    // Add new recording to index (without the data field)
    const { data, ...recordingMeta } = storedRecording;
    index.push(recordingMeta);
    
    localStorage.setItem('audio_recordings_index', JSON.stringify(index));
    
    return id;
  } catch (error) {
    console.error("Error saving recording to storage:", error);
    throw new Error("Failed to save recording");
  }
};

/**
 * Get all stored recordings metadata
 */
export const getStoredRecordings = (): StoredAudioRecording[] => {
  try {
    const index = localStorage.getItem('audio_recordings_index');
    return index ? JSON.parse(index) : [];
  } catch (error) {
    console.error("Error getting stored recordings:", error);
    return [];
  }
};

/**
 * Get a specific recording by ID
 */
export const getStoredRecording = (id: string): AudioRecording | null => {
  try {
    const storedData = localStorage.getItem(`audio_recording_${id}`);
    if (!storedData) return null;
    
    const parsed = JSON.parse(storedData);
    const blob = base64ToBlob(parsed.data, parsed.mimeType);
    
    return {
      url: URL.createObjectURL(blob),
      duration: parsed.duration,
      blob,
      timestamp: parsed.timestamp,
      testType: parsed.testType,
      questionId: parsed.questionId
    };
  } catch (error) {
    console.error("Error getting stored recording:", error);
    return null;
  }
};

/**
 * Delete a stored recording
 */
export const deleteStoredRecording = (id: string): boolean => {
  try {
    // Remove the recording data
    localStorage.removeItem(`audio_recording_${id}`);
    
    // Update the index
    const existingIndex = localStorage.getItem('audio_recordings_index');
    if (existingIndex) {
      const index: StoredAudioRecording[] = JSON.parse(existingIndex);
      const updatedIndex = index.filter(recording => recording.id !== id);
      localStorage.setItem('audio_recordings_index', JSON.stringify(updatedIndex));
    }
    
    return true;
  } catch (error) {
    console.error("Error deleting stored recording:", error);
    return false;
  }
};

/**
 * Get total storage usage for recordings
 */
export const getStorageUsage = (): { totalSize: number; recordingCount: number } => {
  const recordings = getStoredRecordings();
  const totalSize = recordings.reduce((sum, recording) => sum + recording.size, 0);
  
  return {
    totalSize,
    recordingCount: recordings.length
  };
};

/**
 * Format file size for display
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Format duration for display
 */
export const formatDuration = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};
