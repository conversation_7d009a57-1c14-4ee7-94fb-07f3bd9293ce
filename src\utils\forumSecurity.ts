/**
 * Security utilities for the forum
 * 
 * This file contains functions for protecting against common web security threats
 * such as XSS, CSRF, and SQL injection.
 */

import { sanitizeHtml } from './contentModeration';

/**
 * Generate a CSRF token for form submissions
 * @returns A random CSRF token
 */
export const generateCsrfToken = (): string => {
  // Generate a random string for CSRF protection
  return Math.random().toString(36).substring(2, 15) + 
         Math.random().toString(36).substring(2, 15);
};

/**
 * Validate a CSRF token
 * @param token The token to validate
 * @param storedToken The stored token to compare against
 * @returns Boolean indicating if the token is valid
 */
export const validateCsrfToken = (token: string, storedToken: string): boolean => {
  return token === storedToken;
};

/**
 * Sanitize user input to prevent XSS attacks
 * @param input The user input to sanitize
 * @returns Sanitized input
 */
export const sanitizeInput = (input: string): string => {
  return sanitizeHtml(input);
};

/**
 * Sanitize a URL to prevent open redirects
 * @param url The URL to sanitize
 * @returns Sanitized URL or null if the URL is invalid
 */
export const sanitizeUrl = (url: string): string | null => {
  try {
    // Check if the URL is relative (starts with /)
    if (url.startsWith('/')) {
      return url;
    }

    // Parse the URL to validate it
    const parsedUrl = new URL(url);
    
    // Only allow specific protocols
    if (parsedUrl.protocol !== 'http:' && parsedUrl.protocol !== 'https:') {
      return null;
    }

    // You could add additional checks here, such as allowing only specific domains
    
    return url;
  } catch (error) {
    // If the URL is invalid, return null
    return null;
  }
};

/**
 * Rate limiting for forum actions
 * Tracks the number of actions a user has performed in a given time period
 */
interface RateLimitEntry {
  count: number;
  timestamp: number;
}

const rateLimitMap = new Map<string, RateLimitEntry>();

/**
 * Check if a user has exceeded their rate limit for a specific action
 * @param userId The user ID
 * @param action The action being performed (e.g., 'create_topic', 'create_reply')
 * @param limit The maximum number of actions allowed in the time window
 * @param windowMs The time window in milliseconds
 * @returns Boolean indicating if the rate limit has been exceeded
 */
export const checkRateLimit = (
  userId: string,
  action: string,
  limit: number = 5,
  windowMs: number = 60000 // 1 minute
): boolean => {
  const key = `${userId}:${action}`;
  const now = Date.now();
  
  // Get the current rate limit entry or create a new one
  const entry = rateLimitMap.get(key) || { count: 0, timestamp: now };
  
  // If the entry is older than the window, reset it
  if (now - entry.timestamp > windowMs) {
    rateLimitMap.set(key, { count: 1, timestamp: now });
    return false;
  }
  
  // Increment the count
  entry.count++;
  rateLimitMap.set(key, entry);
  
  // Check if the limit has been exceeded
  return entry.count > limit;
};

/**
 * Reset the rate limit for a user and action
 * @param userId The user ID
 * @param action The action
 */
export const resetRateLimit = (userId: string, action: string): void => {
  const key = `${userId}:${action}`;
  rateLimitMap.delete(key);
};

/**
 * Validate a password for strength
 * @param password The password to validate
 * @returns Object with validation results
 */
export const validatePassword = (password: string): {
  isValid: boolean;
  errors: string[];
} => {
  const errors: string[] = [];
  
  // Check length
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }
  
  // Check for uppercase letters
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  // Check for lowercase letters
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  // Check for numbers
  if (!/[0-9]/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  // Check for special characters
  if (!/[^A-Za-z0-9]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Validate an email address
 * @param email The email to validate
 * @returns Boolean indicating if the email is valid
 */
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/;
  return emailRegex.test(email);
};
