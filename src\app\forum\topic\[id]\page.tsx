"use client";

import React, { useState, useEffect, use } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import { useAuth } from "@/context/GoogleAuthContext";
import {
  getTopic,
  getReplies,
  createReply,
  recalculateTopicReplyCount,
} from "@/services/forumService";
import { ForumTopic, ForumReply } from "@/types/forum";
import ReplyCard from "@/components/forum/ReplyCard";
import TopicActions from "@/components/forum/TopicActions";
import { checkRateLimit, resetRateLimit } from "@/utils/forumSecurity";
import {
  ArrowLeftIcon,
  ChatBubbleLeftRightIcon,
  ClockIcon,
  PaperAirplaneIcon,
  ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";
import { DocumentData, QueryDocumentSnapshot } from "firebase/firestore";

interface TopicPageProps {
  params: {
    id: string;
  };
}

export default function TopicPage({ params }: TopicPageProps) {
  const unwrappedParams = use(params);
  const topicId = unwrappedParams.id;
  const { user, loading } = useAuth();
  const router = useRouter();
  const [topic, setTopic] = useState<ForumTopic | null>(null);
  const [replies, setReplies] = useState<ForumReply[]>([]);
  const [lastVisible, setLastVisible] =
    useState<QueryDocumentSnapshot<DocumentData> | null>(null);
  const [hasMore, setHasMore] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [replyContent, setReplyContent] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load topic and replies when the component mounts
  useEffect(() => {
    if (!loading) {
      if (!user) {
        // Redirect to login if not authenticated
        router.push(`/login?redirect=/forum/topic/${topicId}`);
      } else {
        loadTopic();
        loadReplies();
      }
    }
  }, [user, loading, topicId]);

  // Load topic from Firestore
  const loadTopic = async () => {
    try {
      setIsLoading(true);
      const topicData = await getTopic(topicId, user?.uid);
      setTopic(topicData);
    } catch (error) {
      console.error("Error loading topic:", error);
      setError(
        "Failed to load topic. It may have been removed or you don't have permission to view it."
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Load replies from Firestore
  const loadReplies = async () => {
    try {
      setIsLoading(true);
      const result = await getReplies(topicId);
      setReplies(result.replies);
      setLastVisible(result.lastVisible);
      setHasMore(result.hasMore);

      // Recalculate the reply count to ensure it's accurate
      try {
        const updatedCount = await recalculateTopicReplyCount(topicId);
        // If we have a topic loaded, update its reply count in the state
        if (topic) {
          setTopic({
            ...topic,
            replyCount: updatedCount,
          });
        }
      } catch (countError) {
        console.error("Error recalculating reply count:", countError);
        // Continue even if this fails
      }
    } catch (error) {
      console.error("Error loading replies:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Load more replies
  const loadMoreReplies = async () => {
    if (!lastVisible || isLoading) return;

    try {
      setIsLoading(true);
      const result = await getReplies(topicId, 1, lastVisible);
      setReplies([...replies, ...result.replies]);
      setLastVisible(result.lastVisible);
      setHasMore(result.hasMore);
    } catch (error) {
      console.error("Error loading more replies:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle reply submission
  const handleSubmitReply = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!replyContent.trim()) {
      setError("Please enter a reply.");
      return;
    }

    // Check rate limiting
    if (user && checkRateLimit(user.uid, "create_reply", 5, 60000)) {
      // 5 replies per minute
      setError(
        "You're posting too frequently. Please wait a moment before posting another reply."
      );
      return;
    }

    try {
      setIsSubmitting(true);
      setError(null);

      if (!user) {
        throw new Error("You must be logged in to reply.");
      }

      // Create the reply
      await createReply(
        topicId,
        user.uid,
        user.displayName || "Anonymous",
        user.photoURL,
        replyContent
      );

      // Reset rate limit if there was an error
      resetRateLimit(user.uid, "create_reply");

      // Clear the reply content
      setReplyContent("");

      // Reload the replies
      loadReplies();
    } catch (error) {
      console.error("Error creating reply:", error);
      setError(
        error instanceof Error
          ? error.message
          : "An error occurred while posting your reply. Please try again."
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  // Format date for display
  const formatDate = (date: Date | undefined) => {
    if (!date) return "Unknown date";

    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="mx-auto max-w-4xl px-6">
          <div className="text-center">
            <h1 className="text-3xl font-bold tracking-tight text-gray-900">
              Loading...
            </h1>
          </div>
        </div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect in useEffect
  }

  if (error && !topic) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="mx-auto max-w-4xl px-6">
          <Link
            href="/forum"
            className="inline-flex items-center text-sm font-medium text-indigo-600 hover:text-indigo-500 mb-6"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-1" />
            Back to Forum
          </Link>

          <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="mx-auto max-w-4xl px-6">
        <Link
          href="/forum"
          className="inline-flex items-center text-sm font-medium text-indigo-600 hover:text-indigo-500 mb-6"
        >
          <ArrowLeftIcon className="h-4 w-4 mr-1" />
          Back to Forum
        </Link>

        {topic && (
          <>
            {/* Topic */}
            <div className="bg-white rounded-lg shadow overflow-hidden mb-6">
              <div className="p-6">
                <div className="flex flex-col sm:flex-row sm:items-start">
                  {/* Author avatar */}
                  <div className="flex-shrink-0 mb-4 sm:mb-0 sm:mr-4">
                    {topic.authorPhotoURL ? (
                      <Image
                        src={topic.authorPhotoURL}
                        alt={topic.authorName}
                        width={48}
                        height={48}
                        className="rounded-full"
                      />
                    ) : (
                      <div className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center">
                        <span className="text-gray-500 font-medium">
                          {topic.authorName.charAt(0).toUpperCase()}
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Topic content */}
                  <div className="flex-1 min-w-0">
                    <h1 className="text-xl sm:text-2xl font-bold text-gray-900 mb-2">
                      {topic.title}
                    </h1>

                    {/* Meta information */}
                    <div className="flex flex-wrap items-center text-sm text-gray-500 mb-4">
                      <span className="font-medium">{topic.authorName}</span>
                      <span className="mx-1">•</span>
                      <ClockIcon className="h-4 w-4 mr-1" />
                      <span>{formatDate(topic.createdAt)}</span>
                    </div>

                    {/* Tags */}
                    {topic.tags && topic.tags.length > 0 && (
                      <div className="mb-4 flex flex-wrap gap-1.5">
                        {topic.tags.map((tag) => {
                          // Define tag colors based on tag type
                          let tagColor = "";
                          switch (tag) {
                            case "to":
                              tagColor = "bg-blue-100 text-blue-800";
                              break;
                            case "srt":
                              tagColor = "bg-blue-50 text-blue-700";
                              break;
                            case "wat":
                              tagColor = "bg-green-50 text-green-700";
                              break;
                            case "tat":
                              tagColor = "bg-red-50 text-red-700";
                              break;
                            case "sdt":
                              tagColor = "bg-amber-50 text-amber-700";
                              break;
                            case "gto":
                              tagColor = "bg-green-100 text-green-800";
                              break;
                            case "io":
                              tagColor = "bg-amber-100 text-amber-800";
                              break;
                            case "bc":
                              tagColor = "bg-purple-100 text-purple-800";
                              break;
                            case "general":
                              tagColor = "bg-gray-100 text-gray-800";
                              break;
                            case "help":
                              tagColor = "bg-purple-100 text-purple-800";
                              break;
                            case "feedback":
                              tagColor = "bg-red-100 text-red-800";
                              break;
                            default:
                              tagColor = "bg-indigo-100 text-indigo-800";
                          }

                          // Get tag display name
                          let tagName = tag.toUpperCase();
                          if (tag === "to") tagName = "TO";
                          if (tag === "gto") tagName = "GTO";
                          if (tag === "io") tagName = "IO";
                          if (tag === "bc") tagName = "BC";
                          if (tag === "general") tagName = "General";
                          if (tag === "help") tagName = "Help";
                          if (tag === "feedback") tagName = "Feedback";

                          return (
                            <span
                              key={tag}
                              className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${tagColor}`}
                            >
                              {tagName}
                            </span>
                          );
                        })}
                      </div>
                    )}

                    {/* Content */}
                    <div className="prose prose-indigo max-w-none">
                      <p className="text-gray-700 whitespace-pre-line">
                        {topic.content}
                      </p>
                    </div>

                    {/* Stats */}
                    <div className="mt-6 flex items-center text-sm text-gray-500">
                      <div className="flex items-center">
                        <ChatBubbleLeftRightIcon className="h-4 w-4 mr-1" />
                        <span>{topic.replyCount} replies</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Topic actions (Edit/Delete for author, Report for others) */}
                <TopicActions
                  topicId={topic.id}
                  authorId={topic.authorId}
                  authorName={topic.authorName}
                />
              </div>
            </div>

            {/* Replies */}
            <div className="bg-white rounded-lg shadow overflow-hidden mb-6">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">
                  Replies ({topic.replyCount})
                </h2>
              </div>

              <div className="divide-y divide-gray-200">
                {replies.length === 0 && !isLoading ? (
                  <div className="p-6 text-center">
                    <p className="text-gray-500">
                      No replies yet. Be the first to reply!
                    </p>
                  </div>
                ) : (
                  replies.map((reply) => (
                    <ReplyCard
                      key={reply.id}
                      reply={reply}
                      onReplyUpdated={loadReplies}
                    />
                  ))
                )}

                {isLoading && (
                  <div className="p-6 text-center">
                    <p className="text-gray-500">Loading replies...</p>
                  </div>
                )}
              </div>

              {/* Load more button */}
              {hasMore && (
                <div className="p-4 text-center border-t border-gray-200">
                  <button
                    onClick={loadMoreReplies}
                    disabled={isLoading}
                    className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    {isLoading ? "Loading..." : "Load More Replies"}
                  </button>
                </div>
              )}
            </div>

            {/* Reply form */}
            <div className="bg-white rounded-lg shadow overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">
                  Post a Reply
                </h2>
              </div>

              <form onSubmit={handleSubmitReply} className="p-6">
                {/* Error message */}
                {error && (
                  <div className="mb-6 bg-red-50 border-l-4 border-red-400 p-4">
                    <div className="flex items-start">
                      <div className="flex-shrink-0">
                        <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
                      </div>
                      <div className="ml-3">
                        <p className="text-sm text-red-700">{error}</p>
                      </div>
                    </div>
                  </div>
                )}

                <div className="mb-4">
                  <textarea
                    id="reply"
                    name="reply"
                    rows={4}
                    value={replyContent}
                    onChange={(e) => setReplyContent(e.target.value)}
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    placeholder="Write your reply here..."
                    required
                  ></textarea>
                </div>

                <div className="flex justify-end">
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:bg-indigo-400 disabled:cursor-not-allowed"
                  >
                    {isSubmitting ? (
                      "Posting..."
                    ) : (
                      <>
                        <PaperAirplaneIcon className="h-4 w-4 mr-2" />
                        Post Reply
                      </>
                    )}
                  </button>
                </div>
              </form>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
