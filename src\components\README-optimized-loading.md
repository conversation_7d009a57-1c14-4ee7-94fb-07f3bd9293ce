# Optimized Loading Components

This document provides guidelines on when to use different loading components to optimize application performance.

## When to Use Loading Indicators

Loading indicators should be used selectively to avoid unnecessary UI updates that can slow down the application. Here are guidelines for when to use each type of loading component:

### Global Loading (TopProgressBar)

- Automatically shows for all navigation events
- No manual implementation needed - it's included in the root layout

### Use LoadingButton for:

- Submitting test answers - When processing user responses
- Generating PDFs - When creating downloadable content
- Audio recording/playback - In Lecturette and Board Conference sections
- Any operation that takes more than 500ms to complete

### Use SimpleButton for:

- All navigation between pages (the global TopProgressBar will handle loading indicators)
- Back buttons that return to previous pages
- Retry/Try Another buttons that restart a test
- Pause/Resume buttons within tests
- Any simple navigation or action that completes quickly

## Implementation Examples

### Navigation with Router (Use SimpleButton)

```tsx
import SimpleButton from "@/components/SimpleButton";
import { useRouter } from "next/navigation";

function MyComponent() {
  const router = useRouter();

  return (
    <SimpleButton
      onClick={() => router.push("/tests/to")}
      className="flex items-center text-gray-600 hover:text-gray-900"
    >
      <ArrowLeftIcon className="h-5 w-5 mr-2" />
      Back to TO Test
    </SimpleButton>
  );
}
```

### Form Submission (Use LoadingButton)

```tsx
import LoadingButton from "@/components/LoadingButton";

function MyComponent() {
  const handleSubmit = async () => {
    // Process form data
    await submitForm();
  };

  return (
    <LoadingButton
      onClick={handleSubmit}
      className="inline-flex items-center rounded-md bg-blue-600 px-6 py-3 text-base font-medium text-white shadow-sm hover:bg-blue-700"
      loadingText="Submitting..."
    >
      Submit Form
    </LoadingButton>
  );
}
```

## Performance Considerations

- The global TopProgressBar adds minimal overhead and provides consistent loading feedback
- Component-specific loading indicators (LoadingButton) should be used sparingly
- Use SimpleButton for navigation to avoid duplicate loading indicators
- Only use LoadingButton for operations that take significant time to complete
- Consider using the browser's Performance tab to measure the impact of loading indicators on your application
