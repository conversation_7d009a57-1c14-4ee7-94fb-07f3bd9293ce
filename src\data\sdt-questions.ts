import {
  ChatBubbleLeftRightIcon,
  UserCircleIcon,
  UserGroupIcon,
} from "@heroicons/react/24/outline";

export interface SDTQuestionSet {
  questions: string[];
  description: string;
  icon:
    | typeof ChatBubbleLeftRightIcon
    | typeof UserCircleIcon
    | typeof UserGroupIcon;
  category: string;
}

export interface SDTQuestionSets {
  [key: string]: SDTQuestionSet;
}

export const sdtQuestionSets: SDTQuestionSets = {
  SelfDescription: {
    questions: [
      "What do your parents think of you?",
      "What do your teachers/employers think of you?",
      "What do your friends and colleagues think of you?",
      "What do you think about yourself?",
      "What kind of person you would like to become or what improvements you want to bring in yourself?",
    ],
    description:
      "Self Description Test questions to be completed in 15 minutes",
    icon: UserCircleIcon,
    category: "Self Description Test",
  },
};

// All questions in a single array for the test
export const getAllSDTQuestions = (): string[] => {
  let allQuestions: string[] = [];

  Object.values(sdtQuestionSets).forEach((set) => {
    allQuestions = [...allQuestions, ...set.questions];
  });

  return allQuestions;
};
