// Import the auth context
import { useAuth } from "@/context/GoogleAuthContext";

// Role definitions
export type UserRole = "admin" | "normal";

// Check if user has admin role
export const isAdmin = (user: any): boolean => {
  // If the user object has an isAdmin property directly from the context, use that
  if (user && (user.isAdmin === true || user.isAdmin === "true")) {
    return true;
  }

  // Check if user has admin role in roles array
  if (
    user &&
    user.roles &&
    Array.isArray(user.roles) &&
    user.roles.includes("admin")
  ) {
    return true;
  }

  // Fallback to email check if needed
  const emailCheck = user?.email?.includes("admin") || false;
  return emailCheck;
};

// Role-based access control hook
export const useRoleCheck = () => {
  const {
    user,
    isAdmin: contextIsAdmin,
    canAccessAIAnalysis: contextCanAccessAIAnalysis,
    userRoles,
  } = useAuth();

  // Check if the user is an admin, handling different data types
  const checkAdminStatus = (): boolean => {
    // First check the context value
    if (contextIsAdmin === true || contextIsAdmin === "true") {
      return true;
    }

    // If we have a user object, check if it has an isAdmin property
    if (user && (user.isAdmin === true || user.isAdmin === "true")) {
      return true;
    }

    // Check if user has admin role in roles array
    if (userRoles && Array.isArray(userRoles) && userRoles.includes("admin")) {
      return true;
    }

    // Fallback to email check
    const emailCheck = user?.email?.includes("admin") || false;
    return emailCheck;
  };

  // Check if the user can access AI analysis
  const checkAIAccessStatus = (): boolean => {
    // First check the context value
    if (
      contextCanAccessAIAnalysis === true ||
      contextCanAccessAIAnalysis === "true"
    ) {
      return true;
    }

    // If we have a user object, check if it has a canAccessAIAnalysis property
    if (
      user &&
      (user.canAccessAIAnalysis === true || user.canAccessAIAnalysis === "true")
    ) {
      return true;
    }

    // Admin users can also access AI analysis
    if (checkAdminStatus()) {
      return true;
    }

    return false;
  };

  // Calculate the admin status and AI access status
  const adminStatus = checkAdminStatus();
  const aiAccessStatus = checkAIAccessStatus();

  const hasRole = (requiredRole: UserRole): boolean => {
    if (!user) return false;

    if (requiredRole === "admin") {
      // Use the calculated admin status
      return adminStatus;
    }

    // Check if user has the required role in their roles array
    if (userRoles && userRoles.length > 0) {
      return userRoles.includes(requiredRole);
    }

    return requiredRole === "normal"; // Normal users can access normal features
  };

  return {
    hasRole,
    isAdmin: adminStatus,
    canAccessAIAnalysis: aiAccessStatus,
    userRoles,
  };
};
