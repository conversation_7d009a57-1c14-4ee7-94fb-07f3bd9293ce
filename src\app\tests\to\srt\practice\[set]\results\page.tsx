"use client";

import { useRouter } from "next/navigation";
import SimpleButton from "@/components/SimpleButton";
import { useLoading } from "@/context/LoadingContext";
import { useAuth } from "@/context/GoogleAuthContext";
import {
  ArrowLeftIcon,
  DocumentArrowDownIcon,
  LockClosedIcon,
} from "@heroicons/react/24/outline";
import { use } from "react";
import { useState, useEffect } from "react";
import { situationSets } from "@/data/situationsSets";
import { generateSRTPDF } from "@/utils/pdfUtils";
import {
  militaryTraitCategories,
  analyzeResponses,
  calculateOverallScore,
} from "@/utils/evaluationUtils";
import PasswordProtectedDownload from "@/components/PasswordProtectedDownload";
import {
  getResponses,
  saveResponses,
  TestResponse,
  getResponseHistory,
} from "@/services/responseService";
import AIAnalysisSection from "@/components/AIAnalysisSection";

// Add the downloadPDF function for the premium analysis teaser
const downloadPDF = async (
  setName: string,
  responses: string[],
  situations: string[],
  analysis: Record<string, number>,
  setIsGeneratingPDF: (isGenerating: boolean) => void,
  setGenerationStatus: (status: string) => void
) => {
  try {
    // Show loading indicator
    setIsGeneratingPDF(true);
    setGenerationStatus("Preparing PDF...");

    // Short delay to ensure loading state is visible
    await new Promise((resolve) => setTimeout(resolve, 500));

    // Generate the PDF
    setGenerationStatus("Generating PDF...");
    const blob = generateSRTPDF(
      setName,
      responses,
      situations,
      analysis,
      militaryTraitCategories
    );

    // Create download link
    setGenerationStatus("PDF generated! Downloading...");
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `SRT_Set_${setName}_Results.pdf`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Clean up the URL object
    URL.revokeObjectURL(url);

    // Show success message briefly
    setGenerationStatus("Download complete!");
    await new Promise((resolve) => setTimeout(resolve, 1500));
  } catch (error) {
    console.error("Error generating PDF:", error);
    setGenerationStatus("Error generating PDF. Please try again.");
    await new Promise((resolve) => setTimeout(resolve, 2000));
  } finally {
    // Hide loading indicator
    setIsGeneratingPDF(false);
    setGenerationStatus("");
  }
};

export default function SRTResults({
  params,
}: {
  params: Promise<{ set: string }>;
}) {
  const router = useRouter();
  const { startLoading } = useLoading();
  const { user } = useAuth();
  const { set } = use(params);
  const setName = decodeURIComponent(set);
  const [responses, setResponses] = useState<string[]>([]);
  const [situations, setSituations] = useState<string[]>([]);
  const [analysis, setAnalysis] = useState<Record<string, number>>({});
  // Used in the Premium Analysis Teaser section
  const [overallScore, setOverallScore] = useState<number>(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);
  const [generationStatus, setGenerationStatus] = useState("");
  const [currentTimestamp, setCurrentTimestamp] = useState<number>(Date.now());
  const [selectedHistoricalResponse, setSelectedHistoricalResponse] =
    useState<TestResponse | null>(null);

  // Handle selecting a historical response
  const handleSelectHistoricalResponse = (response: TestResponse) => {
    setSelectedHistoricalResponse(response);

    if (response.responses) {
      // Ensure responses array is the same length as situations array
      const paddedResponses = [...response.responses];
      while (paddedResponses.length < situations.length) {
        paddedResponses.push("");
      }

      setResponses(paddedResponses);

      // Analyze responses for the PDF
      const analysisResults = analyzeResponses(
        response.responses,
        militaryTraitCategories
      );
      setAnalysis(analysisResults);

      // Calculate overall score for display
      const overall = calculateOverallScore(
        analysisResults,
        response.responses.length
      );
      setOverallScore(overall);

      // Update the current timestamp for AI analysis
      if (response.timestamp) {
        setCurrentTimestamp(response.timestamp);
        console.log(
          "Updated timestamp for historical response:",
          response.timestamp
        );
      }
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      // Check for timestamp parameter in URL
      const urlParams = new URLSearchParams(window.location.search);
      const timestampParam = urlParams.get("timestamp");
      const specificTimestamp = timestampParam
        ? parseInt(timestampParam)
        : null;

      console.log(`Looking for response with timestamp: ${specificTimestamp}`);

      // setName is now defined at the component level
      let situationsLength = 0;

      // Check if the set exists
      if (situationSets[setName]) {
        const currentSet = situationSets[setName];

        // Get the situations for this set
        if (currentSet.situations) {
          setSituations(currentSet.situations);
          situationsLength = currentSet.situations.length;
        } else {
          setError(`No situations found in set "${setName}".`);
          setLoading(false);
          return;
        }
      } else {
        setError(`Set "${setName}" not found. Please select a valid set.`);
        setLoading(false);
        return;
      }

      try {
        // Try to get responses from Firestore if user is logged in
        if (user) {
          // If we have a specific timestamp, get the response history and find the matching response
          if (specificTimestamp) {
            const responseHistory = await getResponseHistory(
              user.uid,
              "srt",
              setName
            );
            console.log(`Found ${responseHistory.length} historical responses`);

            // Find the response with the matching timestamp
            const matchingResponse = responseHistory.find(
              (r) => r.timestamp === specificTimestamp
            );

            if (matchingResponse) {
              console.log(
                `Found matching response with timestamp ${specificTimestamp}`
              );
              setSelectedHistoricalResponse(matchingResponse);

              // Ensure responses array is the same length as situations array
              const paddedResponses = [...matchingResponse.responses];
              while (paddedResponses.length < situationsLength) {
                paddedResponses.push("");
              }

              setResponses(paddedResponses);

              // Analyze responses for the PDF
              const analysisResults = analyzeResponses(
                matchingResponse.responses,
                militaryTraitCategories
              );
              setAnalysis(analysisResults);

              // Calculate overall score for display
              const overall = calculateOverallScore(
                analysisResults,
                matchingResponse.responses.length
              );
              setOverallScore(overall);

              // Set the current timestamp for AI analysis
              setCurrentTimestamp(specificTimestamp);
              console.log(
                "Using specific timestamp for AI analysis:",
                specificTimestamp
              );

              setLoading(false);
              return;
            } else {
              console.log(
                `No response found with timestamp ${specificTimestamp}, falling back to latest`
              );
            }
          }

          // If no specific timestamp or no matching response found, get the latest response
          const responseData = await getResponses(user.uid, "srt", setName);

          if (responseData && responseData.responses) {
            // Ensure responses array is the same length as situations array
            const paddedResponses = [...responseData.responses];
            while (paddedResponses.length < situationsLength) {
              paddedResponses.push("");
            }

            setResponses(paddedResponses);

            // Analyze responses for the PDF
            const analysisResults = analyzeResponses(
              responseData.responses,
              militaryTraitCategories
            );
            setAnalysis(analysisResults);

            // Calculate overall score for display
            const overall = calculateOverallScore(
              analysisResults,
              responseData.responses.length
            );
            setOverallScore(overall);

            // Set the current timestamp for AI analysis
            if (responseData.timestamp) {
              setCurrentTimestamp(responseData.timestamp);
              console.log(
                "Using response timestamp for AI analysis:",
                responseData.timestamp
              );
            }

            setLoading(false);
            return;
          }
        }

        // Fall back to localStorage if not logged in or no cloud data found
        const storedSetId = localStorage.getItem("srt_current_set");
        if (storedSetId !== setName) {
          console.warn(
            `Stored set ID (${storedSetId}) doesn't match URL parameter (${setName})`
          );
          // We'll continue anyway, but log the warning
        }

        // Try set-specific key first
        let savedResponses = localStorage.getItem(`srt_responses_${setName}`);

        // Fall back to generic key for backward compatibility
        if (!savedResponses) {
          savedResponses = localStorage.getItem("srt_responses");
          console.log("Using generic localStorage key as fallback");
        } else {
          console.log("Using set-specific localStorage key");
        }

        if (savedResponses) {
          try {
            const parsedResponses = JSON.parse(savedResponses);

            // Ensure responses array is the same length as situations array
            const paddedResponses = [...parsedResponses];
            while (paddedResponses.length < situationsLength) {
              paddedResponses.push("");
            }

            setResponses(paddedResponses);

            // Analyze responses for the PDF
            const analysisResults = analyzeResponses(
              parsedResponses,
              militaryTraitCategories
            );
            setAnalysis(analysisResults);

            // Calculate overall score for display
            const overall = calculateOverallScore(
              analysisResults,
              parsedResponses.length
            );
            setOverallScore(overall);

            // If user is logged in, save to cloud storage, but check if they were already saved recently
            if (user) {
              // Check if we have a recent save timestamp in localStorage
              const lastSaveTimestamp = localStorage.getItem(
                `srt_${setName}_last_save_timestamp`
              );
              const currentTime = new Date().getTime();
              const fiveMinutesAgo = currentTime - 5 * 60 * 1000; // 5 minutes in milliseconds

              // Only save if we don't have a timestamp or it's older than 5 minutes
              if (
                !lastSaveTimestamp ||
                parseInt(lastSaveTimestamp) < fiveMinutesAgo
              ) {
                setIsSaving(true);
                try {
                  const timestamp = await saveResponses(
                    user.uid,
                    "srt",
                    setName,
                    parsedResponses
                  );
                  console.log(
                    "Saved responses to Firestore from results page, timestamp:",
                    timestamp
                  );
                  // Update the last save timestamp in localStorage
                  localStorage.setItem(
                    `srt_${setName}_last_save_timestamp`,
                    timestamp.toString()
                  );

                  // Update the current timestamp for AI analysis
                  setCurrentTimestamp(timestamp);
                  console.log(
                    "Updated current timestamp for AI analysis:",
                    timestamp
                  );
                } catch (error) {
                  console.error("Error saving responses to Firestore:", error);
                } finally {
                  setIsSaving(false);
                }
              } else {
                console.log(
                  "Skipping save to Firestore - responses were saved recently"
                );
              }
            }
          } catch (e) {
            setError(`Error parsing saved responses: ${e}`);
          }
        } else {
          setError(
            "No responses found. Please complete the test before viewing results."
          );
        }
      } catch (error) {
        console.error("Error fetching responses:", error);
        setError("Failed to load your responses. Please try again.");
      }

      setLoading(false);
    };

    fetchData();
  }, [set, user]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="mx-auto max-w-3xl px-6">
          <div className="bg-white rounded-2xl shadow-sm p-8">
            <h1 className="text-2xl font-bold text-gray-900">Loading...</h1>
            <p className="mt-4 text-gray-600">
              Please wait while we load your results.
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="mx-auto max-w-3xl px-6">
          <div className="bg-white rounded-2xl shadow-sm p-8">
            <h1 className="text-2xl font-bold text-gray-900">Error</h1>
            <p className="mt-4 text-gray-600">{error}</p>
            <SimpleButton
              onClick={() => router.push("/tests/to/srt/practice")}
              className="mt-6 inline-flex justify-center rounded-md bg-blue-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-700"
            >
              Back to Practice Sets
            </SimpleButton>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="mx-auto max-w-4xl px-6">
        <SimpleButton
          onClick={() => router.push("/tests/to/srt/practice")}
          className="mb-8 flex items-center text-gray-600 hover:text-gray-900"
        >
          <ArrowLeftIcon className="h-5 w-5 mr-2" />
          Back to Practice Sets
        </SimpleButton>

        <div className="space-y-8">
          {/* Results Header */}
          <div className="bg-white rounded-2xl shadow-sm p-4 sm:p-8">
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-4 sm:mb-6 text-center sm:text-left">
              SRT Practice {decodeURIComponent(set)} - Results
            </h1>
            <p className="text-gray-600 text-center sm:text-left">
              Below are your responses to each situation in the Situation
              Reaction Test.
            </p>
          </div>

          {/* PDF Download Section */}
          <div className="bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl shadow-lg p-4 sm:p-8 text-white">
            <div className="flex flex-col sm:flex-row items-center sm:items-start">
              <div className="flex-1">
                <h2 className="text-xl sm:text-2xl font-bold mb-4 text-center sm:text-left">
                  Download Your Responses
                </h2>
                <p className="mb-4 text-center sm:text-left">
                  Get a PDF copy of your responses for offline review:
                </p>
                <ul className="space-y-2 mb-6 text-sm sm:text-base">
                  <li className="flex items-center">
                    <span className="mr-2 text-yellow-300">✓</span> Complete
                    record of all your responses
                  </li>
                  <li className="flex items-center">
                    <span className="mr-2 text-yellow-300">✓</span> Convenient
                    PDF format for offline review
                  </li>
                  <li className="flex items-center">
                    <span className="mr-2 text-yellow-300">✓</span> Printable
                    format for your records
                  </li>
                </ul>
                <div className="flex justify-center sm:justify-start">
                  <PasswordProtectedDownload
                    onDownload={() =>
                      downloadPDF(
                        set,
                        responses,
                        situations,
                        analysis,
                        setIsGeneratingPDF,
                        setGenerationStatus
                      )
                    }
                    buttonClassName="inline-flex items-center justify-center rounded-md bg-white px-4 sm:px-6 py-2 sm:py-3 text-sm font-semibold text-blue-600 shadow-sm hover:bg-gray-100 w-full sm:w-auto"
                    isLoading={isGeneratingPDF}
                    statusMessage={generationStatus}
                  >
                    <LockClosedIcon className="h-5 w-5 mr-1" />
                    <DocumentArrowDownIcon className="h-5 w-5 mr-2" />
                    Download Responses PDF
                  </PasswordProtectedDownload>
                </div>
              </div>
              <div className="hidden sm:block md:w-1/4 mt-4 sm:mt-0">
                <div className="rounded-full bg-white/20 p-6 backdrop-blur-sm">
                  <div className="text-center">
                    <div className="text-4xl font-bold mb-1">100%</div>
                    <div className="text-sm">Free</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Summary Section */}
          <div className="bg-white rounded-2xl shadow-sm p-4 sm:p-8">
            <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-4 sm:mb-6 text-center sm:text-left">
              Test Summary
            </h2>
            <p className="text-gray-600 mb-6 text-center sm:text-left">
              You have responded to{" "}
              {responses.filter((r) => r && r.trim() !== "").length} out of{" "}
              {situations.length} situations. Use the download button above to
              view your complete responses and analysis.
            </p>

            {selectedHistoricalResponse && (
              <div className="bg-blue-50 p-4 rounded-lg mb-4">
                <p className="text-blue-800 font-medium text-center sm:text-left">
                  Viewing historical attempt from{" "}
                  {selectedHistoricalResponse.timestamp
                    ? new Date(
                        selectedHistoricalResponse.timestamp
                      ).toLocaleString()
                    : "an earlier date"}
                </p>
                <div className="flex justify-center sm:justify-start">
                  <button
                    onClick={() => window.location.reload()}
                    className="mt-2 text-blue-600 text-sm font-medium hover:text-blue-800"
                  >
                    Return to latest attempt
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* AI Analysis Section */}
          <AIAnalysisSection
            responses={responses.filter((r) => r && r.trim() !== "")}
            testType="SRT"
            prompts={situations}
            setName={set}
            responseTimestamp={currentTimestamp}
          />

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row justify-between space-y-4 sm:space-y-0">
            <SimpleButton
              onClick={() => router.push(`/tests/to/srt/practice/${set}/test`)}
              className="inline-flex justify-center rounded-md bg-blue-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-700 w-full sm:w-auto"
            >
              Retry Test
            </SimpleButton>
            <SimpleButton
              onClick={() => router.push("/tests/to/srt/practice")}
              className="inline-flex justify-center rounded-md bg-white px-4 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 w-full sm:w-auto"
            >
              Try Another Set
            </SimpleButton>
          </div>
        </div>
      </div>
    </div>
  );
}
