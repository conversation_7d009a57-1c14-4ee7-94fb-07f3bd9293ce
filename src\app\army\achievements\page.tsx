"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import {
  ArrowLeftIcon,
  ChevronRightIcon,
  XMarkIcon,
  ChevronLeftIcon,
} from "@heroicons/react/24/outline";

// Import achievement data from the data file
import {
  achievementSections,
  paginateAchievements,
  ITEMS_PER_PAGE,
  AchievementSection,
  PeacekeepingContent,
  DisasterContent,
  AwardsContent,
  InfrastructureContent,
} from "@/data/armyblog";

export default function ArmyAchievementsPage() {
  const [selectedSection, setSelectedSection] =
    useState<AchievementSection | null>(null);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [currentImageIndex, setCurrentImageIndex] = useState<number>(0);
  const [paginatedData, setPaginatedData] = useState<{
    items: AchievementSection[];
    totalPages: number;
    currentPage: number;
  }>({ items: [], totalPages: 0, currentPage: 1 });

  // Initialize pagination data
  useEffect(() => {
    const data = paginateAchievements(achievementSections, currentPage);
    setPaginatedData(data);
  }, [currentPage]);

  // Reset image index when changing selected section
  useEffect(() => {
    setCurrentImageIndex(0);
  }, [selectedSection]);

  // Handle page navigation
  const handleNextPage = () => {
    if (currentPage < paginatedData.totalPages) {
      setCurrentPage(currentPage + 1);
      // Scroll to top of the page
      window.scrollTo({ top: 0, behavior: "smooth" });
    }
  };

  const handlePreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
      // Scroll to top of the page
      window.scrollTo({ top: 0, behavior: "smooth" });
    }
  };

  // Handle image gallery navigation
  const handleNextImage = () => {
    if (selectedSection?.images) {
      const totalImages = selectedSection.images.additional.length + 1; // +1 for main image
      setCurrentImageIndex((prevIndex) => (prevIndex + 1) % totalImages);
    }
  };

  const handlePreviousImage = () => {
    if (selectedSection?.images) {
      const totalImages = selectedSection.images.additional.length + 1; // +1 for main image
      setCurrentImageIndex(
        (prevIndex) => (prevIndex - 1 + totalImages) % totalImages
      );
    }
  };

  // Function to render the detailed content for each section
  const renderDetailedContent = (section: AchievementSection) => {
    // Display image gallery at the top of each detailed section
    const renderImage = () => {
      // Check if section has multiple images
      if (section.images) {
        // Get current image source and caption
        const allImages = [section.images.main, ...section.images.additional];
        const currentImage = allImages[currentImageIndex];

        // Get caption if available
        let caption = section.title;
        if (
          section.images.captions &&
          section.images.captions[currentImageIndex]
        ) {
          caption = section.images.captions[currentImageIndex];
        }

        return (
          <div className="w-full mb-6 rounded-lg overflow-hidden bg-gray-100">
            <div className="relative">
              {/* Image container with navigation controls as siblings, not children */}
              <div className="relative w-full" style={{ height: "400px" }}>
                <a
                  href={currentImage}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="cursor-zoom-in block absolute inset-0 z-0"
                  title="Click to view full size image"
                  onClick={(e) => {
                    // Only allow click if not clicking on navigation buttons
                    if ((e.target as HTMLElement).closest("button")) {
                      e.preventDefault();
                    }
                  }}
                >
                  <Image
                    src={currentImage}
                    alt={caption}
                    fill
                    sizes="(max-width: 768px) 100vw, 800px"
                    style={{
                      objectFit: "contain",
                    }}
                    priority
                    onError={(e) => {
                      console.error("Image failed to load:", e);
                    }}
                  />
                </a>

                {/* Image navigation controls - positioned as siblings to the link */}
                {allImages.length > 1 && (
                  <>
                    <button
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        handlePreviousImage();
                      }}
                      className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-all z-20"
                      aria-label="Previous image"
                    >
                      <ChevronLeftIcon className="h-6 w-6" />
                    </button>
                    <button
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        handleNextImage();
                      }}
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-all z-20"
                      aria-label="Next image"
                    >
                      <ChevronRightIcon className="h-6 w-6" />
                    </button>
                  </>
                )}
              </div>
            </div>

            {/* Image caption and counter */}
            <div className="p-2 bg-gray-50">
              <p className="text-sm text-gray-500 italic text-center">
                {caption}{" "}
                {allImages.length > 1 &&
                  `(${currentImageIndex + 1}/${allImages.length})`}
              </p>
              <p className="text-xs text-gray-400 text-center mt-1">
                Click to enlarge
              </p>
            </div>

            {/* Thumbnail navigation for multiple images */}
            {allImages.length > 1 && (
              <div className="flex overflow-x-auto gap-2 p-2 bg-gray-50 border-t border-gray-100">
                {allImages.map((img, index) => (
                  <button
                    key={index}
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      setCurrentImageIndex(index);
                    }}
                    className={`relative w-16 h-16 flex-shrink-0 rounded overflow-hidden ${
                      index === currentImageIndex
                        ? "ring-2 ring-blue-500"
                        : "opacity-70"
                    }`}
                  >
                    <Image
                      src={img}
                      alt={`Thumbnail ${index + 1}`}
                      fill
                      sizes="64px"
                      style={{ objectFit: "cover" }}
                    />
                  </button>
                ))}
              </div>
            )}
          </div>
        );
      }

      // Fallback to single image display
      return (
        <div className="w-full mb-6 rounded-lg overflow-hidden bg-gray-100">
          <a
            href={section.image}
            target="_blank"
            rel="noopener noreferrer"
            className="cursor-zoom-in block"
            title="Click to view full size image"
          >
            <div className="relative w-full" style={{ height: "400px" }}>
              <Image
                src={section.image}
                alt={section.title}
                fill
                sizes="(max-width: 768px) 100vw, 800px"
                style={{
                  objectFit: "contain",
                }}
                priority
                onError={(e) => {
                  console.error("Image failed to load:", e);
                }}
              />
            </div>
            <p className="text-sm text-gray-500 italic text-center mt-2">
              {section.title} (Click to enlarge)
            </p>
          </a>
        </div>
      );
    };

    // Detect content type based on structure rather than ID
    const content = section.content;

    // Check if content has statistics property (PeacekeepingContent)
    if ("statistics" in content) {
      const peacekeepingContent = content as PeacekeepingContent;
      return (
        <>
          {renderImage()}
          {peacekeepingContent.mainText.map((text: string, index: number) => (
            <p key={index} className="text-gray-700 mb-4">
              {text}
            </p>
          ))}
          <div
            className={`p-4 rounded-lg border mb-4 ${
              section.color === "blue"
                ? "bg-blue-50 border-blue-100"
                : section.color === "emerald"
                ? "bg-emerald-50 border-emerald-100"
                : section.color === "amber"
                ? "bg-amber-50 border-amber-100"
                : "bg-purple-50 border-purple-100"
            }`}
          >
            <h3
              className={`text-lg font-semibold mb-2 ${
                section.color === "blue"
                  ? "text-blue-800"
                  : section.color === "emerald"
                  ? "text-emerald-800"
                  : section.color === "amber"
                  ? "text-amber-800"
                  : "text-purple-800"
              }`}
            >
              {peacekeepingContent.statistics.title}
            </h3>
            <ul className="list-disc pl-5 text-gray-700 space-y-1">
              {peacekeepingContent.statistics.items.map(
                (item: string, index: number) => (
                  <li key={index}>{item}</li>
                )
              )}
            </ul>
          </div>
          <p className="text-gray-700">{peacekeepingContent.conclusion}</p>
        </>
      );
    }

    // Check if content has award property (AwardsContent)
    else if ("award" in content) {
      const awardsContent = content as AwardsContent;
      return (
        <>
          {renderImage()}
          {awardsContent.mainText.map((text: string, index: number) => (
            <p key={index} className="text-gray-700 mb-4">
              {text}
            </p>
          ))}
          <div
            className={`p-4 rounded-lg border mb-4 ${
              section.color === "blue"
                ? "bg-blue-50 border-blue-100"
                : section.color === "emerald"
                ? "bg-emerald-50 border-emerald-100"
                : section.color === "amber"
                ? "bg-amber-50 border-amber-100"
                : "bg-purple-50 border-purple-100"
            }`}
          >
            <h3
              className={`text-lg font-semibold mb-2 ${
                section.color === "blue"
                  ? "text-blue-800"
                  : section.color === "emerald"
                  ? "text-emerald-800"
                  : section.color === "amber"
                  ? "text-amber-800"
                  : "text-purple-800"
              }`}
            >
              {awardsContent.award.title}
            </h3>
            {awardsContent.award.description.map(
              (text: string, index: number) => (
                <p
                  key={index}
                  className={`text-gray-700 ${
                    index < awardsContent.award.description.length - 1
                      ? "mb-2"
                      : ""
                  }`}
                >
                  {text}
                </p>
              )
            )}
          </div>
          <p className="text-gray-700">{awardsContent.conclusion}</p>
        </>
      );
    }

    // Check if content has projects property (InfrastructureContent)
    else if ("projects" in content) {
      const infrastructureContent = content as InfrastructureContent;
      return (
        <>
          {renderImage()}
          {infrastructureContent.mainText.map((text: string, index: number) => (
            <p key={index} className="text-gray-700 mb-4">
              {text}
            </p>
          ))}

          <div
            className={`p-4 rounded-lg border mb-4 ${
              section.color === "blue"
                ? "bg-blue-50 border-blue-100"
                : section.color === "emerald"
                ? "bg-emerald-50 border-emerald-100"
                : section.color === "amber"
                ? "bg-amber-50 border-amber-100"
                : "bg-purple-50 border-purple-100"
            }`}
          >
            <h3
              className={`text-lg font-semibold mb-2 ${
                section.color === "blue"
                  ? "text-blue-800"
                  : section.color === "emerald"
                  ? "text-emerald-800"
                  : section.color === "amber"
                  ? "text-amber-800"
                  : "text-purple-800"
              }`}
            >
              {infrastructureContent.projects.title}
            </h3>
            <ul className="list-disc pl-5 text-gray-700 space-y-1">
              {infrastructureContent.projects.items.map(
                (item: string, index: number) => (
                  <li key={index}>{item}</li>
                )
              )}
            </ul>
          </div>

          <p className="text-gray-700 mb-4">
            {infrastructureContent.additionalText}
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
            {infrastructureContent.sections.map(
              (
                sectionItem: { title: string; items: string[] },
                sectionIndex: number
              ) => (
                <div
                  key={sectionIndex}
                  className={`p-4 rounded-lg border ${
                    section.color === "blue"
                      ? "bg-blue-50 border-blue-100"
                      : section.color === "emerald"
                      ? "bg-emerald-50 border-emerald-100"
                      : section.color === "amber"
                      ? "bg-amber-50 border-amber-100"
                      : "bg-purple-50 border-purple-100"
                  }`}
                >
                  <h3
                    className={`text-lg font-semibold mb-2 ${
                      section.color === "blue"
                        ? "text-blue-800"
                        : section.color === "emerald"
                        ? "text-emerald-800"
                        : section.color === "amber"
                        ? "text-amber-800"
                        : "text-purple-800"
                    }`}
                  >
                    {sectionItem.title}
                  </h3>
                  <ul className="list-disc pl-5 text-gray-700 space-y-1">
                    {sectionItem.items.map(
                      (item: string, itemIndex: number) => (
                        <li key={itemIndex}>{item}</li>
                      )
                    )}
                  </ul>
                </div>
              )
            )}
          </div>

          <p className="text-gray-700">{infrastructureContent.conclusion}</p>
        </>
      );
    }

    // Check if content has sections property (DisasterContent)
    else if ("sections" in content) {
      const disasterContent = content as DisasterContent;
      return (
        <>
          {renderImage()}
          {disasterContent.mainText.map((text: string, index: number) => (
            <p key={index} className="text-gray-700 mb-4">
              {text}
            </p>
          ))}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
            {disasterContent.sections.map(
              (
                sectionItem: { title: string; items: string[] },
                sectionIndex: number
              ) => (
                <div
                  key={sectionIndex}
                  className={`p-4 rounded-lg border ${
                    section.color === "blue"
                      ? "bg-blue-50 border-blue-100"
                      : section.color === "emerald"
                      ? "bg-emerald-50 border-emerald-100"
                      : section.color === "amber"
                      ? "bg-amber-50 border-amber-100"
                      : "bg-purple-50 border-purple-100"
                  }`}
                >
                  <h3
                    className={`text-lg font-semibold mb-2 ${
                      section.color === "blue"
                        ? "text-blue-800"
                        : section.color === "emerald"
                        ? "text-emerald-800"
                        : section.color === "amber"
                        ? "text-amber-800"
                        : "text-purple-800"
                    }`}
                  >
                    {sectionItem.title}
                  </h3>
                  <ul className="list-disc pl-5 text-gray-700 space-y-1">
                    {sectionItem.items.map(
                      (item: string, itemIndex: number) => (
                        <li key={itemIndex}>{item}</li>
                      )
                    )}
                  </ul>
                </div>
              )
            )}
          </div>
          <p className="text-gray-700">{disasterContent.conclusion}</p>
        </>
      );
    }

    // Default case if content structure doesn't match any known type
    else {
      return (
        <>
          {renderImage()}
          <p className="text-gray-700 mb-4">
            Content details not available for this section.
          </p>
        </>
      );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        {/* Navigation */}
        <div className="mb-8">
          <Link
            href="/army"
            className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-indigo-600"
          >
            <ArrowLeftIcon className="mr-2 h-4 w-4" />
            Back to Nepal Army
          </Link>
        </div>

        {/* Page Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl">
            Modern Achievements of Nepal Army
          </h1>
          <p className="mt-6 text-lg leading-8 text-gray-600 max-w-3xl mx-auto">
            Explore the significant contributions and accomplishments of the
            Nepal Army in recent decades, from international peacekeeping to
            disaster response and humanitarian operations.
          </p>
        </div>

        {/* Achievement Cards Grid */}
        {!selectedSection && (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-8">
              {paginatedData.items.map((section) => (
                <div
                  key={section.id}
                  className={`bg-white rounded-xl shadow-md overflow-hidden border ${
                    section.color === "blue"
                      ? "border-blue-200"
                      : section.color === "emerald"
                      ? "border-emerald-200"
                      : section.color === "amber"
                      ? "border-amber-200"
                      : "border-purple-200"
                  } hover:shadow-lg transition-all duration-300 cursor-pointer`}
                  onClick={() =>
                    setSelectedSection(section as AchievementSection)
                  }
                >
                  <div className="relative h-48 w-full">
                    <Image
                      src={section.image}
                      alt={section.title}
                      fill
                      style={{ objectFit: "cover" }}
                    />
                  </div>
                  <div className="p-6">
                    <div
                      className={`inline-flex items-center justify-center p-2 rounded-full ${
                        section.color === "blue"
                          ? "bg-blue-100 text-blue-700"
                          : section.color === "emerald"
                          ? "bg-emerald-100 text-emerald-700"
                          : section.color === "amber"
                          ? "bg-amber-100 text-amber-700"
                          : "bg-purple-100 text-purple-700"
                      } mb-4`}
                    >
                      <section.icon className="h-6 w-6" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-2">
                      {section.title}
                    </h3>
                    <p className="text-gray-600 mb-4">{section.description}</p>
                    <div className="flex justify-between items-center">
                      <div
                        className={`inline-flex items-center ${
                          section.color === "blue"
                            ? "text-blue-600"
                            : section.color === "emerald"
                            ? "text-emerald-600"
                            : section.color === "amber"
                            ? "text-amber-600"
                            : "text-purple-600"
                        } font-medium`}
                      >
                        Read more <ChevronRightIcon className="ml-1 h-4 w-4" />
                      </div>

                      {/* Show gallery badge if multiple images are available */}
                      {section.images &&
                        section.images.additional.length > 0 && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              className="h-3 w-3 mr-1"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                              />
                            </svg>
                            Gallery ({section.images.additional.length + 1})
                          </span>
                        )}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Pagination Controls */}
            {paginatedData.totalPages > 1 && (
              <div className="flex justify-center items-center space-x-4 mb-16">
                <button
                  onClick={handlePreviousPage}
                  disabled={currentPage === 1}
                  className={`inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium ${
                    currentPage === 1
                      ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                      : "bg-white text-gray-700 hover:bg-gray-50"
                  }`}
                >
                  <ChevronLeftIcon className="h-5 w-5 mr-1" />
                  Previous
                </button>
                <div className="text-sm text-gray-700">
                  Page {currentPage} of {paginatedData.totalPages}
                </div>
                <button
                  onClick={handleNextPage}
                  disabled={currentPage === paginatedData.totalPages}
                  className={`inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium ${
                    currentPage === paginatedData.totalPages
                      ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                      : "bg-white text-gray-700 hover:bg-gray-50"
                  }`}
                >
                  Next
                  <ChevronRightIcon className="h-5 w-5 ml-1" />
                </button>
              </div>
            )}
          </>
        )}

        {/* Detailed Section View */}
        {selectedSection && (
          <div className="bg-white rounded-xl shadow-md p-8 border border-gray-200 mb-12 relative">
            <button
              onClick={() => setSelectedSection(null)}
              className="absolute top-4 right-4 p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
              aria-label="Close"
            >
              <XMarkIcon className="h-5 w-5 text-gray-600" />
            </button>
            <div
              className={`inline-flex items-center justify-center p-2 rounded-full ${
                selectedSection.color === "blue"
                  ? "bg-blue-100 text-blue-700"
                  : selectedSection.color === "emerald"
                  ? "bg-emerald-100 text-emerald-700"
                  : selectedSection.color === "amber"
                  ? "bg-amber-100 text-amber-700"
                  : "bg-purple-100 text-purple-700"
              } mb-4`}
            >
              <selectedSection.icon className="h-6 w-6" />
            </div>
            <h2 className={`text-2xl font-bold text-gray-900 mb-6`}>
              {selectedSection.title}
            </h2>
            <div className="mb-8">{renderDetailedContent(selectedSection)}</div>
            <div className="flex justify-center">
              <button
                onClick={() => setSelectedSection(null)}
                className={`inline-flex items-center px-4 py-2 rounded-md ${
                  selectedSection.color === "blue"
                    ? "bg-blue-100 text-blue-700 hover:bg-blue-200"
                    : selectedSection.color === "emerald"
                    ? "bg-emerald-100 text-emerald-700 hover:bg-emerald-200"
                    : selectedSection.color === "amber"
                    ? "bg-amber-100 text-amber-700 hover:bg-amber-200"
                    : "bg-purple-100 text-purple-700 hover:bg-purple-200"
                } transition-colors`}
              >
                <ArrowLeftIcon className="mr-2 h-4 w-4" />
                Back to all achievements
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
