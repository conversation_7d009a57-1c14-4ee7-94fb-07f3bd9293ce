// SRT Sample Answers
// This file contains sample answers for common SRT situations

export interface SRTSampleAnswer {
  situation: string;
  responses: {
    response: string;
    qualities: string;
  }[];
}

export const srtSampleAnswers: SRTSampleAnswer[] = [
  // Leadership Situations
  {
    situation:
      "His captain was injured before a crucial match, he was asked to lead the team? He..",
    responses: [
      {
        response: "Lead the team, motivated them, played and won the match",
        qualities: "Leadership",
      },
      {
        response:
          "Took the responsibility, made them aware of weak points of opponents that he observed and won the match",
        qualities: "Initiative",
      },
    ],
  },
  {
    situation:
      "You're assigned to lead a team with a member who consistently underperforms. You would...",
    responses: [
      {
        response:
          "Observe team dynamics, have a private conversation with the individual, establish clear expectations, provide specific feedback, and create opportunities for them to succeed",
        qualities: "Leadership, Intelligence",
      },
      {
        response:
          "Identify their strengths and weaknesses, assign tasks accordingly, provide additional training where needed, and recognize improvements while maintaining accountability",
        qualities: "Leadership , Social Intelligence",
      },
    ],
  },

  // Adaptability Situations
  {
    situation:
      "He was on his way to home and suddenly it started raining heavily? He..",
    responses: [
      {
        response:
          "Waited for the rain to stop, informed his parents at home and reached home after the rain stopped",
        qualities: "Adjustability",
      },
      {
        response:
          "Took lift in passing by vehicles or public transport vehicles, reached home",
        qualities: "Social Intelligence",
      },
    ],
  },
  {
    situation:
      "His brother wants to get admission in a medical college but his marks falls short by 1% for admission. He..",
    responses: [
      {
        response:
          "Motivate him to do well next time, bought him more books he required and advised him to start graduation side by side",
        qualities: "Confidence",
      },
      {
        response:
          "Advised him to join another private medical college and he took admission",
        qualities: "Organizing Ability",
      },
    ],
  },

  // Courage and Courage-Taking
  {
    situation:
      "He has to go to a city with cash for work but the way is dangerous because of dacoits. He..",
    responses: [
      {
        response:
          "Left for city with local weapon, torch and mobile phone and reached city",
        qualities: "Stamina",
      },
      {
        response: "Boarded public vehicle and reached the city",
        qualities: "Courage",
      },
    ],
  },
  {
    situation:
      "He was travelling by a train & suddenly a person snatches purse from lady & jumps out of train. He...",
    responses: [
      {
        response:
          "Pulled the chain, chased him, caught him, handed him over to RPF, returned the purse to lady and continued the journey",
        qualities: "Initiative",
      },
      {
        response:
          "Shouted for help from platform passengers, grabbed the thief, handed him over to RPF, returned purse to the lady",
        qualities: "Use of resources, Stamina",
      },
    ],
  },

  // Duty and Responsibility
  {
    situation:
      "He is going to sign a contract, suddenly he got news that one of his friend who had helped him once, met an accident and is in ICU. He...",
    responses: [
      {
        response:
          "Completed his contract and reached ICU, helped his family in maintaining his health",
        qualities: "Leadership ",
      },
      {
        response:
          "Informed the authority, paused the contract, rushed to ICU, called other friends, consolidate the family, signed the contract after his friends arrived",
        qualities: "Responsibility",
      },
    ],
  },
  {
    situation:
      "His mom is seriously ill and his boss doesn't grant leave. He...",
    responses: [
      {
        response: "Convinced boss, took leave and got his mother treated",
        qualities: "Practical Skills",
      },
      {
        response:
          "Called his brother to take the mother to hospital, sent money immediately, directed brother on every step and got his mother treated",
        qualities: "Organizing Ability",
      },
    ],
  },

  // Problem Solving
  {
    situation:
      "He had boarded a wrong train and came to know only when he was asked to pay money to TT. He...",
    responses: [
      {
        response:
          "Showed his ticket, proved himself, got down on next station, took ticket and boarded the train for reaching his place",
        qualities: "Organizing Ability, Intelligence",
      },
      {
        response:
          "Apologised for the mistake, got down from the train on next station, boarded in the bus to the station and reached the station",
        qualities: "Organizing Ability, Reasoning Ability",
      },
    ],
  },
  {
    situation:
      "He was driving the bike without helmet and the traffic police caught him. He...",
    responses: [
      {
        response: "Paid the fine and never repeated this again",
        qualities: "Responsibility",
      },
      {
        response:
          "Apologized, convinced the police and never repeated it again",
        qualities: "Liveliness",
      },
    ],
  },

  // Technical Skills
  {
    situation:
      "He was in charge of wireless board in a sailing ship which lost its communication with the coast. He...",
    responses: [
      {
        response: "Restarted the system and restored communication",
        qualities: " Effective Intelligence",
      },
      {
        response:
          "Retuned the system, closely analysed and found the signal back",
        qualities: "Knowledge",
      },
    ],
  },

  // Social Intelligence
  {
    situation:
      "He went for the picnic with his friends and on the way he had hot arguments with them. He...",
    responses: [
      {
        response: "Changed the topic and enjoyed picnic",
        qualities: "Social Intelligence",
      },
      {
        response:
          "Calmly sorted out the matter, refreshed mood and enjoyed picnic",
        qualities: "Liveliness",
      },
    ],
  },
  {
    situation:
      "His joke was well meant but still his friend became angry. He...",
    responses: [
      {
        response: "Pacified him and apologized",
        qualities: "Social Intelligence",
      },
      {
        response: "Cracked another joke and made him laugh",
        qualities: "Social Intelligence",
      },
    ],
  },

  //  Effective Intelligence
  {
    situation:
      "There is a person who sells smuggled goods. He too, like many others, have purchased a wristwatch from him. But it doesn't work. He...",
    responses: [
      {
        response: "Went to the person and got the watch replaced",
        qualities: " Effective Intelligence",
      },
      {
        response: "Returned the watch and collected the money",
        qualities: " Effective Intelligence",
      },
    ],
  },
  {
    situation: "He urgently needed money. He...",
    responses: [
      {
        response: "Asked for money from his father and got the money",
        qualities: "Social Intelligence",
      },
      {
        response: "Arranged money from friends and relatives",
        qualities: "Social Intelligence",
      },
    ],
  },

  // Organizing Ability
  {
    situation: "He is getting late in reaching meeting. He...",
    responses: [
      {
        response:
          "Hired a private vehicle reached the venue and attended the meeting on time",
        qualities: "Organizing Ability",
      },
      {
        response:
          "Took his bike, went from shortcut and reached venue on time and attended the meeting",
        qualities: "Practical Skills",
      },
    ],
  },
  {
    situation: "During a trekking trip, he was left alone in the jungle. He...",
    responses: [
      {
        response: "Used his navigation system and reached the base camp",
        qualities: "Organizing Ability",
      },
      {
        response:
          "Used his map, went on the way back, found the correct route and joined the group",
        qualities: "Practical Skills",
      },
    ],
  },
  {
    situation:
      "He was weak in studies, and knew that he could not do well in his education. He...",

    responses: [
      {
        response:
          "Made list of subjects as per his aptitude, gave extra time to tough subjects and got good marks in exams",

        qualities: "Leadership ",
      },

      {
        response:
          "Took help from friends, used supplementary books with easy language and theory, repeatedly prepared for it appeared for exams and got good marks",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation:
      "He was playing outdoor and his brother got seriously injured and it started to rain heavily. He...",

    responses: [
      {
        response:
          "Hired a private vehicle, took him to hospital, called parents and got him treated",

        qualities: " Effective Intelligence",
      },

      {
        response:
          "Gave him first aid, called the ambulance, took him to the hospital and get him treated",

        qualities: "Leadership ",
      },
    ],
  },

  {
    situation:
      "He had exams the next day & the road to his school was flooded with continuous rain for last two days.",

    responses: [
      {
        response:
          "Reached school with the help of boat or other public vehicle going and reached school on time",

        qualities: "Organizing Ability",
      },

      {
        response:
          "Told his elder brother or father to drop him school by car, reached school on time and appeared in exam",

        qualities: "Responsibility",
      },
    ],
  },

  {
    situation:
      "He was walking on a dark street with his girlfriend then suddenly 10 armed people came & started harassing his girlfriend. The next police station was 10 km away. He...",

    responses: [
      {
        response:
          "Engaged them and made girlfriend to run and after some time he also ran out of the street, and gathered people around and made those people run.",

        qualities: "Stamina, Liveliness",
      },

      {
        response:
          "Took help of PCR, overpowered the armed men, enjoyed the walk with his girlfriend.",

        qualities: "Use of Resources",
      },
    ],
  },

  {
    situation:
      "His friend was always quarrelling with him and the papers were near. He...",

    responses: [
      {
        response:
          "Persuaded him to not to do so and started preparing for exams",

        qualities: "Straight forwardness",
      },

      {
        response:
          "Goes into the matter, removes misunderstanding and started preparation for examination",

        qualities: "Social Intelligence",
      },
    ],
  },

  {
    situation:
      "His boss gives a piece of work and orders him to follow his instructions to complete the work, but the work is difficult to finish within time. He...",

    responses: [
      {
        response:
          "Managed time according to the task, worked punctually, imparts extra efforts and completed the work on time",

        qualities: "Time Management",
      },

      {
        response:
          "Divided the work into subparts, gave time as per priority and completed work on time",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation:
      "He saw a snake moving near to the bed while his younger brother was sleeping, when he enters his room. He...",

    responses: [
      {
        response:
          "Threw a towel or any cloth over it, wake up his brother and took him aside and killed the snake",

        qualities: "Intelligence",
      },

      {
        response: "Hardly hit the snake from a nearby stick and killed him",

        qualities: "Use of Resources",
      },
    ],
  },

  {
    situation:
      "He received an urgent order from his commander. But he feel that order passed on to him is wrong. He...",

    responses: [
      {
        response:
          "Disclosed his feeling politely, and did what his commander said next",

        qualities: "Organizing Ability",
      },

      {
        response: "Completed the order",

        qualities: "Stamina",
      },
    ],
  },

  {
    situation:
      "He is driving a car on highway at full speed suddenly brakes fail. He...",

    responses: [
      {
        response:
          "Slowly lower downs the gear, after slowing down took it to the nearest repairing shop using hand breaks as alternate, repairs brakes and continues journey",

        qualities: "Knowledge, Courage",
      },

      {
        response:
          "Slowdowns the car by lowering gears, stops car, brought a mechanic nearby, repaired the car and continued the journey",

        qualities: "Knowledge",
      },
    ],
  },

  {
    situation:
      "His joke was well meant but still his friend became angry. He...",

    responses: [
      {
        response: "Pacified him and apologized",

        qualities: "Social Intelligence",
      },

      {
        response: "Cracked another joke and made him laugh",

        qualities: "Social Intelligence",
      },
    ],
  },

  {
    situation:
      "In free period he wanted to study but his friends were continuously disturbing him. He...",

    responses: [
      {
        response: "Told them not to do so and continued studying",

        qualities: "Straight forwardness",
      },

      {
        response: "Went to the library and continued studies",

        qualities: " Effective Intelligence",
      },
    ],
  },

  {
    situation: "In a fit of anger his friend hit him. He...",

    responses: [
      {
        response:
          "Told him not to do so, calmly asked him the matter and sorted it out",

        qualities: "Social Intelligence",
      },

      {
        response: "Pacified him. Removed misunderstanding",

        qualities: "Social Intelligence",
      },
    ],
  },

  {
    situation:
      "While hunting he and his brother lost way in dark (night) in jungle and he has no light. He...",

    responses: [
      {
        response:
          "Went back on the same way from where he have come, came to the right way",

        qualities: "Practical Skills",
      },

      {
        response:
          "Used his map and navigation system to get the right direction and got the right way",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation:
      "He is serving under two senior officers who always give conflicting orders. He...",

    responses: [
      {
        response: "Followed the order of his immediate senior",

        qualities: "Sense of Responsibility",
      },

      {
        response: "Politely asked for a common order and followed it",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation: "Generally people don't listen to his argument. He...",

    responses: [
      {
        response: "Gives facts and stats and strength up his point",

        qualities: "Knowledge",
      },

      {
        response:
          "Use logical and strong points to support and strength up his arguments",

        qualities: "Reasoning",
      },
    ],
  },

  {
    situation:
      "He went to library for a very urgent book and found that all its copies were already issued to others. He...",

    responses: [
      {
        response: "Borrowed and arranged it from his friend",

        qualities: "Organizing Ability",
      },

      {
        response: "Bought a copy of it from market",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation: "He was not getting enough salary for his job. He...",

    responses: [
      {
        response: "Changed the company and got expected salary",

        qualities: "Social Intelligence",
      },

      {
        response:
          "Explained his point to boss, asked for salary hike and got increment",

        qualities: "Reasoning",
      },
    ],
  },

  {
    situation:
      "The sea was very rough and it was night. Ship duty officer fell in sea while taking a round. He...",

    responses: [
      {
        response:
          "Raised whistle/ alarm, threw emergency boat to him and brought him back",

        qualities: " Effective Intelligence",
      },

      {
        response:
          "Raised alarm, dived in the sea with live saving jacket for duty officer and brought him back",

        qualities: " Effective Intelligence",
      },
    ],
  },

  {
    situation:
      "The tractor dashed against a fast moving truck and the driver were thrown off into ditches. He was just away from it. He...",

    responses: [
      {
        response: "Took the driver out of the ditch and gave him first aid",

        qualities: "Initiative",
      },

      {
        response:
          "Gave first aid to the driver, noted down the number of truck and informed the traffic police",

        qualities: " Effective Intelligence, Initiative",
      },
    ],
  },

  {
    situation:
      "As monitor of his class he had to put up the names of those who had not done their homework. His friend was also a culprit. He...",

    responses: [
      {
        response:
          "Gave out the names and later on persuaded him to not to do so again",

        qualities: "Sense of Responsibility",
      },

      {
        response: "Forgave him and warned him to not to repeat this again",

        qualities: "Social Intelligence",
      },
    ],
  },

  {
    situation: "His father needed treatment and he was short of money. He...",

    responses: [
      {
        response:
          "Arranged money from relatives, got his father treated and returned money to relatives on time",

        qualities: "Organizing Ability",
      },

      {
        response:
          "Sold his bike, used money for treatment and bought new bike later",

        qualities: "Organizing Ability,  Effective Intelligence",
      },
    ],
  },

  {
    situation:
      "He returns late in night from NCC camp. His step mother did not allowed him to enter the house. He...",

    responses: [
      {
        response: "Politely talked to her, convinced her and entered the room",

        qualities: "Reasoning",
      },

      {
        response:
          "Spent night outside using his bedding of camp, and entered house in the morning",

        qualities: "Open Mindness",
      },
    ],
  },

  {
    situation:
      "On opening the door of his bathroom in his room he finds a big snake hanging from the ceiling. He...",

    responses: [
      {
        response:
          "Took it down using viper or stick, hardly hit it with stick and killed it",

        qualities: " Effective Intelligence",
      },

      {
        response:
          "Slowly opened the window of the bathroom and closed the door of the bathroom, and snake went out from window by itself",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation:
      "All his family members are ill and his father is out of town. There is no money in the home. He...",

    responses: [
      {
        response:
          "Informed father withdrew money from ATM, hired private vehicle, took family members to hospital and got them treated",

        qualities: "Organizing Ability, Responsibility",
      },

      {
        response:
          "Arranged money from neighbours, went to the nearest hospital and brought the doctor at home, got family members treated and left doctor back to hospital",

        qualities: "Organizing Ability, Responsibility",
      },
    ],
  },

  {
    situation:
      "He was forced by his father to join the Railways but he was really not interested. He...",

    responses: [
      {
        response:
          "Talked to his father convinced him and joined job of his interest",

        qualities: "Reasoning",
      },

      {
        response:
          "Joined railways, side by side prepared for his job of interest and changed job after getting opportunity",

        qualities: "Obey Orders",
      },
    ],
  },

  {
    situation:
      "They decide to give a treat to their retiring professor. He wants to give a dinner party whiles his friends want just a tea - party. He...",

    responses: [
      {
        response: "Persuaded them convinced them and did as per priority",

        qualities: "Social Intelligence",
      },

      {
        response: "Asked professor about his choice and did as per his choice",

        qualities: "Social Intelligence",
      },
    ],
  },

  {
    situation:
      "The leader of his trekking team decides to take a longer route when time is running out, He...",

    responses: [
      {
        response:
          "Showed him the map, described the shorter route, persuaded them and took the route",

        qualities: "Intelligence",
      },

      {
        response: "Enjoyed the route and managed rest of the time",

        qualities: "Time Management",
      },
    ],
  },

  {
    situation:
      "They are climbing a steep wall with help of ropes & some of them reach the top, when the rope gives away.",

    responses: [
      {
        response:
          "Shouted to people at top to throw another rope, and climbed with the help of it",

        qualities: "Use of Resources",
      },

      {
        response:
          "Got back slowly, told people at the top to throw rope, and climbed using it",

        qualities: "Organizing Ability, Use of Resources",
      },
    ],
  },

  {
    situation: "His friends' are having heated arguments over a point. He...",

    responses: [
      {
        response: "Changed the topic and refreshed their mood",

        qualities: "Social Intelligence",
      },

      {
        response:
          "Told them to calmly discuss the matter, interfered and sorted out the matter",

        qualities: "Initiative",
      },
    ],
  },

  {
    situation:
      "He is in final year BA and his father lost the job and his family facing money problems. He...",

    responses: [
      {
        response:
          "Took a part time job continued studies and opted permanent job after graduation",

        qualities: "Responsibility",
      },

      {
        response:
          "Started giving tuitions, continued studies and side by side prepared for competitive exams and got good job after graduation",

        qualities: "Responsibility, Time Management",
      },
    ],
  },

  {
    situation:
      "He is going to market with his sister and some boys start doing mischief with his sister. He...",

    responses: [
      {
        response:
          "Warn them and hardly and made them to leave and reached market",

        qualities: "Courage",
      },

      {
        response: "Gathered people, made them run and reached market",

        qualities: "Social Intelligence",
      },
    ],
  },

  {
    situation:
      "He are walking down the road one fine afternoon and a man steals a lady's hand bag and in the process stabs the lady. He...",

    responses: [
      {
        response:
          "Ran and caught the culprit, handed him over to the police, retired bag to lady and gave her first aid",

        qualities: "Initiative",
      },

      {
        response:
          "Took lady to the nearest hospital, got her treated and reported to the police",

        qualities: "Social Intelligence",
      },
    ],
  },

  {
    situation:
      "He was not a good swimmer, still being persuaded by his friend he go for swimming in river. He stayed in shallow water his friend went midstream and suddenly got caught in whirlpool. He...",

    responses: [
      {
        response:
          "Arranged rope form any boat service nearby, threw it to him and brought him to the bank",

        qualities: "Organizing Ability",
      },

      {
        response:
          "Tied his cloths to make a rope and threw it to him and brought him back",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation: "He went to cinema but he noticed 'houseful' what he do",

    responses: [
      {
        response:
          "Went to another cinema hall, bought tickets and enjoyed movie",

        qualities: "Reasoning Ability",
      },

      {
        response:
          "He already booked the movie seat online, so he will enjoy the movie",

        qualities: "Readiness",
      },
    ],
  },

  {
    situation:
      "He appeared in a competitive exam and he noticed that the minister's son (who was in his examination hall) got a copy of solution from outside...",

    responses: [
      {
        response:
          "Completed the examination, enquired the matter after exam and reported to the exam conducting authority",

        qualities: "Initiative",
      },

      {
        response:
          "Completed examination, enquired the matter, if found guilty, reported to the police station with other aspirants and launched FIR",

        qualities: "Initiative",
      },
    ],
  },

  {
    situation:
      "It was night and train at high speed when a huge built man with pistol entered his compartment and tried to take his suit case. He...",

    responses: [
      {
        response:
          "Gave him suitcase, attacked him while he was trying to open it and handed him over to the RPF personals",

        qualities: "Stamina, Courage ",
      },

      {
        response:
          "Gave him suitcase, informed RPF personals, attacked him caught him and recovered the suitcase",

        qualities: "Stamina, Social Intelligence",
      },
    ],
  },

  {
    situation:
      "Four of them decided to go on foot from Dehradun to Missouri but that day it started raining and they were told the mountains slopes would be slippery. So He...",

    responses: [
      {
        response:
          "Persuaded and convinced his friend to go by bus and enjoyed trip",

        qualities: "Reasoning",
      },

      {
        response:
          "Hired a trekking master, took safety measures and enjoyed the foot journey",

        qualities: "Social Intelligence",
      },
    ],
  },

  {
    situation:
      "In a discussion with his colleagues, he fined his losing ground, He...",

    responses: [
      {
        response: "Used facts and figures to support his point",

        qualities: "Reasoning",
      },

      {
        response: "Gave examples and experiences to strength up his point",

        qualities: " Effective Intelligence",
      },
    ],
  },

  {
    situation:
      "He is on the way to his home for some urgent work. Suddenly his Bicycle got punctured. He...",

    responses: [
      {
        response:
          "Took it to the nearest bicycle repairing shop, got it repaired and reached home",

        qualities: "Organizing Ability",
      },

      {
        response:
          "Took the bicycle to the nearest repairing shop, gave it for repair and locked it, reached home by public vehicle, completed work, came back and claimed the cycle",

        qualities: "Organizing Ability, Use of Resources",
      },
    ],
  },

  {
    situation:
      "During exams his teacher threatened him to fail in examination. He...",

    responses: [
      {
        response:
          "Prepared for the exams, appeared in exams and passed with good marks",

        qualities: "Stamina",
      },

      {
        response:
          "Talked to the teacher, cleared misunderstanding, appeared in exams and got expected marks",

        qualities: "Social Intelligence",
      },
    ],
  },

  {
    situation:
      "He was going for the Nepal Army and on the way he saw a person seriously injured and nobody was there to help him. He...",

    responses: [
      {
        response:
          "Gave him first aid, took him to the nearest hospital, took phone number of his family from him and called them and left for Nepal Army",

        qualities: "Initiative, Intelligence",
      },

      {
        response:
          "Took the person to the hospital in his way, admitted him, informed his family and left for Nepal Army",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation:
      "His friend cracks a joke on him in presence of his girlfriend, He...",

    responses: [
      {
        response: "Laughed and enjoyed the joke",

        qualities: "Social Intelligence",
      },

      {
        response: "Cracked another joke and refreshed mood",

        qualities: "Liveliness",
      },
    ],
  },

  {
    situation: "He is a horse rider. Once the horse went out of control. He...",

    responses: [
      {
        response: "Strongly holder the horse's ropes and controlled it again",

        qualities: "Stamina",
      },

      {
        response:
          "Slowed him down, checked for any problem, removed it and continued riding",

        qualities: "Curiosity ",
      },
    ],
  },

  {
    situation:
      "He missed his school bus at 0830h. The final exam was to start at 0900h. The school is at a distance of 3Km. So He...",

    responses: [
      {
        response:
          "Asked his father or brother to drop him to school by bike or car and reached school on time",

        qualities: "Organizing Ability",
      },

      {
        response: "Hired private vehicle and reached school on time",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation: "His village was raided by dacoits. So He...",

    responses: [
      {
        response:
          "Called police, gathered people with weapons, warned them and made the run",

        qualities: "Organizing Ability, Initiative",
      },

      {
        response:
          "Called police, pose to surrender in front of Dacoits, attacked and caught them with villagers and handed over to police",

        qualities: "Organizing Ability, Stamina",
      },
    ],
  },

  {
    situation:
      "He forgot his identity card and proceeded on TD. He was denied access in the cantonment. He...",

    responses: [
      {
        response:
          "Proved his identity using other identity proofs like bank copy, CSD card etc. and entered cantonment",

        qualities: "Intelligence",
      },

      {
        response:
          "Showed his TD letter or proved by making call to his unit members and took entry in cantonment",

        qualities: "Intelligence",
      },
    ],
  },

  {
    situation:
      "He wanted to get migration cert from the university. He was not granted leave to go to university. So He...",

    responses: [
      {
        response:
          "Sent letter with application and draft to get migration certificate through post and received it in a week",

        qualities: "Reasoning Ability",
      },

      {
        response:
          "Convinced his boss, got one day leave, went to university and got migration certificate",

        qualities: "Reasoning",
      },
    ],
  },

  {
    situation:
      "He was to go on TD. Rail reservation was not available. TTE asked for Rs 200 to give a berth. So He...",

    responses: [
      {
        response: "Boarded general coach and reached the station",

        qualities: "Organizing Ability",
      },

      {
        response: "Went to bus stand and reached station by bus",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation:
      "Ram and Radha decided to go to see evening movie show, Ram's father had given some important task to him for the evening. So Ram...",

    responses: [
      {
        response:
          "Completed the task and went for the movie show and enjoyed it",

        qualities: "Organizing Ability",
      },

      {
        response:
          "Rescheduled the movie plan for next day, completed father's task and enjoyed movie next day",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation:
      "While travelling by train he found four people beating a person. So He...",

    responses: [
      {
        response:
          "Stopped them with the help of other passengers, gave him first aid and reported the RPF personnel",

        qualities: "Initiative",
      },

      {
        response:
          "Called RPF personnel nearby, stopped them and gave first aid to the injured",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation:
      "He have received a telegram to appear for a job interview after four days in a big city, which is 1,500 kilometres away from your town. He do not know anybody in that city. He...",

    responses: [
      {
        response:
          "Checked the address on internet, book tatkal tickets for going and returning also, hired cab to reach office, attended interview in a good manner and got selected, got back on time and reached back.",

        qualities: "Leadership , Intelligence",
      },

      {
        response:
          "Booked flight tickets, to save time, reached city attended interview and got selected and got back as per schedule",

        qualities: "Organizing Ability, Intelligence",
      },
    ],
  },

  {
    situation:
      "He made a very good grade in his Higher Secondary Examination. He wanted to study further but his parents were not in a position to educate him. He...",

    responses: [
      {
        response: "Took a part time job and continued his studies.",

        qualities: "Responsibility",
      },

      {
        response:
          "Opted a permanent job and continued study from correspondence",

        qualities: "Responsibility",
      },
    ],
  },

  {
    situation:
      "His 15 years younger brother was coming to the big city where he was staying, for the first time. He had gone to receive him on the railway station but was not found. He...",

    responses: [
      {
        response:
          "Called him, asked him his position, reached there and took him home",

        qualities: "Organizing Ability",
      },

      {
        response:
          "Went to the announcement room, announced his name, called him there and took him home",

        qualities: "Intelligence",
      },
    ],
  },

  {
    situation:
      "His school was playing a football match against another school. Two minutes before the match to start, two members of the school team were missing. As a Captain of the team he...",

    responses: [
      {
        response:
          "Called them, told them to get there now, played the match and won it",

        qualities: "Sense of Responsibility",
      },

      {
        response:
          "Called them, if found engaged in other tasks then took two substitute players in team, played the match and won it",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation:
      "He prepared for university exam but failed, all of his friends passed. He...",

    responses: [
      {
        response: "Prepared more for exams and cleared it next time.",

        qualities: "Stamina",
      },

      {
        response: "Applied for rechecking and got cleared",

        qualities: "Social Intelligence",
      },
    ],
  },

  {
    situation:
      "While going for trekking, he found that his team has forgotten food material at base camp. He...",

    responses: [
      {
        response:
          "Bought bread or other light food from nearby shop on the route and enjoyed trekking",

        qualities: "Use of Resources",
      },

      {
        response:
          "Sent a person back to bring food items and continued trekking after his arrival",

        qualities: "Reasoning Ability",
      },
    ],
  },

  {
    situation:
      "He was travelling to his Nepal Army centre, and just before Reaching the station, he found that his suitcase is lost. He...",

    responses: [
      {
        response:
          "Filed FIR at RPF station, got a copy of it, produced it in Nepal Army, brought a copy of documents from home through e-mail, bought required material for Nepal Army and appeared in Nepal Army",

        qualities: "Social Intelligence",
      },

      {
        response:
          "Filed FIR to RPF went to home, collected all required material and appeared in Nepal Army with Xerox of documents and FIR",

        qualities: "Reasoning Ability",
      },
    ],
  },

  {
    situation:
      "He is appearing in examination and find it hard to answer the questions. He...",

    responses: [
      {
        response:
          "Read the question paper again, concentrated over it and answered the questions.",

        qualities: "Courage ",
      },

      {
        response:
          "Answered simple questions first, and then goes to other questions as per difficulties and answered all questions",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation:
      "He is carrying money to disburse the salary of his employees. Two miscreants surround him and ask him to surrender money. He...",

    responses: [
      {
        response:
          "Initially pose to surrender, then hit them hard and ran away with money",

        qualities: "Intelligence",
      },

      {
        response: "Gathered people around, warned them and made them run",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation:
      "He is going to restaurant with his girlfriend. After entering, he noticed that he forgot his wallet. He...",

    responses: [
      {
        response: "Used his ATM to swipe and pay",

        qualities: "Organizing Ability",
      },

      {
        response: "Told his girlfriend to pay",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation:
      "He is stranger in the city. He lost his purse and has to deposit money in his institute before 5pm. His home is 24 hours train journey one way. He...",

    responses: [
      {
        response:
          "Lodge FIR, called father and told him to transfer money in his account, withdrew money by ATM and paid fees",

        qualities: "Reasoning Ability",
      },

      {
        response:
          "Lodged FIR, called brother and directed him to deposit money in institute's account, told him to send the receipt and produced it in institute",

        qualities: "Use of Resources",
      },
    ],
  },

  {
    situation:
      "He was to appear for an exam and all of a sudden the curfew was imposed in that area, He...",

    responses: [
      {
        response:
          "Showed his admit card to the army personal, convinced them, reached to the examination centre and appeared for the exam",

        qualities: "Reasoning",
      },

      {
        response:
          "Called the college authority, explained them and went back to home",

        qualities: "Reasoning",
      },
    ],
  },

  {
    situation:
      "Just two days before the semi-finals of a crucial tournament, his partner was called by his parents and had to go out of station for an urgent work. He...",

    responses: [
      {
        response:
          "Took the compatible replacement, took part in tournament and won it",

        qualities: "Organizing Ability",
      },

      {
        response:
          "Told his friend to come back before the tournament, after he arrived, they played the match and won it",

        qualities: "Sense of Responsibility",
      },
    ],
  },

  {
    situation:
      "He was to marry a rich girl chosen by his father but he did not like his. He...",

    responses: [
      {
        response:
          "Convinced his father about his dislike and married with a suitable girl of his father's choice",

        qualities: "Reasoning",
      },

      {
        response: "Had faith on his father's choice and married the girl",

        qualities: "Obey Orders",
      },
    ],
  },

  {
    situation:
      "He was all set for Nepal Army but unfortunately this was a sad demise of his family member. He...",

    responses: [
      {
        response:
          "Paid tribute to him, and left for Nepal Army as per planning",

        qualities: "Reasoning Ability",
      },

      {
        response:
          "Will reschedule Nepal Army, inform Nepal Army and attended absentee batch",

        qualities: "Reasoning Ability",
      },
    ],
  },

  {
    situation:
      "While passing by he found two people sneaking at a house from the window. He...",

    responses: [
      {
        response:
          "Inquired and took action if found problem, caught them and handed them to police",

        qualities: "Curiosity ",
      },

      {
        response:
          "Inquired the matter and found that door was locked from inside, helped them in opening the door",

        qualities: "Curiosity , Helping",
      },
    ],
  },

  {
    situation: "He is in train & lost his purse with money. He...",

    responses: [
      {
        response:
          "Searched it in the coach, if not found then filed FIR to RPF personnel and used extra money he had in his luggage",

        qualities: "Social Intelligence",
      },

      {
        response:
          "Searched it in coach, got down on next station withdrew money from ATM and lodged FIR at RPF station",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation:
      "Some passenger shook him out of sleep and asked for some money because he was pick - pocketed and with that ticket also gone. He...",

    responses: [
      {
        response: "Enquired the matter and helped the passenger",

        qualities: "Curiosity , Helping",
      },

      {
        response: "Enquire the matter and report to the RPF personnel.",

        qualities: "Curiosity ",
      },
    ],
  },

  {
    situation:
      "In his village two leading parties got in conflict during sarpanch election. He...",

    responses: [
      {
        response: "Pacified them, enquired and removed misunderstanding",

        qualities: "Curiosity , Social Intelligence",
      },

      {
        response:
          "Informed higher authority and persuaded them to fight election in a polite way",

        qualities: "Social Intelligence",
      },
    ],
  },

  {
    situation:
      "He is called for interview for a job he badly needed. But same day was his final exam.",

    responses: [
      {
        response:
          "Appeared in the exam in the morning, attended the interview in the evening and got selected",

        qualities: "Sense of Responsibility",
      },

      {
        response:
          "Asked job authorities to reschedule the interview to next day, appeared in the examination, attended the interview on next day and got selected",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation:
      "He is a prefect in his hostel. He notices two of his friends bunking, he...",

    responses: [
      {
        response:
          "Persuaded them to not to bunk classes again and told them to join class",

        qualities: "Reasoning",
      },

      {
        response:
          "Informed the respective authority and persuaded them to not to do so again",

        qualities: "Sense of Responsibility",
      },
    ],
  },

  {
    situation: "Unit Exams are coming near and he falls seriously ill. He...",

    responses: [
      {
        response:
          "Consulted doctor took medicine, had some rest, prepared for examination and got expected marks",

        qualities: "Organizing Ability",
      },

      {
        response:
          "Informed the college, applied for extension of date, got it, got himself treated, prepared for examination and got expected marks",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation:
      "His father and uncle quarrel over his intended inter caste marriage, he...",

    responses: [
      {
        response:
          "Pacified them, persuaded his uncle and married as per his choice",

        qualities: "Reasoning",
      },

      {
        response:
          "Calmed them down discussed the matter with them and goes as per their decision",

        qualities: "Obey Orders",
      },
    ],
  },

  {
    situation:
      "During blackout exercises, he was scout on duty. But one rich man was adamant on keeping his lights on.",

    responses: [
      {
        response:
          "Politely convinced him to switch off the light and made him to do so",

        qualities: "Reasoning",
      },

      {
        response:
          "Asked him to switch off the lights, if he refused then called his senior and made the task done",

        qualities: "Sense of Responsibility",
      },
    ],
  },

  {
    situation:
      "On his 1st day of Nepal Army he gets a call letter from a company to come for interview next day (can't be rescheduled). He...",

    responses: [
      {
        response:
          "Requested the company to reschedule the interview and Concentrated on Nepal Army",

        qualities: "Reasoning",
      },

      {
        response:
          "Save his best in Nepal Army, worked hard and got recommended",

        qualities: "Stamina",
      },
    ],
  },

  {
    situation:
      "He is recently appointed in an organisation. where he is being given extra work load. He...",

    responses: [
      {
        response: "Managed the work with time and completed all of them",

        qualities: "Leadership ",
      },

      {
        response:
          "Reported to concerned authorities asked them to divide the task equally and got problem solved",

        qualities: "Reasoning",
      },
    ],
  },

  {
    situation:
      "He is going to the examination hall from home by scooter. on the way, two policemen stops him and asked him to go to police station as the scooter is registered in different state. He...",

    responses: [
      {
        response:
          "Showed the R.C of the scooter to them, convinced them and reached examination hall on time",

        qualities: "Organizing Ability, Reasoning",
      },

      {
        response:
          "Showed them N.O.C. for vehicle transfer, proved vehicle's registration and reached examination hall",

        qualities: "Reasoning",
      },
    ],
  },

  {
    situation:
      "There is crowd in the bus. he is not able to buy the tickets due to heavy rush. Conductor was invisible and unapproachable due to crowd. He...",

    responses: [
      {
        response: "Boarded the bus and took ticket from conductor into the bus",

        qualities: " Effective Intelligence",
      },

      {
        response:
          "Somehow managed to get the ticket in the crowd and boarded the bus",

        qualities: "Liveliness",
      },
    ],
  },

  {
    situation:
      "He has to receive his friend from abroad. his car got some defect and he has no other vehicle. He...",

    responses: [
      {
        response:
          "Got the car repaired from local mechanic and received his friend",

        qualities: "Leadership ",
      },

      {
        response: "Hired a private taxi and received his friend",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation: "His plan failed in the very beginning. He...",

    responses: [
      {
        response: "Re-analysed the plan, followed it correctly and got success",

        qualities: "Reasoning Ability",
      },

      {
        response: "Made a new plan, executed it and got success",

        qualities: "Initiative",
      },
    ],
  },

  {
    situation:
      "His friend lost his job and is facing financial difficulties. He...",

    responses: [
      {
        response:
          "Helped him financially, side by side prepared him for interview and helped him in getting job",

        qualities: "Social Intelligence",
      },

      {
        response: "Refer him in his known companies, helped him in getting job",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation:
      "He went on a mountain expedition which was a failure and of your friends died in the attempt. He...",

    responses: [
      {
        response:
          "Learns to know the correct art of mountain expedition through sustained practical guidance and climbing operations and ensure success in the subsequent mountain expedition.",

        qualities: "Open Mindness",
      },

      {
        response:
          "Know the flaws in his earlier attempt, tried with full resources and an experienced trainer, succeed in the expedition",

        qualities: "Initiative",
      },
    ],
  },

  {
    situation:
      "He scored less in B.A. His father wants him to join services. He...",

    responses: [
      {
        response: "Prepared for armed forces and joined it",

        qualities: "Sense of Responsibility",
      },

      {
        response:
          "Convinced his father to do further studies, worked hard in P.G and scored good marks",

        qualities: "Social Adaptability",
      },
    ],
  },

  {
    situation:
      "He was to carry some valuables from one place to another and there was danger of the dacoits on the way. He...",

    responses: [
      {
        response: "Took some local weapon and completed the journey",

        qualities: "Organizing Ability",
      },

      {
        response: "Chose public transport and completed the journey",

        qualities: "Organizing Ability, Social Intelligence",
      },
    ],
  },

  {
    situation:
      "He has been served a challenge from the college bully because he had reported against him and the latter was trying to beat his friend. He...",

    responses: [
      {
        response: "Reported to the Principal with his friend as witness",

        qualities: "Sense of Responsibility",
      },

      {
        response: "Calmly talked to him and cleared misunderstanding",

        qualities: "Social Adaptability",
      },
    ],
  },

  {
    situation:
      "He was going from his house to examination hall and on the way he was surrounded by two policemen. He...",

    responses: [
      {
        response:
          "Asked for the matter, showed his admit card and left for the examination hall",

        qualities: "Reasoning Ability",
      },

      {
        response:
          "Asked the matter, cleared misunderstanding and left for the examination",

        qualities: "Reasoning Ability, Social Adaptability",
      },
    ],
  },

  {
    situation: "Drought conditions were prevailing in his village. He...",

    responses: [
      {
        response:
          "Went to higher authorities with village heads, helped the authorities and making helped the villagers in getting out of it.",

        qualities: " Effective Intelligence, Initiative",
      },

      {
        response:
          "Made people aware of compensation and other schemes, told them and helped them in getting benefits, motivated them and overcame the draught conditions",

        qualities: "Social Intelligence, leadership, responsibility",
      },
    ],
  },

  {
    situation:
      "He was in a great hurry to reach home when he found an old man fainted on the way. He...",

    responses: [
      {
        response:
          "Went to him, took him in nearby shelter, gave him some water, helped him to go where he wants, left and reached home on time",

        qualities: "Organizing Ability",
      },

      {
        response:
          "Took the old man to the hospital in his way, called old man’s family, admitted him to hospital and left for home.",

        qualities: " Effective Intelligence",
      },
    ],
  },

  {
    situation:
      "While travelling in the bus some people started arguing with him. He...",

    responses: [
      {
        response: "Ask for the reason and clear the misunderstanding",

        qualities: "Reasoning Ability, Socialism",
      },

      {
        response: "Ignore them and continued the journey",

        qualities: "Straight forwardness",
      },
    ],
  },

  {
    situation:
      "His father is returning from victorious war and he had gone to receive him at the railway station. As he was waiting, he heard a loud sound of explosion. He...",

    responses: [
      {
        response:
          "Took hide in safe area inquired about the cause of explosions, called his father and took him safely to home.",

        qualities: "Reasoning Ability, Organizing Ability",
      },

      {
        response:
          "Went into buildings in railway station and called his father and took him to the home safely.",

        qualities: "Organizing Ability, Organizing Ability",
      },
    ],
  },

  {
    situation:
      "A stranger slipped from moving bus in front of him injuring himself. He...",

    responses: [
      {
        response:
          "Asked for injury, gave first aid if required and boarded him in next bus",

        qualities: "Initiative",
      },

      {
        response: "Stopped the bus helped the person to get back in bus.",

        qualities: "Responsibility",
      },
    ],
  },

  {
    situation:
      "His mother is suffering from long time illness and no one was at home. He is in Himachal terrain. He...",

    responses: [
      {
        response: "Took leave and took care of his mother.",

        qualities: "Organizing Ability, Responsibility",
      },

      {
        response:
          "Took his mother to his posting station and got her treated their and took care of her.",

        qualities: "Social Intelligence, Responsibility",
      },
    ],
  },

  {
    situation:
      "His friends are not willing to donate blood due to the fear of health hazards. He...",

    responses: [
      {
        response:
          "Clarified them about safety measures, convinced them to donate blood donated blood with them",

        qualities: "Social Adaptability",
      },

      {
        response:
          "Told them the pros of donating blood , motivated them to donate blood and donated blood with them",

        qualities: "Knowledge, Social Adaptability",
      },
    ],
  },

  {
    situation:
      "He was one of the good players of college hockey team but was not selected as the captain of the team, he...",

    responses: [
      {
        response: "Played and gave his best and made his team win",

        qualities: "Social Adaptability, Stamina",
      },

      {
        response:
          "Accepted the position cooperated team mates gave suggestions about the playing strategies of opposite team, played hard and won the match",

        qualities: "Organizing Ability, Social Adaptability",
      },
    ],
  },

  {
    situation:
      "While going for the picnic he saw the road jammed due to an accident in the middle of the road. He...",

    responses: [
      {
        response:
          "Went to the site of accident, gave first aid to the injured sent one of his friends to admit injured in nearest hospital and cleared the jam",

        qualities: "Initiative, Organizing Ability",
      },

      {
        response:
          "Gave first aid to the injured, took the injured out of jam and admitted in nearest hospital informing his family and told his friends to clear the jam.",

        qualities: "Initiative, Organizing Ability",
      },
    ],
  },

  {
    situation:
      "While enjoying the fair he found a girl lost from her parents. It’s going to get dark, he...",

    responses: [
      {
        response:
          "Took her to the announcement room, got her name announced called her parents and handed he over to them",

        qualities: "Social Intelligence",
      },

      {
        response: "Find nearby with girl and found his parents",

        qualities: "Social Intelligence",
      },
    ],
  },

  {
    situation:
      "It was his first visit to the snow bonded area at some distance. A man being buried under snow. He...",

    responses: [
      {
        response:
          "Took him out of the snow and took him to the nearest hospital at earliest",

        qualities: "Initiative",
      },

      {
        response:
          "Took him out of snow with people around, gave first aid and called ambulance and admitted him",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation:
      "There were scaling a high wall of fort and the Halfway He saw that the rope gave up. He...",

    responses: [
      {
        response:
          "Stopped other from climbing on the rope, stopped climbing and moving, shouted for another rope from people on top, changed the rope and climbed the wall",

        qualities: "Practical Skills",
      },

      {
        response:
          "Stopped others to climb on rope, made grip on the wall, grabbed rope above the breaking point and reached the top of the wall",

        qualities: "Stamina, Practical Skills",
      },
    ],
  },

  {
    situation:
      "On the usual evening walk he found two cyclists unconscious and there cycles lying on the roadside. No other vehicle seemed to be around, he...",

    responses: [
      {
        response:
          "Took them in shelter, threw water over them, made them conscious, called ambulance, admitted them and called their parents and informed police",

        qualities: "Initiative, Social Intelligence",
      },

      {
        response:
          "Called his brother and brought his car, took them to the hospital and called his parents and informed police",

        qualities: "Social Intelligence, Organizing Ability",
      },
    ],
  },

  {
    situation:
      "He met with an accident while going to the college for examination. He...",

    responses: [
      {
        response: "Took first aid, hired a private vehicle and reached college",

        qualities: "Organizing Ability, Organizing Ability",
      },

      {
        response:
          "Went to the hospital in his route, got himself treated and then reached college on time",

        qualities: "Reasoning Ability",
      },
    ],
  },

  {
    situation:
      "Most of his college boys were donating blood during war. He is not much healthy. He...",

    responses: [
      {
        response: "Donated blood and made sure more students donate blood",

        qualities: "Responsibility, socialism",
      },

      {
        response: "Donated blood as per his medical capacity",

        qualities: "Responsibility",
      },
    ],
  },

  {
    situation:
      "Two Days before finals of tennis tournament he was called at home by his mother who is ill. He...",

    responses: [
      {
        response:
          "Told his brother to take care of him and helped him financially, played match, won it reached home and took care of her mother",

        qualities: "Social Intelligence, Responsibility",
      },

      {
        response:
          "Called the friends or neighbours to help his mother in getting admitted, sent money for it through ATM played match, won it and reached home and took care of her",

        qualities: "Responsibility, Social Adaptability",
      },
    ],
  },

  {
    situation:
      "While going to the market to bring medicines it started raining heavily and all streets got flooded. He...",

    responses: [
      {
        response:
          "Hired private vehicle, went to the market and came back with medicines",

        qualities: "Organizing Ability",
      },

      {
        response:
          "Went to nearest dispensary, bought medicines and came back at earliest.",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation:
      "Due to rain there was a flood in his village. All people are moving out of the village living cattle’s behind. He...",

    responses: [
      {
        response: "Opened and freed all the cattle",

        qualities: "Social Intelligence",
      },

      {
        response:
          "Motivated people to take cattle on higher place and helped them to make temporary shelters their",

        qualities: "Social Intelligence, Leadership",
      },
    ],
  },

  {
    situation:
      "While trekking through deep snow his foot slip and he slipped deep into ditch he cried for help but no one was there. He...",

    responses: [
      {
        response: "Used his hooks and spike shoes and got out of the ditch",

        qualities: "Organizing Ability, Practical Skills",
      },

      {
        response:
          "Called his friends and took their help and came out of the ditch",

        qualities: "Social Intelligence",
      },
    ],
  },

  {
    situation: "A man had fallen into sea when the sea was rough. He...",

    responses: [
      {
        response:
          "Jumped into the sea with lifesaving jacket and took him to the boat",

        qualities: "Initiative",
      },

      {
        response: "Threw live saving jacket and brought him to the boat",

        qualities: "Practical Skills",
      },
    ],
  },

  {
    situation:
      "On a windy afternoon his neighbour’s house caught fire. All his friends were sleeping on first floor. He...",

    responses: [
      {
        response:
          "Called his neighbours and told them about the fire, called fire brigade, evacuated neighbours with the help of friends",

        qualities: "Organizing Ability, Organizing Ability",
      },

      {
        response:
          "Told his friends to call fire brigade and neighbours, evacuated if somebody found inside, made teams and threw water and helped brigade in curbing fire",

        qualities: "Organizing Ability, Organizing Ability",
      },
    ],
  },

  {
    situation:
      "While carrying out his duties in Nagaland he lost his route and had no supply food for next 48 hours. He...",

    responses: [
      {
        response:
          "Used communication device, communicated with team and reached back to team",

        qualities: "Organizing Ability",
      },

      {
        response: "Used map and celestial knowledge and got back to the camp",

        qualities: "Organizing Ability, Practical Skills",
      },
    ],
  },

  {
    situation:
      "While passing through the road he saw some people beating a boy. He...",

    responses: [
      {
        response:
          "Shouted, ran toward them, made them run and gave help to the boy",

        qualities: "Social Intelligence",
      },

      {
        response:
          "Stopped them from beating, tactically took the boy out of the matter and gave him first aid",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation:
      "Just after reaching home on 60 days leave he was called back due to military emergency. He...",

    responses: [
      {
        response:
          "Went back to railway station with packed bags, boarded the next train going to his posting station, reached the unit and reported.",

        qualities: " Effective Intelligence",
      },

      {
        response:
          "Called travel agent, booked air tickets and reached the unit",

        qualities: "Organizing Ability, organizing ability",
      },
    ],
  },

  {
    situation:
      "His younger brother gets into bad practices of alcoholism, he...",

    responses: [
      {
        response: "Inquired and persuade him to not to do so",

        qualities: "Reasoning Ability",
      },

      {
        response:
          "Inquired, persuaded him and engaged him in works to stop alcohol and bad company and checked him constantly",

        qualities: "Reasoning Ability, social adaptability",
      },
    ],
  },

  {
    situation:
      "One of his friends came to him and demanded large amount of money on urgent pretext. He...",

    responses: [
      {
        response: "Gave him as much money as he can",

        qualities: "Organizing Ability",
      },

      {
        response: "Arranged money for him and got his demand fulfilled",

        qualities: "Social Adaptability",
      },
    ],
  },

  {
    situation:
      "During vacations he goes for picnic with college friends. After reaching the spot his find people harassing his friend. He...",

    responses: [
      {
        response: "Warned them and told them to not do this again",

        qualities: "Sense of Responsibility",
      },

      {
        response: "Deterred them with his friends and made them run",

        qualities: " Effective Intelligence",
      },
    ],
  },

  {
    situation:
      "His family members had fixed his marriage but his commanding officer detailed him for an adventure activity he...",

    responses: [
      {
        response:
          "Convinced his family to delay the marriage schedule, attended adventure activity and followed marriage schedule",

        qualities: "Organizing Ability, responsibility",
      },

      {
        response:
          "Inquired about the schedules of both the tasks and arranged them giving priority to adventure activity",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation:
      "After his marriage his in-laws forced him to leave services. He...",

    responses: [
      {
        response:
          "Expressed his dedication of Continuing the service and continued it",

        qualities: "Social Adaptability",
      },

      {
        response:
          "Talked to them, cleared the misunderstanding or myth and continued the service",

        qualities: "Social Adaptability",
      },
    ],
  },

  {
    situation:
      "He is performing the duties of mess Hawaldar and there is often complains of bad quality of food. He⋯",

    responses: [
      {
        response:
          "Inquired the problem and told the concerned person to pay attention toward his work and maintained the food quality again",

        qualities: "Reasoning Ability, Sense of Responsibility",
      },

      {
        response:
          "Personally checked the food, completed the requirement of any food material if required, checked the work personally",

        qualities: "Sense of Responsibility, Organizing Ability",
      },
    ],
  },

  {
    situation:
      "His wife often wrote to him about the ill treatment she was getting from his family members. He⋯",

    responses: [
      {
        response: "Persuaded her to adapt herself and got the problem solved",

        qualities: "Social Adaptability",
      },

      {
        response:
          "Talked to the family and persuaded them to stay polity to her and sorted out the problem",

        qualities: "Social Intelligence",
      },
    ],
  },

  {
    situation:
      "He and his friends are required to go to other side off the bridge but the bridge is not in good condition. He⋯",

    responses: [
      {
        response: "Took alternate longer way and crossed the route.",

        qualities: "Organizing Ability",
      },

      {
        response: "Carefully crossed the bridge",

        qualities: "Stamina,  Effective Intelligence",
      },
    ],
  },

  {
    situation:
      "is walking in small path. He finds a man climbing on a tree for suicide, he⋯",

    responses: [
      {
        response:
          "Rushed to him brought him down, persuaded him and motivated him to not to do this again",

        qualities: "Initiative",
      },

      {
        response: "Brought him down, called his family and handed him to them",

        qualities: "Social Intelligence",
      },
    ],
  },

  {
    situation:
      "He enter the room and found a snake near his child who is playing. He⋯",

    responses: [
      {
        response:
          "Threw towel or shirt over the cobra, took child away from it and kill it",

        qualities: "Organizing Ability",
      },

      {
        response:
          "Took the snake away from the child using stick or viper and killed it",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation:
      "He and his wife are going for watching a movie. Someone picks his pocket having money and identity card and he does not have money to buy ticket and for bus fare to come back. He⋯",

    responses: [
      {
        response:
          "Lodged FIR, withdrew money from ATM watched movie and returned to home",

        qualities: "Organizing Ability, Reasoning Ability",
      },

      {
        response:
          "Lodged FIR, bought tickets from money that his wife had, enjoyed movie and returned back to home",

        qualities: "Reasoning Ability",
      },
    ],
  },

  {
    situation:
      "The person sitting next to him in the train starts fighting with him. He⋯",

    responses: [
      {
        response: "Ignored and continued journey",

        qualities: "Straight forwardness",
      },

      {
        response:
          "Asked for the matter, cleared the misunderstanding and continued the journey",

        qualities: "Reasoning Ability",
      },
    ],
  },

  {
    situation: "His friend is not talking to him. He⋯",

    responses: [
      {
        response: "Asked for the matter and cleared misunderstanding",

        qualities: "Social Adaptability",
      },

      {
        response:
          "Inquired the matter from other friends, cleared the misunderstanding",

        qualities: "Reasoning Ability",
      },
    ],
  },

  {
    situation:
      "He is getting into a train and a person pickpockets his wallet and gets dissolve in the crowd. He⋯",

    responses: [
      {
        response: "Caught him, handed him to the RPF personals",

        qualities: " Effective Intelligence",
      },

      {
        response: "Lodged FIR to the RPF and got a copy of it",

        qualities: "Social Intelligence",
      },
    ],
  },

  {
    situation:
      "He is going downhill with friends and found that his brakes have failed. He⋯",

    responses: [
      {
        response:
          "Stopped the cycle from foot, repaired the breaks using repairing kit and continued cycling",

        qualities: "Courage , Organizing Ability",
      },

      {
        response:
          "Stopped cycle with the help of other friends on cycle, got the breaks repaired from nearest repairing shop and continued cycling",

        qualities: "Practical Skills",
      },
    ],
  },

  {
    situation: "His brother died leaving his wife and 2 children behind. He⋯",

    responses: [
      {
        response:
          "Arranged a job for his wife and supported them in every possible way",

        qualities: "Responsibility",
      },

      {
        response:
          "Motivated her, arranged part time job and ensured good life for children",

        qualities: "Leadership, Responsibility",
      },
    ],
  },

  {
    situation:
      "is going through a jungle and finds a tiger 10 metres in-front of him. He⋯",

    responses: [
      {
        response:
          "Stood firm and calm, waited for tiger to go and crossed the jungle",

        qualities: "Practical Skills",
      },

      {
        response:
          "Took hid behind tree and crossed jungle after tiger goes back",

        qualities: "Practical Skills",
      },
    ],
  },

  {
    situation:
      "His sister’s marriage is approaching and his wife asked him money urgently for opening a beauty parlour. He⋯",

    responses: [
      {
        response:
          "Convinced his wife and fulfilled her demand after sister’s marriage",

        qualities: "Social Adaptability",
      },

      {
        response: "Arranged money from friends and fulfilled wife’s demand",

        qualities: "Reasoning Ability",
      },
    ],
  },

  {
    situation:
      "A person comes to him late night and asked for staying in his home. He⋯",

    responses: [
      {
        response:
          "Politely refuses to do so and told him about nearest Dharamshala or lounge",

        qualities: "Social Adaptability",
      },

      {
        response:
          "Inquired for any reference, if found referred by any know person, helped him",

        qualities: "Reasoning Ability, social adaptability",
      },
    ],
  },

  {
    situation:
      "His sister’s marriage and an urgent meeting are on the same day. He⋯",

    responses: [
      {
        response:
          "Attended the annual inspection in morning and attended the marriage in the evening",

        qualities: "Organizing Ability",
      },

      {
        response:
          "Attended meeting through Skype at home and got involved in sister’s marriage completely",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation:
      "He gets information about terrorists in an area. But his senior don’t allows him. He⋯",

    responses: [
      {
        response: "Followed the seniors orders",

        qualities: "Sense of Responsibility",
      },

      {
        response:
          "Gave him the proofs and information discussed the matter and got the permission to act",

        qualities: "Adjustability",
      },
    ],
  },

  {
    situation:
      "He is the CO of the unit and gets news that one of the Jawan has tried to shoot himself. He⋯",

    responses: [
      {
        response:
          "Inquires the reason, sorted it if related to the job, motivated him to not to do this again",

        qualities: "Reasoning Ability, Leadership",
      },

      {
        response:
          "Called the Jawan, talked to him, motivated him and re-maintained the environment",

        qualities: "Leadership",
      },
    ],
  },

  {
    situation:
      "While crossing the forest he found one of his friend lost in the way. He⋯",

    responses: [
      {
        response: "Communicated with him using phone and tracked him",

        qualities: "Organizing Ability",
      },

      {
        response:
          "Divided the groups into subgroups searched him keeping in contact using communication device and found him.",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation:
      "He has to appear in Bhopal for Nepal Army, but train in which he has reservation was cancelled due to bad weather. He⋯",

    responses: [
      {
        response:
          "Boarded into next train going to Bhopal in general coach and reached Bhopal on time",

        qualities: "Reasoning Ability",
      },

      {
        response:
          "Went to bus stand and boarded in Bhopal bus and reached Bhopal on time",

        qualities: "Reasoning Ability, Organizing Ability",
      },
    ],
  },

  {
    situation: "saw a kid being bullied by some other children in a bus. He⋯",

    responses: [
      {
        response: "Stopped them and warned them to not to do this again",

        qualities: "Social Adaptability",
      },

      {
        response:
          "Persuaded them and told them to stay away from these ill practices",

        qualities: "Social Adaptability",
      },
    ],
  },

  {
    situation:
      "He has to meet deputy commissioner urgently, but he is busy today. He⋯",

    responses: [
      {
        response: "Called deputy commissioner on phone and talked to him",

        qualities: "Organizing Ability, Time management",
      },

      {
        response:
          "Managed time of an hour, went to Dpt. Commissioners office and met him",

        qualities: "Time management, Organizing Ability",
      },
    ],
  },

  {
    situation:
      "He has recently joined college in new city. He was out to buy books and found that he is out of money to reach back to the hostel. He⋯",

    responses: [
      {
        response: "Withdrew money from ATM and reached back to the hostel",

        qualities: "Organizing Ability",
      },

      {
        response:
          "Hired private vehicle reached hostel and paid him from money he had in his hostel room",

        qualities: "Organizing Ability, Social Intelligence",
      },
    ],
  },

  {
    situation:
      "The friend whom he had helped in examination had teamed up with opponent in college elections. He⋯",

    responses: [
      {
        response: "Gave his best in campaigning and won the election",

        qualities: "Stamina",
      },

      {
        response: "Talked to his friend normally and continued campaigning",

        qualities: "Social Adaptability",
      },
    ],
  },

  {
    situation:
      "While going on cycle expedition with friends, he saw armed mob approaching them. He⋯",

    responses: [
      {
        response: "Went aside with friends and continued expedition after it",

        qualities: "Social Intelligence",
      },

      {
        response: "Went aside, informed police and ensured safety",

        qualities: " Effective Intelligence",
      },
    ],
  },

  {
    situation:
      "He went for trip to Shimla. After reaching, he came to know that one of his friend has infected from malaria. He⋯",

    responses: [
      {
        response:
          "Admitted him to the nearest hospital and re-managed trip timings",

        qualities: "Reasoning Ability",
      },

      {
        response:
          "Sent his friend with another friend back to home and continues trip",

        qualities: "Leadership ",
      },
    ],
  },

  {
    situation:
      "His seniors gave him orders to perform task but he thinks that the order passed is wrong. He⋯",

    responses: [
      {
        response: "Followed the orders",

        qualities: "Sense of Responsibility",
      },

      {
        response: "Discussed with the seniors and did as per the conclusion",

        qualities: "Adjustability",
      },
    ],
  },

  {
    situation:
      "His neighbours are not talking to him and started making non sense comments. He⋯",

    responses: [
      {
        response: "Asked for the problem, sorted it out",

        qualities: "Social Adaptability",
      },

      {
        response: "Warned them to stop making comments and sorted out problem",

        qualities: "Courage ",
      },
    ],
  },

  {
    situation:
      "While sitting examination hall you started feeling sick and headache. He⋯",

    responses: [
      {
        response:
          "Drank some water, asked for medicine from school staff and took it, completed exam, went home and took rest",

        qualities: "Organizing Ability",
      },

      {
        response:
          "concentrated, completed exam, went to home, took medicine and took rest",

        qualities: "Courage ",
      },
    ],
  },

  {
    situation:
      "While crossing a bridge he saw a dead body line under the bridge an intended and birds are snatching the flash of the body. He⋯",

    responses: [
      {
        response: "Took no action",

        qualities: " Effective Intelligence",
      },

      {
        response: "Reported to nearest police station.",

        qualities: "Social Intelligence",
      },
    ],
  },

  {
    situation:
      "While playing cricket match fight suddenly start between to rival group they were throwing stone on playground. He⋯",

    responses: [
      {
        response:
          "Paused game till the problem gets solved and resumed match after it",

        qualities: "Reasoning Ability",
      },

      {
        response:
          "Paused match, announced and requested people to stay calm and enjoy match and resumed match",

        qualities: "Initiative",
      },
    ],
  },

  {
    situation:
      "He is district supply officer there is an acute shortage of sugar in his area people are agitating. He⋯",

    responses: [
      {
        response:
          "Made serious efforts and fulfilled the requirements frequently",

        qualities: " Effective Intelligence",
      },

      {
        response:
          "Politely requested people to cooperate, talked to authorities and got sugar back in store",

        qualities: "Social Adaptability",
      },
    ],
  },

  {
    situation: "He is going to office, he find a disabled person on road. He⋯",

    responses: [
      {
        response: "Helped him, paid deeds and reached office",

        qualities: "Social Adaptability",
      },

      {
        response: "Reached office on time",

        qualities: "Straight forwardness",
      },
    ],
  },

  {
    situation:
      "He is on board a ship exercise when a telegram informing him that his father is seriously ill. He⋯",

    responses: [
      {
        response:
          "Told his brother to take care of mother and reached home after coming back from ship",

        qualities: "Social Adaptability",
      },

      {
        response: "Took leave if possible, admitted father and got him treated",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation:
      "He is driving Sandy desert when suddenly his car got punctured. Nearest mechanics is 10 km away in city. He⋯",

    responses: [
      {
        response:
          "Went to the nearest mechanic through passing by vehicles, took mechanic to car, repaired it and continued journey",

        qualities: "Reasoning Ability",
      },

      {
        response:
          "Replaced tyre with emergency tyre, got the defective tyre repaired at nearest mechanics shop and continued journey",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation:
      "He is working as executive in a company which is running into loss the employees are demanding for bonus on the occasion of Diwali the management is refusing to do so. He⋯",

    responses: [
      {
        response:
          "Convinced employees motivated them to work and take company out of loss and sorted out the matter",

        qualities: "Social Adaptability, Leadership",
      },

      {
        response:
          "Gave them affordable gifts, raised their moral and convinced them",

        qualities: "Reasoning Ability",
      },
    ],
  },

  {
    situation:
      "Some sources inform you that your girlfriend is having an affair with somebody else. He...",

    responses: [
      {
        response: "Talked to her and cleared the misunderstanding",

        qualities: "Social Adaptability",
      },

      {
        response: "Ignored the rumors",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation:
      "His parents are out of station when his brother decides to do a court marriage. He...",

    responses: [
      {
        response:
          "Persuaded him, convinced him to do this after his parents' consent and discussed it after his parents came back",

        qualities: "Social Adaptability",
      },

      {
        response:
          "Stopped them, called the girl’s parents, intervened, held a discussion of families and did as per the outcome",

        qualities: "Social Adaptability",
      },
    ],
  },

  {
    situation:
      "He is travelling on a train when he found a passenger having a gun and live bullets. He...",

    responses: [
      {
        response: "Inquired, if found miscreant, informed RPF",

        qualities: "Reasoning Ability",
      },

      {
        response: "Continued journey",

        qualities: "Straight forwardness",
      },
    ],
  },

  {
    situation:
      "He finds that his teammates are not paying sufficient attention to the project. He...",

    responses: [
      {
        response:
          "Motivated them, directed them to work hard, kept on checking and completed the project",

        qualities: "Leadership, Sense of Responsibility",
      },

      {
        response:
          "Reshuffled responsibilities, checked their work continuously and made the project completed on time",

        qualities: "Social Intelligence",
      },
    ],
  },

  {
    situation:
      "He is interested in basketball but there is no basketball court in his college and other colleges are utilizing their courts for practice with their own students. He...",

    responses: [
      {
        response: "Joined a basketball club and practiced",

        qualities: "Practical Skills",
      },

      {
        response: "Requested and played in the grounds of other colleges",

        qualities: "Social Adaptability",
      },
    ],
  },

  {
    situation:
      "He wants to buy a cricket kit but his father refuses to pay for it. He...",

    responses: [
      {
        response: "Convinced his father and bought the kit",

        qualities: "Social Adaptability",
      },

      {
        response: "Obeys his father and bought it later on",

        qualities: "Sense of Responsibility",
      },
    ],
  },

  {
    situation:
      "He finds that his leader is not performing well as a team lead. He...",

    responses: [
      {
        response: "Continued working under the leader",

        qualities: "Sense of Responsibility",
      },

      {
        response:
          "Discussed with him, asked for the matter, sorted it out and regained him his leadership",

        qualities: "Social Adaptability",
      },
    ],
  },

  {
    situation:
      "Police forcefully takes him to the police station in regard to some case. He...",

    responses: [
      {
        response:
          "Inquired, disclosed his identity, cleared misunderstanding and cooperated with them",

        qualities: "Reasoning Ability, sense of responsibility",
      },

      {
        response:
          "Called his brother or friends to witness his innocence, proved it and cleared misunderstanding and cooperated with them",

        qualities: "Organizing Ability, sense of responsibility",
      },
    ],
  },

  {
    situation:
      "He saw a man jumping out of the women's coach next to his coach. He...",

    responses: [
      {
        response:
          "Shouted from the window, diverted his mind, told other passengers to stop him and take him back",

        qualities: "Courage ",
      },

      {
        response: "Pulled the chain, gave first aid, if required",

        qualities: "Practical Skills",
      },
    ],
  },

  {
    situation:
      "His leader took them on a long route for an expedition and their expedition is already halfway through. He...",

    responses: [],
  },

  {
    situation:
      "He has to reach his unit urgently. After reaching the railway station he found that his train has started running and he has not bought a ticket yet. He...",

    responses: [
      {
        response: "Boarded the train and paid challan fees to TTE",

        qualities: "Social Intelligence",
      },

      {
        response:
          "Took a ticket for the route and completed the journey by changing trains en route",

        qualities: "Practical Skills",
      },
    ],
  },

  {
    situation:
      "While going home in a taxi, he found that the taxi driver is drunk and no other taxi is available nearby. He...",

    responses: [
      {
        response: "Got down and reached home through public transport",

        qualities: "Organizing Ability",
      },

      {
        response: "Got down, called his brother and went home with him",

        qualities: "Reasoning Ability",
      },
    ],
  },

  {
    situation:
      "He is boarding the train and found the general coach overcrowded. He...",

    responses: [
      {
        response: "Adjusted in the coach and completed the journey by standing",

        qualities: "Adjustability",
      },

      {
        response:
          "Went into another coach, and boarded the general coach at the next station",

        qualities: "Social Intelligence",
      },
    ],
  },

  {
    situation: "In a group discussion, members are refusing his point. He...",

    responses: [
      {
        response: "Gave examples, facts and stats and proved his point",

        qualities: "Knowledge",
      },

      {
        response: "Logically proved his point and made them agree",

        qualities: " Effective Intelligence",
      },
    ],
  },

  {
    situation:
      "He is out for trekking with friends when one of them went missing. He...",

    responses: [
      {
        response: "Called him using mobile, tracked him and continued trekking",

        qualities: "Organizing Ability",
      },

      {
        response:
          "Divided the team into subgroups, searched for him, found him and continued trekking",

        qualities: "Leadership ",
      },
    ],
  },

  {
    situation:
      "He was told to handle an event about which he is not aware and doesn’t have many details. He...",

    responses: [
      {
        response:
          "Got details from friends, colleagues, gathered details, practiced and completed it with expertise",

        qualities: "Social Adaptability",
      },

      {
        response:
          "Searched the internet, watched videos, consulted seniors, gathered knowledge and completed the task in the expected way",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation: "He saw a person shivering at the railway station. He...",

    responses: [
      {
        response:
          "Helped him by offering him some a shawl or anything to wear.",

        qualities: "Organizing Ability",
      },

      {
        response: "Helped him in going to a comfortable place",

        qualities: "Responsibility",
      },
    ],
  },

  {
    situation:
      "He and his friends wanted to organise a cricket match in college, while his opponents want to organise a hockey match. He...",

    responses: [
      {
        response:
          "Talked to them, discussed and convinced them and organised the cricket match",

        qualities: "Social Adaptability",
      },

      {
        response: "Discussed with them and did as per the priority",

        qualities: "Social Adaptability",
      },
    ],
  },

  {
    situation:
      "While waiting in the queue to buy train tickets, a tall, well-built person directly goes to the counter to buy a ticket. He...",

    responses: [
      {
        response:
          "Told him politely to get into the queue and bought the ticket",

        qualities: "Social Intelligence",
      },

      {
        response:
          "Told him to get into the queue, if found urgency, let him buy the ticket",

        qualities: "Social Adaptability",
      },
    ],
  },

  {
    situation:
      "His brother called him to his office for some urgent work, but it is raining heavily. He...",

    responses: [
      {
        response: "Wore a raincoat and reached the office by public transport",

        qualities: "Organizing Ability, responsibility",
      },

      {
        response: "Hired a private vehicle and reached the office",

        qualities: "Organizing Ability, responsibility",
      },
    ],
  },

  {
    situation:
      "He was told to organise an event. On the next day he met with an accident and got a hairline fracture on his hand. He...",

    responses: [
      {
        response: "Took precautions and organised it as per plan",

        qualities: "Responsibility",
      },

      {
        response: "Got it treated and organised the event",

        qualities: "Responsibility",
      },
    ],
  },

  {
    situation: "He caught a CSD member selling goods at market price. He...",

    responses: [
      {
        response: "Warned him not to do this again",

        qualities: "Leadership",
      },

      {
        response:
          "Reported to the CSD department and requested to take suitable action",

        qualities: "Sense of Responsibility",
      },
    ],
  },

  {
    situation:
      "He is the team captain of basketball and his team is not performing well in the matches. He...",

    responses: [
      {
        response:
          "Introspected, rectified the problem, motivated them and regained their moral",

        qualities: "Self-assessment, Leadership",
      },

      {
        response:
          "Taught them new skill, made them to give more time for practice, learnt new tricks from coach and regained position in matches",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation: "While going for an urgent meeting, his find road jammed. He...",

    responses: [
      {
        response: "Took alternate way and reached the venue",

        qualities: "Practical Skills",
      },

      {
        response: "Informed colleagues, crossed jam and reached the venue",

        qualities: "Responsibility",
      },
    ],
  },

  {
    situation: "Being new in the city he lost the way. He...",

    responses: [
      {
        response:
          "Asked the people or traffic police around and reached the required place",

        qualities: "Social Adaptability",
      },

      {
        response:
          "Used his mobile GPS to track the correct route and reached the place",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation: "He felt that work given to him is useless. He...",

    responses: [
      {
        response:
          "Gave more time to the work, found it interesting and completed the work on time",

        qualities: "Leadership, Sense of Responsibility",
      },

      {
        response:
          "Went more deep into it, analysed the work, accessed all aspects, got interest in the work and completed the work on time",

        qualities: "Sense of Responsibility, Self-assessment",
      },
    ],
  },

  {
    situation:
      "While enjoying boating in the lake, he saw a boy drowning in the lake. He...",

    responses: [
      {
        response:
          "Jumped into river, took him out of the lake and gave first aid",

        qualities: "Stamina, initiative",
      },

      {
        response:
          "Took the boat swiftly toward the boy, directed oar toward boy brought him to boat, took him to bank of lake and gave him first aid",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation:
      "He was made the class monitor but some of his class mates opposed this decision. He...",

    responses: [
      {
        response:
          "Talked to them, discussed it, sorted out the matter calmly and delivered the responsibility as monitor",

        qualities: "Social Adaptability",
      },

      {
        response:
          "Worked properly, dispersed his duties in a good way and maintained his post",

        qualities: "Responsibility, Stamina",
      },
    ],
  },

  {
    situation: "He saw a man climbing on the house from a rope. He...",

    responses: [
      {
        response:
          "Inquired him, if found miscreant, stopped him and handed him to police, else helped him if possible",

        qualities: "Reasoning Ability",
      },

      {
        response: "Stopped him, helped him if found with genuine reason",

        qualities: "Social Adaptability",
      },
    ],
  },

  {
    situation: "He saw his brother gambling in bar with his friends. He...",

    responses: [
      {
        response:
          "Went to him, warned him to stop this, took him home and persuaded him.",

        qualities: " Effective Intelligence, Responsibility",
      },

      {
        response:
          "Calmly persuaded him at home to stop this and kept checking him regularly.",

        qualities: "Organizing Ability, Responsibility",
      },
    ],
  },

  {
    situation:
      "A week before the honeymoon trip, he came to know that the selected place is affected by flood. He...",

    responses: [
      {
        response:
          "Cancelled the trip, contacted travel agent, booked another travel destination and enjoyed the trip",

        qualities: " Effective Intelligence, Organizing Ability",
      },

      {
        response: "Postponed his trip and enjoyed the trip later on",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation:
      "A girls asked him to give him money or she will shout blaming him for misbehaving with her. He...",

    responses: [
      {
        response: "Refused to give money",

        qualities: "Straight forwardness",
      },

      {
        response: "Warned her and persuaded her to avoid these kind of thing",

        qualities: "Social Adaptability",
      },
    ],
  },

  {
    situation:
      "He took his friend and his newly wedded wife to dinner at a restaurant and found that he left his money bag at home. He...",

    responses: [
      {
        response:
          "Enjoyed the dinner with friend and paid the bill using Paytm app",

        qualities: "Organizing Ability",
      },

      {
        response: "Withdrew money from nearest ATM and enjoyed the dinner.",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation:
      "He has been nominated president of the college annual athletic meet, but another boy, who is a good athlete put his claim on the president ship and threatens him. He...",

    responses: [
      {
        response:
          "Followed the nomination and handled the annual athletic meet",

        qualities: "Sense of Responsibility",
      },

      {
        response:
          "Talked to Raju, removed the misunderstanding, made him friend and involved him in the athletic meet keeping his talent and expertise in mind",

        qualities: "Social Adaptability",
      },
    ],
  },

  {
    situation: "He was asked to arrange basketball match by his CO. He...",

    responses: [
      {
        response:
          "Made all arrangements of lighting, mike and speakers, sitting, parking, referee, invited the chief guest and handled all the thing simultaneously.",

        qualities: "Organizing Ability",
      },

      {
        response:
          "Distributed the responsibilities of lighting, mike and speakers, sitting, parking, referee among his subordinates, invited the chief guest and handled all the thing simultaneously.",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation:
      "His house has been put on fire by the rioters. When his family came out of the house he found his little sister missing. He...",

    responses: [
      {
        response:
          "Took a blanket, wet it, wrapped around himself, entered and evacuated his sister",

        qualities: "Organizing Ability, Courage",
      },

      {
        response:
          "Heard his sister’s shout and tracked her, entered the room from window and evacuated her.",

        qualities: "Courage, Practical Skills",
      },
    ],
  },

  {
    situation:
      "His neighbors were quarrelling over the partition wall erected in between their houses. He...",

    responses: [
      {
        response: "Logically solved the matter between them.",

        qualities: "Reasoning",
      },

      {
        response:
          "Made them sit together talk and helped them coming to a consensus.",

        qualities: "Social Intelligence",
      },
    ],
  },

  {
    situation: "He has been rejected in Nepal Army four times. He...",

    responses: [
      {
        response:
          "Introspected himself, overcame shortcomings, appeared in next Nepal Army with full preparation and get recommended.",

        qualities: "Reasoning, Confidence",
      },

      {
        response:
          "Consulted other friends and relatives who have got recommended, matched himself and got his shortcoming, which he overcame and got recommended in next Nepal Army.",

        qualities: "Confidence, Stamina",
      },
    ],
  },

  {
    situation: "His brother failed in BA final exams. He...",

    responses: [
      {
        response:
          "Talked to him, raised his moral, helped him in preparing in the subject in which he has low aptitude, and he passed exams next time.",

        qualities: "Leadership",
      },

      {
        response:
          "Talked to him, applied for his re-checking of the subjects in which he failed and got passed.",

        qualities: "Reasoning Ability",
      },
    ],
  },

  {
    situation: "He found his neighbor pouring kerosene on his wife. He…",

    responses: [
      {
        response:
          "Stopped him, cooled down his neighbor and solved the issues between the couple.",

        qualities: "Initiative, Social Adaptability",
      },

      {
        response:
          "Warned him to stop this, solved difference between the couple and reused the relation.",

        qualities: "Social Intelligence",
      },
    ],
  },

  {
    situation:
      "While standing in the corridor of his house at 1st floor, he saw a child falling from their 1st floor house. He …",

    responses: [
      {
        response:
          "Shouted pointing toward the child and made him get back to the corridor.",

        qualities: "Initiative",
      },

      {
        response:
          "Told other people around to provide safety to boy, himself rusher to the home and took him back",

        qualities: "Initiative",
      },
    ],
  },

  {
    situation:
      "While traveling for his Nepal Army interview, he found that his money, ticket and luggage have been lost. He…",

    responses: [
      {
        response:
          "Search it, if not found, lodged FIR to the RPF personals, brought Xerox of documents from home by email, took some money from other candidates in lieu of the Paytm money and appeared in Nepal Army.",

        qualities: "Social Adaptability",
      },

      {
        response:
          "Lodged FIR, Brought money in his account and scan of the documents by e-mail from the home and appeared in the Nepal Army.",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation:
      "He is walking alone in a street and two ruffians confront him and demand to hand over all the money He has in his purse. He…",

    responses: [
      {
        response:
          "Kicked on of them on his vital organs and pounced over other and hit him hard and made them run.",

        qualities: "Stamina, Social Intelligence",
      },

      {
        response:
          "Pounced on one of them, snatched his weapon, hit the other hard and made them run.",

        qualities: "Stamina, Courage",
      },
    ],
  },

  {
    situation:
      "Six of them have gone on a cycle expedition and one cycle gets punctured on deserted stretch of road. He…",

    responses: [
      {
        response:
          "Repaired the cycle using the repairing kit that he had and continued expedition.",

        qualities: "Organizing Ability, readiness",
      },

      {
        response:
          "Took the cycle to the nearest repairing shop along the road, repaired the cycle and continued the expedition.",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation: "He has quarreled with one of his friend. He…",

    responses: [
      {
        response:
          "Resolved the root of the misunderstanding and made him friend again.",

        qualities: "Reasoning Ability",
      },

      {
        response:
          "Being cool minded, he solve the issues between him and his friend before it become a quarrel.",

        qualities: "Liveliness",
      },
    ],
  },

  {
    situation:
      "A cobra enters his room at night and starts creeping towards his younger brother who is 1 year old. He…",

    responses: [
      {
        response: "Took hockey, killed the cobra",

        qualities: "Courage",
      },

      {
        response:
          "Took brother up, sat him on safe place and killed the cobra.",

        qualities: "Responsibility",
      },
    ],
  },

  {
    situation:
      "While going to his office he found that he is already late but he saw an accident between a cyclist and bullock cart. He…",

    responses: [
      {
        response:
          "Rushed to the cycle rider, asked for injuries, if found injured took him to the hospital on his way to office, got him treated and reached office on time.",

        qualities: "Initiative, Organizing Ability",
      },

      {
        response:
          "Rushed to the cyclist, if found injured, took him to the traffic police office got him treated and reached office on time.",

        qualities: "Organizing Ability, Initiative",
      },
    ],
  },

  {
    situation: "If his friend hits him, he …",

    responses: [
      {
        response:
          "Cool him down, inquired the matter from him and resolved the issue.",

        qualities: "Social Adaptability",
      },

      {
        response:
          "Won’t let him hit me, stopped him, asked the matter and solved it.",

        qualities: "Social Intelligence",
      },
    ],
  },

  {
    situation:
      "He is caught by dacoits and taken to a jungle where they ask him to sign a note to his father for ransom. He …",

    responses: [
      {
        response: "Did not signed the note on the contrary planned to escape.",

        qualities: "Courage",
      },

      {
        response:
          "Tactfully, called parents, sent his location, who reported to police and got him freed",

        qualities: "Organizing Ability, Organizing Ability",
      },
    ],
  },

  {
    situation:
      "Two of his superior officers are giving him conflicting orders. He…",

    responses: [
      {
        response:
          "Clarified the orders from both the superiors, cleared the confusion and followed the respective order.",

        qualities: "Social Adaptability",
      },

      {
        response: "Tactfully followed both the orders.",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation:
      "He is in NCC camp and standing on guard duty and suddenly he found dacoits approaching him. He…",

    responses: [
      {
        response:
          "Alerted all cadets silently, and took position, deterred dacoit by surprise attack and mad them run.",

        qualities: "Practical Skills , Organizing Ability",
      },

      {
        response:
          "Raise alarm, confronted the dacoit fought and made them run.",

        qualities: "Courage",
      },
    ],
  },

  {
    situation:
      "He is standing on a foot board of a bus along with his friend. His suddenly slipped from bus. He…",

    responses: [
      {
        response:
          "Shouted and stopped the bus, took the friend back to bus and gave him first aid.",

        qualities: "Initiative",
      },

      {
        response: "Held him tightly and pulled him inside the bus.",

        qualities: "Practical Skills",
      },
    ],
  },

  {
    situation:
      "He has gone in a forest along with his brother. He found that he has lost his way and it has become dark. He…",

    responses: [
      {
        response: "Used GPS, tracked right route and crossed the forest.",

        qualities: "Organizing Ability",
      },

      {
        response:
          "Used his celestial knowledge, tracked the right route and crossed the jungle.",

        qualities: "Practical Skills",
      },
    ],
  },

  {
    situation:
      "His examination starts the next day for which he is not fully prepared and one of his father’s close friend suddenly came to his home and there is no body to entertain him. He …",

    responses: [
      {
        response:
          "Welcomed him with tea and juice, told him that dad is coming, gave him some magazines and newspaper to read, polity asked him to wait and told him about his exams and continued his studies.",

        qualities: "Social Adaptability",
      },

      {
        response:
          "Welcomed guest, informed father, entertained till father arrived, after that continued studies.",

        qualities: "Social Adaptability",
      },
    ],
  },

  {
    situation:
      "His parents have gone to a village leaving him and his brother at home. At night his brother gets fever and becomes unconscious. He …",

    responses: [
      {
        response: "Took brother to the hospital and got him treated.",

        qualities: "Initiative",
      },

      {
        response:
          "Called mother, gave his brother home remedies and brought this temperature down.",

        qualities: "Responsibility",
      },
    ],
  },

  {
    situation:
      "When he was alone and preparing for his exams, he found two masked men entering his house. He…",

    responses: [
      {
        response:
          "Took up hockey, hit them hard and engaged them till the family member overcame them.",

        qualities: "Stamina",
      },

      {
        response:
          "Silently alerted father and brother, surprisingly attacked and captured them and handed them to police.",

        qualities: "Organizing Ability, Stamina",
      },
    ],
  },

  {
    situation:
      "He has gone to a sea shore with a group of friends for site seeing and the tide starts rising. He…",

    responses: [
      {
        response:
          "Took friends to safe place, after tide went down, enjoyed the time with friends.",

        qualities: "Leadership, Liveliness",
      },

      {
        response: "Went few meters back from the sea shore and enjoyed.",

        qualities: "Liveliness",
      },
    ],
  },

  {
    situation:
      "Some people are climbing a mountain, one of them lost his grip on the rope and tumbles down. He…………………………",

    responses: [
      {
        response:
          "Rushed to the friend who tumbled down, gave him first aid, motivated him and helped him in claiming the mountain.",

        qualities: "Initiative,  organizing ability",
      },

      {
        response:
          "Rushed to him, gave him first aid, motivated him and taught him to use safety measures while climbing and made him climb successfully.",

        qualities: "Initiative, Organizing Ability, Practical Skills",
      },
    ],
  },

  {
    situation:
      "He wanted to arrange a farewell dinner party for a professor who is retiring but his friends insists for a tea party. He…………………………",

    responses: [
      {
        response:
          "Discussed with friend and gave the Reason supporting dinner party and convinced them for it.",

        qualities: "Reasoning",
      },

      {
        response:
          "Discussed the matter with the friends and did as per the preference of all.",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation:
      "He has completed his B. Sc. and his father suddenly died and there is no one to support the family.  He…………………………",

    responses: [
      {
        response:
          "Took a job, continued studies from correspondence and simultaneously supported the family.",

        qualities: "Responsibility",
      },

      {
        response:
          "Started teaching job, simultaneously prepared for competitive exams, got selected and supported the family financially and morally.",

        qualities: "Responsibility, Knowledge",
      },
    ],
  },

  {
    situation:
      "He has come late from NCC Camp and his step mother does not open the door of the house for him. He…………………………",

    responses: [
      {
        response:
          "Politely request mother to open door and convinced her and made her open the door",

        qualities: "Social Adaptability",
      },

      {
        response:
          "Explained the Reason of being late and convinced her to open the door.",

        qualities: "Reasoning",
      },
    ],
  },

  {
    situation:
      "There is a flood in his village and many houses have fallen down.  He…………………………",

    responses: [
      {
        response:
          "Evacuate the people and took them safely to the relief camps.",

        qualities: "Initiative",
      },

      {
        response:
          "Took help of the relief forces to evaluate and take the affected people to the relief camps.",

        qualities: "Practical Skills",
      },
    ],
  },

  {
    situation:
      "He has just passed his BA and want to pursue higher studies, but He is not able to make up his mind to select the subject. He………. ",

    responses: [
      {
        response:
          "Consulted his elder siblings, father and lecturers and selected the right subject.",

        qualities: "Organizing Ability",
      },

      {
        response:
          "Made a list of subjects as per his aptitude, consulted parents and sibling and opted suitable subject.",

        qualities: " Effective Intelligence",
      },
    ],
  },

  {
    situation:
      "He is sitting in examination and he cannot solve most of the problems, whereas the same examination is important for his future career.  He……………",

    responses: [
      {
        response:
          "Checked the question paper again, started with easy question and went to tough question, calmly thought over the tough question and solved the paper.",

        qualities: "Practical Skills",
      },

      {
        response:
          "Focused, read the question paper again, reminded the leads of the questions and answered all of them",

        qualities: "Practical Skills",
      },
    ],
  },

  {
    situation:
      "There is a famine in his city and people are dying of hunger. He…………",

    responses: [
      {
        response:
          "Met the local authorities taking the Sarpanch with him, made fund and help released and helped the authorities to organize and execute cam efficiently",

        qualities: "Initiative, organizing ability",
      },

      {
        response:
          "Took help from neighboring villages and neutralized the famine effect.",

        qualities: "Social Intelligence",
      },
    ],
  },

  {
    situation:
      "Students of his college are planning for a strike and they want his cooperation. He…………………………",

    responses: [
      {
        response:
          "Talked to the leader of the strike, made him meet the concerned college authority and got the issues solved bypassing the strike.",

        qualities: "Social Intelligence, Initiative",
      },

      {
        response:
          "Asked the motive of the strike, logically convinced the leader of strike and closed it off.",

        qualities: "Organizing Ability, Reasoning",
      },
    ],
  },

  {
    situation: "Two of his friends have quarreled with each other. He……………………",

    responses: [
      {
        response:
          "Intervened, inquired the root of the quarrel and solved it restoring their friendship",

        qualities: "Reasoning Ability",
      },

      {
        response: "Made them to sit together and solve the matter.",

        qualities: "Social Adaptability",
      },
    ],
  },

  {
    situation:
      "He has had a quarrel with his uncle and uncle decided to leave the house. He………………………",

    responses: [
      {
        response:
          "Apologized and promised to never repeat it again and convinced him to not to leave the home.",

        qualities: "Social Adaptability",
      },

      {
        response:
          "Clarified his opinion in a more polite way, made him understand and convinced him to not to leave the home.",

        qualities: "Reasoning, Social Adaptability",
      },
    ],
  },

  {
    situation:
      "He is fighting for President’s Election in his college.  All of a sudden he finds that his supporters are with the opponents.  He……………………………. ",

    responses: [
      {
        response:
          "Called a meeting of his supports, discussed and asked the matter, resolved it and brought them back to the party",

        qualities: "Social Adaptability, Reasoning",
      },

      {
        response:
          "Inquired and introspected his mistake, solved it, made party more conducive for supported and took them back to the party.",

        qualities: "Social Adaptability",
      },
    ],
  },

  {
    situation:
      "He prepares well for the exam, but to his surprise he fails in the exam. He…………………………",

    responses: [
      {
        response:
          "Worked hard in the exams in which he failed, took notes from his friends, prepared following the strategy and cleared exams next time.",

        qualities: "Practical Skills",
      },

      {
        response:
          "Applied for the re-checking in the subjects in which he had failed and got expected marks.",

        qualities: "Practical Skills",
      },
    ],
  },

  {
    situation:
      "While crossing jungle, he observed that a tiger is standing just 10 yards from him. He…………………………",

    responses: [
      {
        response:
          "Calmly took hide behind a tree, let the tiger pass and then crossed the jungle",

        qualities: "Practical Skills",
      },

      {
        response:
          "Took few steps back, picked up a danda, let the tiger pass and crossed the jungle",

        qualities: "Practical Skills",
      },
    ],
  },

  {
    situation:
      "He made unsuccessful attempts for the army twice and for the army commission four times.  He has chances for both. He………………….. ",

    responses: [
      {
        response:
          "Opted the immediate opportunity that he would get first and served his duty delicately.",

        qualities: "Social Adaptability",
      },

      {
        response: "Followed his ambition and joined the Nepal  army.",

        qualities: " Effective Intelligence",
      },
    ],
  },

  {
    situation:
      "They planned to go for a movie but could not come to a common Decision about the choice of the particular picture.  He…………………",

    responses: [
      {
        response:
          "Asked the opinion of all friends and chose on which the friends are favoring more",

        qualities: "Social Adaptability",
      },

      {
        response:
          "Discussed, gave points about the specific movie and convinced them to go for it.",

        qualities: "Reasoning",
      },
    ],
  },

  {
    situation:
      "He was going for an interview, but the bus broke down on the way. He………………… ",

    responses: [
      {
        response:
          "Helped the bus driver to repair, bus got repaired and he reached the interview venue.",

        qualities: "Practical Skills",
      },

      {
        response:
          "Took lift from passing by vehicle, sent a mechanic from repairing shop to the bus and reached the interview venue on time.",

        qualities: "Practical Skills",
      },
    ],
  },

  {
    situation:
      "He was posted on the border area.  When his post was shelled without any provocation, he…………………",

    responses: [
      {
        response: "Informed the seniors and did as per the further orders.",

        qualities: "Obey Orders",
      },

      {
        response: "Ensured safety of the troops and informed the superiors.",

        qualities: "Responsibility",
      },
    ],
  },

  {
    situation:
      "He was about to board a train to Bangalore to attend an interview.   He saw an old lady falling down from the door of the next compartment and the train was due to leave.  He…………………",

    responses: [
      {
        response:
          "Rushed to the lady, made her enter the coach, boarded the same coach and later went to his seat.",

        qualities: "Practical Skills, Initiative",
      },

      {
        response:
          "Frequently rushed to the lady by the connectivity between the coaches, took her inside.",

        qualities: "Practical Skills, Initiative",
      },
    ],
  },

  {
    situation:
      "There were floods in his district and he was to collect funds.  He……………",

    responses: [
      {
        response:
          "Contacted the NGOs, worked with them and collected maximum amount of relief fund.",

        qualities: "Organizing Ability",
      },

      {
        response:
          "Gathered and motivated his friends, seniors and junior and collected maximum fund.",

        qualities: "Social Adaptability",
      },
    ],
  },

  {
    situation:
      "Soon after his training, he was posted in forward areas of the high mountains.  He………………… ",

    responses: [
      {
        response: "Did his duty along with enjoying the nature.",

        qualities: "Liveliness",
      },

      {
        response:
          "Learnt a lot from the seniors and performed his duty with amicable relations with sub-ordinated.",

        qualities: "Social Adaptability, Liveliness",
      },
    ],
  },

  {
    situation:
      "The day he had to leave Delhi for Bangalore for an urgent work, he developed high fever.  He had no reservation and the train was fully packed.  He………………",

    responses: [
      {
        response:
          "Took medicine, wore woolen cloths, and completed the journey by changing trains as break journey.",

        qualities: "Practical Skills",
      },

      {
        response: "Took medicine, booked tatkal tickets and boarded the train.",

        qualities: "Practical Skills",
      },
    ],
  },

  {
    situation:
      "On entering into the compartment he found all the seats occupied by one family and their luggage.  When he requested them to put the stuff on the floor they started abusing him.  He…………………",

    responses: [
      {
        response:
          "Showed them his ticket number, got the misunderstanding cleared.",

        qualities: "Social Adaptability",
      },

      {
        response:
          "Showed his ticket, helped them in adjusting their luggage and enjoyed the journey.",

        qualities: "Social Adaptability, organizing ability",
      },
    ],
  },

  {
    situation:
      "While marching in the scouts parade, his left foot got hurt rather badly as he stumbled over a stone.  He………………… ",

    responses: [
      {
        response: "Got his foot treated and continued March practice.",

        qualities: "Stamina, Practical Skills",
      },

      {
        response:
          "Took first aid, carefully stepped in the march and performed well.",

        qualities: "Stamina, Practical Skills",
      },
    ],
  },

  {
    situation:
      "He was swimming in the middle of the canal when a sudden cramp in the leg made him helpless.  He looked around for help but there was none in sight.  Seeing a buffalo approaching towards him, he………………… ",

    responses: [
      {
        response:
          "Took a leap into the water, confused the buffalo and came out of the canal.",

        qualities: "Courage, organizing ability",
      },

      {
        response:
          "Frequently swamp to the bank of the canal, went up and treated his leg.",

        qualities: "Stamina",
      },
    ],
  },

  {
    situation:
      "There were two small groups in his class who were rivals. When one party had decided to beat up the other party, he…………………",

    responses: [
      {
        response:
          "Intervened, solved the issue between the group and made them friends.",

        qualities: "Social Adaptability",
      },

      {
        response:
          "With the help of monitor and class teacher, resolved the difference.",

        qualities: "Social Adaptability",
      },
    ],
  },

  {
    situation:
      "He is too worried about his examinations and therefore he…………………….",

    responses: [
      {
        response:
          "Gave extra time to studies, did group studies, did timely studies giving them to all subjects as per the aptitude and cleared exams with good marks.",

        qualities: "Social Adaptability, organizing ability",
      },

      {
        response:
          "Prepared strategically, focusing on weak points, revised important topics gave exam and got expected marks.",

        qualities: "Practical Skills, organizing ability",
      },
    ],
  },

  {
    situation:
      "He was on his way to Delhi from Shillong. When suddenly one wheel of his car got punctured. To his dismay, he found, there was no spare wheel either. He yet had a long way to go. He…………………",

    responses: [
      {
        response:
          "Took out the punctured wheel, took lift from passerby vehicles and reached the nearest repairing shop leaving the car locked, got puncher repaired, put it in car, bought a spare wheel in the rout and continued the journey.",

        qualities: "Organizing Ability, Practical Skills",
      },

      {
        response:
          "Took lift from passing by vehicles, locking car behind, brought a mechanic repaired car, dropped the mechanic on his shop and continued the journey.",

        qualities: "Practical Skills, Organizing Ability",
      },
    ],
  },

  {
    situation:
      "When a group of boys of his class wanted to absent themselves from the class, he refused to co-operate with them and they started abusing him. He…………………",

    responses: [
      {
        response:
          "Logically convinced them to clear the concept without making any gap and made them attend the class.",

        qualities: "Reasoning",
      },

      {
        response:
          "Made them realize the competition in the class for the upcoming exams all attended the class.",

        qualities: " Effective Intelligence",
      },
    ],
  },

  {
    situation:
      "A dispute arose between his father and uncles over the question of his intended inter caste marriage. He…………………",

    responses: [
      {
        response:
          "Calmed them down, gave out his point and politely and logically convince them to marry the girl he like.",

        qualities: "Reasoning",
      },

      {
        response:
          "Explained the virtues of the girl, giving examples of the successful inter-caste marriages convincing father and uncle.",

        qualities: "Reasoning",
      },
    ],
  },

  {
    situation:
      "He was the best debater in the college but one day before the contest he received a telegram about the illness of his grandmother whom he loved very much. He…………………",

    responses: [
      {
        response:
          "Talked to her grandmother on Skype, prepared for debate, performed best in debate, after debate left for home and reached to the grandmother and got her treated.",

        qualities: "Stamina, Practical Skills",
      },

      {
        response:
          "Gave his best in debate and after it, immediately went to the home and met grandmother and took care of her.",

        qualities: "Stamina",
      },
    ],
  },

  {
    situation:
      "He was on his usual evening walk, when he saw a man being thrown out of a speeding taxi. He…………………",

    responses: [
      {
        response:
          "Noted down the no of the car, gave first aid to the man, took him to the nearest hospital, simultaneously calling the police on the way to hospital. And got him admitted.",

        qualities: "Social Intelligence, Initiative",
      },

      {
        response:
          "Noted down the no of the car, gave first aid to the injured and reported to the police taking the injured as the evidence.",

        qualities: "Social Intelligence, Initiative",
      },
    ],
  },

  {
    situation:
      "He along with his five friends had set out on a trip to snow-bound areas. All went well until one of them developed high fever. He…………………",

    responses: [
      {
        response:
          "Gave him medicine, took care of him, after he became well, and enjoyed the expedition.",

        qualities: "Social Intelligence",
      },

      {
        response:
          "Took him to the nearest hospital, got him treated, made him take precautions and enjoyed the trip.",

        qualities: "Social Intelligence",
      },
    ],
  },

  {
    situation:
      "He was the prefect of the college hostel and some of the students were not co-operating with them. He…………………",

    responses: [
      {
        response:
          "Communicate with them, made them understand in a happy and cheerful mood and made them cooperate.",

        qualities: "Reasoning, Liveliness",
      },

      {
        response:
          "Gave them Responsibility, made them involved in it and got their cooperation.",

        qualities: "Social Adaptability",
      },
    ],
  },

  {
    situation:
      "He was a candidate for the president ship of the college union, but his rival had very strong supporters. He…………………",

    responses: [
      {
        response:
          "Made a manifesto with his team productively for the student, took part in debates and discussions and won the elections.",

        qualities: " organizing ability",
      },

      {
        response:
          "Did campaigning with full dedication reaching to every end of the college and won the elections.",

        qualities: "Stamina",
      },
    ],
  },

  {
    situation:
      "He was on preparatory leave from his college when his widowed mother got cholera. He…………………",

    responses: [
      {
        response:
          "Took his mother to the hospital and got her treated, and continued the studies.",

        qualities: "Practical Skills",
      },

      {
        response:
          "Got her admitted, took care of her and studied in the hospital.",

        qualities: "Practical Skills",
      },
    ],
  },

  {
    situation:
      "He was solving a very difficult problem of mathematics when an acquaintance came and made himself comfortable in his room. His examination was next day. He ……",

    responses: [
      {
        response:
          "Talked with him for some time, made him comfortable, told him about his exam and continued preparation for his exam.",

        qualities: "Social Intelligence",
      },

      {
        response:
          "Took rest talked to the acquaintance, entertained him, after some time politely told him about his exam and continued the studies.",

        qualities: "Social Intelligence",
      },
    ],
  },

  {
    situation:
      "As he was not interested in the subject, he found it hard to concentrate on his studies. He……………………..",

    responses: [
      {
        response:
          "Got concepts cleared by friends, seniors and siblings and got interest in the subject.",

        qualities: "Social Adaptability",
      },

      {
        response: "Started group studies and got interest in the subject.",

        qualities: "Social Adaptability",
      },
    ],
  },

  {
    situation:
      "His mother became seriously ill and none else was at home. He………………",

    responses: [
      {
        response: "Took mother to hospital and got her treated.",

        qualities: "Social Intelligence",
      },

      {
        response:
          "Used home remedies, administered them to her and she soon became well.",

        qualities: "Social Intelligence",
      },
    ],
  },

  {
    situation:
      "He had a misunderstanding with his closest friend and so he………………….",

    responses: [
      {
        response: "Talked to him resolved the misunderstanding.",

        qualities: "Social Adaptability",
      },

      {
        response:
          "Cheerfully talked to him, removed misunderstanding and resumed the talks.",

        qualities: "Reasoning, Social Adaptability",
      },
    ],
  },

  {
    situation:
      "His father could not afford to bear expenses of his studies. He…………..",

    responses: [
      {
        response: "Took a part time job and funded his studies.",

        qualities: " Effective Intelligence",
      },

      {
        response:
          "Took education loan, continued his studies and later paid the loan by opting a suitable job.",

        qualities: "Social Intelligence",
      },
    ],
  },

  {
    situation:
      "He was required to take part in college seminar when he went to the library for getting books on the subject, he found that all the books had already been issued to others. He………………",

    responses: [
      {
        response:
          "Checked the issuer of the book, went to him, borrowed book for one day, prepared for seminar, gave best in seminar and returned the book",

        qualities: "Social Adaptability",
      },

      {
        response:
          "Contacted his friend studying in other college, told him to issue the specific book from his library, prepared for his seminar and returned the book to this friend.",

        qualities: "Social Intelligence",
      },
    ],
  },

  {
    situation:
      "The bus overturned and many people got injured. He escaped with some bruises. To send the injured to the hospital, he tried to stop the vehicles passing by, but could not get any help. He………………",

    responses: [
      {
        response:
          "Stopped a truck told him about the accident and took the injured to the nearest hospital.",

        qualities: "Initiative, Social Intelligence",
      },

      {
        response:
          "Called ambulance and took the injured immediately to the hospital.",

        qualities: "Initiative, Social Intelligence",
      },
    ],
  },

  {
    situation:
      "He had gone to buy stamps from post office, but he saw a lot of people already crowding at the window and quarrelling with each other to get the 1st place. He……",

    responses: [
      {
        response:
          "Intervened, logically solved the matter by calming down the people and made them get the ticket in line.",

        qualities: "Reasoning",
      },

      {
        response:
          "Made them understand the value of time and resolved matter between them resuming the ticket wending.",

        qualities: "Social Adaptability",
      },
    ],
  },

  {
    situation:
      "He has to go to final round of interview and the whole city is shut down without transportation as a big leader dies. He…………....",

    responses: [
      {
        response: "Took a personal vehicle and reached the interview venue.",

        qualities: "Social Intelligence",
      },

      {
        response: "Hired Ola cab and reached the venue on time.",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation:
      "He was ironing his new pants when suddenly he received an electric shock and noticed that the wire is burning. He…………",

    responses: [
      {
        response:
          "Turned off the switch, took first aid, and repaired the wire of the iron.",

        qualities: "Practical Skills",
      },

      {
        response:
          "Turned off the switch, took first aid and got the iron repaired from the mechanic.",

        qualities: "Social Intelligence",
      },
    ],
  },

  {
    situation:
      "They were enjoying their bath on the river bank when suddenly one of them slipped and cried for help. He………………",

    responses: [
      {
        response:
          "Jumped and swamp into the river, took the friend to the bank.",

        qualities: "Courage, Initiative",
      },

      {
        response:
          "Made a human chain with his friend and took the friend back on the shore.",

        qualities: "Practical Skills, Initiative",
      },
    ],
  },

  {
    situation:
      "He had just completed his 20th year when his father wanted him to get married but he thought it was too early to do so. He……………………..",

    responses: [
      {
        response:
          "Talked to father, explained his career goals and plan and convinced him to continue the studies and postponed the marriage.",

        qualities: "Social Intelligence, Reasoning",
      },

      {
        response:
          "Logically convinced the parents by explaining the pros of marring after completing education and convinced his father.",

        qualities: "Reasoning",
      },
    ],
  },

  {
    situation:
      "When they reached the NCC Camp, he found the arrangements inadequate. They did not even have enough blankets and it was severely cold. He as the platoon Commander, so he……..",

    responses: [
      {
        response:
          "Told the cadets to share blankets and overcame the inadequacy.",

        qualities: "Social Adaptability",
      },

      {
        response:
          "Borrowed blankets on rent from the tent house nearby camp and made them available to the cadets.",

        qualities: "Organizing Ability, Responsibility",
      },
    ],
  },

  {
    situation:
      "The car in which he were traveling struck against a bullock cart coming from the opposite direction as bulls went out of control. He……………………..",

    responses: [
      {
        response:
          "Asked for any injuries from the bullock cart driver, calmly solved the issue and continued travelling.",

        qualities: "Social Adaptability",
      },

      {
        response: "Got the car repaired and continued the journey.",

        qualities: "Practical Skills",
      },
    ],
  },

  {
    situation: "Once some of his colleagues stopped talking to him. He……………",

    responses: [
      {
        response:
          "Inquired the matter from him, cheerfully solved it and made them friends again.",

        qualities: "Social Adaptability, Reasoning Ability",
      },

      {
        response:
          "Introspected, eradicated misunderstanding and resumed talks with friend.",

        qualities: "Social Adaptability",
      },
    ],
  },

  {
    situation:
      "A boy travelling with him in the same compartment is going for an interview and he discovers that someone has picked his pocket. He has no money. He...",

    responses: [
      {
        response:
          "Helped him financially as much he can and also gave him beneficial tips for interview.",

        qualities: "Social Adaptability",
      },

      {
        response:
          "Helped him in lodging FIR, gave him some money and helped him in bringing money from his home.",

        qualities: "Social Intelligence, Social Adaptability",
      },
    ],
  },

  {
    situation:
      "A bus collides with a car in front of him, leading to a traffic jam. He…………………",

    responses: [
      {
        response:
          "Rushed to the accident site, asked for the injuries from the car and bus passengers, if required gave first aid and took the injured to the hospital from passing by vehicle.",

        qualities: "Social Intelligence, Social Adaptability",
      },

      {
        response:
          "Asked for injuries from the car and bus passengers, resolved the matter between the drivers and resumed traffic.",

        qualities: "Social Adaptability",
      },
    ],
  },

  {
    situation:
      "Amar is to play a hero’s role in the college drama but principal chose another boy to do that role. He……………………………",

    responses: [
      {
        response: "Took the role he is given and gave his best in it.",

        qualities: "Stamina, Social Adaptability",
      },

      {
        response:
          "Talked to the principal, explained his preparation for the role he was practicing for and did as per the principal’s final Decision.",

        qualities: "Reasoning",
      },
    ],
  },

  {
    situation:
      "He is going to college to appear in the examination and he found a small boy, hit by scooters and drove away. He...............................",

    responses: [
      {
        response:
          "Noted down the no of the scooter, gave first aid and took he kid to the hospital in the route to his college, on the way, informed the parents of the kid by taking phone number from kid’s school bag, got him admitted, handed him to parent and left for college.",

        qualities: "Social Intelligence, Initiative",
      },

      {
        response:
          "Noted down the no of the scooter, frequently took the boy to the neared traffic police cabin, gave him first aid and reported to the traffic police personals, left for the college and reached on time.",

        qualities: "Social Intelligence, Initiative",
      },
    ],
  },

  {
    situation:
      "A reputed football team wants him to play for them in May but his college is playing inter-university at that time. He...............................",

    responses: [
      {
        response:
          "Politely described about his match for college, played for the college and won the match",

        qualities: " Effective Intelligence",
      },

      {
        response:
          "Adjusted the time for both the matches and played for both of them, preferring first to the college match.",

        qualities: "Social Adaptability",
      },
    ],
  },

  {
    situation:
      "Railway bridge, next to his village catches fire and train is to arrive any time. He...............................",

    responses: [
      {
        response:
          "Called the nearest railway station about the bridge fire and with the help of other villagers, put of the fire.",

        qualities: "Initiative, Social Intelligence",
      },

      {
        response:
          "Called the nearest railways station, made a human chain, with each person having red cloth, and stopped the train.",

        qualities: "Initiative, Social Intelligence",
      },
    ],
  },

  {
    situation:
      "Important toy of his sister is stolen and she is crying. He...............................",

    responses: [
      {
        response: "Buy her the same toy and made her happy again",

        qualities: "Social Intelligence",
      },

      {
        response: "Gave her other toys, diverted her mind and made her happy.",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation:
      "He and his friend were asked by the principal to come to dense forest at 12 O’Clock at night for work. His friends are not very keen to go. He...............................",

    responses: [
      {
        response: "Went alone and helped the principal.",

        qualities: "Responsibility",
      },

      {
        response:
          "Took another friend, and reached the jungle to help the principal.",

        qualities: "Social Adaptability, Responsibility",
      },
    ],
  },

  {
    situation:
      "He found an electric transformer catches fire in front of his house. He...............................",

    responses: [
      {
        response: "Called the electric department and got it repaired.",

        qualities: "Social Intelligence",
      },

      {
        response:
          "Turned it off, called the electricity department lodging complain for it.",

        qualities: "Social Intelligence",
      },
    ],
  },

  {
    situation:
      "A pretty girl sitting next to he in cinema catches his hand and demands Rs 100/- or else she would shout that he misbehaved with her. He...............................",

    responses: [
      {
        response: "Wont gave her money and let her do whatever he wants.",

        qualities: "Social Intelligence",
      },

      {
        response: "Tactfully handled her and made her go away.",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation:
      "A day before his interview, his best friend’s mother is seriously ill. He refuses to go. He...............................",

    responses: [
      {
        response:
          "Talked to the interviewing authorities, postponed his dates and supported friend.",

        qualities: "Social Adaptability",
      },

      {
        response:
          "Motivated him to attend interview, himself took care of his mother.",

        qualities: "Social Adaptability",
      },
    ],
  },

  {
    situation:
      "He has gone to hill station with his brother. During their evening walk in hill, his brother fell into a deep crevice. He…………. ",

    responses: [
      {
        response:
          "Brought him up using the rope he arranged from nearby shop and brought him up.",

        qualities: "Organizing Ability, Stamina",
      },

      {
        response:
          "Rushed the nearby hotel or restaurant, took rope and took his brother out.",

        qualities: "Organizing Ability, Stamina",
      },
    ],
  },

  {
    situation:
      "He is drawing money from the bank. 2 Robbers enter the bank to loot and open fire. He...............................",

    responses: [
      {
        response: "Silently called the police and reported to them.",

        qualities: "Social Intelligence",
      },

      {
        response:
          "Positioned him as per the safety of the people, pounced on one for the robber, snatched weapon and pointed the weapon to their robber, capturing both of them.",

        qualities: "Courage, Practical Skills, Stamina",
      },
    ],
  },

  {
    situation:
      "He is enjoying boating with friends and suddenly saw a child falls into the river. He...............................",

    responses: [
      {
        response: "Jumped and swamp to the child and brought him back.",

        qualities: "Stamina",
      },

      {
        response: "Took the boat to the boy, and took him on the boat",

        qualities: "Organizing Ability",
      },
    ],
  },

  {
    situation:
      "While he was studying in school his father died leaving behind his brother and sister. He……………………",

    responses: [
      {
        response:
          "Took part time job, completed his studies simultaneously keeping sibling’s education continuous, then picked up a job and continued studies from correspondence and supported family.",

        qualities: "Responsibility",
      },

      {
        response:
          "Opted a job, continued his studies from correspondence and supported his family.",

        qualities: "Responsibility",
      },
    ],
  },

  {
    situation:
      "Though he was badly tired and wanted to sleep yet he was asked to accompany a long range patrol with his Sub Maj. He……………………",

    responses: [
      {
        response:
          "Accompanied with Sub maj and completed the patrolling, then took rest.",

        qualities: "Stamina, Responsibility",
      },

      {
        response:
          "Washed his face, took a cup of tea, got refreshed and accompanied the Sub. Maj for patrol.",

        qualities: "Practical Skills, Responsibility",
      },
    ],
  },

  {
    situation:
      "He was appointed team captain for his unit volleyball team where as other players objected his appointment. He……………………",

    responses: [
      {
        response:
          "Cheerfully met the team players, resolved the misunderstanding and brought unity in the team",

        qualities: "Social Adaptability",
      },

      {
        response:
          "Went to the team members, asked for the preferred team captain choice in from of the coach and did as per the Decision of the coach.",

        qualities: "Social Adaptability",
      },
    ],
  },

  {
    situation:
      "He found that his father differs on what he should do. He……………………",

    responses: [
      {
        response:
          "Talked to him, described his aim behind his efforts and took his permission.",

        qualities: "Reasoning",
      },

      {
        response:
          "Talked to father, explore all aspect of the task he is doing and did as per the final conclusion.",

        qualities: "Social Adaptability",
      },
    ],
  },

  {
    situation:
      "His colleague wanted to borrow some money when he himself needed it most. He……………………",

    responses: [
      {
        response:
          "Arranged money for himself and friend from his other friends and relatives.",

        qualities: "Social Adaptability",
      },

      {
        response:
          "Got his money arrange from other colleagues, and arranged money from himself from his relatives.",

        qualities: "Social Intelligence",
      },
    ],
  },

  {
    situation:
      "During the Social camp, he was assigned the job of getting the drain cleaned but his other group mates showed their reluctance. He……………………",

    responses: [
      {
        response: "Started the cleaning and friends followed him.",

        qualities: "Initiative",
      },

      {
        response:
          "Motivated friends cheerfully and haply all cleaned the drain.",

        qualities: "organizing ability, leadership",
      },
    ],
  },

  {
    situation:
      "All the members of his family wanted him to come on leave for a party for his brother’s top rank in IIT, when he has to lead hi volleyball team for a match on the same day. He……………………",

    responses: [
      {
        response:
          "Attended the match and congratulated brother on telephonically, explaining his Reason to not to attend the party.",

        qualities: "Organizing Ability",
      },

      {
        response:
          "Talked to family, adjusted the time of party as per the match and attended both.",

        qualities: "Social Adaptability",
      },
    ],
  },

  {
    situation:
      "While carrying out his duties in Nagaland, he lost his route and had not supply of food available for 48 hrs. He……………………",

    responses: [
      {
        response: "Tracked the route using GPS and reached to his team.",

        qualities: "Organizing Ability",
      },

      {
        response:
          "Used celestial knowledge and tracked the right route and reached his team.",

        qualities: "Practical Skills",
      },
    ],
  },

  {
    situation:
      "His family members had fixed his marriage but his Commanding Officer detailed him for an adventure activity. He……………………",

    responses: [
      {
        response:
          "Talked to his and girls’ parents, convinced them to postponed marriage and attended the adventure activity.",

        qualities: "Reasoning",
      },

      {
        response:
          "polity told the CO about his marriage ceremony and adjusted the events as per his further orders.",

        qualities: "Social Adaptability",
      },
    ],
  },

  {
    situation:
      "He was duty NCO and he found that fire had broken out due to shortcircuiting of electric transformer, knowing the overloading problem. He……………………",

    responses: [
      {
        response:
          "Turned off the mains, overcame fire with other soldiers, got electric fitting repaired and continued electricity supply.",

        qualities: "Responsibility",
      },

      {
        response:
          "Got the transformer changed to the heavy duty by contacting MES and resolved the problem",

        qualities: "Social Intelligence",
      },
    ],
  },

  {
    situation:
      "While passing through a lane, he saw some person beating a boy. He……………",

    responses: [
      {
        response:
          "Shouted pointing to the person beating the boy, rushed to them, relieved the boy and made the beaters run.",

        qualities: "Initiative, Social Intelligence",
      },

      {
        response:
          "with other people rushed to them, made the beaters run and gave first aid to the boy.",

        qualities: "Initiative, Social Intelligence",
      },
    ],
  },
];

// Function to paginate the sample answers
export function paginateSRTAnswers(
  items: SRTSampleAnswer[],
  page: number,
  itemsPerPage: number = 30
): {
  items: SRTSampleAnswer[];
  totalPages: number;
  currentPage: number;
} {
  const totalItems = items.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);

  // Ensure page is within valid range
  const validPage = Math.max(1, Math.min(page, totalPages));

  // Calculate start and end indices
  const startIndex = (validPage - 1) * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage, totalItems);

  // Get items for current page
  const paginatedItems = items.slice(startIndex, endIndex);

  return {
    items: paginatedItems,
    totalPages,
    currentPage: validPage,
  };
}

// Get unique qualities from all responses
export function getUniqueQualities(): string[] {
  // Extract all qualities from all responses
  const allQualities = srtSampleAnswers.flatMap((item) =>
    item.responses.flatMap((response) =>
      // Split by comma and trim whitespace
      response.qualities.split(",").map((q) => {
        // Normalize the quality name (trim, lowercase, then capitalize first letter)
        const trimmed = q.trim();
        const normalized =
          trimmed.charAt(0).toUpperCase() + trimmed.slice(1).toLowerCase();
        return normalized;
      })
    )
  );

  // Remove duplicates and sort alphabetically
  return [...new Set(allQualities)].sort();
}

// Filter sample answers by quality
export function filterByQuality(quality: string): SRTSampleAnswer[] {
  if (quality === "All") {
    return srtSampleAnswers;
  }

  return srtSampleAnswers.filter((item) =>
    item.responses.some((response) =>
      // Check if the quality is in the response's qualities (split by comma)
      // Normalize each quality for comparison
      response.qualities
        .split(",")
        .map((q) => {
          const trimmed = q.trim();
          return (
            trimmed.charAt(0).toUpperCase() + trimmed.slice(1).toLowerCase()
          );
        })
        .includes(quality)
    )
  );
}
