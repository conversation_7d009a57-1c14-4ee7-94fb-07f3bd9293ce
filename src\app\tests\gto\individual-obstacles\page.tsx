"use client";

import { ArrowLeftIcon, BoltIcon } from "@heroicons/react/24/outline";
import Link from "next/link";
import NavigationButton from "@/components/NavigationButton";
import Image from "next/image";
import {
  individualObstacles,
  pointsToRemember,
  approachTips,
  recommendedSequence,
} from "@/data/individual-obstacles";
import { useState } from "react";

export default function IndividualObstaclesPage() {
  const [selectedObstacle, setSelectedObstacle] = useState<number | null>(null);

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="mx-auto max-w-4xl px-6">
        <NavigationButton
          href="/tests/gto"
          className="mb-8 inline-flex items-center text-gray-600 hover:text-gray-900"
        >
          <ArrowLeftIcon className="h-5 w-5 mr-2" />
          Back to GTO Test
        </NavigationButton>

        <div className="bg-white rounded-2xl shadow-sm p-8 border-t-4 border-cyan-500">
          <div className="flex items-center space-x-3 mb-6">
            <div className="p-2 rounded-full bg-cyan-100">
              <BoltIcon className="h-8 w-8 text-cyan-600" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900">
              Individual Obstacles
            </h1>
          </div>

          <div className="flex justify-center mb-10">
            <Link
              href="/tests/gto/individual-obstacles/interactive"
              className="bg-cyan-500 hover:bg-cyan-600 text-white font-bold py-4 px-10 rounded-lg shadow-lg transition duration-200 flex items-center text-xl animate-pulse"
            >
              <BoltIcon className="h-7 w-7 mr-3" />
              Try Interactive Obstacles Simulation
            </Link>
          </div>

          <div className="prose prose-lg max-w-none">
            <p className="text-lg text-gray-700 mb-6">
              The Individual Obstacles task is a physical challenge that tests
              your personal determination, problem-solving abilities, physical
              fitness, and mental toughness as you navigate through a series of
              obstacles independently.
            </p>

            <h2 className="text-xl font-semibold text-gray-900 mt-8 mb-4">
              What are Individual Obstacles?
            </h2>
            <p>
              This is one of the most important GTO Tasks in Nepal Army and
              checks out the basic ability, stamina and physical fitness of the
              candidates to perform different sets of situations or tasks
              allotted to them by the authorities. This round also checks the
              decision-making ability of the candidates and the speed with which
              the candidates can make a decision.
            </p>
            <p className="mt-2">
              In this task, candidates must complete a series of 10 obstacles
              within a specified time limit. Each obstacle is designed to test
              different aspects of physical ability, problem-solving, and
              determination.
            </p>

            <h2 className="text-xl font-semibold text-gray-900 mt-8 mb-4">
              Skills Evaluated
            </h2>
            <div className="bg-cyan-50 p-6 rounded-lg mb-8">
              <h3 className="text-lg font-medium text-cyan-900 mb-4">
                Individual Obstacles test your:
              </h3>
              <ul className="list-disc pl-5 space-y-2 text-cyan-800">
                <li>Physical fitness</li>
                <li>Mental toughness</li>
                <li>Problem-solving abilities</li>
                <li>Time management</li>
                <li>Individual determination</li>
                <li>Decision-making under pressure</li>
                <li>Adaptability to challenges</li>
                <li>Perseverance and grit</li>
              </ul>
            </div>

            <h2 className="text-xl font-semibold text-gray-900 mt-8 mb-4">
              The 10 Individual Obstacles
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
              {individualObstacles.map((obstacle) => (
                <div
                  key={obstacle.id}
                  className="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer overflow-hidden"
                  onClick={() => setSelectedObstacle(obstacle.id)}
                >
                  <div className="relative h-32 w-full">
                    <Image
                      src={obstacle.image}
                      alt={`${obstacle.name} obstacle`}
                      fill
                      className="object-cover"
                    />
                  </div>
                  <div className="p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-semibold text-gray-900">
                        {obstacle.id}. {obstacle.name}
                      </h3>
                      <span className="bg-cyan-100 text-cyan-800 text-xs font-medium px-2.5 py-0.5 rounded">
                        {obstacle.points} points
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 line-clamp-2">
                      {obstacle.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>

            {selectedObstacle && (
              <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
                <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                  <div className="p-6">
                    {individualObstacles
                      .filter((o) => o.id === selectedObstacle)
                      .map((obstacle) => (
                        <div key={obstacle.id}>
                          <div className="flex justify-between items-start mb-4">
                            <h2 className="text-2xl font-bold text-gray-900">
                              {obstacle.id}. {obstacle.name}
                            </h2>
                            <button
                              onClick={() => setSelectedObstacle(null)}
                              className="text-gray-500 hover:text-gray-700"
                            >
                              <svg
                                className="w-6 h-6"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth="2"
                                  d="M6 18L18 6M6 6l12 12"
                                ></path>
                              </svg>
                            </button>
                          </div>
                          <div className="relative h-48 w-full mb-4 bg-gray-200 rounded-lg overflow-hidden">
                            <Image
                              src={obstacle.image}
                              alt={`${obstacle.name} obstacle`}
                              fill
                              className="object-cover"
                            />
                          </div>
                          <div className="mb-4">
                            <h3 className="font-semibold text-gray-900 mb-2">
                              Description:
                            </h3>
                            <p className="text-gray-700">
                              {obstacle.description}
                            </p>
                          </div>
                          <div className="mb-4">
                            <h3 className="font-semibold text-gray-900 mb-2">
                              Instructions:
                            </h3>
                            <p className="text-gray-700">
                              {obstacle.instructions}
                            </p>
                          </div>
                          <div className="mb-4">
                            <h3 className="font-semibold text-gray-900 mb-2">
                              Points: {obstacle.points}
                            </h3>
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900 mb-2">
                              Tips:
                            </h3>
                            <ul className="list-disc pl-5 space-y-1 text-gray-700">
                              {obstacle.tips.map((tip, index) => (
                                <li key={index}>{tip}</li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              </div>
            )}

            <h2 className="text-xl font-semibold text-gray-900 mt-8 mb-4">
              Recommended Sequence for Maximum Points
            </h2>
            <div className="bg-cyan-50 p-6 rounded-lg mb-8">
              <p className="mb-4 text-cyan-800">
                The recommended sequence to maximize your points is:
              </p>
              <div className="flex flex-wrap gap-2 mb-4">
                {recommendedSequence.map((id, index) => (
                  <div
                    key={index}
                    className="bg-cyan-200 text-cyan-800 px-3 py-1 rounded-full font-medium"
                    onClick={() => setSelectedObstacle(id)}
                  >
                    {id}
                  </div>
                ))}
              </div>
              <p className="text-cyan-800">
                <strong>Note:</strong> If running out of time, leave obstacles 1
                & 2 and do 7 & 10 to get at least 52 points.
              </p>
            </div>

            <h2 className="text-xl font-semibold text-gray-900 mt-8 mb-4">
              Points to Remember
            </h2>
            <div className="bg-gray-100 p-6 rounded-lg mb-8">
              <ul className="list-disc pl-5 space-y-2 text-gray-700">
                {pointsToRemember.map((point, index) => (
                  <li key={index}>{point}</li>
                ))}
              </ul>
            </div>

            <h2 className="text-xl font-semibold text-gray-900 mt-8 mb-4">
              Approach Strategy
            </h2>
            <div className="bg-gray-100 p-6 rounded-lg mb-8">
              <ul className="list-disc pl-5 space-y-2 text-gray-700">
                {approachTips.map((tip, index) => (
                  <li key={index}>{tip}</li>
                ))}
              </ul>
            </div>

            <h2 className="text-xl font-semibold text-gray-900 mt-8 mb-4">
              The Course Format
            </h2>
            <div className="bg-gray-100 p-6 rounded-lg mb-8">
              <ol className="list-decimal pl-5 space-y-3">
                <li>
                  <strong>Course Layout:</strong> A series of 10 obstacles
                  arranged in clusters.
                </li>
                <li>
                  <strong>Objective:</strong> Score maximum points by completing
                  obstacles and repeating high-value ones.
                </li>
                <li>
                  <strong>Strategy:</strong> Don't follow sequence 1 to 10 as
                  criss-crossing wastes time.
                </li>
                <li>
                  <strong>Time Management:</strong> Obstacles 3 and 5 take
                  maximum time, plan accordingly.
                </li>
                <li>
                  <strong>Approach:</strong> Have a definite plan and choose the
                  correct sequence.
                </li>
                <li>
                  <strong>Composure:</strong> Remain composed when attempting
                  high-rise obstacles or ditches.
                </li>
              </ol>
            </div>

            <h2 className="text-xl font-semibold text-gray-900 mt-8 mb-4">
              Preparation Tips
            </h2>
            <div className="bg-cyan-50 p-6 rounded-lg mb-8">
              <ul className="list-disc pl-5 space-y-3 text-cyan-800">
                <li>
                  <strong>Physical conditioning:</strong> Focus on building
                  upper body strength, core stability, and cardiovascular
                  endurance.
                </li>
                <li>
                  <strong>Specific exercises:</strong> Practice pull-ups,
                  push-ups, rope climbing, and running to build relevant
                  strength.
                </li>
                <li>
                  <strong>Balance training:</strong> Work on your balance
                  through activities like walking on narrow surfaces or yoga.
                </li>
                <li>
                  <strong>Agility drills:</strong> Practice quick direction
                  changes, ladder drills, and zig-zag running patterns.
                </li>
                <li>
                  <strong>Obstacle practice:</strong> If possible, find obstacle
                  courses or playgrounds where you can practice similar
                  challenges.
                </li>
                <li>
                  <strong>Mental preparation:</strong> Develop strategies for
                  approaching different types of obstacles efficiently.
                </li>
                <li>
                  <strong>Endurance building:</strong> Train for sustained
                  effort over the full course, not just individual obstacles.
                </li>
                <li>
                  <strong>Recovery techniques:</strong> Learn to control your
                  breathing and recover quickly between obstacles.
                </li>
              </ul>
            </div>

            <h2 className="text-xl font-semibold text-gray-900 mt-8 mb-4">
              Common Mistakes to Avoid
            </h2>
            <div className="bg-red-50 p-6 rounded-lg mb-8">
              <ul className="list-disc pl-5 space-y-2 text-red-800">
                <li>Starting too fast and burning out quickly</li>
                <li>Attempting obstacles without proper technique</li>
                <li>Giving up after a single failed attempt</li>
                <li>
                  Focusing too much on speed at the expense of proper completion
                </li>
                <li>Not pacing yourself throughout the course</li>
                <li>
                  Becoming visibly frustrated or negative when facing
                  difficulties
                </li>
                <li>
                  Ignoring safety guidelines in pursuit of faster completion
                </li>
                <li>
                  Failing to adapt your approach after an unsuccessful attempt
                </li>
              </ul>
            </div>

            <div className="bg-gray-100 p-6 rounded-lg mt-10">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                Key Takeaways
              </h2>
              <p className="mb-4">
                The Individual Obstacles task is a comprehensive assessment of
                your physical abilities, mental toughness, and problem-solving
                skills under pressure. Success in this task demonstrates your
                potential to overcome physical challenges independently—a
                crucial quality for military officers.
              </p>
              <p>
                Remember that assessors are looking not just at your physical
                performance, but at your approach, determination, and attitude
                when facing challenges. Even if you struggle with certain
                obstacles, showing perseverance and a positive attitude can make
                a strong impression.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
