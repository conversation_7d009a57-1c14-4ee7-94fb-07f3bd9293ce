# Loading Components

This directory contains components for implementing loading indicators throughout the application.

## Components

### TopProgressBar

A global progress bar that appears at the top of the viewport during navigation. It uses NProgress to show a smooth loading indicator.

### LoadingButton

A button component that shows a loading spinner when clicked. It can be used for actions that take time to complete, such as form submissions.

### SimpleButton

A button component without loading indicators, used for simple navigation or quick actions.

## Usage

### Global Navigation Loading

The TopProgressBar component is automatically included in the root layout and will show a loading indicator for all navigation events:

```tsx
// In app/layout.tsx
import TopProgressBar from "@/components/TopProgressBar";

export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        <TopProgressBar />
        {children}
      </body>
    </html>
  );
}
```

### Simple Navigation Buttons

Use SimpleButton for basic navigation without loading indicators:

```tsx
import SimpleButton from "@/components/SimpleButton";
import { useRouter } from "next/navigation";

function MyComponent() {
  const router = useRouter();

  return (
    <SimpleButton onClick={() => router.push("/some-path")} className="...">
      Go to Page
    </SimpleButton>
  );
}
```

### Buttons with Loading State

Use LoadingButton for actions that require loading indicators:

```tsx
import LoadingButton from "@/components/LoadingButton";

function MyComponent() {
  const handleSubmit = async () => {
    // Perform some async action
    await submitForm();
  };

  return (
    <LoadingButton
      onClick={handleSubmit}
      className="..."
      loadingText="Submitting..."
    >
      Submit Form
    </LoadingButton>
  );
}
```

## Implementation Details

### Global Loading

The global loading indicator is implemented using the TopProgressBar component, which uses NProgress to show a progress bar at the top of the viewport. It automatically shows during navigation events and completes when the navigation is done.

### Component-Specific Loading

For component-specific loading states (like form submissions), the loading state is managed by the `LoadingContext` provider, which is included in the root layout. It automatically resets the loading state when:

1. The pathname changes (navigation completes)
2. A fallback timeout of 800ms elapses

This ensures that the loading state is always cleared, even if there are errors during form submission or other actions.
