import { doc, getDoc, setDoc, updateDoc, collection } from "firebase/firestore";
import { db } from "@/lib/firebase";

// System settings document reference - make sure db is initialized
const getSystemSettingsRef = () => {
  if (!db) {
    throw new Error("Firestore not initialized");
  }
  return doc(db, "system", "settings");
};

/**
 * Directly checks if a user is an admin by querying Firestore
 * @param userId The user ID to check
 * @returns Promise resolving to a boolean indicating admin status
 */
export const checkAdminStatus = async (userId: string): Promise<boolean> => {
  try {
    const userRef = doc(db, "users", userId);
    const userDoc = await getDoc(userRef);

    if (!userDoc.exists()) {
      return false;
    }

    const userData = userDoc.data();

    // Check for admin status in various formats
    if (userData.isAdmin === true) {
      return true;
    }

    if (userData.isAdmin === "true") {
      return true;
    }

    if (
      userData.roles &&
      Array.isArray(userData.roles) &&
      userData.roles.includes("admin")
    ) {
      return true;
    }

    return false;
  } catch (error) {
    console.error("Error checking admin status:", error);
    return false;
  }
};

/**
 * Checks if a user can access AI analysis features
 * @param userId The user ID to check
 * @returns Promise resolving to a boolean indicating AI access status
 */
export const checkAIAccessStatus = async (userId: string): Promise<boolean> => {
  try {
    const userRef = doc(db, "users", userId);
    const userDoc = await getDoc(userRef);

    if (!userDoc.exists()) {
      return false;
    }

    const userData = userDoc.data();

    // Admin users can always access AI analysis
    if (await checkAdminStatus(userId)) {
      return true;
    }

    // Check for canAccessAIAnalysis flag
    if (userData.canAccessAIAnalysis === true) {
      return true;
    }

    if (userData.canAccessAIAnalysis === "true") {
      return true;
    }

    return false;
  } catch (error) {
    console.error("Error checking AI access status:", error);
    return false;
  }
};

/**
 * Grants AI analysis access to a user
 * @param userId The user ID to grant access to
 * @returns Promise resolving to a boolean indicating success
 */
export const grantAIAccess = async (userId: string): Promise<boolean> => {
  try {
    const userRef = doc(db, "users", userId);
    const userDoc = await getDoc(userRef);

    if (!userDoc.exists()) {
      return false;
    }

    // Use setDoc with merge option to ensure we don't overwrite other fields
    await setDoc(
      userRef,
      {
        canAccessAIAnalysis: true,
        updatedAt: new Date(),
      },
      { merge: true }
    );

    return true;
  } catch (error) {
    console.error("Error granting AI access:", error);
    return false;
  }
};

/**
 * Revokes AI analysis access from a user
 * @param userId The user ID to revoke access from
 * @returns Promise resolving to a boolean indicating success
 */
export const revokeAIAccess = async (userId: string): Promise<boolean> => {
  try {
    // Check if this user is an admin
    const isAdmin = await checkAdminStatus(userId);
    if (isAdmin) {
      // Cannot revoke AI access from an admin
      console.error("Cannot revoke AI access from an admin user");
      throw new Error("Cannot revoke AI access from an admin user");
    }

    const userRef = doc(db, "users", userId);
    const userDoc = await getDoc(userRef);

    if (!userDoc.exists()) {
      return false;
    }

    // Use setDoc with merge option to ensure we don't overwrite other fields
    await setDoc(
      userRef,
      {
        canAccessAIAnalysis: false,
        updatedAt: new Date(),
      },
      { merge: true }
    );

    return true;
  } catch (error) {
    console.error("Error revoking AI access:", error);
    throw error;
  }
};

/**
 * Sets a user as an admin in Firestore
 * @param userId The user ID to set as admin
 * @returns Promise resolving to a boolean indicating success
 */
export const setUserAsAdmin = async (userId: string): Promise<boolean> => {
  try {
    const userRef = doc(db, "users", userId);
    const userDoc = await getDoc(userRef);

    if (!userDoc.exists()) {
      return false;
    }

    // Use setDoc with merge option to ensure we don't overwrite other fields
    await setDoc(
      userRef,
      {
        isAdmin: true,
        roles: ["user", "admin"],
        canAccessAIAnalysis: true, // Admins automatically get AI access
        updatedAt: new Date(),
      },
      { merge: true }
    );

    return true;
  } catch (error) {
    console.error("Error setting user as admin:", error);
    return false;
  }
};

/**
 * Removes admin status from a user in Firestore
 * @param userId The user ID to remove admin status from
 * @returns Promise resolving to a boolean indicating success
 */
export const removeUserAdmin = async (userId: string): Promise<boolean> => {
  try {
    const userRef = doc(db, "users", userId);
    const userDoc = await getDoc(userRef);

    if (!userDoc.exists()) {
      return false;
    }

    // Use setDoc with merge option to ensure we don't overwrite other fields
    await setDoc(
      userRef,
      {
        isAdmin: false,
        roles: ["user"],
        // Note: We don't change canAccessAIAnalysis here, as that's managed separately
        updatedAt: new Date(),
      },
      { merge: true }
    );

    return true;
  } catch (error) {
    console.error("Error removing admin status:", error);
    throw error;
  }
};

/**
 * Gets the raw user data from Firestore
 * @param userId The user ID to get data for
 * @returns Promise resolving to the user data or null
 */
export const getUserData = async (userId: string): Promise<any | null> => {
  try {
    const userRef = doc(db, "users", userId);
    const userDoc = await getDoc(userRef);

    if (!userDoc.exists()) {
      return null;
    }

    return userDoc.data();
  } catch (error) {
    console.error("Error getting user data:", error);
    return null;
  }
};
