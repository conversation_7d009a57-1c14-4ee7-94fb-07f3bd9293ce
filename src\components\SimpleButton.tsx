"use client";

import { useRouter } from "next/navigation";
import Link from "next/link";

interface SimpleButtonProps {
  onClick?: () => void;
  href?: string;
  className?: string;
  children: React.ReactNode;
  disabled?: boolean;
}

/**
 * A simple button component without loading indicators
 * Use this for simple navigation or actions that don't need loading indicators
 */
export default function SimpleButton({
  onClick,
  href,
  className = "",
  children,
  disabled = false,
}: SimpleButtonProps) {
  const router = useRouter();

  // If href is provided, render a Link component for better client-side navigation
  if (href) {
    return (
      <Link
        href={href}
        className={`${className} ${
          disabled ? "opacity-50 cursor-not-allowed" : ""
        }`}
      >
        {children}
      </Link>
    );
  }

  // Otherwise, render a regular button
  const handleClick = () => {
    if (disabled) return;
    if (onClick) {
      onClick();
    }
  };

  return (
    <button onClick={handleClick} disabled={disabled} className={className}>
      {children}
    </button>
  );
}
