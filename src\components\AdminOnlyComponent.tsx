"use client";

import React from "react";
import { useRoleCheck } from "@/utils/authUtils";
import { useAuth } from "@/context/GoogleAuthContext";

interface AdminOnlyComponentProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

/**
 * A wrapper component that only renders its children if the current user has admin role.
 * Otherwise, it renders the fallback content or nothing.
 */
export default function AdminOnlyComponent({
  children,
  fallback = null,
}: AdminOnlyComponentProps) {
  const { user, loading } = useAuth();
  const { isAdmin } = useRoleCheck();

  // If still loading auth state, don't render anything
  if (loading) return null;

  // If user is not logged in or not an admin, render fallback
  if (!user || !isAdmin) {
    return <>{fallback}</>;
  }

  // User is an admin, render the children
  return <>{children}</>;
}
