"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useAuth } from "@/context/GoogleAuthContext";
import { getTopics } from "@/services/forumService";
import { ForumTopic } from "@/types/forum";
import TopicCard from "@/components/forum/TopicCard";
import {
  ChatBubbleLeftRightIcon,
  PlusIcon,
  ArrowPathIcon,
  AcademicCapIcon,
  BookOpenIcon,
  UserGroupIcon,
  QuestionMarkCircleIcon,
  FlagIcon,
} from "@heroicons/react/24/outline";
import { DocumentData, QueryDocumentSnapshot } from "firebase/firestore";

// Main forum categories
const forumCategories = [
  {
    id: "all",
    name: "All Topics",
    description: "View all discussion topics",
    icon: ChatBubbleLeftRightIcon,
    color: "bg-indigo-100 border-indigo-300 text-indigo-800",
  },
  {
    id: "to",
    name: "Technical Officer",
    description: "Discussions about TO tests and preparation",
    icon: AcademicCapIcon,
    color: "bg-blue-100 border-blue-300 text-blue-800",
  },
  {
    id: "gto",
    name: "Group Testing Officer",
    description: "Discussions about GTO tests and preparation",
    icon: UserGroupIcon,
    color: "bg-green-100 border-green-300 text-green-800",
  },
  {
    id: "io",
    name: "Interviewing Officer",
    description: "Discussions about IO tests and preparation",
    icon: BookOpenIcon,
    color: "bg-amber-100 border-amber-300 text-amber-800",
  },
  {
    id: "bc",
    name: "Board Conference",
    description: "Discussions about BC preparation",
    icon: UserGroupIcon,
    color: "bg-purple-100 border-purple-300 text-purple-800",
  },
  {
    id: "misc",
    name: "Miscellaneous",
    description: "Other topics related to military selection",
    icon: ChatBubbleLeftRightIcon,
    color: "bg-gray-100 border-gray-300 text-gray-800",
  },
];

// TO subcategories - these will be used as tags
const toSubcategories = [
  { id: "srt", name: "SRT", color: "bg-blue-50 text-blue-700" },
  { id: "wat", name: "WAT", color: "bg-green-50 text-green-700" },
  { id: "tat", name: "TAT", color: "bg-red-50 text-red-700" },
  { id: "sdt", name: "SDT", color: "bg-amber-50 text-amber-700" },
  { id: "piq", name: "PIQ", color: "bg-purple-50 text-purple-700" },
];

// GTO subcategories - these will be used as tags
const gtoSubcategories = [
  { id: "gd", name: "GD", color: "bg-blue-50 text-blue-700" },
  { id: "gpe", name: "GPE", color: "bg-green-50 text-green-700" },
  {
    id: "lecturette",
    name: "Lecturette",
    color: "bg-indigo-50 text-indigo-700",
  },
  { id: "pgt", name: "PGT", color: "bg-purple-50 text-purple-700" },
  { id: "snake-race", name: "Snake Race", color: "bg-red-50 text-red-700" },
  { id: "hgt", name: "HGT", color: "bg-amber-50 text-amber-700" },
  {
    id: "individual-obstacles",
    name: "Individual Obstacles",
    color: "bg-cyan-50 text-cyan-700",
  },
  { id: "command-task", name: "CT", color: "bg-emerald-50 text-emerald-700" },
  { id: "fgt", name: "FGT", color: "bg-pink-50 text-pink-700" },
];

export default function ForumPage() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [topics, setTopics] = useState<ForumTopic[]>([]);
  const [lastVisible, setLastVisible] =
    useState<QueryDocumentSnapshot<DocumentData> | null>(null);
  const [hasMore, setHasMore] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedSubcategory, setSelectedSubcategory] = useState<string | null>(
    null
  );

  // Load topics when the component mounts
  useEffect(() => {
    if (!loading) {
      if (!user) {
        // Redirect to login if not authenticated
        router.push("/login?redirect=/forum");
      } else {
        loadTopics();
      }
    }
  }, [user, loading, selectedCategory, selectedSubcategory]);

  // Load topics from Firestore
  const loadTopics = async () => {
    try {
      setIsLoading(true);

      // Determine which tag to filter by
      let filterTag: string | undefined = undefined;

      if (selectedCategory === "to" && selectedSubcategory) {
        // If TO is selected with a subcategory, filter by the subcategory
        filterTag = selectedSubcategory;
      } else if (selectedCategory && selectedCategory !== "all") {
        // For other main categories, filter by the category
        filterTag = selectedCategory;
      }

      const result = await getTopics(1, null, filterTag);
      setTopics(result.topics);
      setLastVisible(result.lastVisible);
      setHasMore(result.hasMore);
    } catch (error) {
      console.error("Error loading topics:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Load more topics
  const loadMoreTopics = async () => {
    if (!lastVisible || isLoading) return;

    try {
      setIsLoading(true);

      // Determine which tag to filter by (same logic as loadTopics)
      let filterTag: string | undefined = undefined;

      if (selectedCategory === "to" && selectedSubcategory) {
        filterTag = selectedSubcategory;
      } else if (selectedCategory && selectedCategory !== "all") {
        filterTag = selectedCategory;
      }

      const result = await getTopics(1, lastVisible, filterTag);
      setTopics([...topics, ...result.topics]);
      setLastVisible(result.lastVisible);
      setHasMore(result.hasMore);
    } catch (error) {
      console.error("Error loading more topics:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle category selection
  const handleCategorySelect = (categoryId: string) => {
    // If selecting the same category, deselect it
    if (categoryId === selectedCategory) {
      setSelectedCategory(null);
      setSelectedSubcategory(null);
    } else {
      setSelectedCategory(categoryId);
      // Reset subcategory when changing categories
      setSelectedSubcategory(null);
    }
  };

  // Handle subcategory selection
  const handleSubcategorySelect = (subcategoryId: string) => {
    setSelectedSubcategory(
      subcategoryId === selectedSubcategory ? null : subcategoryId
    );
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="mx-auto max-w-7xl px-6">
          <div className="text-center">
            <h1 className="text-3xl font-bold tracking-tight text-gray-900">
              Loading...
            </h1>
          </div>
        </div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect in useEffect
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="mx-auto max-w-7xl px-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold tracking-tight text-gray-900">
              Discussion Forum
            </h1>
            <p className="mt-2 text-lg text-gray-600">
              Join the conversation about military selection and preparation
            </p>
          </div>
          <div className="mt-4 md:mt-0">
            <Link
              href="/forum/create"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <PlusIcon className="h-5 w-5 mr-2" />
              New Topic
            </Link>
          </div>
        </div>

        {/* Main Category Tabs */}
        <div className="mb-4 border-b border-gray-200">
          <div className="flex overflow-x-auto pb-1 hide-scrollbar">
            {forumCategories.map((category) => (
              <button
                key={category.id}
                onClick={() =>
                  handleCategorySelect(
                    category.id === "all" ? null : category.id
                  )
                }
                className={`flex items-center whitespace-nowrap px-4 py-2 border-b-2 font-medium text-sm mr-4 ${
                  (category.id === "all" && !selectedCategory) ||
                  selectedCategory === category.id
                    ? "border-indigo-500 text-indigo-600"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
              >
                <category.icon
                  className={`h-5 w-5 mr-2 ${
                    (category.id === "all" && !selectedCategory) ||
                    selectedCategory === category.id
                      ? "text-indigo-500"
                      : "text-gray-400"
                  }`}
                />
                {category.name}
              </button>
            ))}
          </div>
        </div>

        {/* TO Subcategory Tabs - only shown when TO is selected */}
        {selectedCategory === "to" && (
          <div className="mb-6 bg-blue-50 rounded-lg p-2">
            <div className="flex overflow-x-auto pb-1 hide-scrollbar">
              <div className="text-sm text-blue-700 font-medium px-2 py-1 flex items-center">
                <AcademicCapIcon className="h-4 w-4 mr-1" />
                TO Tests:
              </div>
              {toSubcategories.map((subcat) => (
                <button
                  key={subcat.id}
                  onClick={() => handleSubcategorySelect(subcat.id)}
                  className={`whitespace-nowrap px-3 py-1 rounded-full text-xs font-medium mx-1 ${
                    selectedSubcategory === subcat.id
                      ? `${subcat.color} border border-blue-300`
                      : "bg-white text-blue-600 border border-blue-200 hover:bg-blue-50"
                  }`}
                >
                  {subcat.name}
                </button>
              ))}
            </div>
          </div>
        )}

        {/* GTO Subcategory Tabs - only shown when GTO is selected */}
        {selectedCategory === "gto" && (
          <div className="mb-6 bg-green-50 rounded-lg p-2">
            <div className="flex overflow-x-auto pb-1 hide-scrollbar">
              <div className="text-sm text-green-700 font-medium px-2 py-1 flex items-center">
                <UserGroupIcon className="h-4 w-4 mr-1" />
                GTO Tasks:
              </div>
              {gtoSubcategories.map((subcat) => (
                <button
                  key={subcat.id}
                  onClick={() => handleSubcategorySelect(subcat.id)}
                  className={`whitespace-nowrap px-3 py-1 rounded-full text-xs font-medium mx-1 ${
                    selectedSubcategory === subcat.id
                      ? `${subcat.color} border border-green-300`
                      : "bg-white text-green-600 border border-green-200 hover:bg-green-50"
                  }`}
                >
                  {subcat.name}
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Filter indicator */}
        {(selectedCategory || selectedSubcategory) && (
          <div className="flex flex-col sm:flex-row sm:items-center mb-4 bg-white p-3 rounded-lg shadow-sm">
            <span className="text-gray-700">
              Filtering by:{" "}
              <span className="font-semibold">
                {selectedCategory === "to" && selectedSubcategory
                  ? `${forumCategories.find((c) => c.id === "to")?.name} - ${
                      toSubcategories.find((s) => s.id === selectedSubcategory)
                        ?.name
                    }`
                  : selectedCategory === "gto" && selectedSubcategory
                  ? `${forumCategories.find((c) => c.id === "gto")?.name} - ${
                      gtoSubcategories.find((s) => s.id === selectedSubcategory)
                        ?.name
                    }`
                  : forumCategories.find((c) => c.id === selectedCategory)
                      ?.name}
              </span>
            </span>
            <button
              onClick={() => {
                setSelectedCategory(null);
                setSelectedSubcategory(null);
              }}
              className="mt-2 sm:mt-0 sm:ml-3 text-indigo-600 hover:text-indigo-800 text-sm font-medium flex items-center"
            >
              <ArrowPathIcon className="h-4 w-4 mr-1" />
              Reset Filter
            </button>
          </div>
        )}

        {/* Topics list */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="divide-y divide-gray-200">
            {topics.length === 0 && !isLoading ? (
              <div className="p-6 text-center">
                <p className="text-gray-500">
                  {selectedCategory
                    ? "No topics found in this category. Be the first to start a discussion!"
                    : "No topics found. Be the first to start a discussion!"}
                </p>
                <Link
                  href="/forum/create"
                  className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200"
                >
                  <PlusIcon className="h-5 w-5 mr-2" />
                  Create New Topic
                </Link>
              </div>
            ) : (
              topics.map((topic) => <TopicCard key={topic.id} topic={topic} />)
            )}

            {isLoading && (
              <div className="p-6 text-center">
                <p className="text-gray-500">Loading topics...</p>
              </div>
            )}
          </div>
        </div>

        {/* Load more button */}
        {hasMore && (
          <div className="mt-6 text-center">
            <button
              onClick={loadMoreTopics}
              disabled={isLoading}
              className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              {isLoading ? (
                "Loading..."
              ) : (
                <>
                  <ArrowPathIcon className="h-5 w-5 mr-2" />
                  Load More
                </>
              )}
            </button>
          </div>
        )}

        {/* Community guidelines link */}
        <div className="mt-8 text-center">
          <Link
            href="/forum/guidelines"
            className="text-indigo-600 hover:text-indigo-800 text-sm font-medium"
          >
            View Community Guidelines
          </Link>
        </div>
      </div>
    </div>
  );
}
