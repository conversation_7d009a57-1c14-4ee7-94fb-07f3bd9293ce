"use client";

import React from "react";
import { PlusCircleIcon } from "@heroicons/react/24/outline";
import AdminOnlyComponent from "./AdminOnlyComponent";

interface SetManagementButtonProps {
  onClick: () => void;
  testType: "TAT" | "WAT" | "SRT";
  className?: string;
}

/**
 * A button component for adding new sets, only visible to admin users.
 */
export default function SetManagementButton({
  onClick,
  testType,
  className = "",
}: SetManagementButtonProps) {
  // Get the appropriate color based on test type
  const getButtonColor = () => {
    switch (testType) {
      case "TAT":
        return "bg-red-600 hover:bg-red-700 focus:ring-red-500";
      case "WAT":
        return "bg-green-600 hover:bg-green-700 focus:ring-green-500";
      case "SRT":
        return "bg-blue-600 hover:bg-blue-700 focus:ring-blue-500";
      default:
        return "bg-indigo-600 hover:bg-indigo-700 focus:ring-indigo-500";
    }
  };

  const buttonColor = getButtonColor();

  return (
    <AdminOnlyComponent>
      <button
        onClick={onClick}
        className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white ${buttonColor} focus:outline-none focus:ring-2 focus:ring-offset-2 ${className}`}
      >
        <PlusCircleIcon className="h-5 w-5 mr-2" />
        Add New {testType} Set
      </button>
    </AdminOnlyComponent>
  );
}
