"use client";

import {
  ArrowLeftIcon,
  MegaphoneIcon,
  ClockIcon,
  CheckIcon,
  ArrowPathIcon,
  MicrophoneIcon,
  StopIcon,
  PlayIcon,
} from "@heroicons/react/24/outline";
import React, { useState, useEffect, useRef, useCallback } from "react";
import NavigationButton from "@/components/NavigationButton";
import SimpleButton from "@/components/SimpleButton";
import { lecturetteTopics, LecturetteTopic } from "@/data/lecturette-topics";
import AudioRecordingManager from "@/components/AudioRecordingManager";
import { AudioRecording } from "@/utils/audioRecordingUtils";

export default function LecturettePage() {
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedTopics, setSelectedTopics] = useState<LecturetteTopic[]>([]);
  const [selectedTopic, setSelectedTopic] = useState<LecturetteTopic | null>(
    null
  );
  const [stage, setStage] = useState<
    "selection" | "preparation" | "speaking" | "completed" | "results"
  >("selection");
  const [timeLeft, setTimeLeft] = useState(180); // 3 minutes in seconds
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const [notes, setNotes] = useState("");

  // Audio recording states
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [recordings, setRecordings] = useState<Record<string, AudioRecording>>(
    {}
  );
  const [audioStream, setAudioStream] = useState<MediaStream | null>(null);
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(
    null
  );
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentPlayingKey, setCurrentPlayingKey] = useState<string | null>(
    null
  );

  // Audio recording refs
  const audioChunksRef = useRef<BlobPart[]>([]);
  const currentRecordingTimeRef = useRef<number>(0);
  const startTimeRef = useRef<number>(0);
  const audioPlayerRef = useRef<HTMLAudioElement | null>(null);

  // Function to stop recording - defined with useCallback to avoid dependency issues
  const stopRecording = useCallback(() => {
    if (mediaRecorder && isRecording) {
      // Calculate final duration before stopping anything
      const finalDuration = Math.floor(
        (Date.now() - startTimeRef.current) / 1000
      );
      currentRecordingTimeRef.current = finalDuration;

      // Stop the recorder - this will trigger the onstop event
      mediaRecorder.stop();

      // Setting isRecording to false will trigger the useEffect cleanup
      setIsRecording(false);

      // Stop and release microphone
      if (audioStream) {
        audioStream.getTracks().forEach((track) => track.stop());
      }
    }
  }, [mediaRecorder, isRecording, audioStream]);

  // Generate 3-4 random topics when a category is selected
  useEffect(() => {
    if (selectedCategory) {
      const category = lecturetteTopics.find(
        (cat) => cat.name === selectedCategory
      );
      if (category) {
        // Shuffle and pick 3-4 random topics
        const shuffled = [...category.topics].sort(() => 0.5 - Math.random());
        setSelectedTopics(shuffled.slice(0, 4));
      }
    }
  }, [selectedCategory]);

  // Timer functionality for preparation stage only
  useEffect(() => {
    if (stage === "preparation" && timeLeft > 0) {
      timerRef.current = setInterval(() => {
        setTimeLeft((prev) => {
          if (prev <= 1) {
            clearInterval(timerRef.current as NodeJS.Timeout);
            // Move to speaking stage when timer ends
            setStage("speaking");
            return 180; // Reset timer for speaking stage
          }
          return prev - 1;
        });
      }, 1000);
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [stage, timeLeft]);

  // Separate timer for speaking stage - only starts when recording begins
  useEffect(() => {
    if (stage === "speaking" && isRecording && timeLeft > 0) {
      timerRef.current = setInterval(() => {
        setTimeLeft((prev) => {
          if (prev <= 1) {
            clearInterval(timerRef.current as NodeJS.Timeout);
            stopRecording(); // Stop recording if it's still going
            setStage("completed");
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [stage, isRecording, timeLeft, stopRecording]);

  // Recording timer functionality
  useEffect(() => {
    if (isRecording) {
      // Set the start time when recording begins
      startTimeRef.current = Date.now();

      // Start a timer to update the recording time display
      const recordingTimer = setInterval(() => {
        const elapsedSeconds = Math.floor(
          (Date.now() - startTimeRef.current) / 1000
        );
        setRecordingTime(elapsedSeconds);
      }, 1000);

      // Clean up the timer when recording stops
      return () => {
        clearInterval(recordingTimer);
        setRecordingTime(0);
      };
    }
  }, [isRecording]);

  // Function to start recording
  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      setAudioStream(stream);

      const recorder = new MediaRecorder(stream);
      setMediaRecorder(recorder);

      audioChunksRef.current = [];

      recorder.ondataavailable = (e) => {
        audioChunksRef.current.push(e.data);
      };

      recorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, {
          type: "audio/webm",
        });
        const audioUrl = URL.createObjectURL(audioBlob);

        // Save recording with the final recording time
        const finalDuration = currentRecordingTimeRef.current;

        // Use the topic title as the recording key
        const recordingKey = selectedTopic ? selectedTopic.title : "unknown";

        setRecordings((prev) => ({
          ...prev,
          [recordingKey]: {
            url: audioUrl,
            duration: finalDuration,
            blob: audioBlob,
          },
        }));
      };

      // Reset the timer to 3 minutes when starting recording
      setTimeLeft(180);

      recorder.start();
      setIsRecording(true);
    } catch (error) {
      console.error("Error accessing microphone:", error);
      alert("Could not access your microphone. Please check your permissions.");
    }
  };

  // Function to play recording
  const playRecording = (recordingKey: string) => {
    // If already playing this recording, stop it
    if (isPlaying && currentPlayingKey === recordingKey) {
      stopPlayback();
      return;
    }

    // If another recording is playing, stop it first
    if (isPlaying && audioPlayerRef.current) {
      stopPlayback();
    }

    // Create and play the new audio
    const audio = new Audio(recordings[recordingKey]?.url);
    audioPlayerRef.current = audio;

    // Set up event listeners
    audio.addEventListener("ended", () => {
      setIsPlaying(false);
      setCurrentPlayingKey(null);
      audioPlayerRef.current = null;
    });

    // Start playback
    audio
      .play()
      .then(() => {
        setIsPlaying(true);
        setCurrentPlayingKey(recordingKey);
      })
      .catch((error) => {
        console.error("Error playing audio:", error);
        setIsPlaying(false);
        setCurrentPlayingKey(null);
        audioPlayerRef.current = null;
      });
  };

  // Function to stop audio playback
  const stopPlayback = () => {
    if (audioPlayerRef.current) {
      audioPlayerRef.current.pause();
      audioPlayerRef.current.currentTime = 0;
      audioPlayerRef.current = null;
    }
    setIsPlaying(false);
    setCurrentPlayingKey(null);
  };

  // Format time as MM:SS
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs < 10 ? "0" : ""}${secs}`;
  };

  // Handle topic selection
  const handleTopicSelect = (topic: LecturetteTopic) => {
    setSelectedTopic(topic);
    setStage("preparation");
    setTimeLeft(180); // 3 minutes for preparation
  };

  // Start speaking phase
  const handleStartSpeaking = () => {
    setStage("speaking");
    setTimeLeft(180); // 3 minutes for speaking
  };

  // Reset everything
  const handleReset = () => {
    setSelectedCategory(null);
    setSelectedTopics([]);
    setSelectedTopic(null);
    setStage("selection");
    setTimeLeft(180);
    setNotes("");
    setIsRecording(false);
    stopPlayback();
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
    if (audioStream) {
      audioStream.getTracks().forEach((track) => track.stop());
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="mx-auto max-w-4xl px-6">
        <NavigationButton
          href="/tests/gto"
          className="mb-8 inline-flex items-center text-gray-600 hover:text-gray-900"
        >
          <ArrowLeftIcon className="h-5 w-5 mr-2" />
          Back to GTO Test
        </NavigationButton>

        {/* Practice Section */}
        {stage === "selection" && !selectedCategory && (
          <div className="bg-white rounded-2xl shadow-sm p-8 border-t-4 border-indigo-500 mb-8">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-3">
                <div className="p-2 rounded-full bg-indigo-100">
                  <MegaphoneIcon className="h-8 w-8 text-indigo-600" />
                </div>
                <h1 className="text-3xl font-bold text-gray-900">
                  Lecturette Practice
                </h1>
              </div>

              {Object.keys(recordings).length > 0 && (
                <button
                  onClick={() => setStage("results")}
                  className="inline-flex items-center px-4 py-2 border border-indigo-300 text-indigo-700 bg-indigo-50 hover:bg-indigo-100 text-sm font-medium rounded-md"
                >
                  <PlayIcon className="h-5 w-5 mr-2" />
                  View Recordings ({Object.keys(recordings).length})
                </button>
              )}
            </div>

            <p className="text-lg text-gray-700 mb-6">
              Select a category to get 3-4 random topics. Choose one topic, then
              you&apos;ll have 3 minutes to prepare and 3 minutes to speak.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-8">
              {lecturetteTopics.map((category) => (
                <SimpleButton
                  key={category.name}
                  onClick={() => setSelectedCategory(category.name)}
                  className="bg-indigo-50 hover:bg-indigo-100 p-6 rounded-xl text-left transition duration-200 border-2 border-transparent hover:border-indigo-300"
                >
                  <div className="flex items-center mb-3">
                    <div className="p-2 rounded-full bg-indigo-100 mr-3">
                      <category.icon className="h-6 w-6 text-indigo-600" />
                    </div>
                    <h3 className="font-semibold text-lg text-gray-900">
                      {category.name}
                    </h3>
                  </div>
                  <p className="text-gray-600 text-sm">
                    {category.description}
                  </p>
                </SimpleButton>
              ))}
            </div>
          </div>
        )}

        {/* Topic Selection */}
        {stage === "selection" && selectedCategory && (
          <div className="bg-white rounded-2xl shadow-sm p-8 border-t-4 border-indigo-500 mb-8">
            <div className="flex justify-between items-center mb-6">
              <div className="flex items-center space-x-3">
                <div className="p-2 rounded-full bg-indigo-100">
                  <MegaphoneIcon className="h-8 w-8 text-indigo-600" />
                </div>
                <h1 className="text-3xl font-bold text-gray-900">
                  Select a Topic
                </h1>
              </div>
              <button
                onClick={handleReset}
                className="text-gray-500 hover:text-gray-700 flex items-center"
              >
                <ArrowPathIcon className="h-5 w-5 mr-1" />
                Change Category
              </button>
            </div>

            <p className="text-lg text-gray-700 mb-6">
              Choose one of the following topics for your lecturette.
              You&apos;ll have 3 minutes to prepare and 3 minutes to speak.
            </p>

            <div className="space-y-4 mt-6">
              {selectedTopics.map((topic, index) => (
                <SimpleButton
                  key={index}
                  onClick={() => handleTopicSelect(topic)}
                  className="w-full bg-indigo-50 hover:bg-indigo-100 p-6 rounded-xl text-left transition duration-200 border-2 border-transparent hover:border-indigo-300"
                >
                  <h3 className="font-semibold text-lg text-gray-900 mb-2">
                    {topic.title}
                  </h3>
                  <div className="text-gray-600">
                    <p className="font-medium text-sm mb-1">
                      Key points to consider:
                    </p>
                    <ul className="list-disc pl-5 space-y-1 text-sm">
                      {topic.keyPoints.map((point, i) => (
                        <li key={i}>{point}</li>
                      ))}
                    </ul>
                  </div>
                </SimpleButton>
              ))}
            </div>
          </div>
        )}

        {/* Preparation Stage */}
        {stage === "preparation" && selectedTopic && (
          <div className="bg-white rounded-2xl shadow-sm p-8 border-t-4 border-indigo-500 mb-8">
            <div className="flex justify-between items-center mb-6">
              <div className="flex items-center space-x-3">
                <div className="p-2 rounded-full bg-indigo-100">
                  <MegaphoneIcon className="h-8 w-8 text-indigo-600" />
                </div>
                <h1 className="text-2xl font-bold text-gray-900">
                  Preparation Time
                </h1>
              </div>
              <div className="text-lg font-medium text-red-600 bg-red-50 px-4 py-2 rounded-full flex items-center">
                <ClockIcon className="h-5 w-5 mr-2" />
                {formatTime(timeLeft)}
              </div>
            </div>

            <div className="bg-indigo-50 p-6 rounded-xl mb-6">
              <h2 className="text-xl font-semibold text-indigo-900 mb-2">
                {selectedTopic.title}
              </h2>
              <div className="text-indigo-800">
                <p className="font-medium text-sm mb-1">
                  Key points to consider:
                </p>
                <ul className="list-disc pl-5 space-y-1">
                  {selectedTopic.keyPoints.map((point: string, i: number) => (
                    <li key={i}>{point}</li>
                  ))}
                </ul>
              </div>
            </div>

            <div className="mb-6">
              <label
                htmlFor="notes"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Preparation Notes (optional)
              </label>
              <textarea
                id="notes"
                rows={6}
                className="w-full border border-gray-300 rounded-lg p-3 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="Jot down your key points here..."
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
              ></textarea>
            </div>

            <div className="flex justify-center">
              <SimpleButton
                onClick={handleStartSpeaking}
                className="bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-3 px-8 rounded-lg shadow transition duration-200"
              >
                I&apos;m Ready to Speak
              </SimpleButton>
            </div>
          </div>
        )}

        {/* Speaking Stage */}
        {stage === "speaking" && selectedTopic && (
          <div className="bg-white rounded-2xl shadow-sm p-8 border-t-4 border-indigo-500 mb-8">
            <div className="flex justify-between items-center mb-6">
              <div className="flex items-center space-x-3">
                <div className="p-2 rounded-full bg-indigo-100">
                  <MegaphoneIcon className="h-8 w-8 text-indigo-600" />
                </div>
                <h1 className="text-2xl font-bold text-gray-900">
                  Speaking Time
                </h1>
              </div>
              {isRecording && (
                <div className="text-lg font-medium text-red-600 bg-red-50 px-4 py-2 rounded-full flex items-center">
                  <ClockIcon className="h-5 w-5 mr-2" />
                  {formatTime(timeLeft)}
                </div>
              )}
            </div>

            <div className="bg-indigo-50 p-6 rounded-xl mb-6">
              <h2 className="text-xl font-semibold text-indigo-900 mb-2">
                {selectedTopic.title}
              </h2>
              <p className="text-indigo-700 italic">
                Speak clearly and confidently for 3 minutes on this topic.
              </p>
            </div>

            {notes && (
              <div className="mb-6 bg-gray-50 p-4 rounded-lg border border-gray-200">
                <h3 className="text-sm font-medium text-gray-700 mb-2">
                  Your Preparation Notes:
                </h3>
                <p className="text-gray-600 whitespace-pre-line">{notes}</p>
              </div>
            )}

            {/* Recording Controls */}
            <div className="mt-8 mb-6">
              {!isRecording && !recordings[selectedTopic.title] ? (
                <button
                  onClick={startRecording}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-base font-medium rounded-md text-white bg-red-600 hover:bg-red-700 shadow-sm"
                >
                  <MicrophoneIcon className="h-5 w-5 mr-2" />
                  Start Recording
                </button>
              ) : isRecording ? (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center text-red-600">
                      <span className="animate-pulse mr-2">●</span>
                      <span>
                        Recording...{" "}
                        {recordingTime > 0 ? formatTime(recordingTime) : "0:00"}
                      </span>
                    </div>
                    <p className="text-sm text-gray-500 italic">
                      Speak clearly and at a moderate pace
                    </p>
                  </div>
                  <button
                    onClick={stopRecording}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-base font-medium rounded-md text-white bg-gray-600 hover:bg-gray-700 shadow-sm"
                  >
                    <StopIcon className="h-5 w-5 mr-2" />
                    Stop Recording
                  </button>
                </div>
              ) : (
                <div className="space-y-4">
                  <button
                    onClick={() => playRecording(selectedTopic.title)}
                    className={`inline-flex items-center px-4 py-2 border text-base font-medium rounded-md ${
                      isPlaying && currentPlayingKey === selectedTopic.title
                        ? "border-red-300 text-red-700 bg-red-50 hover:bg-red-100"
                        : "border-indigo-300 text-indigo-700 bg-indigo-50 hover:bg-indigo-100"
                    }`}
                  >
                    {isPlaying && currentPlayingKey === selectedTopic.title ? (
                      <>
                        <StopIcon className="h-5 w-5 mr-2" />
                        Stop Playing
                      </>
                    ) : (
                      <>
                        <PlayIcon className="h-5 w-5 mr-2" />
                        Play Recording (
                        {formatTime(
                          recordings[selectedTopic.title]?.duration || 0
                        )}
                        )
                      </>
                    )}
                  </button>
                  <button
                    onClick={startRecording}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-base font-medium rounded-md text-white bg-red-600 hover:bg-red-700 shadow-sm"
                  >
                    <MicrophoneIcon className="h-5 w-5 mr-2" />
                    Record Again
                  </button>
                </div>
              )}
            </div>

            <div className="flex justify-center space-x-4 mt-6">
              <SimpleButton
                onClick={() => {
                  if (isRecording) {
                    stopRecording();
                  }
                  setStage("completed");
                }}
                className="bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-3 px-8 rounded-lg shadow transition duration-200"
              >
                Finish Early
              </SimpleButton>
            </div>
          </div>
        )}

        {/* Completed Stage */}
        {stage === "completed" && selectedTopic && (
          <div className="bg-white rounded-2xl shadow-sm p-8 border-t-4 border-green-500 mb-8">
            <div className="flex items-center space-x-3 mb-6">
              <div className="p-2 rounded-full bg-green-100">
                <CheckIcon className="h-8 w-8 text-green-600" />
              </div>
              <h1 className="text-2xl font-bold text-gray-900">
                Lecturette Completed
              </h1>
            </div>

            <div className="bg-green-50 p-6 rounded-xl mb-6 text-center">
              <p className="text-lg text-green-800 mb-2">
                Great job completing your lecturette on:
              </p>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                {selectedTopic.title}
              </h2>
              <p className="text-gray-600">
                Continue practicing with different topics to improve your public
                speaking skills.
              </p>
            </div>

            {/* Recording Playback */}
            {recordings[selectedTopic.title] && (
              <div className="mb-8 bg-gray-50 p-6 rounded-lg border border-gray-200">
                <h3 className="text-lg font-medium text-gray-800 mb-4">
                  Your Recording
                </h3>
                <div className="flex items-center">
                  <button
                    onClick={() => playRecording(selectedTopic.title)}
                    className={`inline-flex items-center px-4 py-2 border text-base font-medium rounded-md ${
                      isPlaying && currentPlayingKey === selectedTopic.title
                        ? "border-red-300 text-red-700 bg-red-50 hover:bg-red-100"
                        : "border-indigo-300 text-indigo-700 bg-indigo-50 hover:bg-indigo-100"
                    }`}
                  >
                    {isPlaying && currentPlayingKey === selectedTopic.title ? (
                      <>
                        <StopIcon className="h-5 w-5 mr-2" />
                        Stop Playing
                      </>
                    ) : (
                      <>
                        <PlayIcon className="h-5 w-5 mr-2" />
                        Play Recording (
                        {formatTime(
                          recordings[selectedTopic.title]?.duration || 0
                        )}
                        )
                      </>
                    )}
                  </button>
                </div>
              </div>
            )}

            {/* Audio Recording Manager for Download/Save */}
            {Object.keys(recordings).length > 0 && (
              <div className="mt-8">
                <AudioRecordingManager
                  recordings={recordings}
                  testType="LECTURETTE"
                  isPlaying={isPlaying}
                  currentPlayingKey={currentPlayingKey}
                  onPlay={playRecording}
                  onStop={stopPlayback}
                  className="bg-gray-50 rounded-xl p-6 border border-gray-200"
                />
              </div>
            )}

            <div className="flex justify-center space-x-4 mt-8">
              <SimpleButton
                onClick={handleReset}
                className="bg-white border border-gray-300 hover:bg-gray-50 text-gray-700 font-semibold py-3 px-6 rounded-lg shadow-sm transition duration-200"
              >
                Try Another Topic
              </SimpleButton>
              <SimpleButton
                onClick={() => {
                  setSelectedTopic(null);
                  setStage("selection");
                  setTimeLeft(180);
                  setNotes("");
                }}
                className="bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-3 px-6 rounded-lg shadow transition duration-200"
              >
                Choose from Same Category
              </SimpleButton>
            </div>
          </div>
        )}

        {/* Results Stage - Show all recordings */}
        {stage === "results" && (
          <div className="bg-white rounded-2xl shadow-sm p-8 border-t-4 border-indigo-500 mb-8">
            <div className="flex items-center space-x-3 mb-6">
              <div className="p-2 rounded-full bg-indigo-100">
                <MegaphoneIcon className="h-8 w-8 text-indigo-600" />
              </div>
              <h1 className="text-2xl font-bold text-gray-900">
                Your Lecturette Recordings
              </h1>
            </div>

            {Object.keys(recordings).length === 0 ? (
              <div className="bg-gray-50 p-6 rounded-lg text-center">
                <p className="text-gray-600">
                  You haven&apos;t made any recordings yet. Practice a
                  lecturette to create recordings.
                </p>
              </div>
            ) : (
              <div className="space-y-6">
                {Object.entries(recordings).map(([topic, recording]) => (
                  <div
                    key={topic}
                    className="bg-gray-50 p-6 rounded-lg border border-gray-200"
                  >
                    <h3 className="text-lg font-medium text-gray-800 mb-2">
                      {topic}
                    </h3>
                    <div className="flex items-center">
                      <button
                        onClick={() => playRecording(topic)}
                        className={`inline-flex items-center px-4 py-2 border text-base font-medium rounded-md ${
                          isPlaying && currentPlayingKey === topic
                            ? "border-red-300 text-red-700 bg-red-50 hover:bg-red-100"
                            : "border-indigo-300 text-indigo-700 bg-indigo-50 hover:bg-indigo-100"
                        }`}
                      >
                        {isPlaying && currentPlayingKey === topic ? (
                          <>
                            <StopIcon className="h-5 w-5 mr-2" />
                            Stop Playing
                          </>
                        ) : (
                          <>
                            <PlayIcon className="h-5 w-5 mr-2" />
                            Play Recording ({formatTime(recording.duration)})
                          </>
                        )}
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Audio Recording Manager */}
            <div className="mt-12">
              <AudioRecordingManager
                recordings={recordings}
                testType="LECTURETTE"
                isPlaying={isPlaying}
                currentPlayingKey={currentPlayingKey}
                onPlay={playRecording}
                onStop={stopPlayback}
                className="bg-gray-50 rounded-xl p-6 border border-gray-200"
              />
            </div>

            <div className="flex justify-center mt-8">
              <SimpleButton
                onClick={handleReset}
                className="bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-3 px-6 rounded-lg shadow transition duration-200"
              >
                Back to Practice
              </SimpleButton>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
