"use client";

import { useState, useEffect, useRef } from "react";
import {
  PlayIcon,
  PauseIcon,
  ArrowDownTrayIcon,
  TrashIcon,
  ClockIcon,
  FolderOpenIcon,
} from "@heroicons/react/24/outline";
import {
  StoredAudioRecording,
  getStoredRecordings,
  getStoredRecording,
  deleteStoredRecording,
  downloadAudioRecording,
  getStorageUsage,
  formatDuration,
  formatFileSize,
} from "@/utils/audioRecordingUtils";

export default function StoredRecordingsViewer() {
  const [recordings, setRecordings] = useState<StoredAudioRecording[]>([]);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentPlayingId, setCurrentPlayingId] = useState<string | null>(null);
  const [storageUsage, setStorageUsage] = useState({ totalSize: 0, recordingCount: 0 });
  const audioPlayerRef = useRef<HTMLAudioElement | null>(null);

  useEffect(() => {
    loadRecordings();
  }, []);

  const loadRecordings = () => {
    const storedRecordings = getStoredRecordings();
    setRecordings(storedRecordings.sort((a, b) => 
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    ));
    setStorageUsage(getStorageUsage());
  };

  const handlePlay = (recordingId: string) => {
    // If already playing this recording, pause it
    if (isPlaying && currentPlayingId === recordingId) {
      stopPlayback();
      return;
    }

    // If another recording is playing, stop it first
    if (isPlaying && audioPlayerRef.current) {
      stopPlayback();
    }

    // Get the recording and play it
    const recording = getStoredRecording(recordingId);
    if (!recording) {
      alert("Recording not found or corrupted");
      return;
    }

    const audio = new Audio(recording.url);
    audioPlayerRef.current = audio;

    audio.addEventListener("ended", () => {
      setIsPlaying(false);
      setCurrentPlayingId(null);
      audioPlayerRef.current = null;
    });

    audio.addEventListener("error", () => {
      alert("Error playing recording");
      setIsPlaying(false);
      setCurrentPlayingId(null);
      audioPlayerRef.current = null;
    });

    audio.play();
    setIsPlaying(true);
    setCurrentPlayingId(recordingId);
  };

  const stopPlayback = () => {
    if (audioPlayerRef.current) {
      audioPlayerRef.current.pause();
      audioPlayerRef.current.currentTime = 0;
      audioPlayerRef.current = null;
    }
    setIsPlaying(false);
    setCurrentPlayingId(null);
  };

  const handleDownload = (recordingId: string, filename: string) => {
    const recording = getStoredRecording(recordingId);
    if (!recording) {
      alert("Recording not found or corrupted");
      return;
    }

    try {
      downloadAudioRecording(recording, filename);
    } catch (error) {
      console.error("Download failed:", error);
      alert("Failed to download recording. Please try again.");
    }
  };

  const handleDelete = (recordingId: string) => {
    if (confirm("Are you sure you want to delete this recording? This action cannot be undone.")) {
      // Stop playback if this recording is currently playing
      if (currentPlayingId === recordingId) {
        stopPlayback();
      }

      const success = deleteStoredRecording(recordingId);
      if (success) {
        loadRecordings(); // Refresh the list
      } else {
        alert("Failed to delete recording. Please try again.");
      }
    }
  };

  const formatDate = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleDateString() + " " + date.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const groupRecordingsByTest = (recordings: StoredAudioRecording[]) => {
    return recordings.reduce((groups, recording) => {
      const testType = recording.testType;
      if (!groups[testType]) {
        groups[testType] = [];
      }
      groups[testType].push(recording);
      return groups;
    }, {} as Record<string, StoredAudioRecording[]>);
  };

  const groupedRecordings = groupRecordingsByTest(recordings);

  if (recordings.length === 0) {
    return (
      <div className="text-center py-12">
        <FolderOpenIcon className="h-16 w-16 mx-auto text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Stored Recordings</h3>
        <p className="text-gray-500 mb-4">
          You haven't saved any recordings to browser storage yet.
        </p>
        <p className="text-sm text-gray-400">
          Use the "Save to Browser Storage" button in test sections to store recordings.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Storage Usage Summary */}
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Your Audio Library</h3>
            <p className="text-sm text-gray-600">
              {storageUsage.recordingCount} recordings • {formatFileSize(storageUsage.totalSize)} total
            </p>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-blue-600">{storageUsage.recordingCount}</div>
            <div className="text-xs text-gray-500">recordings</div>
          </div>
        </div>
      </div>

      {/* Recordings by Test Type */}
      {Object.entries(groupedRecordings).map(([testType, testRecordings]) => (
        <div key={testType} className="bg-white border border-gray-200 rounded-lg overflow-hidden">
          <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
            <h4 className="text-md font-semibold text-gray-900 uppercase tracking-wide">
              {testType} ({testRecordings.length})
            </h4>
          </div>
          
          <div className="divide-y divide-gray-200">
            {testRecordings.map((recording) => (
              <div key={recording.id} className="p-4 hover:bg-gray-50 transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex-1 min-w-0">
                    <h5 className="text-sm font-medium text-gray-900 truncate">
                      {recording.questionId}
                    </h5>
                    <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                      <span className="flex items-center">
                        <ClockIcon className="h-3 w-3 mr-1" />
                        {formatDuration(recording.duration)}
                      </span>
                      <span>{formatFileSize(recording.size)}</span>
                      <span>{formatDate(recording.timestamp)}</span>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2 ml-4">
                    {/* Play/Pause Button */}
                    <button
                      onClick={() => handlePlay(recording.id)}
                      className="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-full transition-colors"
                      title={isPlaying && currentPlayingId === recording.id ? "Pause" : "Play"}
                    >
                      {isPlaying && currentPlayingId === recording.id ? (
                        <PauseIcon className="h-4 w-4" />
                      ) : (
                        <PlayIcon className="h-4 w-4" />
                      )}
                    </button>

                    {/* Download Button */}
                    <button
                      onClick={() => handleDownload(recording.id, recording.name)}
                      className="p-2 text-green-600 hover:text-green-800 hover:bg-green-50 rounded-full transition-colors"
                      title="Download Recording"
                    >
                      <ArrowDownTrayIcon className="h-4 w-4" />
                    </button>

                    {/* Delete Button */}
                    <button
                      onClick={() => handleDelete(recording.id)}
                      className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-full transition-colors"
                      title="Delete Recording"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      ))}

      {/* Storage Info */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg
              className="h-5 w-5 text-yellow-400"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-yellow-800">Storage Information</h3>
            <div className="mt-2 text-sm text-yellow-700">
              <p>
                Recordings are stored in your browser's local storage. They will persist across sessions
                but may be cleared if you clear your browser data or use incognito mode.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
