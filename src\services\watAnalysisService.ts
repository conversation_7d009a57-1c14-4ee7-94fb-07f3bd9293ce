/**
 * WAT Analysis Service
 * This service provides a specialized implementation for analyzing WAT responses
 * to avoid JSON parsing issues with the DeepSeek API
 */

import OpenAI from "openai";
import { AIAnalysisResult } from "./aiService";
// Import militaryTraitsForAI from aiService instead
import { militaryTraitsForAI } from "./aiService";

/**
 * Analyze WAT responses using DeepSeek API
 * This is a specialized implementation to handle WAT responses more reliably
 * @param responses Array of user responses to analyze
 * @param prompts Optional array of prompts/questions that elicited the responses
 * @returns Promise resolving to the AI analysis result
 */
/**
 * Evaluate the quality of association between a stimulus word and response
 * @param stimulus The stimulus word
 * @param response The response word/phrase
 * @returns "Good", "Moderate", or "Bad" based on the quality of association
 */
const evaluateWATAssociation = (
  stimulus: string,
  response: string
): "Good" | "Moderate" | "Bad" => {
  // Convert to lowercase for case-insensitive comparison
  const lowerStimulus = stimulus.toLowerCase().trim();
  const lowerResponse = response.toLowerCase().trim();

  // If response is empty, it's a bad association
  if (!lowerResponse) {
    return "Bad";
  }

  // Direct match is always good
  if (lowerStimulus === lowerResponse) {
    return "Good";
  }

  // Check for common word associations (this is a simplified implementation)
  // In a real system, this would use a more comprehensive database or API
  const commonAssociations: Record<string, string[]> = {
    // Military values
    courage: [
      "brave",
      "fearless",
      "valor",
      "hero",
      "bold",
      "daring",
      "strength",
      "bravery",
    ],
    loyalty: [
      "faithful",
      "devoted",
      "allegiance",
      "commitment",
      "dedicated",
      "true",
      "trust",
    ],
    discipline: [
      "order",
      "control",
      "training",
      "obedience",
      "strict",
      "regimen",
      "routine",
    ],
    integrity: [
      "honest",
      "moral",
      "ethics",
      "principle",
      "honor",
      "truth",
      "character",
    ],
    resilience: [
      "strong",
      "tough",
      "endurance",
      "perseverance",
      "grit",
      "bounce",
      "recover",
    ],
    teamwork: [
      "cooperation",
      "together",
      "collaboration",
      "unity",
      "group",
      "team",
      "support",
    ],
    honor: [
      "respect",
      "dignity",
      "pride",
      "esteem",
      "glory",
      "reputation",
      "integrity",
    ],
    duty: [
      "responsibility",
      "obligation",
      "task",
      "job",
      "mission",
      "service",
      "work",
    ],

    // Opposites are also valid associations
    cowardice: ["brave", "courage", "fear", "weak", "timid", "scared", "run"],
    disloyal: ["loyal", "betray", "unfaithful", "traitor", "trust", "faithful"],
    undisciplined: [
      "discipline",
      "chaotic",
      "unruly",
      "disobedient",
      "wild",
      "control",
    ],

    // Military concepts
    mission: [
      "objective",
      "goal",
      "task",
      "assignment",
      "duty",
      "purpose",
      "operation",
    ],
    command: [
      "order",
      "lead",
      "direct",
      "control",
      "authority",
      "leadership",
      "instruct",
    ],
    training: [
      "practice",
      "drill",
      "exercise",
      "prepare",
      "learn",
      "teach",
      "instruct",
    ],
    weapon: [
      "gun",
      "rifle",
      "pistol",
      "firearm",
      "defense",
      "protect",
      "attack",
    ],
    uniform: [
      "dress",
      "clothes",
      "attire",
      "outfit",
      "standard",
      "regulation",
      "military",
    ],
  };

  // Check if we have associations for this stimulus - these are strong associations
  if (commonAssociations[lowerStimulus]) {
    if (commonAssociations[lowerStimulus].includes(lowerResponse)) {
      return "Good";
    }
  }

  // Check for semantic relationships - these are moderate to good associations

  // 1. Contains the stimulus word or vice versa - strong relationship
  if (
    lowerResponse.includes(lowerStimulus) ||
    lowerStimulus.includes(lowerResponse)
  ) {
    return "Good";
  }

  // 2. Is a common antonym - strong relationship
  const antonyms: Record<string, string[]> = {
    good: ["bad", "evil", "poor", "negative"],
    strong: ["weak", "fragile", "feeble"],
    brave: ["coward", "afraid", "scared", "fearful"],
    loyal: ["disloyal", "unfaithful", "traitor"],
    disciplined: ["undisciplined", "chaotic", "unruly"],
    honest: ["dishonest", "liar", "deceitful"],
  };

  // Check both directions for antonyms
  if (
    antonyms[lowerStimulus] &&
    antonyms[lowerStimulus].includes(lowerResponse)
  ) {
    return "Good";
  }

  // Check if response is a key in antonyms and stimulus is in its values
  for (const [key, values] of Object.entries(antonyms)) {
    if (key === lowerResponse && values.includes(lowerStimulus)) {
      return "Good";
    }
  }

  // 3. Starts with same letter (alliteration) - moderate relationship
  if (lowerStimulus.charAt(0) === lowerResponse.charAt(0)) {
    return "Moderate";
  }

  // 4. Rhymes with stimulus (very simplified check) - moderate relationship
  if (lowerStimulus.length > 2 && lowerResponse.length > 2) {
    if (lowerStimulus.slice(-2) === lowerResponse.slice(-2)) {
      return "Moderate";
    }
  }

  // 5. Check for military context - if response is military-related, it's at least moderate
  const militaryTerms = [
    "army",
    "navy",
    "air force",
    "marine",
    "soldier",
    "officer",
    "command",
    "rank",
    "mission",
    "duty",
    "honor",
    "courage",
    "loyalty",
    "discipline",
    "service",
    "weapon",
    "uniform",
    "combat",
    "defense",
    "attack",
    "strategy",
    "tactical",
    "training",
    "drill",
    "order",
    "regiment",
    "battalion",
    "squad",
    "platoon",
    "company",
    "division",
    "corps",
    "brigade",
    "military",
    "war",
    "battle",
    "fight",
  ];

  if (militaryTerms.some((term) => lowerResponse.includes(term))) {
    return "Moderate";
  }

  // Check for clearly bad responses
  const clearlyBad =
    lowerResponse.length < 2 || // Too short
    lowerResponse.length > 30 || // Too long for a WAT response
    /^\d+$/.test(lowerResponse); // Just a number

  if (clearlyBad) {
    return "Bad";
  }

  // If we've reached here, it's not clearly good or bad, so mark as moderate
  return "Moderate";
};

export const analyzeWATResponsesWithDeepSeek = async (
  responses: string[],
  prompts?: string[]
): Promise<AIAnalysisResult> => {
  try {
    // Check if DeepSeek API key is configured
    const apiKey = process.env.NEXT_PUBLIC_DEEPSEEK_API_KEY;
    if (!apiKey || apiKey === "your-deepseek-api-key" || apiKey.trim() === "") {
      console.error("DeepSeek API key is not configured or is invalid");
      return generateWATFallbackAnalysis(responses, undefined, prompts);
    }

    // Filter out empty responses
    const validResponses = responses.filter(
      (response) => response && response.trim() !== ""
    );

    // If no valid responses, return a fallback analysis
    if (validResponses.length === 0) {
      return generateWATFallbackAnalysis(responses, undefined, prompts);
    }

    // Prepare the context for the API call
    let context = `Analyze the following responses from a military Word Association Test (WAT) to evaluate the candidate's military aptitude.\n\n`;

    if (prompts && prompts.length > 0) {
      context += "The responses are to these word prompts:\n\n";
      prompts.forEach((prompt, index) => {
        if (prompt) {
          context += `Word ${index + 1}: ${prompt}\n`;
        }
      });
      context += "\n";
    }

    context += "Responses:\n\n";
    validResponses.forEach((response, index) => {
      context += `Response ${index + 1}: ${response}\n\n`;
    });

    // Create the prompt for DeepSeek
    const prompt = `You are a military psychological assessment expert. Analyze the candidate's responses to a Word Association Test (WAT) to evaluate their military aptitude.

In a WAT, candidates are given stimulus words and asked to respond with the first word or short phrase that comes to mind. The goal is to assess their immediate associations, which can reveal psychological traits.

For military WAT analysis, consider:
1. Logical associations between stimulus and response
2. Positive vs. negative word choices
3. Military-relevant associations
4. Consistency in response patterns

Please provide your analysis in the following format:

OVERALL_SCORE: [A number between 0-100]

STRENGTHS:
- [Strength 1]
- [Strength 2]
- [Strength 3]

IMPROVEMENTS:
- [Improvement 1]
- [Improvement 2]
- [Improvement 3]

DETAILED_FEEDBACK:
[Provide a detailed paragraph about the candidate's military aptitude based on their responses]

RESPONSE_QUALITY:
[For each response, indicate if it's "Good", "Moderate", or "Bad" based on the quality of word association:
- "Good": Strong, direct, logical associations with the stimulus word
- "Moderate": Acceptable but weaker associations that could be improved
- "Bad": Poor or irrelevant associations that need significant improvement
Provide brief improvement suggestions for "Moderate" and "Bad" responses.]

Here is the candidate's information:
${context}

IMPORTANT: Follow the exact format specified above. Do not include any additional sections or formatting. Remember that WAT responses are typically single words or short phrases, so don't penalize responses for being brief - that's expected in this test.`;

    // Initialize OpenAI client with OpenRouter configuration
    const openai = new OpenAI({
      baseURL: "https://openrouter.ai/api/v1",
      apiKey: apiKey,
      dangerouslyAllowBrowser: true, // Required when running in browser environment
      defaultHeaders: {
        "HTTP-Referer":
          process.env.NEXT_PUBLIC_APP_URL || "https://balaramshiwakoti.com.np",
        "X-Title": "Military Aptitude Analysis",
      },
    });

    // Call the DeepSeek API via OpenRouter
    let analysisText;
    try {
      console.log("Sending request to DeepSeek API for WAT analysis...");
      const completion = await openai.chat.completions.create({
        model: "deepseek/deepseek-chat-v3-0324:free",
        messages: [
          {
            role: "user",
            content: prompt,
          },
        ],
        temperature: 0.1, // Very low temperature for more predictable, structured output
        max_tokens: 1500,
      });

      // Extract the response text
      analysisText = completion.choices[0]?.message?.content;
      console.log("Received response from DeepSeek API for WAT analysis");

      if (!analysisText) {
        console.error("No analysis content received from DeepSeek for WAT");
        return generateWATFallbackAnalysis(
          responses,
          "No content received from DeepSeek API",
          prompts
        );
      }
    } catch (apiError: any) {
      console.error("Error calling DeepSeek API for WAT:", apiError);
      return generateWATFallbackAnalysis(
        responses,
        `Error calling DeepSeek API: ${apiError.message || "Unknown error"}`,
        prompts
      );
    }

    // Parse the structured text response
    console.log("Raw DeepSeek WAT response:", analysisText);

    // Extract the overall score
    const overallScoreMatch = analysisText.match(/OVERALL_SCORE:\s*(\d+)/i);
    const overallScore = overallScoreMatch
      ? parseInt(overallScoreMatch[1])
      : 65;

    // Extract strengths
    const strengthsSection = analysisText.match(
      /STRENGTHS:([\s\S]*?)(?=IMPROVEMENTS:|$)/i
    );
    const strengths = strengthsSection
      ? strengthsSection[1]
          .split("\n")
          .filter((line) => line.trim().startsWith("-"))
          .map((line) => line.replace(/^-\s*/, "").trim())
      : ["Adaptability", "Communication", "Teamwork"];

    // Extract improvements
    const improvementsSection = analysisText.match(
      /IMPROVEMENTS:([\s\S]*?)(?=DETAILED_FEEDBACK:|$)/i
    );
    const improvements = improvementsSection
      ? improvementsSection[1]
          .split("\n")
          .filter((line) => line.trim().startsWith("-"))
          .map((line) => line.replace(/^-\s*/, "").trim())
      : ["Decision Making", "Problem Solving", "Resource Management"];

    // Extract detailed feedback
    const detailedFeedbackSection = analysisText.match(
      /DETAILED_FEEDBACK:([\s\S]*?)(?=RESPONSE_QUALITY:|$)/i
    );
    const detailedFeedback = detailedFeedbackSection
      ? detailedFeedbackSection[1].trim()
      : "Based on the responses, the candidate shows potential for military service but needs development in key areas.";

    // Extract response quality assessments
    const responseQualitySection = analysisText.match(
      /RESPONSE_QUALITY:([\s\S]*?)$/i
    );
    const responseQualityText = responseQualitySection
      ? responseQualitySection[1]
      : "";

    // Create response quality assessment object
    const responseQualityAssessment: {
      [key: number]: {
        quality: "Good" | "Moderate" | "Bad";
        response: string;
        improvementSuggestions?: string[];
      };
    } = {};

    // Try to parse response quality from the text
    validResponses.forEach((response, index) => {
      // Look for mentions of this response in the quality section
      const responseMatch = new RegExp(
        `Response\\s*${index + 1}[^\\n]*?(Good|Moderate|Bad)`,
        "i"
      ).exec(responseQualityText);
      // For WAT, responses are typically single words or short phrases
      // We should evaluate if there's a logical association between the stimulus word and response
      const stimulusWord = prompts && prompts[index] ? prompts[index] : "";

      // Determine the quality of the response
      let quality: "Good" | "Moderate" | "Bad" = "Moderate"; // Default to moderate

      // If we have a direct evaluation from the AI, use that
      if (responseMatch) {
        const aiQuality = responseMatch[1].toLowerCase();
        if (aiQuality === "good") quality = "Good";
        else if (aiQuality === "bad") quality = "Bad";
        else quality = "Moderate";

        console.log(`Response ${index + 1} evaluated by AI as ${quality}`);
      }
      // Otherwise, evaluate the association ourselves if we have both stimulus and response
      else if (stimulusWord && response) {
        try {
          quality = evaluateWATAssociation(stimulusWord, response);
          console.log(
            `Response ${
              index + 1
            }: "${response}" to "${stimulusWord}" evaluated as ${quality}`
          );
        } catch (error) {
          console.error("Error evaluating WAT association:", error);
          // Default to moderate if evaluation fails
          quality = "Moderate";
        }
      }

      // Create the assessment object
      responseQualityAssessment[index] = {
        quality: quality,
        response:
          response.substring(0, 50) + (response.length > 50 ? "..." : ""),
      };

      // Add improvement suggestions based on quality
      if (quality !== "Good") {
        // Try to extract specific suggestions from the text
        const suggestionMatch = new RegExp(
          `Response\\s*${
            index + 1
          }[^\\n]*?(Moderate|Bad)[^\\n]*?suggestions?:([^\\n]*)`,
          "i"
        ).exec(responseQualityText);

        if (suggestionMatch) {
          responseQualityAssessment[index].improvementSuggestions = [
            suggestionMatch[2].trim(),
          ];
        } else if (quality === "Bad") {
          responseQualityAssessment[index].improvementSuggestions = [
            "Consider a more relevant association with the stimulus word",
            "Try to provide a response that relates to military context",
            "Avoid unrelated or random associations",
          ];
        } else if (quality === "Moderate") {
          responseQualityAssessment[index].improvementSuggestions = [
            "Consider a stronger association with the stimulus word",
            "Military-specific terminology would improve this response",
          ];
        }
      }
    });

    // Create traits object based on strengths and improvements
    const traits: { [key: string]: { score: number; feedback: string } } = {};

    // Add traits based on strengths (high scores)
    strengths.forEach((strength) => {
      traits[strength] = {
        score: 75 + Math.floor(Math.random() * 15),
        feedback: `You demonstrate strong ${strength.toLowerCase()} in your responses.`,
      };
    });

    // Add traits based on improvements (lower scores)
    improvements.forEach((improvement) => {
      traits[improvement] = {
        score: 40 + Math.floor(Math.random() * 15),
        feedback: `You should focus on improving your ${improvement.toLowerCase()}.`,
      };
    });

    // Add any missing traits from our standard list
    militaryTraitsForAI.forEach((trait) => {
      if (!traits[trait.name]) {
        traits[trait.name] = {
          score: 50 + Math.floor(Math.random() * 10),
          feedback: "You show average potential in this area.",
        };
      }
    });

    // Construct the final analysis result
    const result: AIAnalysisResult = {
      overallScore,
      strengths,
      improvements,
      detailedFeedback,
      traits,
      responseQualityAssessment,
      isAIGenerated: true,
    };

    return result;
  } catch (error: any) {
    console.error("Error in WAT analysis:", error);
    return generateWATFallbackAnalysis(
      responses,
      `Error in WAT analysis: ${error.message || "Unknown error"}`,
      prompts
    );
  }
};

/**
 * Generate a fallback analysis for WAT responses
 * @param responses Array of user responses
 * @param errorMessage Optional error message to include in the feedback
 * @param prompts Optional array of prompts/questions that elicited the responses
 * @returns A basic analysis result
 */
const generateWATFallbackAnalysis = (
  responses: string[],
  errorMessage?: string,
  prompts?: string[]
): AIAnalysisResult => {
  // Filter valid responses
  const validResponses = responses.filter(
    (response) => response && response.trim() !== ""
  );

  // Create response quality assessment
  const responseQualityAssessment: {
    [key: number]: {
      quality: "Good" | "Moderate" | "Bad";
      response: string;
      improvementSuggestions?: string[];
    };
  } = {};

  // Add basic assessment for each response
  validResponses.forEach((response, index) => {
    // Get the stimulus word if available
    const stimulusWord = prompts && prompts[index] ? prompts[index] : "";

    // Evaluate the association
    let quality: "Good" | "Moderate" | "Bad" = "Moderate"; // Default to moderate

    if (stimulusWord && response) {
      try {
        quality = evaluateWATAssociation(stimulusWord, response);
        console.log(
          `Fallback - Response ${
            index + 1
          }: "${response}" to "${stimulusWord}" evaluated as ${quality}`
        );
      } catch (error) {
        console.error("Error in fallback WAT evaluation:", error);
        // Default to moderate if evaluation fails
        quality = "Moderate";
      }
    }

    responseQualityAssessment[index] = {
      quality: quality,
      response: response.substring(0, 50) + (response.length > 50 ? "..." : ""),
    };

    // Add improvement suggestions based on quality
    if (quality !== "Good") {
      if (quality === "Bad") {
        responseQualityAssessment[index].improvementSuggestions = [
          "Consider a more relevant association with the stimulus word",
          "Try to provide a response that relates to military context",
          "Avoid unrelated or random associations",
        ];
      } else if (quality === "Moderate") {
        responseQualityAssessment[index].improvementSuggestions = [
          "Consider a stronger association with the stimulus word",
          "Military-specific terminology would improve this response",
        ];
      }
    }
  });

  // Create traits
  const traits: { [key: string]: { score: number; feedback: string } } = {};
  militaryTraitsForAI.forEach((trait) => {
    traits[trait.name] = {
      score: 60,
      feedback: "Score estimated due to analysis limitations.",
    };
  });

  // Create the fallback analysis
  return {
    overallScore: 65,
    strengths: ["Adaptability", "Communication", "Teamwork"],
    improvements: ["Decision Making", "Problem Solving", "Resource Management"],
    detailedFeedback: errorMessage
      ? `Analysis could not be completed: ${errorMessage}. This is a fallback analysis.`
      : "This is a fallback analysis based on limited information.",
    traits,
    responseQualityAssessment,
    isAIGenerated: true,
  };
};
