"use client";

import { useRouter } from "next/navigation";

/**
 * Navigate to a new page with a loading button
 * This utility function is used to replace the global loading overlay
 * with localized loading states in buttons
 * 
 * @param router Next.js router instance
 * @param href The URL to navigate to
 * @param setLoading Function to set the loading state
 */
export function navigateWithLoading(
  router: ReturnType<typeof useRouter>,
  href: string,
  setLoading?: (loading: boolean) => void
) {
  // Set loading state if provided
  if (setLoading) {
    setLoading(true);
  }
  
  // Navigate to the specified href
  router.push(href);
}
