"use client";

import React, { useState } from "react";
import <PERSON> from "next/link";
import { TestResponse } from "@/services/responseService";
import {
  UserGroupIcon,
  DocumentTextIcon,
  ChatBubbleLeftRightIcon,
  UserIcon,
  ArrowLeftIcon,
  ClockIcon,
  TrashIcon,
} from "@heroicons/react/24/outline";

// Define the main sections
const mainSections = [
  {
    id: "to",
    name: "Technical Officer",
    icon: DocumentTextIcon,
    color: "blue",
    subsections: [
      { id: "srt", name: "Situation Reaction Test", color: "blue" },
      { id: "wat", name: "Word Association Test", color: "green" },
      { id: "tat", name: "Thematic Apperception Test", color: "red" },
      { id: "sdt", name: "Self Description Test", color: "amber" },
      {
        id: "piq",
        name: "Personal Information Questionnaire",
        color: "purple",
      },
    ],
  },
  {
    id: "gto",
    name: "Group Testing Officer",
    icon: UserGroupIcon,
    color: "green",
    subsections: [
      { id: "gd", name: "Group Discussion", color: "blue" },
      { id: "gpe", name: "Group Planning Exercise", color: "green" },
      { id: "pgt", name: "Progressive Group Task", color: "purple" },
      { id: "snake-race", name: "Snake Race", color: "red" },
      { id: "hgt", name: "Half Group Task", color: "amber" },
      { id: "lecturette", name: "Lecturette", color: "indigo" },
      {
        id: "individual-obstacles",
        name: "Individual Obstacles",
        color: "cyan",
      },
      { id: "command-task", name: "Command Task", color: "emerald" },
      { id: "fgt", name: "Final Group Task", color: "pink" },
    ],
  },
  {
    id: "io",
    name: "Interviewing Officer",
    icon: ChatBubbleLeftRightIcon,
    color: "indigo",
    subsections: [],
  },
  {
    id: "bc",
    name: "Board Conference",
    icon: UserIcon,
    color: "amber",
    subsections: [],
  },
];

// Format date from timestamp
const formatDate = (timestamp?: number) => {
  if (!timestamp) return "Unknown date";
  const date = new Date(timestamp);
  return date.toLocaleString();
};

interface ProfileResponseGridProps {
  responses: TestResponse[];
  onDeleteResponse: (
    response: TestResponse,
    navigationState?: NavigationState
  ) => void;
  isDeleting: boolean;
  initialNavigationState?: NavigationState;
}

interface NavigationState {
  currentView: "main" | "section" | "subsection" | "set";
  selectedSection: string | null;
  selectedSubsection: string | null;
  selectedSet: string | null;
}

export default function ProfileResponseGrid({
  responses,
  onDeleteResponse,
  isDeleting,
  initialNavigationState,
}: ProfileResponseGridProps) {
  // State for navigation
  const [currentView, setCurrentView] = useState<
    "main" | "section" | "subsection" | "set"
  >(initialNavigationState?.currentView || "main");
  const [selectedSection, setSelectedSection] = useState<string | null>(
    initialNavigationState?.selectedSection || null
  );
  const [selectedSubsection, setSelectedSubsection] = useState<string | null>(
    initialNavigationState?.selectedSubsection || null
  );
  const [selectedSet, setSelectedSet] = useState<string | null>(
    initialNavigationState?.selectedSet || null
  );

  // Group responses by test type and count them properly
  const groupedByTestType: Record<string, TestResponse[]> = {};

  // Add debug logging
  console.log(`Total responses received: ${responses.length}`);

  // Group responses by test type
  responses.forEach((response) => {
    const testType = response.testType;
    if (!groupedByTestType[testType]) {
      groupedByTestType[testType] = [];
    }
    groupedByTestType[testType].push(response);
  });

  // Log the grouped responses for debugging
  Object.keys(groupedByTestType).forEach((testType) => {
    console.log(`${testType}: ${groupedByTestType[testType].length} responses`);
  });

  // Group responses by set within a subsection
  const getResponsesBySet = (testType: string) => {
    const setResponses: Record<string, TestResponse[]> = {};

    // For TO and GTO subsections, we need to handle them differently
    const section = mainSections.find((s) => s.id === selectedSection);
    const isSubsectionOfMainSection = section?.subsections.some(
      (s) => s.id === testType
    );

    // Filter responses for the specific test type
    const filteredResponses = responses.filter(
      (response) => response.testType === testType
    );

    // Log for debugging
    console.log(
      `getResponsesBySet for ${testType}: found ${filteredResponses.length} responses`
    );
    console.log(`Is subsection of main section: ${isSubsectionOfMainSection}`);

    // Group by setName
    filteredResponses.forEach((response) => {
      const setName = response.setName;
      if (!setResponses[setName]) {
        setResponses[setName] = [];
      }
      setResponses[setName].push(response);
    });

    // If no responses found but this is a main section (TO/GTO), check if we need to look for responses in subsections
    if (
      filteredResponses.length === 0 &&
      section &&
      section.id === testType &&
      section.subsections.length > 0
    ) {
      console.log(`Checking subsections for ${testType}`);

      // Look for responses in all subsections
      section.subsections.forEach((subsection) => {
        const subsectionResponses = responses.filter(
          (response) => response.testType === subsection.id
        );

        console.log(
          `Found ${subsectionResponses.length} responses for subsection ${subsection.id}`
        );

        // Group these by setName too
        subsectionResponses.forEach((response) => {
          const setName = response.setName;
          if (!setResponses[setName]) {
            setResponses[setName] = [];
          }
          setResponses[setName].push(response);
        });
      });
    }

    // Log set counts for debugging
    Object.keys(setResponses).forEach((setName) => {
      console.log(
        `${testType} - ${setName}: ${setResponses[setName].length} responses`
      );
    });

    // Sort responses within each set by timestamp (newest first)
    Object.keys(setResponses).forEach((setName) => {
      setResponses[setName].sort(
        (a, b) => (b.timestamp || 0) - (a.timestamp || 0)
      );
    });

    return setResponses;
  };

  // Navigate to a section
  const navigateToSection = (sectionId: string) => {
    setSelectedSection(sectionId);
    setCurrentView("section");
  };

  // Navigate to a subsection
  const navigateToSubsection = (subsectionId: string) => {
    setSelectedSubsection(subsectionId);
    setCurrentView("subsection");
  };

  // Navigate to a set
  const navigateToSet = (setName: string) => {
    setSelectedSet(setName);
    setCurrentView("set");
  };

  // Go back to previous view
  const goBack = () => {
    if (currentView === "set") {
      setCurrentView("subsection");
      setSelectedSet(null);
    } else if (currentView === "subsection") {
      setCurrentView("section");
      setSelectedSubsection(null);
    } else if (currentView === "section") {
      setCurrentView("main");
      setSelectedSection(null);
    }
  };

  // Render the main grid view
  const renderMainGrid = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
      {mainSections.map((section) => (
        <div
          key={section.id}
          onClick={() => navigateToSection(section.id)}
          className={`bg-white rounded-xl p-4 sm:p-6 shadow-sm border-t-4 border-${section.color}-500 hover:shadow-md cursor-pointer transition-shadow`}
        >
          <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-3 mb-4">
            <div
              className={`p-2 rounded-full bg-${section.color}-100 mb-2 sm:mb-0 self-center sm:self-auto`}
            >
              <section.icon className={`h-6 w-6 text-${section.color}-600`} />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 text-center sm:text-left">
              {section.name}
            </h3>
          </div>
          <p className="text-sm text-gray-600 mb-4 text-center sm:text-left">
            {section.subsections.length > 0
              ? `${section.subsections.length} subsections available`
              : "View your responses"}
          </p>
          <div className="text-sm text-gray-500 text-center sm:text-left">
            {(() => {
              // For main sections like TO, we need to count responses for all its subsections
              let count = 0;

              if (section.subsections.length > 0) {
                // For TO and GTO, count responses from all subsections
                section.subsections.forEach((subsection) => {
                  const subsectionResponses = responses.filter(
                    (response) => response.testType === subsection.id
                  );
                  count += subsectionResponses.length;
                });
              } else {
                // For IO and BC, count responses directly
                const sectionResponses = responses.filter(
                  (response) => response.testType === section.id
                );
                count = sectionResponses.length;
              }

              // Log for debugging
              console.log(
                `Main section ${section.id}: ${count} total responses`
              );

              return count > 0
                ? `${count} responses recorded`
                : "No responses yet";
            })()}
          </div>
        </div>
      ))}
    </div>
  );

  // Render the section view with subsections
  const renderSectionView = () => {
    const section = mainSections.find((s) => s.id === selectedSection);
    if (!section) return null;

    return (
      <div>
        <button
          onClick={goBack}
          className="mb-6 inline-flex items-center text-gray-600 hover:text-gray-900"
        >
          <ArrowLeftIcon className="h-5 w-5 mr-2" />
          Back to Main View
        </button>

        <h2
          className={`text-2xl font-bold mb-6 text-${section.color}-700 text-center sm:text-left`}
        >
          {section.name}
        </h2>

        {section.subsections.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {section.subsections.map((subsection) => (
              <div
                key={subsection.id}
                onClick={() => navigateToSubsection(subsection.id)}
                className={`bg-white rounded-lg p-4 shadow-sm border-l-4 border-${subsection.color}-500 hover:shadow-md cursor-pointer transition-shadow`}
              >
                <h3 className="font-semibold text-gray-900 mb-2 text-center sm:text-left">
                  {subsection.name}
                </h3>
                <p className="text-sm text-gray-600 text-center sm:text-left">
                  {(() => {
                    // Count responses for this specific subsection
                    const subsectionResponses = responses.filter(
                      (response) => response.testType === subsection.id
                    );
                    const count = subsectionResponses.length;

                    // Log for debugging
                    console.log(`${subsection.id}: ${count} responses`);

                    return count > 0
                      ? `${count} responses recorded`
                      : "No responses yet";
                  })()}
                </p>
              </div>
            ))}
          </div>
        ) : (
          // For IO and BC that don't have subsections, show sets directly
          renderSubsectionView()
        )}
      </div>
    );
  };

  // Render the subsection view with sets
  const renderSubsectionView = () => {
    const section = mainSections.find((s) => s.id === selectedSection);
    const subsection = section?.subsections.find(
      (s) => s.id === selectedSubsection
    );
    const testType = selectedSubsection || selectedSection;

    if (!testType) return null;

    // Add extra debugging for this view
    console.log(`Rendering subsection view for ${testType}`);
    console.log(`Selected section: ${selectedSection}`);
    console.log(`Selected subsection: ${selectedSubsection}`);

    // Count responses for this specific test type
    const directResponses = responses.filter(
      (response) => response.testType === testType
    );
    console.log(`Direct responses for ${testType}: ${directResponses.length}`);

    const setResponses = getResponsesBySet(testType);
    const setNames = Object.keys(setResponses);

    return (
      <div>
        <button
          onClick={goBack}
          className="mb-6 inline-flex items-center text-gray-600 hover:text-gray-900"
        >
          <ArrowLeftIcon className="h-5 w-5 mr-2" />
          Back to {subsection ? section?.name : "Main View"}
        </button>

        <h2 className="text-2xl font-bold mb-2 text-gray-900 text-center sm:text-left">
          {subsection ? subsection.name : section?.name}
        </h2>
        <p className="text-gray-600 mb-6 text-center sm:text-left">
          Select a set to view your responses
        </p>

        {setNames.length > 0 ? (
          <div className="space-y-4">
            {setNames.map((setName) => (
              <div
                key={setName}
                onClick={() => navigateToSet(setName)}
                className="bg-white rounded-lg p-4 shadow-sm border border-gray-200 hover:shadow-md cursor-pointer transition-shadow"
              >
                <h3 className="font-semibold text-gray-900 mb-2 text-center sm:text-left">
                  Set: {setName}
                </h3>
                <div className="flex flex-col sm:flex-row sm:justify-between">
                  <p className="text-sm text-gray-600 text-center sm:text-left">
                    {setResponses[setName].length}{" "}
                    {setResponses[setName].length === 1
                      ? "attempt"
                      : "attempts"}
                  </p>
                  <p className="text-sm text-gray-500 text-center sm:text-left mt-1 sm:mt-0">
                    Last attempt:{" "}
                    {formatDate(setResponses[setName][0]?.timestamp)}
                  </p>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="bg-gray-50 rounded-lg p-8 text-center">
            <p className="text-gray-500">
              No responses recorded for this section yet.
            </p>
          </div>
        )}
      </div>
    );
  };

  // Render the set view with responses
  const renderSetView = () => {
    const section = mainSections.find((s) => s.id === selectedSection);
    const subsection = section?.subsections.find(
      (s) => s.id === selectedSubsection
    );
    const testType = selectedSubsection || selectedSection;

    if (!testType || !selectedSet) return null;

    const setResponses = getResponsesBySet(testType)[selectedSet] || [];

    return (
      <div>
        <button
          onClick={goBack}
          className="mb-6 inline-flex items-center text-gray-600 hover:text-gray-900"
        >
          <ArrowLeftIcon className="h-5 w-5 mr-2" />
          Back to {subsection ? subsection.name : section?.name}
        </button>

        <div className="mb-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 text-center sm:text-left">
              Set: {selectedSet}
            </h2>
            <p className="text-gray-600 text-center sm:text-left">
              {subsection ? subsection.name : section?.name}
            </p>
          </div>
        </div>

        <div className="space-y-4">
          {setResponses.map((response, index) => (
            <div
              key={response.timestamp || index}
              className="bg-white rounded-lg p-4 shadow-sm border border-gray-200"
            >
              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-3">
                <h3 className="font-semibold text-gray-900 text-center sm:text-left mb-2 sm:mb-0">
                  Attempt {index + 1}
                </h3>
                <div className="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-4">
                  <div className="flex items-center text-gray-500 text-sm">
                    <ClockIcon className="h-4 w-4 mr-1" />
                    {formatDate(response.timestamp)}
                  </div>
                  <button
                    onClick={() =>
                      onDeleteResponse(response, {
                        currentView,
                        selectedSection,
                        selectedSubsection,
                        selectedSet,
                      })
                    }
                    disabled={isDeleting}
                    className="text-red-600 hover:text-red-800 p-1"
                    title="Delete this response"
                  >
                    <TrashIcon className="h-5 w-5" />
                  </button>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-3 sm:space-y-0">
                <p className="text-sm text-gray-600 text-center sm:text-left">
                  Completed:{" "}
                  {
                    response.responses.filter((r) => r && r.trim() !== "")
                      .length
                  }{" "}
                  of {response.responses.length} questions
                </p>

                <div className="flex justify-center sm:justify-start">
                  <Link
                    href={`/tests/${
                      section?.id
                    }/${testType}/practice/${encodeURIComponent(
                      selectedSet
                    )}/results?timestamp=${response.timestamp}`}
                    className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200"
                  >
                    View Results
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Render the appropriate view based on current state
  const renderCurrentView = () => {
    switch (currentView) {
      case "main":
        return renderMainGrid();
      case "section":
        return renderSectionView();
      case "subsection":
        return renderSubsectionView();
      case "set":
        return renderSetView();
      default:
        return renderMainGrid();
    }
  };

  return (
    <div className="bg-white shadow overflow-hidden sm:rounded-lg">
      <div className="px-4 py-5 sm:px-6">
        <h3 className="text-lg leading-6 font-medium text-gray-900">
          Your Test Responses
        </h3>
        <p className="mt-1 max-w-2xl text-sm text-gray-500">
          View and manage your test responses by section
        </p>
      </div>
      <div className="border-t border-gray-200 px-4 py-5 sm:p-6">
        {renderCurrentView()}
      </div>
    </div>
  );
}
