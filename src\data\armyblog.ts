import {
  GlobeAltIcon,
  ShieldExclamationIcon,
  TrophyIcon,
  WrenchScrewdriverIcon,
} from "@heroicons/react/24/outline";

// Define content types
export type PeacekeepingContent = {
  mainText: string[];
  statistics: {
    title: string;
    items: string[];
  };
  conclusion: string;
};

export type DisasterContent = {
  mainText: string[];
  sections: {
    title: string;
    items: string[];
  }[];
  conclusion: string;
};

export type AwardsContent = {
  mainText: string[];
  award: {
    title: string;
    description: string[];
  };
  conclusion: string;
};

export type InfrastructureContent = {
  mainText: string[];
  projects: {
    title: string;
    items: string[];
  };
  additionalText: string;
  sections: {
    title: string;
    items: string[];
  }[];
  conclusion: string;
};

// Define image gallery type
export type ImageGallery = {
  main: string; // Main image (displayed as cover)
  additional: string[]; // Additional images for the gallery
  captions?: string[]; // Optional captions for images (index corresponds to images array)
};

// Define the section type
export type AchievementSection = {
  id: string;
  title: string;
  description: string;
  icon: any; // Using any for simplicity
  image: string; // For backward compatibility
  images?: ImageGallery; // New field for multiple images
  color: "blue" | "emerald" | "amber" | "purple";
  content:
    | PeacekeepingContent
    | DisasterContent
    | AwardsContent
    | InfrastructureContent;
};

// Define the achievement sections
// Original achievement sections data
const allAchievementSections = [
  {
    id: "2",
    title: "Military Training Excellence",
    description:
      "Nepal Army's advanced training programs and facilities that prepare soldiers for diverse challenges.",
    icon: GlobeAltIcon,
    image: "/images/military-hero.jpg", // For backward compatibility

    color: "blue",
    content: {
      mainText: [
        "The Nepal Army has developed world-class training facilities and programs to prepare its personnel for a wide range of military and humanitarian operations. From high-altitude mountain warfare to jungle operations and peacekeeping missions, the training regimen is comprehensive and rigorous.",
        "The Army's training philosophy emphasizes adaptability, resilience, and ethical conduct in all operations. This approach has earned the Nepal Army international recognition for the quality and discipline of its personnel.",
      ],
      statistics: {
        title: "Training Highlights",
        items: [
          "High-altitude warfare training up to 5,500 meters above sea level",
          "Jungle warfare school in Amlekhganj with international participation",
          "Counter-terrorism training facility in Kathmandu Valley",
          "Peacekeeping training center in Panchkhal recognized by the UN",
          "Mountain warfare school in Jomsom, Mustang district",
        ],
      },
      conclusion:
        "The Nepal Army continues to enhance its training capabilities through international partnerships and the adoption of modern training methodologies. These efforts ensure that Nepal's soldiers remain prepared for both traditional military roles and emerging security challenges.",
    },
  },
  {
    id: "3",
    title: "Global Peacekeeping Leadership",
    description:
      "Nepal's contributions to UN peacekeeping missions worldwide, becoming the largest troop-contributing nation.",
    icon: GlobeAltIcon,
    image: "/images/un-army.jpg",
    color: "blue",
    content: {
      mainText: [
        "In February 2024, Nepal achieved a remarkable milestone by becoming the largest troop-contributing nation to United Nations peacekeeping missions worldwide, surpassing Bangladesh. As of November 30, 2023, Nepal had deployed 6,247 military and police personnel to various conflict zones around the world.",
        "Nepal's peacekeeping journey began in 1958 when it sent five military observers to the United Nations Observer Group in Lebanon (UNOGIL). The first Nepali contingent, Purano Gorakh Battalion, was deployed to the United Nations Emergency Force (UNEF-II) in Egypt in 1974.",
      ],
      statistics: {
        title: "Key Peacekeeping Statistics",
        items: [
          "Total peacekeepers deployed since 1958: 149,890",
          "Current deployment: 6,247 personnel (as of November 2023)",
          "Female peacekeepers: 602 (10% of current deployment)",
          "UN missions served: 44 missions across the globe",
          "Fallen heroes: 73 Nepali peacekeepers have made the ultimate sacrifice",
        ],
      },
      conclusion:
        "Nepal Army personnel have served in senior positions including Force Commanders, Deputy Force Commanders, and Military Advisors. The Army also operates the Birendra Peace Operations Training Center in Panchkhal, Kavre, which provides training for regional and international peacekeeping operations.",
    },
  },
  {
    id: "4",
    title: "Disaster Response and Humanitarian Operations",
    description:
      "Nepal Army's exceptional capabilities in disaster response, including the 2015 earthquake and annual monsoon operations.",
    icon: ShieldExclamationIcon,
    image: "/images/military-hero.jpg",
    color: "emerald",
    content: {
      mainText: [
        'The Nepal Army has demonstrated exceptional capabilities in disaster response, most notably during the devastating 2015 earthquake that struck Nepal. The Army launched "Operation Sankatmochan," mobilizing approximately 66,069 personnel for search, rescue, and relief operations.',
      ],
      sections: [
        {
          title: "2015 Earthquake Response",
          items: [
            "Rescued over 1,800 people from rubble",
            "Evacuated more than 4,000 injured civilians",
            "Cleared roads and established emergency supply routes",
            "Distributed essential supplies to affected communities",
            "Coordinated with international rescue teams",
          ],
        },
        {
          title: "Flood and Landslide Response",
          items: [
            "Annual monsoon rescue operations",
            "Helicopter evacuations from remote areas",
            "Emergency medical assistance",
            "Construction of temporary shelters",
            "Distribution of relief materials",
          ],
        },
      ],
      conclusion:
        "The Nepal Army has developed specialized disaster response units and invested in modern equipment and training to enhance its capabilities. These include engineering corps, medical teams, and specialized search and rescue units that can be rapidly deployed during emergencies.",
    },
  },
  {
    id: "5",
    title: "International Recognition and Awards",
    description:
      "Recognition of Nepal Army's contributions to international peace and security, including the International Peace Prize Medal.",
    icon: TrophyIcon,
    image: "/images/army-medal.png",
    color: "amber",
    content: {
      mainText: [
        "The contributions of the Nepal Army to international peace and security have earned significant recognition worldwide. One of the most prestigious acknowledgments came when 11,332 Nepal Army personnel received the International Peace Prize Medal for their service in UN peacekeeping missions from 1958 to 1988.",
      ],
      award: {
        title: "International Peace Prize Medal",
        description: [
          "When the Nobel Peace Committee awarded the Nobel Peace Prize to UN Peacekeeping Forces in 1988, all personnel who served in UN missions between 1958 and 1988 became eligible for the International Peace Prize Medal. After years of effort, Nepal Army personnel finally received this recognition in 2018.",
          "Recipients included 555 Lieutenant Generals and officers and 10,777 personnel of different ranks, totaling 11,332 individuals who served in missions in Lebanon, Egypt, and other conflict zones.",
        ],
      },
      conclusion:
        "Nepal Army contingents regularly receive commendations and medals for their exceptional performance in UN missions, including the UN Medal, which is awarded to military personnel who complete at least 90 days of service in a UN peacekeeping mission.",
    },
  },
  {
    id: "6",
    title: "Infrastructure Development Projects",
    description:
      "Nepal Army's significant contributions to national infrastructure development, including strategic road construction projects.",
    icon: WrenchScrewdriverIcon,
    image: "/images/army-construction-1.jpg",
    color: "purple",
    content: {
      mainText: [
        "The Nepal Army has played a crucial role in developing national infrastructure, particularly in challenging terrains where civilian contractors face difficulties. The Army's Engineering Corps has been entrusted with numerous strategic road construction projects that are vital for national security and economic development.",
      ],
      projects: {
        title: "Major Road Construction Projects",
        items: [
          "Kathmandu-Terai Fast Track: A strategic 76.2 km expressway connecting Kathmandu to Nijgadh",
          "Kaligandaki Corridor: A 435 km north-south highway connecting China border to India border",
          "Karnali Corridor: A vital road network connecting remote western regions",
          "Midhill Highway: Contributing to sections of this major east-west highway",
          "Koshi Corridor: Connecting eastern hill districts to the Terai region",
        ],
      },
      additionalText:
        "According to recent reports, the Nepal Army is currently responsible for constructing approximately 25 roads across the country. While progress varies by project, the Army has demonstrated its capability to work in challenging geographical and weather conditions where civilian contractors often struggle.",
      sections: [
        {
          title: "Fast Track Project Highlights",
          items: [
            "Total length: 76.2 kilometers",
            "Estimated cost: NPR 175 billion",
            "Features: 87 bridges, 6 flyovers, 4 tunnels",
            "Will reduce travel time from Kathmandu to Terai from 5 hours to 1 hour",
            "Expected completion: 2025",
          ],
        },
        {
          title: "Engineering Capabilities",
          items: [
            "Tunnel construction expertise",
            "Bridge building in difficult terrain",
            "Disaster-resistant infrastructure design",
            "High-altitude construction techniques",
            "Monsoon-resistant road engineering",
          ],
        },
      ],
      conclusion:
        "While there have been some criticisms regarding the pace of certain projects, the Nepal Army has consistently demonstrated its commitment to completing these vital infrastructure projects despite numerous challenges including difficult terrain, adverse weather conditions, and resource constraints. These infrastructure projects are crucial for Nepal's economic development, national security, and improving connectivity to remote regions of the country.",
    },
  },
];

// Define pagination settings
export const ITEMS_PER_PAGE = 3; // Number of items to display per page

// Function to paginate the achievement sections
export function paginateAchievements(
  items: AchievementSection[],
  page: number
): {
  items: AchievementSection[];
  totalPages: number;
  currentPage: number;
} {
  // Create a copy of the items array to avoid modifying the original
  const sortedItems = [...items];

  // Sort items by ID in descending order (highest/newest ID first)
  sortedItems.sort((a, b) => {
    // Convert IDs to numbers for proper numerical sorting
    const idA = parseInt(a.id);
    const idB = parseInt(b.id);
    return idB - idA; // Descending order (newest first)
  });

  const startIndex = (page - 1) * ITEMS_PER_PAGE;
  const endIndex = startIndex + ITEMS_PER_PAGE;
  const paginatedItems = sortedItems.slice(startIndex, endIndex);
  const totalPages = Math.ceil(sortedItems.length / ITEMS_PER_PAGE);

  return {
    items: paginatedItems,
    totalPages,
    currentPage: page,
  };
}

// Export the original array for backward compatibility
export const achievementSections =
  allAchievementSections as AchievementSection[];
