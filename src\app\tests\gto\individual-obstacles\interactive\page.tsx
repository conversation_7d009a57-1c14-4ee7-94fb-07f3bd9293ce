"use client";

import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import {
  ArrowLeftIcon,
  BoltIcon,
  ClockIcon,
  CheckCircleIcon,
  ArrowRightIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import NavigationButton from "@/components/NavigationButton";
import {
  individualObstacles,
  recommendedSequence,
} from "@/data/individual-obstacles";

export default function IndividualObstaclesInteractivePage() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState<
    "intro" | "course" | "results"
  >("intro");
  const [selectedObstacles, setSelectedObstacles] = useState<number[]>([]);
  const [timeRemaining, setTimeRemaining] = useState(180); // 3 minutes in seconds
  const [isTimerRunning, setIsTimerRunning] = useState(false);
  const [score, setScore] = useState(0);
  const [feedback, setFeedback] = useState("");

  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // Start timer when course begins
  useEffect(() => {
    if (isTimerRunning && timeRemaining > 0) {
      timerRef.current = setInterval(() => {
        setTimeRemaining((prev) => {
          if (prev <= 1) {
            clearInterval(timerRef.current as NodeJS.Timeout);
            setIsTimerRunning(false);
            setCurrentStep("results");
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [isTimerRunning, timeRemaining]);

  // Format time as MM:SS
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  };

  // Handle obstacle selection
  const handleObstacleSelect = (id: number) => {
    if (selectedObstacles.includes(id)) {
      return; // Already selected
    }

    const obstacle = individualObstacles.find((o) => o.id === id);
    if (obstacle) {
      setSelectedObstacles([...selectedObstacles, id]);
      setScore((prevScore) => prevScore + obstacle.points);
    }
  };

  // Calculate final score and feedback
  const calculateResults = () => {
    let feedbackText = "";

    if (score >= 80) {
      feedbackText =
        "Excellent performance! You've demonstrated outstanding physical ability and strategic planning.";
    } else if (score >= 60) {
      feedbackText =
        "Good job! You've shown solid physical capabilities and decent strategic planning.";
    } else if (score >= 40) {
      feedbackText =
        "Fair attempt. You've completed several obstacles but could improve your strategy and efficiency.";
    } else {
      feedbackText =
        "You need more practice. Focus on improving your physical fitness and developing a better strategy.";
    }

    setFeedback(feedbackText);
  };

  // Start the course
  const startCourse = () => {
    setCurrentStep("course");
    setIsTimerRunning(true);
  };

  // End the course
  const endCourse = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
    setIsTimerRunning(false);
    calculateResults();
    setCurrentStep("results");
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="mx-auto max-w-4xl px-6">
        <NavigationButton
          href="/tests/gto/individual-obstacles"
          className="mb-8 inline-flex items-center text-gray-600 hover:text-gray-900"
        >
          <ArrowLeftIcon className="h-5 w-5 mr-2" />
          Back to Individual Obstacles
        </NavigationButton>

        {currentStep === "intro" && (
          <div className="bg-white rounded-2xl shadow-sm p-8 border-t-4 border-cyan-500">
            <div className="flex items-center space-x-3 mb-6">
              <div className="p-2 rounded-full bg-cyan-100">
                <BoltIcon className="h-8 w-8 text-cyan-600" />
              </div>
              <h1 className="text-3xl font-bold text-gray-900">
                Individual Obstacles Simulation
              </h1>
            </div>

            <div className="prose prose-lg max-w-none">
              <p className="text-lg text-gray-700 mb-6">
                This is a simulation of the Individual Obstacles course. You'll
                have 3 minutes to complete as many obstacles as possible,
                following a strategic sequence to maximize your score.
              </p>

              <div className="bg-cyan-50 p-6 rounded-lg mb-8">
                <h3 className="text-lg font-medium text-cyan-900 mb-4">
                  Instructions:
                </h3>
                <ol className="list-decimal pl-5 space-y-2 text-cyan-800">
                  <li>
                    You have 3 minutes (180 seconds) to complete the course
                  </li>
                  <li>
                    Select obstacles in a strategic order to maximize your score
                  </li>
                  <li>High-value obstacles are worth more points</li>
                  <li>You can repeat obstacles, but choose wisely</li>
                  <li>The recommended sequence is: 10-6-8-9-4-3-5-1-2-7-10</li>
                </ol>
              </div>

              <div className="flex justify-center mt-8">
                <button
                  onClick={startCourse}
                  className="bg-cyan-600 hover:bg-cyan-700 text-white font-bold py-3 px-6 rounded-lg shadow-md transition duration-200"
                >
                  Start Course
                </button>
              </div>
            </div>
          </div>
        )}

        {currentStep === "course" && (
          <div className="bg-white rounded-2xl shadow-sm p-8 border-t-4 border-cyan-500">
            <div className="flex justify-between items-center mb-6">
              <h1 className="text-2xl font-bold text-gray-900">
                Individual Obstacles Course
              </h1>
              <div className="flex items-center space-x-4">
                <div className="bg-cyan-100 text-cyan-800 px-4 py-2 rounded-lg flex items-center">
                  <ClockIcon className="h-5 w-5 mr-2" />
                  <span className="font-mono font-bold">
                    {formatTime(timeRemaining)}
                  </span>
                </div>
                <div className="bg-green-100 text-green-800 px-4 py-2 rounded-lg">
                  <span className="font-bold">Score: {score}</span>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-8">
              {individualObstacles.map((obstacle) => (
                <button
                  key={obstacle.id}
                  onClick={() => handleObstacleSelect(obstacle.id)}
                  className={`rounded-lg border-2 transition-all overflow-hidden ${
                    selectedObstacles.includes(obstacle.id)
                      ? "border-green-500"
                      : "border-gray-200 hover:border-cyan-300"
                  }`}
                  disabled={selectedObstacles.includes(obstacle.id)}
                >
                  <div className="relative h-24 w-full">
                    <Image
                      src={obstacle.image}
                      alt={`${obstacle.name} obstacle`}
                      fill
                      className="object-cover"
                    />
                    {selectedObstacles.includes(obstacle.id) && (
                      <div className="absolute inset-0 bg-green-500 bg-opacity-20 flex items-center justify-center">
                        <CheckCircleIcon className="h-8 w-8 text-white" />
                      </div>
                    )}
                  </div>
                  <div className="p-2 text-center">
                    <div className="text-sm font-medium">
                      {obstacle.id}. {obstacle.name}
                    </div>
                    <div className="bg-cyan-100 text-cyan-800 text-xs font-medium px-2 py-1 rounded mt-1">
                      {obstacle.points} pts
                    </div>
                  </div>
                </button>
              ))}
            </div>

            <div className="bg-gray-100 p-4 rounded-lg mb-6">
              <h3 className="font-medium mb-2">Recommended Sequence:</h3>
              <div className="flex flex-wrap gap-2">
                {recommendedSequence.map((id, index) => (
                  <div
                    key={index}
                    className={`px-3 py-1 rounded-full font-medium ${
                      selectedObstacles.includes(id)
                        ? "bg-green-200 text-green-800"
                        : "bg-cyan-200 text-cyan-800"
                    }`}
                  >
                    {id}
                  </div>
                ))}
              </div>
            </div>

            <div className="flex justify-center">
              <button
                onClick={endCourse}
                className="bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-6 rounded-lg shadow-md transition duration-200"
              >
                End Course
              </button>
            </div>
          </div>
        )}

        {currentStep === "results" && (
          <div className="bg-white rounded-2xl shadow-sm p-8 border-t-4 border-cyan-500">
            <div className="flex items-center space-x-3 mb-6">
              <div className="p-2 rounded-full bg-cyan-100">
                <CheckCircleIcon className="h-8 w-8 text-cyan-600" />
              </div>
              <h1 className="text-3xl font-bold text-gray-900">
                Course Results
              </h1>
            </div>

            <div className="prose prose-lg max-w-none">
              <div className="bg-gray-100 p-6 rounded-lg mb-8">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  Your Performance
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <p className="text-gray-700 mb-2">
                      <strong>Final Score:</strong> {score} points
                    </p>
                    <p className="text-gray-700 mb-2">
                      <strong>Time Used:</strong>{" "}
                      {formatTime(180 - timeRemaining)} of 03:00
                    </p>
                    <p className="text-gray-700 mb-2">
                      <strong>Obstacles Completed:</strong>{" "}
                      {selectedObstacles.length}
                    </p>
                  </div>
                  <div>
                    <h4 className="font-medium mb-2">Obstacles Completed:</h4>
                    <div className="flex flex-wrap gap-2">
                      {selectedObstacles.map((id, index) => (
                        <div
                          key={index}
                          className="bg-green-200 text-green-800 px-3 py-1 rounded-full font-medium"
                        >
                          {id}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-cyan-50 p-6 rounded-lg mb-8">
                <h3 className="text-xl font-semibold text-cyan-900 mb-4">
                  Feedback
                </h3>
                <p className="text-cyan-800">{feedback}</p>
              </div>

              <div className="flex justify-center space-x-4 mt-8">
                <button
                  onClick={() => {
                    setCurrentStep("intro");
                    setSelectedObstacles([]);
                    setScore(0);
                    setTimeRemaining(180);
                  }}
                  className="bg-cyan-600 hover:bg-cyan-700 text-white font-bold py-3 px-6 rounded-lg shadow-md transition duration-200"
                >
                  Try Again
                </button>
                <button
                  onClick={() => router.push("/tests/gto/individual-obstacles")}
                  className="bg-gray-600 hover:bg-gray-700 text-white font-bold py-3 px-6 rounded-lg shadow-md transition duration-200"
                >
                  Return to Guide
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
