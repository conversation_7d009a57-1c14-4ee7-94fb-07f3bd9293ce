"use client";

// Timeline data (ensure descriptions are concise)
const timelineData = [
  // ... (keep the same data as before) ...
  {
    year: "1559",
    title: "Gorkha Kingdom Founded",
    description:
      "<PERSON><PERSON><PERSON> establishes the Gorkha Kingdom, laying the foundation for the future Nepali state.",
    category: "formation",
  },
  {
    year: "1600",
    title: "Early Gorkhali Army",
    description:
      "Initial standing army organized under Gorkha rulers, primarily for local defense and expansion.",
    category: "formation",
  },
  {
    year: "1744",
    title: "<PERSON><PERSON><PERSON> Begins Unification",
    description:
      "King <PERSON><PERSON><PERSON> ascends the throne and formally organizes the army, launching the campaign to unify Nepal.",
    category: "formation",
  },
  {
    year: "1768-1769",
    title: "Conquest of Kathmandu Valley",
    description:
      "The Gorkhali Army conquers the Malla kingdoms of Kathmandu, Patan, and Bhadgaon, making Kathmandu the capital.",
    category: "war",
  },
  {
    year: "1775",
    title: "Unification Completed (Initial Phase)",
    description:
      "Following <PERSON><PERSON><PERSON>'s death, the unification campaign continued, consolidating much of modern Nepal.",
    category: "war",
  },
  {
    year: "1814-1816",
    title: "Anglo-Nepalese War",
    description:
      "Conflict with the British East India Company. Despite territorial losses (Sugauli Treaty), Gorkhali bravery was recognized.",
    category: "war",
  },
  {
    year: "1846",
    title: "Kot Massacre & Rana Regime",
    description:
      "<PERSON> <PERSON> consolidates power, establishing the hereditary Rana Prime Ministership; army loyalty shifts.",
    category: "political",
  },
  {
    year: "1914-1918",
    title: "World War I",
    description:
      "Nepali soldiers (Gurkhas) serve with distinction alongside the Allied forces, particularly with the British Army.",
    category: "international",
  },
  {
    year: "1939-1945",
    title: "World War II",
    description:
      "Gurkhas again play a crucial role for the Allies across multiple theatres, earning numerous gallantry awards.",
    category: "international",
  },
  {
    year: "1951",
    title: "End of Rana Regime & Royal Nepali Army",
    description:
      "Democracy introduced; King Tribhuvan takes control. The army is formally named the Royal Nepali Army.",
    category: "political",
  },
  {
    year: "1958",
    title: "First UN Peacekeeping Mission",
    description:
      "Nepal Army deploys observers to Lebanon (UNOGIL), beginning its long history of international peacekeeping.",
    category: "international",
  },
  {
    year: "1990",
    title: "People's Movement I (Jana Andolan I)",
    description:
      "Army maintains neutrality during protests leading to constitutional monarchy and multi-party democracy.",
    category: "political",
  },
  {
    year: "1996-2006",
    title: "Maoist Insurgency",
    description:
      "Decade-long civil war; the Army engages in counter-insurgency operations across the country.",
    category: "war",
  },
  {
    year: "2006",
    title: "People's Movement II (Jana Andolan II)",
    description:
      "Mass protests lead to the end of direct royal rule; Army supports the transition to a republic.",
    category: "political",
  },
  {
    year: "2008",
    title: "Republic Declared & Nepal Army Renamed",
    description:
      "Nepal becomes a Federal Democratic Republic. The 'Royal' prefix is dropped; becomes the 'Nepal Army'.",
    category: "political",
  },
  {
    year: "2009-2012",
    title: "Integration of Maoist Combatants",
    description:
      "Process begins to integrate former Maoist combatants into the Nepal Army under the peace agreement.",
    category: "formation",
  },
  {
    year: "2015",
    title: "Nepal Earthquake Response",
    description:
      "Army plays a critical role in search, rescue, relief distribution, and rebuilding efforts after the devastating earthquake.",
    category: "international",
  },
  {
    year: "2023-2025",
    title: "Digital Transformation",
    description:
      "Ongoing initiatives to modernize communication, cybersecurity, and command systems for enhanced operational efficiency.",
    category: "modernization",
  },
  {
    year: "Feb 2024",
    title: "Top UN Peacekeeping Contributor",
    description:
      "Nepal achieves the rank of the largest troop contributor to UN peacekeeping missions globally.",
    category: "international",
  },
];

// Category color mapping
const categoryColors = {
  formation: "bg-green-500",
  war: "bg-red-500",
  political: "bg-blue-500",
  international: "bg-amber-500",
  modernization: "bg-purple-500",
};

export default function ArmyHistoryPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="relative max-w-5xl mx-auto">
          {/* Vertical Center Line */}
          <div
            className="absolute left-1/2 top-0 bottom-0 w-0.5 bg-indigo-200 -translate-x-1/2"
            aria-hidden="true"
          ></div>

          {/* Timeline Items */}
          <div className="space-y-16">
            {timelineData.map((event, index) => (
              <div key={index} className="relative">
                {/* Center Circle - Always centered */}
                <div className="absolute left-1/2 top-0 -translate-x-1/2 z-20">
                  <div
                    className={`w-16 h-16 ${
                      categoryColors[
                        event.category as keyof typeof categoryColors
                      ] || "bg-gray-400"
                    } rounded-full flex items-center justify-center shadow-lg`}
                  >
                    <span className="text-white text-xs font-semibold text-center px-1">
                      {event.year}
                    </span>
                  </div>
                </div>

                {/* Content - Alternating sides on desktop */}
                <div
                  className={`flex flex-col items-center md:items-start md:flex-row ${
                    index % 2 === 0 ? "md:justify-end" : "md:justify-start"
                  }`}
                >
                  <div
                    className={`mt-20 md:mt-0 md:w-5/12 p-4 ${
                      index % 2 === 0 ? "md:mr-16" : "md:ml-16"
                    }`}
                  >
                    <div
                      className={`bg-white shadow-lg p-6 border border-gray-200 hover:shadow-xl transition-shadow duration-300 relative
                      rounded-xl md:rounded-lg
                      ${
                        index % 2 === 0
                          ? "md:rounded-tl-none"
                          : "md:rounded-tr-none"
                      }`}
                    >
                      {/* Triangular pointed corner - desktop only */}
                      <div
                        className={`absolute w-0 h-0 hidden md:block
                        ${
                          index % 2 === 0
                            ? "top-0 left-0 border-t-0 border-r-[15px] border-b-[15px] border-l-0 border-r-transparent border-b-white -translate-x-[15px]"
                            : "top-0 right-0 border-t-0 border-r-0 border-b-[15px] border-l-[15px] border-l-transparent border-b-white translate-x-[15px]"
                        }`}
                      ></div>

                      {/* Border for the triangle */}
                      <div
                        className={`absolute w-0 h-0 hidden md:block
                        ${
                          index % 2 === 0
                            ? "top-0 left-0 border-t-0 border-r-[16px] border-b-[16px] border-l-0 border-r-transparent border-b-gray-200 -translate-x-[16px] z-[-1]"
                            : "top-0 right-0 border-t-0 border-r-0 border-b-[16px] border-l-[16px] border-l-transparent border-b-gray-200 translate-x-[16px] z-[-1]"
                        }`}
                      ></div>
                      <h3 className="text-lg font-semibold text-gray-800 mb-2">
                        {event.title}
                      </h3>
                      <p className="text-sm text-gray-600 leading-relaxed">
                        {event.description}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Additional Information Section */}
        <div className="mt-24 bg-white rounded-xl shadow-lg p-8 border border-gray-200 max-w-5xl mx-auto">
          <h2 className="text-2xl font-semibold text-gray-900 mb-6">
            Legacy of Service and Global Contribution
          </h2>
          <div className="grid gap-6 md:grid-cols-2">
            <div className="bg-indigo-50 rounded-xl p-6 border border-indigo-100">
              <h3 className="text-lg font-medium text-indigo-800 mb-3">
                Gurkha Heritage
              </h3>
              <p className="text-sm text-indigo-700 leading-relaxed">
                The legendary discipline and valor of Nepali soldiers (Gurkhas)
                earned global renown, forming a core part of British and Indian
                army traditions, symbolized by their motto: &ldquo;Kaphar hunu
                bhanda marnu ramro&rdquo; (Better to die than be a coward).
              </p>
            </div>
            <div className="bg-amber-50 rounded-xl p-6 border border-amber-100">
              <h3 className="text-lg font-medium text-amber-800 mb-3">
                Peacekeeping Excellence
              </h3>
              <p className="text-sm text-amber-700 leading-relaxed">
                Since 1958, Nepal has been a major contributor to UN
                peacekeeping, deploying personnel to numerous conflict zones
                globally, consistently ranking among the top troop-contributing
                nations.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
