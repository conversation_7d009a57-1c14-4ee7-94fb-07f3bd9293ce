import { db } from "@/lib/firebase";
import {
  collection,
  doc,
  setDoc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  serverTimestamp,
  DocumentData,
} from "firebase/firestore";
import { TestType } from "./responseService";

// Interface for set data
export interface SetData {
  name: string;
  description: string;
  createdAt?: any;
  updatedAt?: any;
  createdBy?: string;
  isActive?: boolean;
  [key: string]: any; // Allow for additional properties based on test type
}

/**
 * Add a new set to Firestore
 * @param userId ID of the user adding the set
 * @param testType Type of test (TAT, WAT, SRT)
 * @param setData Data for the new set
 * @returns Promise resolving to the ID of the new set
 */
export const addSet = async (
  userId: string,
  testType: TestType,
  setData: SetData
): Promise<string> => {
  try {
    // Create a unique ID for the set document
    const setId = `${testType}_${setData.name
      .replace(/\s+/g, "_")
      .toLowerCase()}`;
    const setRef = doc(db, "sets", setId);

    // Check if a set with this ID already exists
    const existingSet = await getDoc(setRef);
    if (existingSet.exists()) {
      throw new Error(
        `A set with the name "${setData.name}" already exists for ${testType}`
      );
    }

    // Add timestamps and user info
    const setDataWithMeta = {
      ...setData,
      testType,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      createdBy: userId,
      isActive: true,
    };

    // Save the set to Firestore
    await setDoc(setRef, setDataWithMeta);

    return setId;
  } catch (error) {
    console.error(`Error adding ${testType} set:`, error);

    // Check if it's a permissions error
    if (
      error instanceof Error &&
      (error.message.includes("permission") ||
        error.message.includes("Missing or insufficient permissions"))
    ) {
      throw new Error(
        "Firebase permissions error: Your Firestore security rules need to be updated to allow access to the 'sets' collection. " +
          "Please contact the administrator to update the security rules."
      );
    }

    throw error;
  }
};

/**
 * Get all sets for a specific test type
 * @param testType Type of test (TAT, WAT, SRT)
 * @returns Promise resolving to an array of set data
 */
export const getSets = async (testType: TestType): Promise<SetData[]> => {
  try {
    const setsRef = collection(db, "sets");
    const q = query(
      setsRef,
      where("testType", "==", testType),
      where("isActive", "==", true),
      orderBy("createdAt", "desc")
    );

    const querySnapshot = await getDocs(q);

    return querySnapshot.docs.map((doc) => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        createdAt: data.createdAt?.toDate?.() || null,
        updatedAt: data.updatedAt?.toDate?.() || null,
      } as SetData;
    });
  } catch (error) {
    console.error(`Error getting ${testType} sets:`, error);

    // Check if it's a permissions error
    if (
      error instanceof Error &&
      (error.message.includes("permission") ||
        error.message.includes("Missing or insufficient permissions"))
    ) {
      // Return an empty array instead of throwing an error
      console.warn(
        "Firebase permissions error: Returning empty sets array. Security rules need to be updated."
      );
      return [];
    }

    throw error;
  }
};

/**
 * Get a specific set by ID
 * @param setId ID of the set to retrieve
 * @returns Promise resolving to the set data or null if not found
 */
export const getSetById = async (setId: string): Promise<SetData | null> => {
  try {
    const setRef = doc(db, "sets", setId);
    const setDoc = await getDoc(setRef);

    if (!setDoc.exists()) {
      return null;
    }

    const data = setDoc.data();
    return {
      id: setDoc.id,
      ...data,
      createdAt: data.createdAt?.toDate?.() || null,
      updatedAt: data.updatedAt?.toDate?.() || null,
    } as SetData;
  } catch (error) {
    console.error(`Error getting set ${setId}:`, error);
    throw error;
  }
};
