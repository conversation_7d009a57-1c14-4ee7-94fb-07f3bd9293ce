"use client";

import { useState, useEffect, useMemo } from "react";
import { useRouter } from "next/navigation";
import NavigationButton from "@/components/NavigationButton";
import LoadingButton from "@/components/LoadingButton";
import SimpleButton from "@/components/SimpleButton";
import { useLoading } from "@/context/LoadingContext";
import {
  ArrowLeftIcon,
  DocumentArrowDownIcon,
  LockClosedIcon,
} from "@heroicons/react/24/outline";
import { jsPDF } from "jspdf";
import "jspdf-autotable";
import PasswordProtectedDownload from "@/components/PasswordProtectedDownload";

interface FormData {
  // Header Section
  selectionBoard: string;
  batchNo: string;
  chestNo: string;
  loksewaRollNo: string;
  testType: string;

  // Personal Details
  name: string;
  fathersName: string;

  // Residence Details
  maximumResidence: {
    place: string;
    district: string;
    province: string;
    population: string;
    isDistrictHQ: boolean;
  };
  presentResidence: {
    place: string;
    district: string;
    province: string;
    population: string;
    isDistrictHQ: boolean;
  };
  permanentResidence: {
    place: string;
    district: string;
    province: string;
    population: string;
    isDistrictHQ: boolean;
  };

  // Demographic Details
  stateProvince: string;
  district: string;
  religion: string;
  category: string;
  motherTongue: string;
  dateOfBirth: string;
  maritalStatus: string;

  // Parent Details
  parentsAlive: boolean;
  motherDeathAge: string;
  fatherDeathAge: string;

  // Educational Record
  education: {
    qualification: string;
    institution: string;
    board: string;
    year: string;
    division: string;
    medium: string;
    boarderDayScholar: string;
    achievements: string;
  }[];

  // Physical Details
  age: string;
  ageMonths: string;
  heightFeet: string;
  heightInches: string;
  weight: string;

  // Occupation
  occupation: string;
  monthlyIncome: string;

  // NCC Training
  hasNCCTraining: boolean;
  nccDetails: {
    years: string;
    wing: string;
    division: string;
    certificate: string;
  };

  // Activities
  games: {
    name: string;
    period: string;
    represented: string;
    achievements: string;
  }[];
  hobbies: string;
  extracurricular: {
    activity: string;
    duration: string;
    achievements: string;
  }[];
  positionsHeld: string;

  // Commission Details
  commissionNature: string;
  serviceChoice: string[];
  chancesAvailed: string;

  // Family Details
  familyDetails: {
    father: {
      education: string;
      occupation: string;
      monthlyIncome: string;
    };
    mother: {
      education: string;
      occupation: string;
      monthlyIncome: string;
    };
    guardian: {
      education: string;
      occupation: string;
      monthlyIncome: string;
    };
    brothers: {
      education: string;
      occupation: string;
      monthlyIncome: string;
    }[];
    sisters: {
      education: string;
      occupation: string;
      monthlyIncome: string;
    }[];
  };
}

const generatePIQPDF = (formData: FormData) => {
  const doc = new jsPDF();
  const pageWidth = doc.internal.pageSize.width;
  const pageHeight = doc.internal.pageSize.height;
  const margin = 14;
  const maxY = pageHeight - margin;

  // Function to add watermark to each page
  const addWatermark = () => {
    // Save current state
    const currentFontSize = doc.getFontSize();
    const currentTextColor = doc.getTextColor();

    // Add a divider line
    doc.setDrawColor(200, 200, 200);
    doc.line(15, pageHeight - 15, pageWidth - 15, pageHeight - 15);

    // Add website name at the bottom right of the page
    doc.setFontSize(10);
    doc.setTextColor(150, 150, 150); // Light gray color
    doc.textWithLink(
      "balaramshiwakoti.com.np",
      pageWidth - 15,
      pageHeight - 10,
      {
        align: "right",
        url: "https://balaramshiwakoti.com.np",
      }
    );

    // Add Facebook link at the bottom left of the page
    doc.setTextColor(59, 89, 152); // Facebook blue color
    doc.setFontSize(12);
    doc.textWithLink("My Facebook", 15, pageHeight - 10, {
      url: "https://www.facebook.com/hercules.shiwakoti",
    });

    // Restore original state
    doc.setFontSize(currentFontSize);
    doc.setTextColor(currentTextColor);
  };

  // Empty function as we're removing page numbers
  const addPageNumber = () => {
    // Page numbers removed as requested
  };

  // Function to check if we need a new page
  const checkForNewPage = (neededSpace = 20) => {
    if (yPos + neededSpace > maxY) {
      doc.addPage();
      yPos = margin + 10; // Reset Y position with some padding
      addWatermark();
      addPageNumber(); // No parameter needed anymore
      return true;
    }
    return false;
  };

  // Initialize page counter (still needed for internal tracking)
  let yPos = 45;

  // Add title
  doc.setFontSize(18);
  doc.text(`Personal Information Questionnaire (PIQ)`, margin, 20);

  // Add header section
  doc.setFontSize(14);
  doc.text("Selection Details", margin, 35);

  // Add watermark to first page
  addWatermark();

  // Header section
  doc.setFontSize(10);
  doc.text(
    `Selection Board: ${formData.selectionBoard || "Not answered"}`,
    margin,
    yPos
  );
  yPos += 7;
  doc.text(`Batch No: ${formData.batchNo || "Not answered"}`, margin, yPos);
  yPos += 7;
  doc.text(`Chest No: ${formData.chestNo || "Not answered"}`, margin, yPos);
  yPos += 7;
  doc.text(
    `Lok Sewa Roll No: ${formData.loksewaRollNo || "Not answered"}`,
    margin,
    yPos
  );
  yPos += 7;
  doc.text(
    `Test Location: ${formData.testType || "Not answered"}`,
    margin,
    yPos
  );
  yPos += 15;

  // Check if we need a new page before the next section
  checkForNewPage(25);

  // Personal details
  doc.setFontSize(14);
  doc.text("Personal Details", margin, yPos);
  yPos += 10;

  doc.setFontSize(10);
  doc.text(`Name: ${formData.name || "Not answered"}`, margin, yPos);
  yPos += 7;
  doc.text(
    `Father's Name: ${formData.fathersName || "Not answered"}`,
    margin,
    yPos
  );
  yPos += 7;
  doc.text(`Hobbies: ${formData.hobbies || "Not answered"}`, margin, yPos);
  yPos += 15;

  // Check if we need a new page before the next section
  checkForNewPage(25);

  // Demographic details
  doc.setFontSize(14);
  doc.text("Demographic Details", margin, yPos);
  yPos += 10;

  doc.setFontSize(10);
  doc.text(
    `State/Province: ${formData.stateProvince || "Not answered"}`,
    margin,
    yPos
  );
  yPos += 7;
  doc.text(`District: ${formData.district || "Not answered"}`, margin, yPos);
  yPos += 7;
  doc.text(`Religion: ${formData.religion || "Not answered"}`, margin, yPos);
  yPos += 7;
  doc.text(`Caste: ${formData.category || "Not answered"}`, margin, yPos);
  yPos += 7;
  doc.text(
    `Mother Tongue: ${formData.motherTongue || "Not answered"}`,
    margin,
    yPos
  );
  yPos += 7;
  doc.text(
    `Date of birth: ${formData.dateOfBirth || "Not answered"}`,
    margin,
    yPos
  );
  yPos += 7;
  doc.text(
    `Marital Status: ${formData.maritalStatus || "Not answered"}`,
    margin,
    yPos
  );
  yPos += 15;

  // Check if we need a new page before the next section
  checkForNewPage(25);

  // Present Residence details
  doc.setFontSize(14);
  doc.text("Present Residence Details", margin, yPos);
  yPos += 10;

  doc.setFontSize(10);
  doc.text(
    `Place: ${formData.presentResidence.place || "Not answered"}`,
    margin,
    yPos
  );
  yPos += 7;
  doc.text(
    `District: ${formData.presentResidence.district || "Not answered"}`,
    margin,
    yPos
  );
  yPos += 7;
  doc.text(
    `Province: ${formData.presentResidence.province || "Not answered"}`,
    margin,
    yPos
  );
  yPos += 7;
  doc.text(
    `Population: ${formData.presentResidence.population || "Not answered"}`,
    margin,
    yPos
  );
  yPos += 15;

  // Check if we need a new page before the next section
  checkForNewPage(25);

  // Permanent Residence details
  doc.setFontSize(14);
  doc.text("Permanent Residence Details", margin, yPos);
  yPos += 10;

  doc.setFontSize(10);
  doc.text(
    `Place: ${formData.permanentResidence.place || "Not answered"}`,
    margin,
    yPos
  );
  yPos += 7;
  doc.text(
    `District: ${formData.permanentResidence.district || "Not answered"}`,
    margin,
    yPos
  );
  yPos += 7;
  doc.text(
    `Province: ${formData.permanentResidence.province || "Not answered"}`,
    margin,
    yPos
  );
  yPos += 7;
  doc.text(
    `Population: ${formData.permanentResidence.population || "Not answered"}`,
    margin,
    yPos
  );
  yPos += 15;

  // Check if we need a new page before the next section
  checkForNewPage(25);

  // Family details
  doc.setFontSize(14);
  doc.text("Family Details", margin, yPos);
  yPos += 10;

  //Father details
  doc.setFontSize(10);
  doc.text("Father:", margin, yPos);
  yPos += 7;
  doc.text(
    `Education: ${formData.familyDetails.father.education || "Not answered"}`,
    margin,
    yPos
  );
  yPos += 7;
  doc.text(
    `Occupation: ${formData.familyDetails.father.occupation || "Not answered"}`,
    margin,
    yPos
  );
  yPos += 7;
  doc.text(
    `Monthly Income: ${
      formData.familyDetails.father.monthlyIncome || "Not answered"
    }`,
    margin,
    yPos
  );
  yPos += 15;

  // Check if we need a new page before continuing
  checkForNewPage(25);

  //Mother details
  doc.setFontSize(10);
  doc.text("Mother:", margin, yPos);
  yPos += 7;
  doc.text(
    `Education: ${formData.familyDetails.mother.education || "Not answered"}`,
    margin,
    yPos
  );
  yPos += 7;
  doc.text(
    `Occupation: ${formData.familyDetails.mother.occupation || "Not answered"}`,
    margin,
    yPos
  );
  yPos += 7;
  doc.text(
    `Monthly Income: ${
      formData.familyDetails.mother.monthlyIncome || "Not answered"
    }`,
    margin,
    yPos
  );
  yPos += 15;

  // Check if we need a new page before continuing
  checkForNewPage(25);

  //Guardian details
  doc.setFontSize(10);
  doc.text("Guardian:", margin, yPos);
  yPos += 7;
  doc.text(
    `Education: ${formData.familyDetails.guardian.education || "Not answered"}`,
    margin,
    yPos
  );
  yPos += 7;
  doc.text(
    `Occupation: ${
      formData.familyDetails.guardian.occupation || "Not answered"
    }`,
    margin,
    yPos
  );
  yPos += 7;
  doc.text(
    `Monthly Income: ${
      formData.familyDetails.guardian.monthlyIncome || "Not answered"
    }`,
    margin,
    yPos
  );
  yPos += 15;

  // Check if we need a new page before continuing
  checkForNewPage(25);

  //Brothers details
  if (formData.familyDetails.brothers.length > 0) {
    doc.setFontSize(10);
    doc.text("Brothers:", margin, yPos);
    yPos += 10;

    formData.familyDetails.brothers.forEach((brother, index) => {
      // Check if we need a new page before each brother
      checkForNewPage(30);

      doc.text(`Brother ${index + 1}:`, margin, yPos);
      yPos += 7;
      doc.text(
        `Education: ${brother.education || "Not answered"}`,
        margin,
        yPos
      );
      yPos += 7;
      doc.text(
        `Occupation: ${brother.occupation || "Not answered"}`,
        margin,
        yPos
      );
      yPos += 7;
      doc.text(
        `Monthly Income: ${brother.monthlyIncome || "Not answered"}`,
        margin,
        yPos
      );
      yPos += 15;
    });
  }

  // Check if we need a new page before continuing
  checkForNewPage(25);

  //Sisters details
  if (formData.familyDetails.sisters.length > 0) {
    doc.setFontSize(10);
    doc.text("Sisters:", margin, yPos);
    yPos += 10;

    formData.familyDetails.sisters.forEach((sister, index) => {
      // Check if we need a new page before each sister
      checkForNewPage(30);

      doc.text(`Sister ${index + 1}:`, margin, yPos);
      yPos += 7;
      doc.text(
        `Education: ${sister.education || "Not answered"}`,
        margin,
        yPos
      );
      yPos += 7;
      doc.text(
        `Occupation: ${sister.occupation || "Not answered"}`,
        margin,
        yPos
      );
      yPos += 7;
      doc.text(
        `Monthly Income: ${sister.monthlyIncome || "Not answered"}`,
        margin,
        yPos
      );
      yPos += 25;
    });
  }

  // Force Educational Record to start on a new page
  doc.addPage();

  yPos = margin + 10; // Reset Y position with some padding
  addWatermark();

  // Educational Record
  doc.setFontSize(14);
  doc.text("Educational Record", margin, yPos);
  yPos += 10;

  if (formData.education.length > 0) {
    formData.education.forEach((education, index) => {
      // Check if we need a new page before each education entry
      checkForNewPage(60);

      doc.setFontSize(10);
      doc.text(`Education ${index + 1}:`, margin, yPos);
      yPos += 7;
      doc.text(
        `Qualification: ${education.qualification || "Not answered"}`,
        margin,
        yPos
      );
      yPos += 7;
      doc.text(
        `Institution: ${education.institution || "Not answered"}`,
        margin,
        yPos
      );
      yPos += 7;
      doc.text(`Board: ${education.board || "Not answered"}`, margin, yPos);
      yPos += 7;
      doc.text(`Year: ${education.year || "Not answered"}`, margin, yPos);
      yPos += 7;
      doc.text(
        `Division: ${education.division || "Not answered"}`,
        margin,
        yPos
      );
      yPos += 7;
      doc.text(`Medium: ${education.medium || "Not answered"}`, margin, yPos);
      yPos += 7;
      doc.text(
        `Boarder/Day Scholar: ${education.boarderDayScholar || "Not answered"}`,
        margin,
        yPos
      );
      yPos += 7;
      doc.text(
        `Achievements: ${education.achievements || "Not answered"}`,
        margin,
        yPos
      );
      yPos += 15;
    });
  } else {
    doc.setFontSize(10);
    doc.text("No educational records provided", margin, yPos);
    yPos += 15;
  }

  // Check if we need a new page before the next section
  checkForNewPage(25);

  // Physical Details
  doc.setFontSize(14);
  doc.text("Physical Details", margin, yPos);
  yPos += 10;
  doc.setFontSize(10);

  doc.text(
    `Age: ${formData.age || "Not answered"} years ${
      formData.ageMonths || "0"
    } months`,
    margin,
    yPos
  );
  yPos += 7;
  doc.text(
    `Height: ${formData.heightFeet || "Not answered"} feet ${
      formData.heightInches || "0"
    } inches`,
    margin,
    yPos
  );
  yPos += 7;
  doc.text(`Weight: ${formData.weight || "Not answered"} kg`, margin, yPos);
  yPos += 15;

  // Check if we need a new page before the next section
  checkForNewPage(25);

  // Sports participation
  doc.setFontSize(14);
  doc.text("Sports Participation", margin, yPos);
  yPos += 10;
  doc.setFontSize(10);

  // Handle games as an array
  if (formData.games.length > 0) {
    formData.games.forEach((game, index) => {
      // Check if we need a new page before each game
      checkForNewPage(30);

      doc.text(`Game ${index + 1}:`, margin, yPos);
      yPos += 7;
      doc.text(`Name: ${game.name || "Not answered"}`, margin, yPos);
      yPos += 7;
      doc.text(
        `Period of duration: ${game.period || "Not answered"}`,
        margin,
        yPos
      );
      yPos += 7;
      doc.text(
        `Represented: ${game.represented || "Not answered"}`,
        margin,
        yPos
      );
      yPos += 7;
      doc.text(
        `Achievements: ${game.achievements || "Not answered"}`,
        margin,
        yPos
      );
      yPos += 15;
    });
  } else {
    doc.text("No sports participation records provided", margin, yPos);
    yPos += 15;
  }

  // Check if we need a new page before the next section
  checkForNewPage(25);

  // Extra curricular
  doc.setFontSize(14);
  doc.text("Extra Curricular Activities", margin, yPos);
  yPos += 10;
  doc.setFontSize(10);

  // Handle extracurricular activities as an array
  if (formData.extracurricular.length > 0) {
    formData.extracurricular.forEach((activity, index) => {
      // Check if we need a new page before each activity
      checkForNewPage(30);

      doc.text(`Activity ${index + 1}:`, margin, yPos);
      yPos += 7;
      doc.text(`Name: ${activity.activity || "Not answered"}`, margin, yPos);
      yPos += 7;
      doc.text(
        `Duration: ${activity.duration || "Not answered"}`,
        margin,
        yPos
      );
      yPos += 7;
      doc.text(
        `Achievements: ${activity.achievements || "Not answered"}`,
        margin,
        yPos
      );
      yPos += 15;
    });
  } else {
    doc.text("No extracurricular activities provided", margin, yPos);
    yPos += 15;
  }

  return doc.output("blob");
};

export default function PIQForm() {
  const router = useRouter();
  const { startLoading } = useLoading();

  // Default empty form data - memoized to prevent unnecessary re-renders
  const defaultFormData = useMemo<FormData>(
    () => ({
      // Header Section
      selectionBoard: "",
      batchNo: "",
      chestNo: "",
      loksewaRollNo: "",
      testType: "",

      // Personal Details
      name: "",
      fathersName: "",
      hobbies: "",

      // Residence Details
      maximumResidence: {
        place: "",
        district: "",
        province: "",
        population: "",
        isDistrictHQ: false,
      },
      presentResidence: {
        place: "",
        district: "",
        province: "",
        population: "",
        isDistrictHQ: false,
      },
      permanentResidence: {
        place: "",
        district: "",
        province: "",
        population: "",
        isDistrictHQ: false,
      },

      // Demographic Details
      stateProvince: "",
      district: "",
      religion: "",
      category: "",
      motherTongue: "",
      dateOfBirth: "",
      maritalStatus: "",

      // Parent Details
      parentsAlive: true,
      motherDeathAge: "",
      fatherDeathAge: "",

      // Educational Record
      education: [
        {
          qualification: "",
          institution: "",
          board: "",
          year: "",
          division: "",
          medium: "",
          boarderDayScholar: "",
          achievements: "",
        },
      ],

      // Physical Details
      age: "",
      ageMonths: "",
      heightFeet: "",
      heightInches: "",
      weight: "",

      // Occupation
      occupation: "",
      monthlyIncome: "",

      // NCC Training
      hasNCCTraining: false,
      nccDetails: {
        years: "",
        wing: "",
        division: "",
        certificate: "",
      },

      // Activities
      games: [
        {
          name: "",
          period: "",
          represented: "",
          achievements: "",
        },
      ],
      extracurricular: [
        {
          activity: "",
          duration: "",
          achievements: "",
        },
      ],
      positionsHeld: "",

      // Commission Details
      commissionNature: "",
      serviceChoice: [],
      chancesAvailed: "",

      // Family Details
      familyDetails: {
        father: {
          education: "",
          occupation: "",
          monthlyIncome: "",
        },
        mother: {
          education: "",
          occupation: "",
          monthlyIncome: "",
        },
        guardian: {
          education: "",
          occupation: "",
          monthlyIncome: "",
        },
        brothers: [
          {
            education: "",
            occupation: "",
            monthlyIncome: "",
          },
        ],
        sisters: [
          {
            education: "",
            occupation: "",
            monthlyIncome: "",
          },
        ],
      },
    }),
    []
  );

  const [formData, setFormData] = useState<FormData>(defaultFormData);

  // Load saved form data from localStorage when component mounts (client-side only)
  useEffect(() => {
    // Try to load saved form data from localStorage
    if (typeof window !== "undefined") {
      const savedData = localStorage.getItem("piqFormData");
      if (savedData) {
        try {
          const parsedData = JSON.parse(savedData);
          // Merge saved data with default data to ensure all fields exist
          setFormData({
            ...defaultFormData,
            ...parsedData,
            // Ensure nested objects are properly merged
            familyDetails: {
              ...defaultFormData.familyDetails,
              ...(parsedData.familyDetails || {}),
              father: {
                ...defaultFormData.familyDetails.father,
                ...(parsedData.familyDetails?.father || {}),
              },
              mother: {
                ...defaultFormData.familyDetails.mother,
                ...(parsedData.familyDetails?.mother || {}),
              },
              guardian: {
                ...defaultFormData.familyDetails.guardian,
                ...(parsedData.familyDetails?.guardian || {}),
              },
              brothers:
                parsedData.familyDetails?.brothers ||
                defaultFormData.familyDetails.brothers,
              sisters:
                parsedData.familyDetails?.sisters ||
                defaultFormData.familyDetails.sisters,
            },
          });
        } catch (error) {
          console.error("Error parsing saved form data:", error);
        }
      }
    }
  }, [defaultFormData]);

  // Save form data to localStorage whenever it changes
  useEffect(() => {
    if (typeof window !== "undefined") {
      localStorage.setItem("piqFormData", JSON.stringify(formData));
    }
  }, [formData]);

  const handleInputChange = (
    field: keyof FormData,
    value: string | FormData["education"]
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleResidenceChange = (
    residenceType:
      | "maximumResidence"
      | "presentResidence"
      | "permanentResidence",
    field: string,
    value: string | boolean
  ) => {
    setFormData((prev) => ({
      ...prev,
      [residenceType]: {
        ...prev[residenceType],
        [field]: value,
      },
    }));
  };

  const handleFamilyMemberChange = (
    memberType: "father" | "mother" | "guardian" | "brothers" | "sisters",
    index: number,
    field: string,
    value: string
  ) => {
    setFormData((prev) => {
      if (memberType === "brothers" || memberType === "sisters") {
        const updatedMembers = [...prev.familyDetails[memberType]];
        updatedMembers[index] = {
          ...updatedMembers[index],
          [field]: value,
        };
        return {
          ...prev,
          familyDetails: {
            ...prev.familyDetails,
            [memberType]: updatedMembers,
          },
        };
      } else {
        return {
          ...prev,
          familyDetails: {
            ...prev.familyDetails,
            [memberType]: {
              ...prev.familyDetails[memberType],
              [field]: value,
            },
          },
        };
      }
    });
  };

  const addFamilyMember = (memberType: "brothers" | "sisters") => {
    setFormData((prev) => ({
      ...prev,
      familyDetails: {
        ...prev.familyDetails,
        [memberType]: [
          ...prev.familyDetails[memberType],
          {
            education: "",
            occupation: "",
            monthlyIncome: "",
          },
        ],
      },
    }));
  };

  const removeFamilyMember = (
    memberType: "brothers" | "sisters",
    index: number
  ) => {
    setFormData((prev) => ({
      ...prev,
      familyDetails: {
        ...prev.familyDetails,
        [memberType]: prev.familyDetails[memberType].filter(
          (_, i) => i !== index
        ),
      },
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Save the final form data
    if (typeof window !== "undefined") {
      localStorage.setItem("piqFormData", JSON.stringify(formData));
    }
  };

  // Separate function for PDF download to use with password protection
  const downloadPDF = () => {
    // Generate and download PDF
    const blob = generatePIQPDF(formData);
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `PIQ_Form_${formData.name || "Response"}.pdf`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleClearForm = () => {
    if (typeof window !== "undefined") {
      localStorage.removeItem("piqFormData");
      setFormData({
        // Header Section
        selectionBoard: "",
        batchNo: "",
        chestNo: "",
        loksewaRollNo: "",
        testType: "",

        // Personal Details
        name: "",
        fathersName: "",
        hobbies: "",

        // Residence Details
        maximumResidence: {
          place: "",
          district: "",
          province: "",
          population: "",
          isDistrictHQ: false,
        },
        presentResidence: {
          place: "",
          district: "",
          province: "",
          population: "",
          isDistrictHQ: false,
        },
        permanentResidence: {
          place: "",
          district: "",
          province: "",
          population: "",
          isDistrictHQ: false,
        },

        // Demographic Details
        stateProvince: "",
        district: "",
        religion: "",
        category: "",
        motherTongue: "",
        dateOfBirth: "",
        maritalStatus: "",

        // Parent Details
        parentsAlive: true,
        motherDeathAge: "",
        fatherDeathAge: "",

        // Educational Record
        education: [
          {
            qualification: "",
            institution: "",
            board: "",
            year: "",
            division: "",
            medium: "",
            boarderDayScholar: "",
            achievements: "",
          },
        ],

        // Physical Details
        age: "",
        ageMonths: "",
        heightFeet: "",
        heightInches: "",
        weight: "",

        // Occupation
        occupation: "",
        monthlyIncome: "",

        // NCC Training
        hasNCCTraining: false,
        nccDetails: {
          years: "",
          wing: "",
          division: "",
          certificate: "",
        },

        // Activities
        games: [
          {
            name: "",
            period: "",
            represented: "",
            achievements: "",
          },
        ],
        extracurricular: [
          {
            activity: "",
            duration: "",
            achievements: "",
          },
        ],
        positionsHeld: "",

        // Commission Details
        commissionNature: "",
        serviceChoice: [],
        chancesAvailed: "",

        // Family Details
        familyDetails: {
          father: {
            education: "",
            occupation: "",
            monthlyIncome: "",
          },
          mother: {
            education: "",
            occupation: "",
            monthlyIncome: "",
          },
          guardian: {
            education: "",
            occupation: "",
            monthlyIncome: "",
          },
          brothers: [
            {
              education: "",
              occupation: "",
              monthlyIncome: "",
            },
          ],
          sisters: [
            {
              education: "",
              occupation: "",
              monthlyIncome: "",
            },
          ],
        },
      });
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8 md:py-12">
      <div className="mx-auto max-w-4xl px-6">
        <div className="flex justify-between items-center mb-8">
          <SimpleButton
            onClick={() => router.push("/tests/to/piq")}
            className="flex items-center text-gray-600 hover:text-gray-900"
          >
            <ArrowLeftIcon className="h-5 w-5 mr-2" />
            Back to PIQ
          </SimpleButton>
          <SimpleButton
            onClick={handleClearForm}
            className="text-sm text-red-600 hover:text-red-700"
          >
            Clear Saved Form
          </SimpleButton>
        </div>

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Header Section */}
          <div className="bg-white rounded-2xl shadow-sm p-8">
            <div className="text-center mb-6">
              <h1 className="text-2xl font-bold text-gray-900">PIQ FORM </h1>
              <p className="text-red-600 font-semibold mt-2">CONFIDENTIAL</p>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Selection Board (Number and Place)
                </label>
                <input
                  type="text"
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  value={formData.selectionBoard}
                  onChange={(e) =>
                    handleInputChange("selectionBoard", e.target.value)
                  }
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Batch No.
                </label>
                <input
                  type="text"
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  value={formData.batchNo}
                  onChange={(e) => handleInputChange("batchNo", e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Chest No.
                </label>
                <input
                  type="text"
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  value={formData.chestNo}
                  onChange={(e) => handleInputChange("chestNo", e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Lok sewa Roll No.
                </label>
                <input
                  type="text"
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  value={formData.loksewaRollNo}
                  onChange={(e) =>
                    handleInputChange("loksewaRollNo", e.target.value)
                  }
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Location of test
                </label>
                <input
                  type="text"
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  value={formData.testType}
                  onChange={(e) =>
                    handleInputChange("testType", e.target.value)
                  }
                />
              </div>
            </div>
          </div>

          {/* Personal Details Section */}
          <div className="bg-white rounded-2xl shadow-sm p-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              Personal Details
            </h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Name (IN BOLD LETTERS)
                </label>
                <input
                  type="text"
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Father&apos;s Name
                </label>
                <input
                  type="text"
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  value={formData.fathersName}
                  onChange={(e) =>
                    handleInputChange("fathersName", e.target.value)
                  }
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Hobbies
                </label>
                <input
                  type="text"
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  value={formData.hobbies}
                  onChange={(e) => handleInputChange("hobbies", e.target.value)}
                  placeholder="Enter your hobbies (e.g., Reading, Sports, Music)"
                />
              </div>
            </div>
          </div>

          {/* Demographic Details Section */}
          <div className="bg-white rounded-2xl shadow-sm p-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              Demographic Details
            </h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  State/Province
                </label>
                <input
                  type="text"
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  value={formData.stateProvince}
                  onChange={(e) =>
                    handleInputChange("stateProvince", e.target.value)
                  }
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  District
                </label>
                <input
                  type="text"
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  value={formData.district}
                  onChange={(e) =>
                    handleInputChange("district", e.target.value)
                  }
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Religion
                </label>
                <input
                  type="text"
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  value={formData.religion}
                  onChange={(e) =>
                    handleInputChange("religion", e.target.value)
                  }
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Caste/Category
                </label>
                <input
                  type="text"
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  value={formData.category}
                  onChange={(e) =>
                    handleInputChange("category", e.target.value)
                  }
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Mother Tongue
                </label>
                <input
                  type="text"
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  value={formData.motherTongue}
                  onChange={(e) =>
                    handleInputChange("motherTongue", e.target.value)
                  }
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Date of Birth
                </label>
                <input
                  type="date"
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  value={formData.dateOfBirth}
                  onChange={(e) =>
                    handleInputChange("dateOfBirth", e.target.value)
                  }
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Marital Status
                </label>
                <select
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  value={formData.maritalStatus}
                  onChange={(e) =>
                    handleInputChange("maritalStatus", e.target.value)
                  }
                >
                  <option value="">Select Status</option>
                  <option value="Single">Single</option>
                  <option value="Married">Married</option>
                  <option value="Widowed">Widowed</option>
                  <option value="Divorced">Divorced</option>
                </select>
              </div>
            </div>
          </div>

          {/* Residence Details Section */}
          <div className="bg-white rounded-2xl shadow-sm p-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              Residence Details
            </h2>

            {/* Present Residence */}
            <div className="mb-8">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Present Residence
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Place
                  </label>
                  <input
                    type="text"
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    value={formData.presentResidence.place}
                    onChange={(e) =>
                      handleResidenceChange(
                        "presentResidence",
                        "place",
                        e.target.value
                      )
                    }
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    District
                  </label>
                  <input
                    type="text"
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    value={formData.presentResidence.district}
                    onChange={(e) =>
                      handleResidenceChange(
                        "presentResidence",
                        "district",
                        e.target.value
                      )
                    }
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Province
                  </label>
                  <input
                    type="text"
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    value={formData.presentResidence.province}
                    onChange={(e) =>
                      handleResidenceChange(
                        "presentResidence",
                        "province",
                        e.target.value
                      )
                    }
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Population
                  </label>
                  <input
                    type="text"
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    value={formData.presentResidence.population}
                    onChange={(e) =>
                      handleResidenceChange(
                        "presentResidence",
                        "population",
                        e.target.value
                      )
                    }
                  />
                </div>
              </div>
            </div>

            {/* Permanent Residence */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Permanent Residence
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Place
                  </label>
                  <input
                    type="text"
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    value={formData.permanentResidence.place}
                    onChange={(e) =>
                      handleResidenceChange(
                        "permanentResidence",
                        "place",
                        e.target.value
                      )
                    }
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    District
                  </label>
                  <input
                    type="text"
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    value={formData.permanentResidence.district}
                    onChange={(e) =>
                      handleResidenceChange(
                        "permanentResidence",
                        "district",
                        e.target.value
                      )
                    }
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Province
                  </label>
                  <input
                    type="text"
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    value={formData.permanentResidence.province}
                    onChange={(e) =>
                      handleResidenceChange(
                        "permanentResidence",
                        "province",
                        e.target.value
                      )
                    }
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Population
                  </label>
                  <input
                    type="text"
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    value={formData.permanentResidence.population}
                    onChange={(e) =>
                      handleResidenceChange(
                        "permanentResidence",
                        "population",
                        e.target.value
                      )
                    }
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Family Details Section */}
          <div className="bg-white rounded-2xl shadow-sm p-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">
              Family Details
            </h2>

            {/* Father's Details */}
            <div className="mb-8">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                {`Father's Details`}
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Education
                  </label>
                  <input
                    type="text"
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    value={formData.familyDetails.father.education}
                    onChange={(e) =>
                      handleFamilyMemberChange(
                        "father",
                        0,
                        "education",
                        e.target.value
                      )
                    }
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Occupation
                  </label>
                  <input
                    type="text"
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    value={formData.familyDetails.father.occupation}
                    onChange={(e) =>
                      handleFamilyMemberChange(
                        "father",
                        0,
                        "occupation",
                        e.target.value
                      )
                    }
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Monthly Income
                  </label>
                  <input
                    type="text"
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    value={formData.familyDetails.father.monthlyIncome}
                    onChange={(e) =>
                      handleFamilyMemberChange(
                        "father",
                        0,
                        "monthlyIncome",
                        e.target.value
                      )
                    }
                  />
                </div>
              </div>
            </div>

            {/* Mother's Details */}
            <div className="mb-8">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                {`Mother's Details`}
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Education
                  </label>
                  <input
                    type="text"
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    value={formData.familyDetails.mother.education}
                    onChange={(e) =>
                      handleFamilyMemberChange(
                        "mother",
                        0,
                        "education",
                        e.target.value
                      )
                    }
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Occupation
                  </label>
                  <input
                    type="text"
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    value={formData.familyDetails.mother.occupation}
                    onChange={(e) =>
                      handleFamilyMemberChange(
                        "mother",
                        0,
                        "occupation",
                        e.target.value
                      )
                    }
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Monthly Income
                  </label>
                  <input
                    type="text"
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    value={formData.familyDetails.mother.monthlyIncome}
                    onChange={(e) =>
                      handleFamilyMemberChange(
                        "mother",
                        0,
                        "monthlyIncome",
                        e.target.value
                      )
                    }
                  />
                </div>
              </div>
            </div>

            {/* Guardian's Details */}
            <div className="mb-8">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                {`Guardian's Details`}
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Education
                  </label>
                  <input
                    type="text"
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    value={formData.familyDetails.guardian.education}
                    onChange={(e) =>
                      handleFamilyMemberChange(
                        "guardian",
                        0,
                        "education",
                        e.target.value
                      )
                    }
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Occupation
                  </label>
                  <input
                    type="text"
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    value={formData.familyDetails.guardian.occupation}
                    onChange={(e) =>
                      handleFamilyMemberChange(
                        "guardian",
                        0,
                        "occupation",
                        e.target.value
                      )
                    }
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Monthly Income
                  </label>
                  <input
                    type="text"
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    value={formData.familyDetails.guardian.monthlyIncome}
                    onChange={(e) =>
                      handleFamilyMemberChange(
                        "guardian",
                        0,
                        "monthlyIncome",
                        e.target.value
                      )
                    }
                  />
                </div>
              </div>
            </div>

            {/* Brothers' Details */}
            <div className="mb-8">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-900">
                  {`Brothers' Details`}
                </h3>
                <LoadingButton
                  type="button"
                  onClick={() => addFamilyMember("brothers")}
                  className="text-sm text-indigo-600 hover:text-indigo-500"
                  loadingText="Loading..."
                >
                  + Add Brother
                </LoadingButton>
              </div>
              {formData.familyDetails.brothers.map((brother, index) => (
                <div
                  key={index}
                  className="mb-4 p-4 border border-gray-200 rounded-lg"
                >
                  <div className="flex justify-between items-center mb-2">
                    <h4 className="text-md font-medium text-gray-700">
                      Brother {index + 1}
                    </h4>
                    {index > 0 && (
                      <LoadingButton
                        type="button"
                        onClick={() => removeFamilyMember("brothers", index)}
                        className="text-sm text-red-600 hover:text-red-500"
                        loadingText="Loading..."
                      >
                        Remove
                      </LoadingButton>
                    )}
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Education
                      </label>
                      <input
                        type="text"
                        className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                        value={brother.education}
                        onChange={(e) =>
                          handleFamilyMemberChange(
                            "brothers",
                            index,
                            "education",
                            e.target.value
                          )
                        }
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Occupation
                      </label>
                      <input
                        type="text"
                        className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                        value={brother.occupation}
                        onChange={(e) =>
                          handleFamilyMemberChange(
                            "brothers",
                            index,
                            "occupation",
                            e.target.value
                          )
                        }
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Monthly Income
                      </label>
                      <input
                        type="text"
                        className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                        value={brother.monthlyIncome}
                        onChange={(e) =>
                          handleFamilyMemberChange(
                            "brothers",
                            index,
                            "monthlyIncome",
                            e.target.value
                          )
                        }
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Sisters' Details */}
            <div className="mb-8">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-900">
                  {`Sisters' Details`}
                </h3>
                <LoadingButton
                  type="button"
                  onClick={() => addFamilyMember("sisters")}
                  className="text-sm text-indigo-600 hover:text-indigo-500"
                  loadingText="Loading..."
                >
                  + Add Sister
                </LoadingButton>
              </div>
              {formData.familyDetails.sisters.map((sister, index) => (
                <div
                  key={index}
                  className="mb-4 p-4 border border-gray-200 rounded-lg"
                >
                  <div className="flex justify-between items-center mb-2">
                    <h4 className="text-md font-medium text-gray-700">
                      Sister {index + 1}
                    </h4>
                    {index > 0 && (
                      <LoadingButton
                        type="button"
                        onClick={() => removeFamilyMember("sisters", index)}
                        className="text-sm text-red-600 hover:text-red-500"
                        loadingText="Loading..."
                      >
                        Remove
                      </LoadingButton>
                    )}
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Education
                      </label>
                      <input
                        type="text"
                        className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                        value={sister.education}
                        onChange={(e) =>
                          handleFamilyMemberChange(
                            "sisters",
                            index,
                            "education",
                            e.target.value
                          )
                        }
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Occupation
                      </label>
                      <input
                        type="text"
                        className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                        value={sister.occupation}
                        onChange={(e) =>
                          handleFamilyMemberChange(
                            "sisters",
                            index,
                            "occupation",
                            e.target.value
                          )
                        }
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Monthly Income
                      </label>
                      <input
                        type="text"
                        className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                        value={sister.monthlyIncome}
                        onChange={(e) =>
                          handleFamilyMemberChange(
                            "sisters",
                            index,
                            "monthlyIncome",
                            e.target.value
                          )
                        }
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Educational Record Section */}
          <div className="bg-white rounded-2xl shadow-sm p-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">
              Educational Record
            </h2>

            {/* SLC Details */}
            <div className="mb-8">
              <h3 className="text-lg font-medium text-gray-900 mb-4">SLC</h3>
              <div className="grid grid-cols-1 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Name of Institute
                  </label>
                  <input
                    type="text"
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    value={formData.education[0].institution}
                    onChange={(e) => {
                      setFormData((prev) => ({
                        ...prev,
                        education: prev.education.map((edu, index) =>
                          index === 0
                            ? { ...edu, institution: e.target.value }
                            : edu
                        ),
                      }));
                    }}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Board
                  </label>
                  <input
                    type="text"
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    value={formData.education[0].board}
                    onChange={(e) =>
                      handleInputChange("education", [
                        {
                          ...formData.education[0],
                          board: e.target.value,
                        },
                        ...formData.education.slice(1),
                      ])
                    }
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Year
                  </label>
                  <input
                    type="text"
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    value={formData.education[0].year}
                    onChange={(e) =>
                      handleInputChange("education", [
                        {
                          ...formData.education[0],
                          year: e.target.value,
                        },
                        ...formData.education.slice(1),
                      ])
                    }
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Marks
                  </label>
                  <input
                    type="text"
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    value={formData.education[0].division}
                    onChange={(e) =>
                      handleInputChange("education", [
                        {
                          ...formData.education[0],
                          division: e.target.value,
                        },
                        ...formData.education.slice(1),
                      ])
                    }
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Achievements
                  </label>
                  <input
                    type="text"
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    value={formData.education[0].achievements}
                    onChange={(e) =>
                      handleInputChange("education", [
                        {
                          ...formData.education[0],
                          achievements: e.target.value,
                        },
                        ...formData.education.slice(1),
                      ])
                    }
                  />
                </div>
              </div>
            </div>

            {/* +2 Details */}
            <div className="mb-8">
              <h3 className="text-lg font-medium text-gray-900 mb-4">+2</h3>
              <div className="grid grid-cols-1 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Name of Institute
                  </label>
                  <input
                    type="text"
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    value={formData.education[1]?.institution || ""}
                    onChange={(e) =>
                      handleInputChange("education", [
                        formData.education[0],
                        {
                          ...formData.education[1],
                          institution: e.target.value,
                        },
                        ...formData.education.slice(2),
                      ])
                    }
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Board
                  </label>
                  <input
                    type="text"
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    value={formData.education[1]?.board || ""}
                    onChange={(e) =>
                      handleInputChange("education", [
                        formData.education[0],
                        {
                          ...formData.education[1],
                          board: e.target.value,
                        },
                        ...formData.education.slice(2),
                      ])
                    }
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Year
                  </label>
                  <input
                    type="text"
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    value={formData.education[1]?.year || ""}
                    onChange={(e) =>
                      handleInputChange("education", [
                        formData.education[0],
                        {
                          ...formData.education[1],
                          year: e.target.value,
                        },
                        ...formData.education.slice(2),
                      ])
                    }
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Marks
                  </label>
                  <input
                    type="text"
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    value={formData.education[1]?.division || ""}
                    onChange={(e) =>
                      handleInputChange("education", [
                        formData.education[0],
                        {
                          ...formData.education[1],
                          division: e.target.value,
                        },
                        ...formData.education.slice(2),
                      ])
                    }
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Achievements
                  </label>
                  <input
                    type="text"
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    value={formData.education[1]?.achievements || ""}
                    onChange={(e) =>
                      handleInputChange("education", [
                        formData.education[0],
                        {
                          ...formData.education[1],
                          achievements: e.target.value,
                        },
                        ...formData.education.slice(2),
                      ])
                    }
                  />
                </div>
              </div>
            </div>

            {/* Bachelors Details */}
            <div className="mb-8">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Bachelors
              </h3>
              <div className="grid grid-cols-1 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Name of Institute
                  </label>
                  <input
                    type="text"
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    value={formData.education[2]?.institution || ""}
                    onChange={(e) =>
                      handleInputChange("education", [
                        formData.education[0],
                        formData.education[1],
                        {
                          ...formData.education[2],
                          institution: e.target.value,
                        },
                        ...formData.education.slice(3),
                      ])
                    }
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    University
                  </label>
                  <input
                    type="text"
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    value={formData.education[2]?.board || ""}
                    onChange={(e) =>
                      handleInputChange("education", [
                        formData.education[0],
                        formData.education[1],
                        {
                          ...formData.education[2],
                          board: e.target.value,
                        },
                        ...formData.education.slice(3),
                      ])
                    }
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Year
                  </label>
                  <input
                    type="text"
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    value={formData.education[2]?.year || ""}
                    onChange={(e) =>
                      handleInputChange("education", [
                        formData.education[0],
                        formData.education[1],
                        {
                          ...formData.education[2],
                          year: e.target.value,
                        },
                        ...formData.education.slice(3),
                      ])
                    }
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Marks
                  </label>
                  <input
                    type="text"
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    value={formData.education[2]?.division || ""}
                    onChange={(e) =>
                      handleInputChange("education", [
                        formData.education[0],
                        formData.education[1],
                        {
                          ...formData.education[2],
                          division: e.target.value,
                        },
                        ...formData.education.slice(3),
                      ])
                    }
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Achievements
                  </label>
                  <input
                    type="text"
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    value={formData.education[2]?.achievements || ""}
                    onChange={(e) =>
                      handleInputChange("education", [
                        formData.education[0],
                        formData.education[1],
                        {
                          ...formData.education[2],
                          achievements: e.target.value,
                        },
                        ...formData.education.slice(3),
                      ])
                    }
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Physical Details Section */}
          <div className="bg-white rounded-2xl shadow-sm p-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">
              Physical Details
            </h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {/* Age */}
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Age (Years)
                </label>
                <input
                  type="number"
                  min="0"
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  value={formData.age}
                  onChange={(e) => handleInputChange("age", e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Age (Months)
                </label>
                <input
                  type="number"
                  min="0"
                  max="11"
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  value={formData.ageMonths}
                  onChange={(e) =>
                    handleInputChange("ageMonths", e.target.value)
                  }
                />
              </div>

              {/* Height */}
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Height (Feet)
                </label>
                <input
                  type="number"
                  min="0"
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  value={formData.heightFeet}
                  onChange={(e) =>
                    handleInputChange("heightFeet", e.target.value)
                  }
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Height (Inches)
                </label>
                <input
                  type="number"
                  min="0"
                  max="11"
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  value={formData.heightInches}
                  onChange={(e) =>
                    handleInputChange("heightInches", e.target.value)
                  }
                />
              </div>

              {/* Weight */}
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Weight (kg)
                </label>
                <input
                  type="number"
                  step="0.1"
                  min="0"
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  value={formData.weight}
                  onChange={(e) => handleInputChange("weight", e.target.value)}
                />
              </div>
            </div>
          </div>

          {/* Sports Participation Section */}
          <div className="bg-white rounded-2xl shadow-sm p-8">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-gray-900">
                Sports Participation
              </h2>
              <LoadingButton
                type="button"
                onClick={() => {
                  setFormData((prev) => ({
                    ...prev,
                    games: [
                      ...prev.games,
                      {
                        name: "",
                        period: "",
                        represented: "",
                        achievements: "",
                      },
                    ],
                  }));
                }}
                className="text-sm text-indigo-600 hover:text-indigo-500"
                loadingText="Loading..."
              >
                + Add Sport
              </LoadingButton>
            </div>

            {formData.games.map((game, index) => (
              <div
                key={index}
                className="mb-6 p-4 border border-gray-200 rounded-lg"
              >
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-gray-900">
                    Sport {index + 1}
                  </h3>
                  {index > 0 && (
                    <LoadingButton
                      type="button"
                      onClick={() => {
                        setFormData((prev) => ({
                          ...prev,
                          games: prev.games.filter((_, i) => i !== index),
                        }));
                      }}
                      className="text-sm text-red-600 hover:text-red-500"
                      loadingText="Loading..."
                    >
                      Remove
                    </LoadingButton>
                  )}
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Game/Sport
                    </label>
                    <input
                      type="text"
                      className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                      value={game.name}
                      onChange={(e) => {
                        const updatedGames = [...formData.games];
                        updatedGames[index] = {
                          ...updatedGames[index],
                          name: e.target.value,
                        };
                        setFormData((prev) => ({
                          ...prev,
                          games: updatedGames,
                        }));
                      }}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Period of Duration
                    </label>
                    <input
                      type="text"
                      className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                      value={game.period}
                      onChange={(e) => {
                        const updatedGames = [...formData.games];
                        updatedGames[index] = {
                          ...updatedGames[index],
                          period: e.target.value,
                        };
                        setFormData((prev) => ({
                          ...prev,
                          games: updatedGames,
                        }));
                      }}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Represented (School/College/University)
                    </label>
                    <input
                      type="text"
                      className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                      value={game.represented}
                      onChange={(e) => {
                        const updatedGames = [...formData.games];
                        updatedGames[index] = {
                          ...updatedGames[index],
                          represented: e.target.value,
                        };
                        setFormData((prev) => ({
                          ...prev,
                          games: updatedGames,
                        }));
                      }}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Achievements
                    </label>
                    <input
                      type="text"
                      className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                      value={game.achievements}
                      onChange={(e) => {
                        const updatedGames = [...formData.games];
                        updatedGames[index] = {
                          ...updatedGames[index],
                          achievements: e.target.value,
                        };
                        setFormData((prev) => ({
                          ...prev,
                          games: updatedGames,
                        }));
                      }}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Extra-Curricular Activities Section */}
          <div className="bg-white rounded-2xl shadow-sm p-8">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-gray-900">
                Extra-Curricular Activities
              </h2>
              <LoadingButton
                type="button"
                onClick={() => {
                  setFormData((prev) => ({
                    ...prev,
                    extracurricular: [
                      ...prev.extracurricular,
                      {
                        activity: "",
                        duration: "",
                        achievements: "",
                      },
                    ],
                  }));
                }}
                className="text-sm text-indigo-600 hover:text-indigo-500"
                loadingText="Loading..."
              >
                + Add Activity
              </LoadingButton>
            </div>

            {formData.extracurricular.map((activity, index) => (
              <div
                key={index}
                className="mb-6 p-4 border border-gray-200 rounded-lg"
              >
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-gray-900">
                    Activity {index + 1}
                  </h3>
                  {index > 0 && (
                    <LoadingButton
                      type="button"
                      onClick={() => {
                        setFormData((prev) => ({
                          ...prev,
                          extracurricular: prev.extracurricular.filter(
                            (_, i) => i !== index
                          ),
                        }));
                      }}
                      className="text-sm text-red-600 hover:text-red-500"
                      loadingText="Loading..."
                    >
                      Remove
                    </LoadingButton>
                  )}
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Name of Activity Group
                    </label>
                    <input
                      type="text"
                      className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                      value={activity.activity}
                      onChange={(e) => {
                        const updatedActivities = [...formData.extracurricular];
                        updatedActivities[index] = {
                          ...updatedActivities[index],
                          activity: e.target.value,
                        };
                        setFormData((prev) => ({
                          ...prev,
                          extracurricular: updatedActivities,
                        }));
                      }}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Duration of Participation
                    </label>
                    <input
                      type="text"
                      className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                      value={activity.duration}
                      onChange={(e) => {
                        const updatedActivities = [...formData.extracurricular];
                        updatedActivities[index] = {
                          ...updatedActivities[index],
                          duration: e.target.value,
                        };
                        setFormData((prev) => ({
                          ...prev,
                          extracurricular: updatedActivities,
                        }));
                      }}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Outstanding Achievement (if any)
                    </label>
                    <input
                      type="text"
                      className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                      value={activity.achievements}
                      onChange={(e) => {
                        const updatedActivities = [...formData.extracurricular];
                        updatedActivities[index] = {
                          ...updatedActivities[index],
                          achievements: e.target.value,
                        };
                        setFormData((prev) => ({
                          ...prev,
                          extracurricular: updatedActivities,
                        }));
                      }}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="flex justify-end space-x-4 mt-8">
            <SimpleButton
              onClick={() => router.push("/tests/to/piq")}
              className="rounded-md bg-white px-4 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
            >
              Save & Exit
            </SimpleButton>
            <PasswordProtectedDownload
              onDownload={downloadPDF}
              buttonClassName="rounded-md bg-indigo-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 flex items-center justify-center"
            >
              <LockClosedIcon className="h-5 w-5 mr-1" />
              <DocumentArrowDownIcon className="h-5 w-5 mr-2" />
              Download Response
            </PasswordProtectedDownload>
          </div>
        </form>
      </div>
    </div>
  );
}
