"use client";

import { useState } from "react";
import { AIAnalysisResult } from "@/services/aiService";
import { SparklesIcon, TrashIcon } from "@heroicons/react/24/outline";
import {
  AIAnalysisDocument,
  deleteAIAnalysis,
} from "@/services/aiAnalysisService";
import MilitaryPsychologistDisplay from "./MilitaryPsychologistDisplay";

interface AIAnalysisDisplayProps {
  analysis: AIAnalysisDocument;
  isExpanded?: boolean;
  onDelete?: () => void;
}

export default function AIAnalysisDisplay({
  analysis,
  isExpanded = false,
  onDelete,
}: AIAnalysisDisplayProps) {
  const [expanded, setExpanded] = useState(isExpanded);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const result = analysis.analysis;

  const handleDelete = async () => {
    if (!analysis.docId) return;

    setIsDeleting(true);
    try {
      const success = await deleteAIAnalysis(
        analysis.docId,
        analysis.userId,
        analysis.testType,
        analysis.setName,
        analysis.responseTimestamp,
        analysis.aiProvider as "google" | "deepseek"
      );

      if (success && onDelete) {
        onDelete();
      }
    } catch (error) {
      console.error("Error deleting analysis:", error);
    } finally {
      setIsDeleting(false);
      setShowDeleteConfirm(false);
    }
  };

  // Format date
  const formatDate = (timestamp?: number) => {
    if (!timestamp) return "Unknown date";
    return new Date(timestamp).toLocaleString();
  };

  // Get test type display name
  const getTestTypeDisplay = (testType: string) => {
    switch (testType) {
      case "srt":
        return "Situation Reaction Test";
      case "wat":
        return "Word Association Test";
      case "tat":
        return "Thematic Apperception Test";
      case "sdt":
        return "Self Description Test";
      default:
        return testType.toUpperCase();
    }
  };

  // Trait scores rendering removed as requested

  // Log whether responseQualityAssessment is present
  if (result.responseQualityAssessment) {
    console.log(
      "Response quality assessment is present in the display:",
      Object.keys(result.responseQualityAssessment).length,
      "responses assessed"
    );
  } else {
    console.warn("Response quality assessment is NOT present in the display");
  }

  // Check if this is a military psychologist analysis
  if (result.militaryPsychologist) {
    console.log(
      "Rendering military psychologist display for saved analysis",
      result.militaryPsychologist
    );

    // Force the aiProvider to be "google" for military psychologist analyses
    const militaryAnalysis = {
      ...analysis,
      aiProvider: "google",
    };

    return (
      <MilitaryPsychologistDisplay
        analysis={militaryAnalysis}
        isExpanded={expanded}
        onDelete={onDelete}
      />
    );
  }

  return (
    <div className="bg-white shadow rounded-lg p-6 mt-4">
      <div className="flex items-center justify-between mb-4">
        <div>
          <div className="flex items-center">
            <SparklesIcon className="h-5 w-5 mr-2 text-blue-500" />
            <h2 className="text-xl font-bold text-gray-900">
              AI Analysis: {getTestTypeDisplay(analysis.testType)}
            </h2>
          </div>
          <p className="text-sm text-gray-500 mt-1">
            Set: {analysis.setName} • Generated on:{" "}
            {formatDate(analysis.responseTimestamp)} • Provider:{" "}
            {analysis.aiProvider === "deepseek"
              ? "DeepSeek AI"
              : "Military Psychologist"}
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <button
            onClick={() => setExpanded(!expanded)}
            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
          >
            {expanded ? "Show Less" : "Show More"}
          </button>

          {onDelete && (
            <>
              {showDeleteConfirm ? (
                <div className="flex items-center space-x-2">
                  <button
                    onClick={handleDelete}
                    disabled={isDeleting}
                    className="text-red-600 hover:text-red-800 text-sm font-medium"
                  >
                    {isDeleting ? "Deleting..." : "Confirm"}
                  </button>
                  <button
                    onClick={() => setShowDeleteConfirm(false)}
                    disabled={isDeleting}
                    className="text-gray-600 hover:text-gray-800 text-sm font-medium"
                  >
                    Cancel
                  </button>
                </div>
              ) : (
                <button
                  onClick={() => setShowDeleteConfirm(true)}
                  className="text-gray-500 hover:text-red-600 flex items-center text-sm"
                >
                  <TrashIcon className="h-4 w-4 mr-1" />
                  Delete
                </button>
              )}
            </>
          )}
        </div>
      </div>

      <div className="space-y-6">
        {/* Overall Score */}
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">
            Overall Military Aptitude
          </h3>
          <div className="text-2xl font-bold text-blue-600">
            {Math.round(result.overallScore / 10)}/10
          </div>
        </div>

        {/* Progress bar for overall score */}
        <div className="w-full bg-gray-200 rounded-full h-3">
          <div
            className="bg-blue-600 h-3 rounded-full"
            style={{ width: `${result.overallScore}%` }}
          ></div>
        </div>

        {/* Strengths and Improvements sections removed as requested */}

        {/* Response Quality Assessment - always visible if available */}
        {result.responseQualityAssessment &&
          Object.keys(result.responseQualityAssessment).length > 0 && (
            <div className="mt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-3">
                Response Quality Assessment
              </h3>
              <div className="space-y-4">
                {Object.entries(result.responseQualityAssessment).map(
                  ([index, assessment]) => (
                    <div
                      key={index}
                      className={`border rounded-md p-4 ${
                        assessment.quality === "Good"
                          ? "border-green-200 bg-green-50"
                          : "border-red-200 bg-red-50"
                      }`}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center">
                          <span className="text-sm font-medium mr-2">
                            Response {parseInt(index) + 1}:
                          </span>
                          <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              assessment.quality === "Good"
                                ? "bg-green-100 text-green-800"
                                : "bg-red-100 text-red-800"
                            }`}
                          >
                            {assessment.quality}
                          </span>
                        </div>
                      </div>

                      <div className="text-sm text-gray-700 mb-2 italic">
                        "{assessment.response}..."
                      </div>

                      {assessment.quality === "Bad" &&
                        assessment.improvementSuggestions && (
                          <div className="mt-2">
                            <h4 className="text-sm font-medium text-gray-900 mb-1">
                              Improvement Suggestions:
                            </h4>
                            <ul className="list-disc pl-5 space-y-1 text-sm">
                              {assessment.improvementSuggestions.map(
                                (suggestion, i) => (
                                  <li key={i} className="text-red-700">
                                    {suggestion}
                                  </li>
                                )
                              )}
                            </ul>
                          </div>
                        )}
                    </div>
                  )
                )}
              </div>
            </div>
          )}

        {/* Trait Scores removed as requested */}

        {/* Detailed Feedback - always visible */}
        {result.detailedFeedback && (
          <div className="mt-6">
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Detailed Feedback
            </h3>
            <div className="bg-gray-50 rounded-md p-4">
              <p className="text-gray-700 whitespace-pre-line">
                {result.detailedFeedback}
              </p>
            </div>
          </div>
        )}

        {/* AI Generated Notice */}
        <div className="text-xs text-gray-500 mt-4 flex items-center">
          <SparklesIcon className="h-3 w-3 mr-1" />
          {result.isAIGenerated
            ? `This analysis was generated using ${
                analysis.aiProvider === "deepseek"
                  ? "DeepSeek AI"
                  : "Military Psychologist"
              } technology.`
            : `This is a basic analysis. AI analysis feature is currently not available.`}
        </div>
      </div>
    </div>
  );
}
