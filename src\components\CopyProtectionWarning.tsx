"use client";

import { useState, useEffect } from "react";
import { XMarkIcon } from "@heroicons/react/24/outline";

export default function CopyProtectionWarning() {
  const [showWarning, setShowWarning] = useState(false);
  const [warningMessage, setWarningMessage] = useState("");

  useEffect(() => {
    // Listen for copy attempts
    const handleCopy = (e: ClipboardEvent) => {
      e.preventDefault();
      setWarningMessage("Copying content is not allowed on this website.");
      setShowWarning(true);
    };

    // Listen for right-click attempts
    const handleContextMenu = (e: MouseEvent) => {
      if (
        e.target instanceof HTMLInputElement ||
        e.target instanceof HTMLTextAreaElement
      ) {
        // Allow right-click in form fields
        return true;
      }

      setWarningMessage("Action not allowed.");
      setShowWarning(true);
    };

    // Listen for print attempts
    const handleBeforePrint = () => {
      setWarningMessage("Printing this page is not allowed.");
      setShowWarning(true);
    };

    // Add event listeners
    document.addEventListener("copy", handleCopy);
    document.addEventListener("contextmenu", handleContextMenu);
    window.addEventListener("beforeprint", handleBeforePrint);

    // Auto-hide warning after 3 seconds
    let timeoutId: NodeJS.Timeout;
    if (showWarning) {
      timeoutId = setTimeout(() => {
        setShowWarning(false);
      }, 3000);
    }

    // Clean up event listeners
    return () => {
      document.removeEventListener("copy", handleCopy);
      document.removeEventListener("contextmenu", handleContextMenu);
      window.removeEventListener("beforeprint", handleBeforePrint);
      clearTimeout(timeoutId);
    };
  }, [showWarning]);

  if (!showWarning) return null;

  return (
    <div className="fixed bottom-4 right-4 z-50 bg-red-600 text-white px-4 py-3 rounded-lg shadow-lg max-w-md animate-fade-in">
      <div className="flex items-start">
        <div className="flex-1">
          <p className="font-medium">{warningMessage}</p>
          <p className="text-sm mt-1 text-red-100">
            This content is protected for security reasons.
          </p>
        </div>
        <button
          onClick={() => setShowWarning(false)}
          className="ml-4 text-white hover:text-red-100"
        >
          <XMarkIcon className="h-5 w-5" />
        </button>
      </div>
    </div>
  );
}
