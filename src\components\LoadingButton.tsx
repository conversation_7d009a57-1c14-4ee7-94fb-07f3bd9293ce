"use client";

import { useState, useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import Link from "next/link";

interface LoadingButtonProps {
  onClick?: () => void;
  href?: string;
  className?: string;
  children: React.ReactNode;
  loadingText?: string;
  disabled?: boolean;
  type?: "button" | "submit" | "reset";
}

export default function LoadingButton({
  onClick,
  href,
  className = "",
  children,
  loadingText = "Loading...",
  disabled = false,
  type = "button",
}: LoadingButtonProps) {
  const router = useRouter();
  const pathname = usePathname();
  const [isLoading, setIsLoading] = useState(false);
  const [targetPath, setTargetPath] = useState<string | null>(null);

  // Effect to detect navigation completion
  useEffect(() => {
    // If we're loading and have a target path that doesn't match the current path,
    // then navigation has completed
    if (isLoading && targetPath && !pathname.includes(targetPath)) {
      setIsLoading(false);
      setTargetPath(null);
    }
  }, [pathname, isLoading, targetPath]);

  // If href is provided, render a Link component
  if (href) {
    const handleLinkClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
      if (disabled || isLoading) {
        e.preventDefault();
        return;
      }

      // Start loading state
      setIsLoading(true);
      setTargetPath(href);

      // Execute any additional onClick handler if provided
      if (onClick) {
        onClick();
      }

      // Let the Link component handle the navigation
      // Set a fallback timeout to clear loading state
      setTimeout(() => {
        setIsLoading(false);
      }, 800);
    };

    return (
      <Link
        href={href}
        onClick={handleLinkClick}
        className={`${className} ${
          isLoading || disabled ? "opacity-90 cursor-wait" : ""
        }`}
      >
        {isLoading ? (
          <div className="flex items-center justify-center">
            <div className="animate-spin -ml-1 mr-2 h-5 w-5 text-current">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                className="w-5 h-5"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
            </div>
            <span>{loadingText}</span>
          </div>
        ) : (
          children
        )}
      </Link>
    );
  }

  // Otherwise, render a regular button
  const handleButtonClick = () => {
    if (disabled || isLoading) return;

    // Start loading state
    setIsLoading(true);

    // Call the provided onClick handler
    if (onClick) {
      Promise.resolve(onClick())
        .catch((error) =>
          console.error("Error in button click handler:", error)
        )
        .finally(() => {
          // Reset loading state after a shorter timeout
          setTimeout(() => {
            setIsLoading(false);
          }, 800);
        });
    }
  };

  return (
    <button
      type={type}
      onClick={handleButtonClick}
      disabled={disabled || isLoading}
      className={`${className} ${isLoading ? "opacity-90 cursor-wait" : ""}`}
    >
      {isLoading ? (
        <div className="flex items-center justify-center">
          <div className="animate-spin -ml-1 mr-2 h-5 w-5 text-current">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              className="w-5 h-5"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              ></circle>
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
          </div>
          <span>{loadingText}</span>
        </div>
      ) : (
        children
      )}
    </button>
  );
}
