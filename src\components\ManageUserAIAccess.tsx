"use client";

import { useState } from "react";
import { grantAIAccess, revokeAIAccess } from "@/utils/adminUtils";
import {
  SparklesIcon,
  LockOpenIcon,
  LockClosedIcon,
} from "@heroicons/react/24/outline";

interface ManageUserAIAccessProps {
  userId: string;
  hasAIAccess: boolean;
  isAdmin: boolean;
  isProcessing?: boolean;
  onAccessChanged?: () => void;
}

export default function ManageUserAIAccess({
  userId,
  hasAIAccess,
  isAdmin,
  isProcessing = false,
  onAccessChanged,
}: ManageUserAIAccessProps) {
  const [isUpdating, setIsUpdating] = useState(false);
  const [currentAccess, setCurrentAccess] = useState(hasAIAccess);
  const [error, setError] = useState<string | null>(null);

  const handleToggleAccess = async () => {
    if (isAdmin) {
      // Cannot change AI access for admin users
      setError("Admin users automatically have AI access");
      return;
    }

    setIsUpdating(true);
    setError(null);

    try {
      if (currentAccess) {
        // Revoke access
        await revokeAIAccess(userId);
        setCurrentAccess(false);
      } else {
        // Grant access
        await grantAIAccess(userId);
        setCurrentAccess(true);
      }

      // Call the callback if provided
      if (onAccessChanged) {
        onAccessChanged();
      }
    } catch (err) {
      console.error("Error updating AI access:", err);
      setError(
        err instanceof Error ? err.message : "Failed to update AI access"
      );
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <div className="mt-2">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <SparklesIcon className="h-5 w-5 mr-2 text-blue-500" />
          <span className="text-sm font-medium text-gray-700">
            AI Analysis Access
          </span>
        </div>
        <div>
          {isAdmin ? (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
              <LockOpenIcon className="h-3 w-3 mr-1" />
              Always Enabled for Admins
            </span>
          ) : (
            <button
              onClick={handleToggleAccess}
              disabled={isUpdating || isProcessing}
              className={`inline-flex items-center px-3 py-1 border rounded-md text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                currentAccess
                  ? "border-red-300 text-red-700 bg-red-50 hover:bg-red-100 focus:ring-red-500"
                  : "border-green-300 text-green-700 bg-green-50 hover:bg-green-100 focus:ring-green-500"
              } disabled:opacity-50 disabled:cursor-not-allowed`}
            >
              {isUpdating || isProcessing ? (
                <svg
                  className="animate-spin -ml-1 mr-2 h-4 w-4 text-current"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
              ) : currentAccess ? (
                <LockClosedIcon className="h-4 w-4 mr-1.5" />
              ) : (
                <LockOpenIcon className="h-4 w-4 mr-1.5" />
              )}
              {currentAccess ? "Revoke Access" : "Grant Access"}
            </button>
          )}
        </div>
      </div>
      {error && <p className="mt-1 text-sm text-red-600">{error}</p>}
      <p className="mt-1 text-xs text-gray-500">
        {isAdmin
          ? "Admin users automatically have access to AI analysis features."
          : currentAccess
          ? "This user can access AI analysis features."
          : "This user cannot access AI analysis features."}
      </p>
    </div>
  );
}
