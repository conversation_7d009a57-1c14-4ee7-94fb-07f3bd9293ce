"use client";

import React from "react";
import Image from "next/image";
import { ForumReply } from "@/types/forum";
import { useAuth } from "@/context/GoogleAuthContext";
import ReplyActions from "./ReplyActions";
import { ClockIcon } from "@heroicons/react/24/outline";

interface ReplyCardProps {
  reply: ForumReply;
  onReplyUpdated?: () => void;
}

export default function ReplyCard({ reply, onReplyUpdated }: ReplyCardProps) {
  const { user } = useAuth();

  // Format date for display
  const formatDate = (date: Date | undefined) => {
    if (!date) return "Unknown date";

    // Check if the date is today
    const today = new Date();
    const isToday =
      date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear();

    if (isToday) {
      // Format as time if today
      return `Today at ${date.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      })}`;
    } else {
      // Format as date otherwise
      return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      });
    }
  };

  // Check if the current user is the author of the reply
  const isAuthor = user && user.uid === reply.authorId;

  return (
    <div className="p-4 sm:p-6">
      <div className="flex flex-col sm:flex-row sm:items-start">
        {/* Author avatar */}
        <div className="flex-shrink-0 mb-3 sm:mb-0 sm:mr-4">
          {reply.authorPhotoURL ? (
            <Image
              src={reply.authorPhotoURL}
              alt={reply.authorName}
              width={40}
              height={40}
              className="rounded-full"
            />
          ) : (
            <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center">
              <span className="text-gray-500 font-medium">
                {reply.authorName.charAt(0).toUpperCase()}
              </span>
            </div>
          )}
        </div>

        {/* Reply content */}
        <div className="flex-1 min-w-0">
          <div className="flex flex-wrap items-center mb-2">
            <span className="font-medium text-gray-900">
              {reply.authorName}
            </span>
            {isAuthor && (
              <span className="ml-2 px-2 py-0.5 text-xs font-medium bg-indigo-100 text-indigo-800 rounded-full">
                You
              </span>
            )}
            <span className="mx-2 text-gray-500">•</span>
            <div className="flex items-center text-sm text-gray-500">
              <ClockIcon className="h-4 w-4 mr-1" />
              <span>{formatDate(reply.createdAt)}</span>
            </div>
          </div>

          <div className="prose prose-indigo max-w-none">
            <p className="text-gray-700 whitespace-pre-line">{reply.content}</p>
          </div>

          {/* Reply actions (Edit/Delete for author, Report for others) */}
          <ReplyActions
            replyId={reply.id}
            topicId={reply.topicId}
            authorId={reply.authorId}
            authorName={reply.authorName}
            content={reply.content}
            onReplyUpdated={onReplyUpdated}
          />
        </div>
      </div>
    </div>
  );
}
