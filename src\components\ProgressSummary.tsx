"use client";

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/context/GoogleAuthContext';
import { getProgressSummary } from '@/utils/progressTracker';
import {
  ChartBarIcon,
  TrophyIcon,
  ClockIcon,
  ArrowTrendingUpIcon
} from '@heroicons/react/24/outline';

interface ProgressSummaryProps {
  className?: string;
}

interface ProgressSummaryData {
  totalTests: number;
  completedTests: number;
  percentage: number;
  recentActivity: string[];
}

export default function ProgressSummary({ className = "" }: ProgressSummaryProps) {
  const { user } = useAuth();
  const [summary, setSummary] = useState<ProgressSummaryData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchSummary = async () => {
      if (!user) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const summaryData = await getProgressSummary(user.uid);
        setSummary(summaryData);
      } catch (error) {
        console.error('Error fetching progress summary:', error);
        setSummary({
          totalTests: 0,
          completedTests: 0,
          percentage: 0,
          recentActivity: []
        });
      } finally {
        setLoading(false);
      }
    };

    fetchSummary();
  }, [user]);

  if (!user) {
    return null;
  }

  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="grid grid-cols-3 gap-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!summary) {
    return null;
  }

  const getProgressColor = (percentage: number) => {
    if (percentage >= 80) return 'text-green-600 bg-green-100';
    if (percentage >= 50) return 'text-blue-600 bg-blue-100';
    if (percentage >= 25) return 'text-yellow-600 bg-yellow-100';
    return 'text-gray-600 bg-gray-100';
  };

  const getProgressBarColor = (percentage: number) => {
    if (percentage >= 80) return 'bg-green-500';
    if (percentage >= 50) return 'bg-blue-500';
    if (percentage >= 25) return 'bg-yellow-500';
    return 'bg-gray-400';
  };

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <ChartBarIcon className="h-5 w-5 mr-2 text-gray-600" />
            Test Progress
          </h3>
          <div className={`px-3 py-1 rounded-full text-sm font-medium ${getProgressColor(summary.percentage)}`}>
            {summary.percentage}% Complete
          </div>
        </div>

        {/* Progress Stats Grid */}
        <div className="grid grid-cols-3 gap-4 mb-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">
              {summary.completedTests}
            </div>
            <div className="text-sm text-gray-500">Completed</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">
              {summary.totalTests - summary.completedTests}
            </div>
            <div className="text-sm text-gray-500">Remaining</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">
              {summary.totalTests}
            </div>
            <div className="text-sm text-gray-500">Total Tests</div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">Overall Progress</span>
            <span className="text-sm text-gray-500">
              {summary.completedTests}/{summary.totalTests}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div 
              className={`h-3 rounded-full transition-all duration-500 ${getProgressBarColor(summary.percentage)}`}
              style={{ width: `${summary.percentage}%` }}
            ></div>
          </div>
        </div>

        {/* Recent Activity */}
        {summary.recentActivity.length > 0 && (
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
              <ClockIcon className="h-4 w-4 mr-2" />
              Recent Activity
            </h4>
            <div className="space-y-2">
              {summary.recentActivity.slice(0, 3).map((activity, index) => (
                <div 
                  key={index}
                  className="flex items-center text-sm text-gray-600 bg-gray-50 rounded-lg px-3 py-2"
                >
                  <ArrowTrendingUpIcon className="h-4 w-4 mr-2 text-green-500" />
                  <span className="truncate">{activity}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Achievement Badge */}
        {summary.percentage > 0 && (
          <div className="mt-6 pt-4 border-t border-gray-200">
            <div className="flex items-center justify-center">
              {summary.percentage >= 75 ? (
                <div className="flex items-center text-yellow-600 bg-yellow-50 px-3 py-2 rounded-full">
                  <TrophyIcon className="h-4 w-4 mr-2" />
                  <span className="text-sm font-medium">High Achiever</span>
                </div>
              ) : summary.percentage >= 50 ? (
                <div className="flex items-center text-blue-600 bg-blue-50 px-3 py-2 rounded-full">
                  <ArrowTrendingUpIcon className="h-4 w-4 mr-2" />
                  <span className="text-sm font-medium">Making Progress</span>
                </div>
              ) : summary.percentage >= 25 ? (
                <div className="flex items-center text-green-600 bg-green-50 px-3 py-2 rounded-full">
                  <ChartBarIcon className="h-4 w-4 mr-2" />
                  <span className="text-sm font-medium">Getting Started</span>
                </div>
              ) : (
                <div className="flex items-center text-gray-600 bg-gray-50 px-3 py-2 rounded-full">
                  <ClockIcon className="h-4 w-4 mr-2" />
                  <span className="text-sm font-medium">Just Beginning</span>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
