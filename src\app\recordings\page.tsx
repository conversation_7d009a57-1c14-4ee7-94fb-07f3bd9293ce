"use client";

import { useState } from "react";
import Link from "next/link";
import { ArrowLeftIcon, MicrophoneIcon } from "@heroicons/react/24/outline";
import StoredRecordingsViewer from "@/components/StoredRecordingsViewer";
import { useAuth } from "@/context/GoogleAuthContext";

export default function RecordingsPage() {
  const { user } = useAuth();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link
                href="/profile"
                className="inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700"
              >
                <ArrowLeftIcon className="h-4 w-4 mr-2" />
                Back to Profile
              </Link>
            </div>
            <div className="flex items-center space-x-3">
              <MicrophoneIcon className="h-6 w-6 text-indigo-600" />
              <h1 className="text-xl font-semibold text-gray-900">
                My Recordings
              </h1>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {user ? (
          <div className="space-y-6">
            {/* Welcome Section */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center space-x-4">
                <div className="flex-shrink-0">
                  <div className="h-12 w-12 rounded-full bg-indigo-100 flex items-center justify-center">
                    <MicrophoneIcon className="h-6 w-6 text-indigo-600" />
                  </div>
                </div>
                <div>
                  <h2 className="text-lg font-semibold text-gray-900">
                    Welcome back, {user.displayName || user.email}!
                  </h2>
                  <p className="text-sm text-gray-600">
                    Manage your saved audio recordings from practice sessions
                  </p>
                </div>
              </div>
            </div>

            {/* Recordings Viewer */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <StoredRecordingsViewer />
            </div>

            {/* Help Section */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-blue-900 mb-3">
                About Your Recordings
              </h3>
              <div className="space-y-2 text-sm text-blue-800">
                <p>
                  • <strong>Storage:</strong> Recordings are stored locally in
                  your browser
                </p>
                <p>
                  • <strong>Privacy:</strong> Your recordings never leave your
                  device unless you download them
                </p>
                <p>
                  • <strong>Persistence:</strong> Recordings will be available
                  across browser sessions
                </p>
                <p>
                  • <strong>Backup:</strong> Download important recordings to
                  keep them safe
                </p>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Quick Actions
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <Link
                  href="/tests/io"
                  className="inline-flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                >
                  IO Practice
                </Link>
                <Link
                  href="/tests/gto/lecturette"
                  className="inline-flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                >
                  Lecturette
                </Link>
                <Link
                  href="/tests/gto/gpe"
                  className="inline-flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                >
                  GPE Practice
                </Link>
                <Link
                  href="/tests/gto/gd/practice"
                  className="inline-flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                >
                  GD Practice
                </Link>
                <Link
                  href="/tests/bc"
                  className="inline-flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                >
                  BC Practice
                </Link>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-12">
            <MicrophoneIcon className="h-16 w-16 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Sign In Required
            </h3>
            <p className="text-gray-500 mb-6">
              Please sign in to view and manage your recordings.
            </p>
            <Link
              href="/auth/signin"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
            >
              Sign In
            </Link>
          </div>
        )}
      </div>
    </div>
  );
}
