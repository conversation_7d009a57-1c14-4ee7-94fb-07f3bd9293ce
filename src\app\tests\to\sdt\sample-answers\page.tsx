"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import {
  ArrowLeftIcon,
  BookOpenIcon,
  ChevronDownIcon,
  ChevronUpIcon,
} from "@heroicons/react/24/outline";
import SimpleButton from "@/components/SimpleButton";
import { sdtSampleAnswers } from "@/data/sdt_sample_answers";

export default function SDTSampleAnswers() {
  const router = useRouter();
  const [expandedItems, setExpandedItems] = useState<number[]>([0]); // First item expanded by default

  // Toggle expanded state for an item - only one open at a time
  const toggleItem = (index: number) => {
    if (expandedItems.includes(index)) {
      setExpandedItems([]);
    } else {
      setExpandedItems([index]);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-6 sm:py-12">
      <div className="mx-auto max-w-4xl px-4 sm:px-6">
        <SimpleButton
          onClick={() =>
            router.push("/tests/to/sdt/practice/SelfDescription/instructions")
          }
          className="mb-4 sm:mb-8 flex items-center text-gray-600 hover:text-gray-900"
        >
          <ArrowLeftIcon className="h-5 w-5 mr-2" />
          <span className="text-sm sm:text-base">Back to SDT Instructions</span>
        </SimpleButton>

        <div className="bg-white rounded-xl shadow-sm overflow-hidden">
          <div className="p-6 sm:p-8 border-b">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4">
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">
                Self Description Test - Sample Answers
              </h1>
              <SimpleButton
                onClick={() =>
                  router.push("/tests/to/sdt/practice/SelfDescription/test")
                }
                className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-amber-600 hover:bg-amber-700 transition-colors"
              >
                Start SDT Test
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 ml-2"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </SimpleButton>
            </div>

            <div className="mb-6 sm:mb-8 text-gray-700">
              <p className="mb-4 text-sm sm:text-base">
                This page provides sample answers for the Self Description Test
                (SDT), which evaluates your self-awareness and how you perceive
                yourself in relation to others. Each sample demonstrates
                effective responses to the standard SDT questions.
              </p>
              <div className="bg-amber-50 p-3 sm:p-4 rounded-lg border border-amber-200 mb-4">
                <h3 className="font-semibold text-amber-800 mb-2 text-sm sm:text-base">
                  How to use these samples:
                </h3>
                <ul className="list-disc list-inside space-y-1 text-amber-800 text-xs sm:text-sm">
                  <li>Study the pattern of honest self-reflection</li>
                  <li>
                    Notice how responses balance strengths and areas for
                    improvement
                  </li>
                  <li>
                    Observe how the descriptions reflect leadership and military
                    values
                  </li>
                  <li>
                    Use these as inspiration, but write your own authentic
                    responses
                  </li>
                </ul>
              </div>
            </div>

            {/* Sample answers accordion */}
            <div className="space-y-4">
              {sdtSampleAnswers.map((item, index) => (
                <div key={index} className="border rounded-lg overflow-hidden">
                  <button
                    onClick={() => toggleItem(index)}
                    className="w-full flex justify-between items-center p-4 bg-amber-50 hover:bg-amber-100 transition-colors text-left"
                  >
                    <h3 className="text-lg font-semibold text-gray-900">
                      {item.title}
                    </h3>
                    {expandedItems.includes(index) ? (
                      <ChevronUpIcon className="h-5 w-5 text-amber-600" />
                    ) : (
                      <ChevronDownIcon className="h-5 w-5 text-amber-600" />
                    )}
                  </button>

                  {expandedItems.includes(index) && (
                    <div className="p-4 bg-white">
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-semibold text-amber-700 mb-2">
                            What do your parents think of you?
                          </h4>
                          <p className="text-gray-700 text-sm">
                            {item.sections.parents}
                          </p>
                        </div>

                        <div>
                          <h4 className="font-semibold text-amber-700 mb-2">
                            What do your teachers/employers think of you?
                          </h4>
                          <p className="text-gray-700 text-sm">
                            {item.sections.teachers}
                          </p>
                        </div>

                        <div>
                          <h4 className="font-semibold text-amber-700 mb-2">
                            What do your friends/colleagues think of you?
                          </h4>
                          <p className="text-gray-700 text-sm">
                            {item.sections.friends}
                          </p>
                        </div>

                        <div>
                          <h4 className="font-semibold text-amber-700 mb-2">
                            What do you think about yourself?
                          </h4>
                          <p className="text-gray-700 text-sm">
                            {item.sections.self}
                          </p>
                        </div>

                        <div>
                          <h4 className="font-semibold text-amber-700 mb-2">
                            What kind of person would you like to become or what
                            improvements you want to bring in yourself?
                          </h4>
                          <p className="text-gray-700 text-sm">
                            {item.sections.aims}
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Tips section */}
        <div className="mt-8 sm:mt-12 bg-gray-100 p-4 sm:p-6 rounded-md sm:rounded-lg">
          <h2 className="text-lg sm:text-xl font-bold text-gray-900 mb-3 sm:mb-4">
            Tips for Effective SDT Responses
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
            <div>
              <h3 className="font-semibold text-gray-800 mb-2 text-sm sm:text-base">
                Do:
              </h3>
              <ul className="list-disc list-inside space-y-1 text-gray-700 text-xs sm:text-sm">
                <li>Be honest and authentic in your responses</li>
                <li>Demonstrate self-awareness and introspection</li>
                <li>Balance strengths with areas for improvement</li>
                <li>Show consistency across all sections</li>
                <li>Include specific examples to support your claims</li>
              </ul>
            </div>
            <div className="mt-3 sm:mt-0">
              <h3 className="font-semibold text-gray-800 mb-2 text-sm sm:text-base">
                Don't:
              </h3>
              <ul className="list-disc list-inside space-y-1 text-gray-700 text-xs sm:text-sm">
                <li>Exaggerate or fabricate qualities</li>
                <li>Present an unrealistically perfect image</li>
                <li>Contradict yourself across different sections</li>
                <li>Focus only on weaknesses or only on strengths</li>
                <li>Use vague statements without supporting details</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
