"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import { useRouter } from "next/navigation";
import NavigationButton from "@/components/NavigationButton";
import LoadingButton from "@/components/LoadingButton";
import SimpleButton from "@/components/SimpleButton";
import { useLoading } from "@/context/LoadingContext";
import { useAuth } from "@/context/GoogleAuthContext";
import { use } from "react";
import {
  PlayIcon,
  PauseIcon,
  ArrowRightIcon,
  CheckIcon,
} from "@heroicons/react/24/outline";
import { tatSets } from "../../../../../../../data/tat-sets";
import Image from "next/image";
import TestTimer from "@/components/TestTimer";
import { saveResponses } from "@/services/responseService";

export default function TATTest({
  params,
}: {
  params: Promise<{ set: string }>;
}) {
  const router = useRouter();
  const { startLoading } = useLoading();
  const { user } = useAuth();
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [timeLeft, setTimeLeft] = useState(30); // 30 seconds to view image
  const [storyTimeLeft, setStoryTimeLeft] = useState(240); // 4 minutes to write story
  const storyEndTimeRef = useRef<number | null>(null); // Store the end time as a ref
  const [isPaused, setIsPaused] = useState(false);
  const [isCompleted] = useState(false);
  const [isViewingImage, setIsViewingImage] = useState(true); // Start with viewing image
  const [response, setResponse] = useState("");
  const [responses, setResponses] = useState<string[]>([]);
  const [isSaving, setIsSaving] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { set } = use(params);
  const setName = decodeURIComponent(set);
  const images = tatSets[setName]?.images || [];

  // Keep a stable reference to the latest responses
  const responsesRef = useRef(responses);
  useEffect(() => {
    responsesRef.current = responses;
  }, [responses]);

  // Handle next image only (navigation)
  const handleNext = useCallback(() => {
    const newResponses = [...responsesRef.current];
    newResponses[currentImageIndex] = response;
    setResponses(newResponses);

    if (currentImageIndex < images.length - 1) {
      setCurrentImageIndex((prev) => prev + 1);
      setTimeLeft(30);
      setStoryTimeLeft(240);
      setIsViewingImage(true);
      setResponse("");
      // Reset story end time ref (will be set again when switching to story mode)
      storyEndTimeRef.current = null;
    }
  }, [currentImageIndex, response, images.length]);

  // Handle final submission
  const handleSubmit = useCallback(async () => {
    if (isSubmitting) return;
    setIsSubmitting(true);

    // Stop the timer by setting isPaused to true and clearing the timer end reference
    setIsPaused(true);
    storyEndTimeRef.current = null;

    console.log("Submitting final responses...");

    // Save the current response first
    const newResponses = [...responsesRef.current];
    newResponses[currentImageIndex] = response;
    setResponses(newResponses);

    // Save to localStorage with set-specific key
    localStorage.setItem(
      `tat_responses_${setName}`,
      JSON.stringify(newResponses)
    );
    localStorage.setItem("tat_responses", JSON.stringify(newResponses)); // Keep for backward compatibility
    localStorage.setItem("tat_current_set", setName);

    // Save to cloud storage if user is logged in
    if (user) {
      setIsSaving(true);
      try {
        const timestamp = await saveResponses(
          user.uid,
          "tat",
          setName,
          newResponses
        );
        console.log("Saved responses to Firestore, timestamp:", timestamp);
        // Save the timestamp to localStorage to prevent duplicate saves
        localStorage.setItem(
          `tat_${setName}_last_save_timestamp`,
          timestamp.toString()
        );
      } catch (error) {
        console.error("Error saving responses to cloud:", error);
        // Continue anyway since we have localStorage backup
      } finally {
        setIsSaving(false);
      }
    }

    startLoading();
    router.push(`/tests/to/tat/practice/${set}/results`);
  }, [
    currentImageIndex,
    response,
    router,
    set,
    setName,
    user,
    startLoading,
    isSubmitting,
  ]);

  // Switch from viewing image to writing story
  const switchToStoryMode = useCallback(() => {
    setIsViewingImage(false);
    // Set the end time to 4 minutes (240 seconds) from now
    storyEndTimeRef.current = Date.now() + 240 * 1000;
    setStoryTimeLeft(240); // Reset to 4 minutes
  }, []);

  // Update time left from timer component
  const updateImageTimeLeft = useCallback((time: number) => {
    setTimeLeft(time);
  }, []);

  // We don't need updateStoryTimeLeft anymore as we're using our custom timer

  // Custom timer for story mode that won't reset on re-renders
  useEffect(() => {
    // Only run when in story mode and not paused
    if (isViewingImage || isPaused || !storyEndTimeRef.current) {
      return;
    }

    const intervalId = setInterval(() => {
      const now = Date.now();
      const endTime = storyEndTimeRef.current as number;
      const remaining = Math.max(0, Math.floor((endTime - now) / 1000));

      setStoryTimeLeft(remaining);

      if (remaining === 0) {
        clearInterval(intervalId);

        // If not on the last image, automatically move to the next one
        if (currentImageIndex < images.length - 1) {
          handleNext();
        }
        // If on the last image, just let the timer expire and wait for manual submission
      }
    }, 100);

    return () => clearInterval(intervalId);
  }, [isViewingImage, isPaused, handleNext, currentImageIndex, images.length]);

  // Prevent Enter key from triggering any navigation in the TAT section
  useEffect(() => {
    const preventEnterNavigation = (event: KeyboardEvent) => {
      // If the key is Enter, prevent default behavior in both image viewing and story writing modes
      if (event.key === "Enter" && !isPaused && !isCompleted) {
        // Only prevent if the target is not the textarea (already handled by textarea's onKeyDown)
        if (!(event.target instanceof HTMLTextAreaElement)) {
          event.stopPropagation();
          event.preventDefault();
        }
      }
    };

    // Add the event listener at the document level to catch all Enter key presses
    document.addEventListener("keydown", preventEnterNavigation, true);

    // Clean up
    return () => {
      document.removeEventListener("keydown", preventEnterNavigation, true);
    };
  }, [isPaused, isCompleted]);

  const pausedTimeRef = useRef<number>(0);

  const handlePause = () => {
    const newPausedState = !isPaused;
    setIsPaused(newPausedState);

    // Handle story timer pausing
    if (!isViewingImage) {
      if (newPausedState) {
        // Pausing - store the remaining time
        pausedTimeRef.current = storyTimeLeft;
      } else {
        // Resuming - recalculate the end time
        storyEndTimeRef.current = Date.now() + pausedTimeRef.current * 1000;
      }
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  // Use the timer component for image viewing only
  const imageTimerActive = !isCompleted && isViewingImage;

  // Hidden timer component for image viewing
  const imageTimer = (
    <TestTimer
      initialTime={30}
      isPaused={isPaused}
      isActive={imageTimerActive}
      onTimeUp={switchToStoryMode}
      onTimeUpdate={updateImageTimeLeft}
    />
  );

  if (isCompleted) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="mx-auto max-w-3xl px-6">
          <div className="bg-white rounded-2xl shadow-sm p-8 text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Test Completed
            </h2>
            <p className="text-gray-600 mb-8">
              Thank you for completing the TAT practice test.
            </p>
            <LoadingButton
              onClick={() => router.push("/tests/to")}
              className="inline-flex justify-center rounded-md bg-red-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-700"
              loadingText="Loading..."
            >
              Return to Tests
            </LoadingButton>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      {/* Hidden timer */}
      <div className="hidden">{imageTimer}</div>

      <div className="mx-auto max-w-3xl px-6">
        <div className="bg-white rounded-2xl shadow-sm p-8">
          <div className="flex justify-between items-center mb-8">
            <div className="text-2xl font-bold text-gray-900">
              Image {currentImageIndex + 1} of {images.length}
            </div>
            <div className="text-2xl font-bold text-indigo-600">
              {isViewingImage ? `${timeLeft}s` : formatTime(storyTimeLeft)}
            </div>
          </div>

          {isViewingImage ? (
            <div className="text-center mb-8">
              <div className="relative w-full h-64 md:h-96 mb-4 bg-gray-100 rounded-lg overflow-hidden">
                {images[currentImageIndex] === "blank" ? (
                  <div className="absolute inset-0 flex items-center justify-center bg-gray-200">
                    <p className="text-gray-500 text-lg font-medium">
                      Blank Image
                    </p>
                  </div>
                ) : (
                  <Image
                    src={images[currentImageIndex]}
                    alt={`TAT Image ${currentImageIndex + 1}`}
                    fill
                    style={{ objectFit: "contain" }}
                  />
                )}
              </div>
              <div>
                <p className="text-gray-600">
                  Study this image carefully. It will be hidden when the timer
                  ends.
                </p>
                <p className="text-sm text-red-500 mt-2">
                  <strong>Note:</strong> Use the 'Write Story' button to
                  proceed. Do not use the Enter key.
                </p>
              </div>
            </div>
          ) : (
            <div className="mb-8">
              <label
                htmlFor="response"
                className="block text-lg font-medium text-gray-900 mb-2"
              >
                Write your story about the image:
              </label>
              <textarea
                id="response"
                rows={10}
                className="block w-full rounded-md border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 allow-select"
                placeholder="Include what led up to the scene, what is happening now, what the characters are thinking and feeling, and what will happen next..."
                value={response}
                onChange={(e) => setResponse(e.target.value)}
                onKeyDown={(e) => {
                  // Prevent Enter key from triggering navigation
                  if (e.key === "Enter") {
                    e.stopPropagation();
                  }
                }}
              ></textarea>
            </div>
          )}

          {!isViewingImage && (
            <div className="mb-4">
              <p className="text-sm text-gray-500">
                {currentImageIndex < images.length - 1
                  ? "Click 'Next Image' when you've finished writing your story"
                  : "Click Submit to complete the test and view your results"}
              </p>
              {currentImageIndex < images.length - 1 && (
                <p className="text-sm text-red-500 mt-1">
                  <strong>Note:</strong> Use Enter key for line breaks only. To
                  navigate to the next image, click the 'Next Image' button.
                </p>
              )}
            </div>
          )}

          <div className="flex justify-between items-center">
            <SimpleButton
              onClick={handlePause}
              className="rounded-md bg-gray-100 px-4 py-2 text-sm font-semibold text-gray-600 hover:bg-gray-200"
            >
              {isPaused ? (
                <>
                  <PlayIcon className="h-5 w-5 inline-block mr-2" />
                  Resume
                </>
              ) : (
                <>
                  <PauseIcon className="h-5 w-5 inline-block mr-2" />
                  Pause
                </>
              )}
            </SimpleButton>

            {isViewingImage ? (
              // When viewing image, show button to proceed to story writing
              <SimpleButton
                onClick={switchToStoryMode}
                className="inline-flex items-center justify-center rounded-md bg-red-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                // Explicitly prevent keyboard shortcuts for this button
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    e.stopPropagation();
                  }
                }}
              >
                Write Story
                <ArrowRightIcon className="h-4 w-4 ml-2" />
              </SimpleButton>
            ) : currentImageIndex < images.length - 1 ? (
              // When writing story for non-last image, show Next Image button
              <SimpleButton
                onClick={handleNext}
                className="inline-flex items-center justify-center rounded-md bg-red-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                // Explicitly prevent keyboard shortcuts for this button
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    e.stopPropagation();
                  }
                }}
              >
                Next Image
                <ArrowRightIcon className="h-4 w-4 ml-2" />
              </SimpleButton>
            ) : (
              // When writing story for last image, show Submit button
              <SimpleButton
                onClick={handleSubmit}
                disabled={isSubmitting}
                className="inline-flex items-center justify-center rounded-md bg-red-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? "Submitting..." : "Submit Test"}
                <CheckIcon className="h-4 w-4 ml-2" />
              </SimpleButton>
            )}
          </div>

          {isPaused && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <div className="bg-white p-8 rounded-lg max-w-md w-full">
                <h3 className="text-xl font-bold text-gray-900 mb-4">
                  Test Paused
                </h3>
                <p className="text-gray-600 mb-6">
                  The timer has been paused. Click resume when you&apos;re ready
                  to continue.
                </p>
                <SimpleButton
                  onClick={handlePause}
                  className="w-full rounded-md bg-red-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-700"
                >
                  <PlayIcon className="h-5 w-5 inline-block mr-2" />
                  Resume Test
                </SimpleButton>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
