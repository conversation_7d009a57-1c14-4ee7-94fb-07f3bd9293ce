import Link from "next/link";
import { ArrowLeftIcon } from "@heroicons/react/24/outline";

export default function NotFound() {
  // Static quote instead of random selection to avoid client-side logic
  const quote = "Error 404: Target not acquired. Requesting backup.";

  return (
    <div className="min-h-screen bg-gray-100 py-12 flex flex-col items-center justify-center">
      <div className="mx-auto max-w-3xl px-6 text-center">
        <div className="relative h-64 w-64 mx-auto mb-8">
          <img
            src="/images/military-404.png"
            alt="Military officer looking confused with binoculars"
            className="w-full h-full object-contain"
          />
        </div>

        <div className="bg-white rounded-2xl shadow-md p-8 border-t-4 border-indigo-700">
          <h1 className="text-5xl font-bold text-gray-900 mb-4">
            ERROR <span className="text-indigo-700">404</span>
          </h1>

          <div className="bg-gray-100 p-4 rounded-lg border-l-4 border-amber-500 mb-6">
            <p className="text-xl font-medium text-gray-800 italic">
              {`"${quote}"`}
            </p>
          </div>

          <p className="text-lg text-gray-700 mb-8">
            {`The page you're looking for has been reassigned or doesn't exist in our records.`}
            <br />
            Please return to base and try a different approach.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/"
              className="inline-flex items-center justify-center rounded-md bg-indigo-700 px-6 py-3 text-base font-semibold text-white shadow-sm hover:bg-indigo-600 transition-colors"
            >
              <ArrowLeftIcon className="h-5 w-5 mr-2" />
              Return to Base
            </Link>

            <Link
              href="/tests/to"
              className="inline-flex items-center justify-center rounded-md bg-gray-200 px-6 py-3 text-base font-semibold text-gray-800 shadow-sm hover:bg-gray-300 transition-colors"
            >
              Begin Training
            </Link>
          </div>
        </div>

        <div className="mt-8 flex items-center justify-center">
          <div className="h-px w-12 bg-gray-300"></div>
          <p className="mx-4 text-sm text-gray-500">
            Military Selection Test Prep
          </p>
          <div className="h-px w-12 bg-gray-300"></div>
        </div>
      </div>
    </div>
  );
}
