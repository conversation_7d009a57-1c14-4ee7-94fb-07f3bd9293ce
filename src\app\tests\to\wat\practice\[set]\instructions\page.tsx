"use client";

import { useRouter } from "next/navigation";
import NavigationButton from "@/components/NavigationButton";
import SimpleButton from "@/components/SimpleButton";
import {
  ArrowLeftIcon,
  PlayIcon,
  BookOpenIcon,
} from "@heroicons/react/24/outline";
import { use } from "react";

export default function WATInstructions({
  params,
}: {
  params: Promise<{ set: string }>;
}) {
  const router = useRouter();
  const { set } = use(params);
  const setName = decodeURIComponent(set);

  // Using SimpleButton with href instead of onClick for better performance
  const testUrl = `/tests/to/wat/practice/${set}/test`;

  return (
    <div className="min-h-screen bg-gray-50 py-6 sm:py-12">
      <div className="mx-auto max-w-3xl px-4 sm:px-6">
        <SimpleButton
          onClick={() => router.back()}
          className="mb-4 sm:mb-8 flex items-center text-gray-600 hover:text-gray-900"
        >
          <ArrowLeftIcon className="h-5 w-5 mr-2" />
          <span className="text-sm sm:text-base">Back to Set Selection</span>
        </SimpleButton>

        <div className="bg-white rounded-xl sm:rounded-2xl shadow-sm p-4 sm:p-6 md:p-8">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-4 sm:mb-6">
            Instructions for {setName}
          </h1>

          <div className="space-y-4 sm:space-y-6 text-gray-700">
            <div>
              <h2 className="text-lg sm:text-xl font-semibold mb-1 sm:mb-2">
                Test Format:
              </h2>
              <ul className="list-disc list-inside space-y-1 sm:space-y-2 text-sm sm:text-base">
                <li>You will be shown 60 words one at a time</li>
                <li>Each word will be displayed for 15 seconds</li>
                <li>
                  Write your immediate thought or association for each word
                </li>
                <li>Be honest and write your first natural response</li>
                <li>Focus on emotional and personal responses</li>
              </ul>
            </div>

            <div>
              <h2 className="text-lg sm:text-xl font-semibold mb-1 sm:mb-2">
                Tips:
              </h2>
              <ul className="list-disc list-inside space-y-1 sm:space-y-2 text-sm sm:text-base">
                <li>Don&apos;t overthink your responses</li>
                <li>Be consistent in your writing style</li>
                <li>Stay focused and maintain concentration</li>
                <li>Use the full time if needed</li>
                <li>You can pause the test at any time</li>
                <li>You can stop the test early if needed</li>
              </ul>
            </div>

            {/* Sample Answers Link */}
            <div className="bg-amber-50 p-3 sm:p-4 rounded-lg border border-amber-200">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-0">
                <div className="flex items-start sm:items-center">
                  <BookOpenIcon className="h-5 w-5 sm:h-6 sm:w-6 text-amber-600 mr-2 sm:mr-3 mt-0.5 sm:mt-0 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-amber-800 mb-0.5 sm:mb-1 text-sm sm:text-base">
                      Need examples of good responses?
                    </h3>
                    <p className="text-xs sm:text-sm text-amber-700">
                      View sample associations to understand effective response
                      patterns
                    </p>
                  </div>
                </div>
                <SimpleButton
                  onClick={() => router.push("/tests/to/wat/sample-answers")}
                  className="w-full sm:w-auto px-3 sm:px-4 py-1.5 sm:py-2 text-xs sm:text-sm bg-amber-600 text-white rounded hover:bg-amber-700 transition-colors text-center"
                >
                  View Samples
                </SimpleButton>
              </div>
            </div>

            <div className="text-center mt-6 sm:mt-8">
              <SimpleButton
                href={testUrl}
                className="inline-flex justify-center rounded-md bg-green-600 px-4 sm:px-6 py-2 sm:py-3 text-sm sm:text-base font-semibold text-white shadow-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
              >
                <PlayIcon className="h-4 w-4 sm:h-5 sm:w-5 inline-block mr-1.5 sm:mr-2" />
                Start Practice
              </SimpleButton>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
