"use client";

import { useState } from "react";
import {
  PlayIcon,
  PauseIcon,
  ArrowDownTrayIcon,
  CloudArrowUpIcon,
  TrashIcon,
  ClockIcon,
} from "@heroicons/react/24/outline";
import {
  AudioRecording,
  downloadAudioRecording,
  generateAudioFilename,
  saveRecordingToStorage,
  formatDuration,
  formatFileSize,
} from "@/utils/audioRecordingUtils";

interface AudioRecordingManagerProps {
  recordings: Record<string, AudioRecording>;
  testType: string;
  isPlaying: boolean;
  currentPlayingKey: string | null;
  onPlay: (key: string) => void;
  onStop: () => void;
  className?: string;
}

export default function AudioRecordingManager({
  recordings,
  testType,
  isPlaying,
  currentPlayingKey,
  onPlay,
  onStop,
  className = "",
}: AudioRecordingManagerProps) {
  const [savingStates, setSavingStates] = useState<Record<string, boolean>>({});
  const [saveMessages, setSaveMessages] = useState<Record<string, string>>({});

  const handleDownload = (recordingKey: string, recording: AudioRecording) => {
    try {
      if (!recording.blob) {
        alert("Recording data is not available for download.");
        return;
      }
      const filename = generateAudioFilename(testType, recordingKey);
      downloadAudioRecording(recording, filename);
    } catch (error) {
      console.error("Download failed:", error);
      alert("Failed to download recording. Please try again.");
    }
  };

  const handleSaveToStorage = async (
    recordingKey: string,
    recording: AudioRecording
  ) => {
    if (!recording.blob) {
      alert("Recording data is not available for saving.");
      return;
    }

    setSavingStates((prev) => ({ ...prev, [recordingKey]: true }));
    setSaveMessages((prev) => ({ ...prev, [recordingKey]: "Saving..." }));

    try {
      await saveRecordingToStorage(recording, testType, recordingKey);
      setSaveMessages((prev) => ({ ...prev, [recordingKey]: "Saved!" }));

      // Clear success message after 2 seconds
      setTimeout(() => {
        setSaveMessages((prev) => {
          const newMessages = { ...prev };
          delete newMessages[recordingKey];
          return newMessages;
        });
      }, 2000);
    } catch (error) {
      console.error("Save failed:", error);
      setSaveMessages((prev) => ({ ...prev, [recordingKey]: "Save failed" }));

      // Clear error message after 3 seconds
      setTimeout(() => {
        setSaveMessages((prev) => {
          const newMessages = { ...prev };
          delete newMessages[recordingKey];
          return newMessages;
        });
      }, 3000);
    } finally {
      setSavingStates((prev) => ({ ...prev, [recordingKey]: false }));
    }
  };

  const recordingEntries = Object.entries(recordings);

  if (recordingEntries.length === 0) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <div className="text-gray-500">
          <ClockIcon className="h-12 w-12 mx-auto mb-2 opacity-50" />
          <p>No recordings yet</p>
          <p className="text-sm">
            Start recording to see your audio files here
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">
          Your Recordings ({recordingEntries.length})
        </h3>
        <div className="text-sm text-gray-500">
          Total:{" "}
          {formatFileSize(
            recordingEntries.reduce((sum, [, recording]) => {
              return sum + (recording.blob?.size || 0);
            }, 0)
          )}
        </div>
      </div>

      <div className="space-y-3">
        {recordingEntries.map(([recordingKey, recording]) => (
          <div
            key={recordingKey}
            className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow"
          >
            <div className="flex items-center justify-between">
              <div className="flex-1 min-w-0">
                <h4 className="text-sm font-medium text-gray-900 truncate">
                  {recordingKey}
                </h4>
                <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                  <span className="flex items-center">
                    <ClockIcon className="h-3 w-3 mr-1" />
                    {formatDuration(recording.duration)}
                  </span>
                  <span>{formatFileSize(recording.blob?.size || 0)}</span>
                </div>
              </div>

              <div className="flex items-center space-x-2 ml-4">
                {/* Play/Pause Button */}
                <button
                  onClick={() => {
                    if (isPlaying && currentPlayingKey === recordingKey) {
                      onStop();
                    } else {
                      onPlay(recordingKey);
                    }
                  }}
                  className="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-full transition-colors"
                  title={
                    isPlaying && currentPlayingKey === recordingKey
                      ? "Pause"
                      : "Play"
                  }
                >
                  {isPlaying && currentPlayingKey === recordingKey ? (
                    <PauseIcon className="h-4 w-4" />
                  ) : (
                    <PlayIcon className="h-4 w-4" />
                  )}
                </button>

                {/* Download Button */}
                <button
                  onClick={() => handleDownload(recordingKey, recording)}
                  className="p-2 text-green-600 hover:text-green-800 hover:bg-green-50 rounded-full transition-colors"
                  title="Download Recording"
                >
                  <ArrowDownTrayIcon className="h-4 w-4" />
                </button>

                {/* Save to Storage Button */}
                <button
                  onClick={() => handleSaveToStorage(recordingKey, recording)}
                  disabled={savingStates[recordingKey]}
                  className="p-2 text-purple-600 hover:text-purple-800 hover:bg-purple-50 rounded-full transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  title="Save to Browser Storage"
                >
                  <CloudArrowUpIcon className="h-4 w-4" />
                </button>
              </div>
            </div>

            {/* Save Status Message */}
            {saveMessages[recordingKey] && (
              <div className="mt-2 text-xs">
                <span
                  className={`px-2 py-1 rounded-full ${
                    saveMessages[recordingKey] === "Saved!"
                      ? "bg-green-100 text-green-800"
                      : saveMessages[recordingKey] === "Save failed"
                      ? "bg-red-100 text-red-800"
                      : "bg-blue-100 text-blue-800"
                  }`}
                >
                  {saveMessages[recordingKey]}
                </span>
              </div>
            )}
          </div>
        ))}
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg
              className="h-5 w-5 text-blue-400"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">
              Recording Tips
            </h3>
            <div className="mt-2 text-sm text-blue-700">
              <ul className="list-disc list-inside space-y-1">
                <li>
                  <strong>Download:</strong> Save recordings directly to your
                  device
                </li>
                <li>
                  <strong>Browser Storage:</strong> Keep recordings for future
                  sessions
                </li>
                <li>
                  Recordings are saved in WebM format for best compatibility
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
