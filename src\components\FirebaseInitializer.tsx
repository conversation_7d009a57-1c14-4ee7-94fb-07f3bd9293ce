"use client";

import { useEffect, useState } from "react";

/**
 * This component ensures Firebase is only initialized on the client side
 * It doesn't render anything, just ensures Firebase is properly initialized
 */
export default function FirebaseInitializer() {
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    // Only initialize Firebase on the client side
    const initFirebase = async () => {
      try {
        // Dynamic import to ensure Firebase is only loaded on the client
        const { app, auth, db } = await import("@/lib/firebase");
        
        // Check if Firebase was initialized successfully
        if (app && auth && db) {
          console.log("Firebase initialized successfully on client side");
        } else {
          console.warn("Firebase initialization incomplete - some services may not be available");
        }
        
        setIsInitialized(true);
      } catch (error) {
        console.error("Error initializing Firebase on client side:", error);
      }
    };

    initFirebase();
  }, []);

  // This component doesn't render anything
  return null;
}
