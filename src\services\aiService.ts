/**
 * AI Service for analyzing test responses
 * This service provides functions to analyze responses using DeepSeek API and Military Psychologist analysis
 */

import OpenAI from "openai";

// Define the structure of the AI analysis response
export interface AIAnalysisResult {
  overallScore: number;
  strengths: string[];
  improvements: string[];
  detailedFeedback: string;
  traits: {
    [key: string]: {
      score: number;
      feedback: string;
    };
  };
  isAIGenerated: boolean;
  // Deepseek AI Analysis enhanced fields
  responseQualityAssessment?: {
    [responseIndex: number]: {
      quality: "Good" | "Moderate" | "Bad";
      response: string;
      improvementSuggestions?: string[];
    };
  };
  // Military Psychologist Analysis fields
  militaryPsychologist?: {
    positivityScore?: number; // 1-10 scale
    quickThinkingRating?: "Fast" | "Moderate" | "Slow";
    situationalAwarenessFlag?: "✅" | "⚠️" | "❌";
    topStrength?: string; // 3-word summary
    criticalWeakness?: string; // 3-word summary
    bestResponseSegment?: string; // Quote from candidate's best response
    weakestResponseSegment?: string; // Quote from candidate's weakest response
    weakestResponseReasoning?: string; // Reasoning for why it's weak
    positivityAnalysis?: string; // Tone analysis + resilience indicators
    sensibleLogicAnalysis?: string; // Practicality score + rationale
    quickThinkingAnalysis?: string; // Response time estimation + stress handling
    situationalAwarenessAnalysis?: string; // Context understanding + threat detection
    simulationScenario?: string; // AI-created military scenario
    simulationPrediction?: string; // Predicted behavior
    simulationReasoning?: string; // Trait evidence for prediction
    recommendation?: "Accept" | "Reinvestigate" | "Reject";
    recommendationReasons?: string[]; // 3 bullet points connecting analysis to requirements

    // Cultural Fit Algorithm
    culturalFitAlgorithm?: {
      purpose?: string; // Purpose of the analysis
      collectiveLanguageScore?: {
        weUsage?: string; // "We" usage count vs "I" usage count
        militaryJargonMatch?: string; // Percentage of proper military terminology used
        chainOfCommandMentions?: string; // Count of hierarchy references
      };
      culturalCompatibility?: {
        sensitivityFlags?: number; // Number of cultural sensitivity flags
        humorStyle?: string; // Self-deprecating/Mocking/Neutral
        sharedValueAlignment?: string; // Value mentions like "Sacrifice" vs "Rights"
      };
      authorityResponsePattern?: {
        orderChallengeProbability?: string; // Percentage based on phrases like "But what if..."
        leadershipSeek?: string; // Count of unsolicited command attempts
      };
      teamLanguageIndex?: string; // Overall team language index percentage
      riskProfile?: string; // Risk assessment for order challenges
      recommendedAction?: string; // Recommended action for integration
    };
  };
}

// Define the structure for the military traits to be analyzed
export interface MilitaryTraitForAI {
  name: string;
  description: string;
}

// Military traits for AI analysis
export const militaryTraitsForAI: MilitaryTraitForAI[] = [
  {
    name: "Leadership",
    description:
      "Ability to guide, influence, and inspire others to achieve objectives",
  },
  {
    name: "Adaptability",
    description:
      "Ability to adjust to new conditions and handle unexpected situations",
  },
  {
    name: "Decision Making",
    description:
      "Ability to make sound judgments and choices, especially under pressure",
  },
  {
    name: "Teamwork",
    description: "Ability to work effectively with others toward common goals",
  },
  {
    name: "Communication",
    description: "Ability to convey information clearly and effectively",
  },
  {
    name: "Problem Solving",
    description: "Ability to identify issues and develop effective solutions",
  },
  {
    name: "Resilience",
    description:
      "Ability to recover from difficulties and maintain effectiveness",
  },
  {
    name: "Discipline",
    description: "Adherence to rules, self-control, and organized behavior",
  },
  {
    name: "Integrity",
    description: "Adherence to moral and ethical principles",
  },
  {
    name: "Resource Management",
    description: "Ability to efficiently utilize available resources",
  },
];

/**
 * Analyze responses using Google's Gemini API
 * @param responses Array of user responses to analyze
 * @param testType Type of test (SRT, WAT, TAT, SDT)
 * @param prompts Optional array of prompts/questions that elicited the responses
 * @returns Promise resolving to the AI analysis result
 */
/**
 * Analyze responses using DeepSeek API via OpenRouter
 * @param responses Array of user responses to analyze
 * @param testType Type of test (SRT, WAT, TAT, SDT)
 * @param prompts Optional array of prompts/questions that elicited the responses
 * @returns Promise resolving to the AI analysis result
 */
export const analyzeResponsesWithDeepSeek = async (
  responses: string[],
  testType: string,
  prompts?: string[]
): Promise<AIAnalysisResult> => {
  try {
    // Check if DeepSeek API key is configured
    const apiKey = process.env.NEXT_PUBLIC_DEEPSEEK_API_KEY;
    if (!apiKey || apiKey === "your-deepseek-api-key" || apiKey.trim() === "") {
      console.error("DeepSeek API key is not configured or is invalid");
      return generateFallbackAnalysis(responses);
    }

    // Filter out empty responses
    const validResponses = responses.filter(
      (response) => response && response.trim() !== ""
    );

    // If no valid responses, return a fallback analysis
    if (validResponses.length === 0) {
      return generateFallbackAnalysis(responses);
    }

    // Prepare the context for the API call
    let context = `Analyze the following responses from a military psychological test (${testType}) to evaluate the candidate's military aptitude.\n\n`;

    if (prompts && prompts.length > 0) {
      context += "The responses are to these prompts/questions:\n\n";
      prompts.forEach((prompt, index) => {
        if (prompt) {
          context += `Prompt ${index + 1}: ${prompt}\n`;
        }
      });
      context += "\n";
    }

    context += "Responses:\n\n";
    validResponses.forEach((response, index) => {
      context += `Response ${index + 1}: ${response}\n\n`;
    });

    // Create the prompt for DeepSeek
    const prompt = `You are a military psychological assessment expert. Analyze the candidate's responses to evaluate their military aptitude across these traits: ${militaryTraitsForAI
      .map((trait) => trait.name)
      .join(", ")}.

Your analysis should have TWO main components:

PART 1: TRAIT ANALYSIS
For each trait, provide:
1. A score from 0-100
2. A brief feedback comment explaining the score

Also identify:
- Top 3 strengths (highest scoring traits)
- Top 3 areas for improvement (lowest scoring traits)
- An overall score (average of all traits)
- Detailed feedback with specific observations from the responses

PART 2: RESPONSE QUALITY ASSESSMENT
For each individual response, provide:
1. A quality assessment: "Good", "Moderate", or "Bad" based on relevance, clarity, and completeness
   - "Good": Strong, relevant, clear, and complete responses
   - "Moderate": Acceptable but could be improved in some aspects
   - "Bad": Poor, irrelevant, unclear, or incomplete responses
2. For responses marked "Moderate" or "Bad", provide 1-3 concise, actionable improvement suggestions

IMPORTANT: Your response MUST be a properly formatted JSON object. Follow these guidelines:
- Use double quotes for all property names and string values
- Do not use single quotes anywhere in the JSON
- Ensure all property names are in double quotes
- Ensure all string values are in double quotes
- Do not include trailing commas in objects or arrays
- Escape any special characters in strings properly
- Do not include any text before or after the JSON object

Format your response as a VALID JSON object with EXACTLY these fields:
{
  "traits": {
    "TraitName1": {
      "score": 75,
      "feedback": "Feedback about this trait"
    },
    "TraitName2": {
      "score": 60,
      "feedback": "Feedback about this trait"
    }
  },
  "strengths": ["TraitName1", "TraitName3", "TraitName4"],
  "improvements": ["TraitName2", "TraitName5", "TraitName6"],
  "overallScore": 70,
  "detailedFeedback": "Comprehensive analysis text goes here...",
  "responseQualityAssessment": {
    "0": {
      "quality": "Good",
      "response": "First 50 chars of response 1..."
    },
    "1": {
      "quality": "Bad",
      "response": "First 50 chars of response 2...",
      "improvementSuggestions": [
        "Suggestion 1",
        "Suggestion 2"
      ]
    }
  }
}

IMPORTANT: The responseQualityAssessment field MUST be included in your response and MUST follow the exact format specified above. Each response must be evaluated individually.

The "traits" object MUST include ALL of these traits: ${militaryTraitsForAI
      .map((trait) => `"${trait.name}"`)
      .join(", ")}.

EXAMPLE OF VALID JSON RESPONSE:
{
  "traits": {
    "Leadership": {
      "score": 80,
      "feedback": "The candidate demonstrates strong leadership qualities through clear decision-making and initiative."
    },
    "Teamwork": {
      "score": 75,
      "feedback": "Shows good collaborative skills and consideration for team dynamics."
    },
    "Communication": {
      "score": 70,
      "feedback": "Expresses ideas clearly but could improve on providing more detailed explanations."
    },
    "Problem Solving": {
      "score": 60,
      "feedback": "Shows basic problem-solving skills but lacks depth in analyzing complex situations."
    },
    "Discipline": {
      "score": 65,
      "feedback": "Demonstrates adequate discipline but could improve consistency."
    },
    "Resource Management": {
      "score": 55,
      "feedback": "Needs improvement in efficiently allocating and utilizing resources."
    }
  },
  "strengths": ["Leadership", "Teamwork", "Communication"],
  "improvements": ["Problem Solving", "Discipline", "Resource Management"],
  "overallScore": 75,
  "detailedFeedback": "The candidate demonstrates strong leadership qualities and teamwork skills. Their communication is effective but could be more detailed in certain contexts. Areas for improvement include problem-solving approaches, particularly when facing complex situations.",
  "responseQualityAssessment": {
    "0": {
      "quality": "Good",
      "response": "I would approach this situation by first gathering..."
    },
    "1": {
      "quality": "Moderate",
      "response": "I would try to solve the problem by...",
      "improvementSuggestions": [
        "Be more specific about your approach",
        "Consider military protocols in your solution"
      ]
    },
    "2": {
      "quality": "Bad",
      "response": "I don't know what to do in this situation...",
      "improvementSuggestions": [
        "Add specific examples to strengthen your argument",
        "Consider the military chain of command in your response",
        "Mention how you would communicate with your team"
      ]
    },
    "3": {
      "quality": "Good",
      "response": "In a military context, I would follow protocol by..."
    }
  }
}

IMPORTANT: Make sure to include an assessment for EACH response in the responseQualityAssessment object.

Keep your analysis focused on military aptitude and leadership potential.

Here is the candidate's information:
${context}

IMPORTANT: Respond ONLY with a valid JSON object. Do NOT use markdown formatting, code blocks, or any other formatting. Just return a raw JSON object.

CRITICAL FORMATTING RULES:
1. Use double quotes (") for all strings and property names, never single quotes (')
2. Do not include any control characters or special characters that would break JSON parsing
3. Ensure all property names are properly quoted
4. Do not include trailing commas in arrays or objects
5. Make sure all strings are properly escaped, especially quotes within strings
6. Do not include any text before or after the JSON object

Example of correct formatting:
{
  "traits": {
    "Leadership": {
      "score": 75,
      "feedback": "Shows strong leadership qualities."
    }
  },
  "strengths": ["Leadership", "Teamwork"],
  "improvements": ["Problem Solving"],
  "overallScore": 70,
  "detailedFeedback": "The candidate demonstrates good potential."
}

FINAL REMINDER:
1. Your response MUST be a VALID JSON object
2. It MUST include the responseQualityAssessment field with an assessment for each response
3. All property names MUST be in double quotes
4. All string values MUST be in double quotes
5. Do NOT use single quotes anywhere in the JSON
6. Do NOT include any text before or after the JSON object
7. Make sure all property names and values are properly formatted
8. Check that all opening braces, brackets, and quotes have matching closing ones
9. Ensure there are no trailing commas at the end of objects or arrays
10. Escape any special characters in strings with a backslash
11. If you're unsure about the JSON format, simplify your response rather than risk invalid syntax`;

    // Initialize OpenAI client with OpenRouter configuration
    const openai = new OpenAI({
      baseURL: "https://openrouter.ai/api/v1",
      apiKey: apiKey,
      dangerouslyAllowBrowser: true, // Required when running in browser environment
      defaultHeaders: {
        "HTTP-Referer":
          process.env.NEXT_PUBLIC_APP_URL || "https://balaramshiwakoti.com.np",
        "X-Title": "Military Aptitude Analysis",
      },
    });

    // Call the DeepSeek API via OpenRouter
    let analysisText;
    try {
      console.log("Sending request to DeepSeek API...");
      const completion = await openai.chat.completions.create({
        model: "deepseek/deepseek-chat-v3-0324:free",
        messages: [
          {
            role: "user",
            content: prompt,
          },
        ],
        temperature: 0.1, // Very low temperature for more predictable, structured output
        max_tokens: 1500,
      });

      // Extract the response text
      analysisText = completion.choices[0]?.message?.content;
      console.log("Received response from DeepSeek API");

      if (!analysisText) {
        console.error("No analysis content received from DeepSeek");
        return generateFallbackAnalysis(
          responses,
          false,
          "No content received from DeepSeek API"
        );
      }
    } catch (apiError) {
      console.error("Error calling DeepSeek API:", apiError);
      return generateFallbackAnalysis(
        responses,
        false,
        `Error calling DeepSeek API: ${apiError.message || "Unknown error"}`
      );
    }

    // Parse the JSON response
    try {
      // Log the raw response for debugging
      console.log("Raw DeepSeek response:", analysisText);
      console.log("Prompt sent to DeepSeek:", prompt);

      // Try to parse the response as JSON
      let parsedAnalysis: AIAnalysisResult | null = null;

      // Try to extract JSON directly using a more aggressive approach first
      const jsonRegex = /\{(?:[^{}]|(?:\{(?:[^{}]|(?:\{[^{}]*\}))*\}))*\}/g;
      const jsonMatches = analysisText.match(jsonRegex);

      if (jsonMatches && jsonMatches.length > 0) {
        console.log(
          `Found ${jsonMatches.length} potential JSON objects in the response`
        );
        // Try each match, starting with the longest (most likely to be the complete JSON)
        const sortedMatches = [...jsonMatches].sort(
          (a, b) => b.length - a.length
        );

        for (const jsonCandidate of sortedMatches) {
          try {
            console.log(
              "Attempting to parse JSON candidate:",
              jsonCandidate.substring(0, 100) + "..."
            );
            const candidateResult = JSON.parse(jsonCandidate);
            if (candidateResult && typeof candidateResult === "object") {
              console.log("Successfully parsed JSON directly from response!");
              parsedAnalysis = candidateResult;
              break;
            }
          } catch (error) {
            const directJsonError = error as Error;
            console.log(
              "Failed to parse JSON candidate:",
              directJsonError.message || "Unknown error"
            );
            // Continue to the next candidate
          }
        }
      }

      // First, clean up the response text to handle potential issues
      let cleanedText = analysisText;

      // Remove any non-JSON content before the first opening brace
      const firstBraceIndex = cleanedText.indexOf("{");
      if (firstBraceIndex > 0) {
        cleanedText = cleanedText.substring(firstBraceIndex);
        console.log("Removed content before first brace");
      }

      // Find the last closing brace to remove any content after the JSON
      const lastBraceIndex = cleanedText.lastIndexOf("}");
      if (lastBraceIndex > 0 && lastBraceIndex < cleanedText.length - 1) {
        cleanedText = cleanedText.substring(0, lastBraceIndex + 1);
        console.log("Removed content after last brace");
      }

      // Remove any control characters that would break JSON parsing
      cleanedText = cleanedText.replace(/[\u0000-\u001F\u007F-\u009F]/g, "");

      // Specifically handle common problematic control characters
      cleanedText = cleanedText
        .replace(/\r/g, "")
        .replace(/\n/g, " ")
        .replace(/\t/g, " ")
        .replace(/\b/g, "")
        .replace(/\f/g, "");

      // Fix common escape sequence issues
      cleanedText = cleanedText.replace(/\\(?!["\\/bfnrt])/g, "\\\\");

      // Fix unescaped quotes within string values
      cleanedText = cleanedText.replace(/(?<!\\)"(?=(.*?".*?":))/g, '\\"');

      console.log(
        "Cleaned text after control character removal:",
        cleanedText.substring(0, 100) + "..."
      );

      // Fix common JSON formatting issues
      // Replace single quotes with double quotes
      cleanedText = cleanedText.replace(/'/g, '"');

      // Fix trailing commas in objects and arrays
      cleanedText = cleanedText.replace(/,(\s*[}\]])/g, "$1");

      // Fix missing commas between properties
      cleanedText = cleanedText.replace(/(["\d])\s*\n\s*"/g, '$1,\n"');
      cleanedText = cleanedText.replace(/(["\d])\s*\r\n\s*"/g, '$1,\r\n"');

      // Fix property names without quotes
      cleanedText = cleanedText.replace(
        /([{,]\s*)([a-zA-Z0-9_]+)(\s*:)/g,
        '$1"$2"$3'
      );

      // Fix unescaped quotes in string values
      cleanedText = cleanedText.replace(
        /"([^"]*)"([^"]*)"([^"]*)"/g,
        function (match, p1, p2, p3) {
          return '"' + p1 + '\\"' + p2 + '\\"' + p3 + '"';
        }
      );

      // Fix missing quotes around string values
      cleanedText = cleanedText.replace(
        /:\s*([a-zA-Z][a-zA-Z0-9_]*)\s*([,}])/g,
        ':"$1"$2'
      );

      // Fix newlines in string values
      cleanedText = cleanedText.replace(
        /:\s*"([^"]*)(\r?\n)([^"]*)"/g,
        ':"$1\\n$3"'
      );

      console.log("Cleaned and fixed JSON text:", cleanedText);

      // Remove any non-JSON content after the last closing brace
      const finalBraceIndex = cleanedText.lastIndexOf("}");
      if (finalBraceIndex !== -1 && finalBraceIndex < cleanedText.length - 1) {
        cleanedText = cleanedText.substring(0, finalBraceIndex + 1);
        console.log("Removed content after last brace");
      }

      // Replace any invalid escape sequences
      cleanedText = cleanedText.replace(/\\([^"\\\/bfnrtu])/g, "\\\\$1");

      // Fix common JSON syntax errors
      cleanedText = cleanedText
        .replace(/,\s*}/g, "}") // Remove trailing commas in objects
        .replace(/,\s*]/g, "]") // Remove trailing commas in arrays
        .replace(/(['"])?([a-zA-Z0-9_]+)(['"])?:/g, '"$2":') // Ensure property names are quoted
        .replace(/:\s*'([^']*)'/g, ':"$1"'); // Replace single quotes with double quotes for values

      console.log("Cleaned response text:", cleanedText);

      try {
        // First attempt: direct parsing of cleaned text
        try {
          parsedAnalysis = JSON.parse(cleanedText);
          console.log("Successfully parsed cleaned JSON directly");
        } catch (directParseError) {
          console.error("Direct parse error:", directParseError);
          console.error("Error location:", directParseError.message);

          // Log the first 200 characters of the cleaned text for debugging
          console.log(
            "First 200 chars of cleaned text:",
            cleanedText.substring(0, 200)
          );

          // Try more aggressive cleaning for problematic JSON
          try {
            // Replace all newlines and extra whitespace
            let aggressivelyCleaned = cleanedText.replace(/\s+/g, " ").trim();

            // Try to fix trailing commas in objects and arrays which are invalid in JSON
            aggressivelyCleaned = aggressivelyCleaned
              .replace(/,\s*}/g, "}")
              .replace(/,\s*\]/g, "]");

            // Try to fix missing quotes around property names
            aggressivelyCleaned = aggressivelyCleaned.replace(
              /(\{|\,)\s*([a-zA-Z0-9_]+)\s*:/g,
              '$1"$2":'
            );

            console.log(
              "Aggressively cleaned JSON (first 200 chars):",
              aggressivelyCleaned.substring(0, 200)
            );

            try {
              parsedAnalysis = JSON.parse(aggressivelyCleaned);
              console.log("Successfully parsed aggressively cleaned JSON");
              // If we get here, parsing succeeded with aggressive cleaning
              cleanedText = aggressivelyCleaned;
            } catch (aggressiveCleanError) {
              console.error(
                "Aggressive clean parse error:",
                aggressiveCleanError
              );
              // Continue with the original approach if aggressive cleaning fails
            }
          } catch (cleaningError) {
            console.error("Error during aggressive cleaning:", cleaningError);
          }

          // Try to fix the specific error location if possible
          if (
            directParseError.message.includes("position") &&
            !parsedAnalysis
          ) {
            try {
              // Extract position from error message
              const positionMatch =
                directParseError.message.match(/position (\d+)/);
              if (positionMatch && positionMatch[1]) {
                const errorPosition = parseInt(positionMatch[1]);
                console.log(
                  `Error at position ${errorPosition}, surrounding text:`,
                  cleanedText.substring(
                    Math.max(0, errorPosition - 20),
                    errorPosition
                  ) +
                    " >>> " +
                    cleanedText.substring(errorPosition, errorPosition + 20)
                );

                // Try to fix common issues at the error position
                const beforeError = cleanedText.substring(0, errorPosition);
                const afterError = cleanedText.substring(errorPosition);
                let fixed = false;

                // Check if we're missing a comma between properties
                if (/"\s*$/.test(beforeError) && /^\s*"/.test(afterError)) {
                  cleanedText = beforeError + "," + afterError;
                  console.log("Added missing comma between properties");
                  fixed = true;
                }
                // Check if we're missing quotes around a property name
                else if (
                  /[{,]\s*$/.test(beforeError) &&
                  /^\s*[a-zA-Z0-9_]+\s*:/.test(afterError)
                ) {
                  const propertyMatch = afterError.match(
                    /^\s*([a-zA-Z0-9_]+)\s*:/
                  );
                  if (propertyMatch && propertyMatch[1]) {
                    cleanedText =
                      beforeError +
                      '"' +
                      propertyMatch[1] +
                      '"' +
                      afterError.substring(propertyMatch[0].length - 1);
                    console.log("Added missing quotes around property name");
                    fixed = true;
                  }
                }
                // Check if we're missing a colon between property name and value
                else if (
                  /"[a-zA-Z0-9_]+"\s*$/.test(beforeError) &&
                  /^\s*["{[0-9a-zA-Z]/.test(afterError)
                ) {
                  cleanedText = beforeError + ":" + afterError;
                  console.log("Added missing colon after property name");
                  fixed = true;
                }
                // Check if we have an invalid character in a string
                else if (
                  /"[^"]*$/.test(beforeError) &&
                  /^[^"]*"/.test(afterError)
                ) {
                  // This is likely an unescaped character in a string
                  // Try to escape the character at the error position
                  cleanedText = beforeError + "\\" + afterError;
                  console.log("Escaped character at error position");
                  fixed = true;
                }
                // Check if we have an extra comma at the end of an object or array
                else if (
                  /,\s*$/.test(beforeError) &&
                  /^\s*[}\]]/.test(afterError)
                ) {
                  cleanedText = beforeError.replace(/,\s*$/, "") + afterError;
                  console.log("Removed trailing comma");
                  fixed = true;
                }

                // If we couldn't fix the specific issue, try a more aggressive approach
                if (!fixed) {
                  // Try to identify the problematic property or value
                  const lastPropertyMatch = beforeError.match(
                    /"([^"]+)"\s*:\s*[^,{}\[\]]*$/
                  );
                  if (lastPropertyMatch) {
                    const propertyName = lastPropertyMatch[1];
                    console.log(
                      `Problematic property appears to be "${propertyName}"`
                    );

                    // Try to find where this property should end
                    const propertyEndMatch = afterError.match(
                      /^[^,{}\[\]]*([,{}\[\]])/
                    );
                    if (propertyEndMatch) {
                      // Replace the problematic value with a simple string
                      const endPos =
                        propertyEndMatch.index + propertyEndMatch[0].length - 1;
                      cleanedText =
                        beforeError +
                        '"FIXED_VALUE"' +
                        afterError.substring(endPos);
                      console.log(
                        `Replaced problematic value for property "${propertyName}" with "FIXED_VALUE"`
                      );
                      fixed = true;
                    }
                  }
                }

                // Try parsing again after fixes
                if (fixed) {
                  try {
                    parsedAnalysis = JSON.parse(cleanedText);
                    console.log(
                      "Successfully parsed JSON after position-specific fixes"
                    );
                  } catch (positionFixError) {
                    console.error("Position fix error:", positionFixError);

                    // If we still have an error, try to get more information
                    if (positionFixError.message.includes("position")) {
                      const newPositionMatch =
                        positionFixError.message.match(/position (\d+)/);
                      if (newPositionMatch && newPositionMatch[1]) {
                        const newErrorPosition = parseInt(newPositionMatch[1]);
                        console.log(
                          `New error at position ${newErrorPosition}, surrounding text:`,
                          cleanedText.substring(
                            Math.max(0, newErrorPosition - 30),
                            newErrorPosition
                          ) +
                            " >>> " +
                            cleanedText.substring(
                              newErrorPosition,
                              Math.min(
                                cleanedText.length,
                                newErrorPosition + 30
                              )
                            )
                        );
                      }
                    }
                  }
                } else {
                  try {
                    parsedAnalysis = JSON.parse(cleanedText);
                    console.log(
                      "Successfully parsed JSON without position-specific fixes"
                    );
                  } catch (positionFixError) {
                    console.error("Position fix error:", positionFixError);
                  }
                }
              }
            } catch (positionExtractionError) {
              console.error(
                "Error extracting position:",
                positionExtractionError
              );
            }
          }

          // Second attempt: Check if the response is wrapped in a markdown code block
          const codeBlockMatch = analysisText.match(
            /```(?:json)?\s*([\s\S]*?)\s*```/
          );

          if (codeBlockMatch && codeBlockMatch[1]) {
            console.log("Found code block, attempting to parse content");
            let codeBlockContent = codeBlockMatch[1];

            // Clean the code block content
            codeBlockContent = codeBlockContent
              .replace(/(['"])?([a-zA-Z0-9_]+)(['"])?:/g, '"$2":')
              .replace(/:\s*'([^']*)'/g, ':"$1"')
              .replace(/,\s*}/g, "}")
              .replace(/,\s*]/g, "]");

            try {
              parsedAnalysis = JSON.parse(codeBlockContent);
              console.log("Successfully parsed code block content");
            } catch (codeBlockError) {
              console.error(
                "Error parsing code block content:",
                codeBlockError
              );

              // Try to extract JSON from the code block
              const jsonMatch = codeBlockContent.match(/\{[\s\S]*\}/);
              if (jsonMatch) {
                try {
                  parsedAnalysis = JSON.parse(jsonMatch[0]);
                  console.log(
                    "Successfully extracted and parsed JSON from code block"
                  );
                } catch (extractError) {
                  throw new Error("Could not parse JSON from code block");
                }
              } else {
                throw new Error("No valid JSON found in code block");
              }
            }
          } else {
            // Third attempt: Try to extract JSON pattern from the text
            const jsonMatch = cleanedText.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
              try {
                parsedAnalysis = JSON.parse(jsonMatch[0]);
                console.log("Successfully extracted and parsed JSON pattern");
              } catch (extractError) {
                console.error(
                  "Failed to parse extracted JSON pattern:",
                  extractError
                );

                // Last resort: Create a minimal valid JSON structure
                console.log("Creating fallback JSON structure");
                parsedAnalysis = {
                  overallScore: 65,
                  strengths: ["Adaptability", "Communication", "Teamwork"],
                  improvements: [
                    "Decision Making",
                    "Problem Solving",
                    "Resource Management",
                  ],
                  detailedFeedback:
                    "Analysis could not be properly parsed from the AI response. This is a fallback analysis. The original response contained formatting errors that prevented proper parsing.",
                  traits: {
                    Leadership: {
                      score: 65,
                      feedback:
                        "Shows potential for leadership with room for improvement.",
                    },
                    Communication: {
                      score: 70,
                      feedback: "Demonstrates adequate communication skills.",
                    },
                    Teamwork: {
                      score: 68,
                      feedback: "Works well with others in group settings.",
                    },
                    "Problem Solving": {
                      score: 60,
                      feedback:
                        "Can solve basic problems but needs development with complex scenarios.",
                    },
                    Discipline: {
                      score: 65,
                      feedback:
                        "Shows reasonable self-control and adherence to rules.",
                    },
                  },
                  responseQualityAssessment: {},
                };

                // Add basic response quality assessment for each response
                if (validResponses && validResponses.length > 0) {
                  validResponses.forEach((response, index) => {
                    // Simple heuristic: longer responses are generally better
                    const isGood = response.length > 100;

                    parsedAnalysis.responseQualityAssessment[index] = {
                      quality: isGood ? "Good" : "Bad",
                      response: response.substring(0, 50) + "...",
                      improvementSuggestions: isGood
                        ? undefined
                        : [
                            "Provide more details in your response",
                            "Consider military context in your answer",
                            "Be more specific and clear in your explanation",
                          ],
                    };
                  });
                }

                // Add basic traits
                militaryTraitsForAI.forEach((trait) => {
                  parsedAnalysis.traits[trait.name] = {
                    score: 65,
                    feedback: "Score estimated due to parsing issues.",
                  };
                });
              }
            } else {
              // If all parsing attempts fail, create a fallback structure
              console.log(
                "No valid JSON pattern found, creating fallback structure"
              );
              parsedAnalysis = {
                overallScore: 60,
                strengths: ["Adaptability", "Communication", "Teamwork"],
                improvements: [
                  "Decision Making",
                  "Problem Solving",
                  "Resource Management",
                ],
                detailedFeedback:
                  "The AI response could not be parsed as JSON. This is a fallback analysis.",
                traits: {},
              };

              // Add basic traits
              militaryTraitsForAI.forEach((trait) => {
                parsedAnalysis.traits[trait.name] = {
                  score: 60,
                  feedback: "Score estimated due to parsing issues.",
                };
              });
            }
          }
        }
      } catch (jsonError) {
        console.error("All JSON parsing attempts failed:", jsonError);

        // Create a fallback analysis structure
        parsedAnalysis = {
          overallScore: 55,
          strengths: ["Adaptability", "Communication", "Teamwork"],
          improvements: [
            "Decision Making",
            "Problem Solving",
            "Resource Management",
          ],
          detailedFeedback:
            "There was an error processing the AI response. This is a fallback analysis.",
          traits: {},
        };

        // Add basic traits
        militaryTraitsForAI.forEach((trait) => {
          parsedAnalysis.traits[trait.name] = {
            score: 55,
            feedback: "Score estimated due to parsing issues.",
          };
        });
      }

      // Validate the parsed analysis has the expected structure
      if (!parsedAnalysis || typeof parsedAnalysis !== "object") {
        console.error("Invalid analysis structure:", parsedAnalysis);
        console.error("Original response text:", analysisText);
        return generateFallbackAnalysis(
          responses,
          true,
          "DeepSeek response had invalid structure"
        );
      }

      // Check if traits exist and have the expected format
      if (!parsedAnalysis.traits || typeof parsedAnalysis.traits !== "object") {
        console.error("Missing or invalid traits in analysis:", parsedAnalysis);
        console.error("Original response text:", analysisText);

        // Try to create a traits object if we have some data to work with
        if (
          parsedAnalysis.overallScore &&
          (parsedAnalysis.strengths ||
            parsedAnalysis.improvements ||
            parsedAnalysis.detailedFeedback)
        ) {
          console.log("Attempting to generate traits from partial data");

          // Create a basic traits object based on available data
          const traits: { [key: string]: { score: number; feedback: string } } =
            {};
          const militaryTraitNames = militaryTraitsForAI.map(
            (trait) => trait.name
          );

          // If we have strengths, assign them high scores
          if (
            parsedAnalysis.strengths &&
            Array.isArray(parsedAnalysis.strengths)
          ) {
            parsedAnalysis.strengths.forEach((strength: string) => {
              if (militaryTraitNames.includes(strength)) {
                traits[strength] = {
                  score: 75 + Math.floor(Math.random() * 15),
                  feedback: `You show strong potential in ${strength}.`,
                };
              }
            });
          }

          // If we have improvements, assign them lower scores
          if (
            parsedAnalysis.improvements &&
            Array.isArray(parsedAnalysis.improvements)
          ) {
            parsedAnalysis.improvements.forEach((improvement: string) => {
              if (militaryTraitNames.includes(improvement)) {
                traits[improvement] = {
                  score: 40 + Math.floor(Math.random() * 15),
                  feedback: `This is an area where you could improve.`,
                };
              }
            });
          }

          // Fill in any missing traits with average scores
          militaryTraitNames.forEach((trait) => {
            if (!traits[trait]) {
              traits[trait] = {
                score: 50 + Math.floor(Math.random() * 10),
                feedback:
                  "Based on your responses, you show average potential in this area.",
              };
            }
          });

          // Add the traits to the analysis
          parsedAnalysis.traits = traits;
        } else {
          return generateFallbackAnalysis(
            responses,
            true,
            "DeepSeek response missing required traits"
          );
        }
      }

      // Create a response quality assessment object
      let responseQualityAssessment: AIAnalysisResult["responseQualityAssessment"] =
        {};

      try {
        // If responseQualityAssessment is in the parsed analysis, use it
        if (
          parsedAnalysis.responseQualityAssessment &&
          typeof parsedAnalysis.responseQualityAssessment === "object"
        ) {
          console.log(
            "Found responseQualityAssessment in parsed analysis:",
            parsedAnalysis.responseQualityAssessment
          );

          // Safely copy and validate the response quality assessment
          Object.keys(parsedAnalysis.responseQualityAssessment).forEach(
            (key) => {
              const index = Number(key);
              if (!isNaN(index)) {
                const rawAssessment =
                  parsedAnalysis.responseQualityAssessment[key];
                if (rawAssessment && typeof rawAssessment === "object") {
                  // Create a validated assessment object
                  const quality =
                    rawAssessment.quality === "Good" ||
                    rawAssessment.quality === "good"
                      ? "Good"
                      : "Bad";

                  // Create assessment object without undefined values for Firestore compatibility
                  const assessmentObj: {
                    quality: "Good" | "Bad";
                    response: string;
                    improvementSuggestions?: string[];
                  } = {
                    quality: quality as "Good" | "Bad",
                    response:
                      typeof rawAssessment.response === "string"
                        ? rawAssessment.response
                        : "No response text available",
                  };

                  // Only add improvementSuggestions for "Bad" quality responses
                  if (quality === "Bad") {
                    assessmentObj.improvementSuggestions = Array.isArray(
                      rawAssessment.improvementSuggestions
                    )
                      ? rawAssessment.improvementSuggestions
                      : [
                          "This response could be improved with more detail and clarity.",
                        ];
                  }

                  responseQualityAssessment[index] = assessmentObj;
                }
              }
            }
          );
        } else {
          // If responseQualityAssessment is not in the parsed analysis, create it manually
          console.log(
            "No responseQualityAssessment found in parsed analysis, creating manually"
          );

          // Get the original responses
          const validResponses = responses.filter(
            (response) => response && response.trim() !== ""
          );

          if (validResponses.length > 0) {
            // Create a basic assessment for each response
            validResponses.forEach((response, index) => {
              // Simple heuristic: longer responses are generally better
              const isGood = response.length > 100;

              // Create assessment object without undefined values for Firestore compatibility
              const assessmentObj: {
                quality: "Good" | "Bad";
                response: string;
                improvementSuggestions?: string[];
              } = {
                quality: isGood ? "Good" : "Bad",
                response: response.substring(0, 50),
              };

              // Only add improvementSuggestions for "Bad" quality responses
              if (!isGood) {
                assessmentObj.improvementSuggestions = [
                  "Provide more details and examples in your response",
                  "Consider military context and protocols in your answer",
                ];
              }

              responseQualityAssessment[index] = assessmentObj;
            });
          }
        }
      } catch (assessmentError) {
        console.error(
          "Error creating response quality assessment:",
          assessmentError
        );

        // Create a fallback response quality assessment
        console.log("Creating fallback response quality assessment");

        // Get the original responses
        const validResponses = responses.filter(
          (response) => response && response.trim() !== ""
        );

        if (validResponses.length > 0) {
          // Create a basic assessment for each response
          validResponses.forEach((response, index) => {
            // Simple fallback assessment
            responseQualityAssessment[index] = {
              quality: "Good", // Default to "Good" to avoid showing improvement suggestions
              response: response.substring(0, 50),
            };
          });
        } else {
          // If no valid responses, create a dummy assessment
          responseQualityAssessment[0] = {
            quality: "Good",
            response: "No valid responses provided",
          };
        }
      }

      // Final check to ensure responseQualityAssessment is not empty
      if (Object.keys(responseQualityAssessment).length === 0) {
        console.log(
          "Response quality assessment is empty, adding fallback entry"
        );
        responseQualityAssessment[0] = {
          quality: "Good",
          response: "Response quality assessment could not be generated",
        };
      }

      // Ensure we have detailed feedback
      let detailedFeedback =
        parsedAnalysis.detailedFeedback || parsedAnalysis.feedback || "";

      // If detailed feedback is empty or too short, generate a basic one
      if (!detailedFeedback || detailedFeedback.length < 50) {
        console.log(
          "Detailed feedback is missing or too short, generating a basic one"
        );

        // Calculate overall score
        const overallScore =
          parsedAnalysis.overallScore ||
          calculateAverageScore(parsedAnalysis.traits);

        // Generate feedback based on score
        if (overallScore >= 80) {
          detailedFeedback =
            "Your responses demonstrate excellent military aptitude. You show strong leadership potential, good decision-making skills, and the ability to adapt to challenging situations. Continue to develop these strengths while working on areas for improvement.";
        } else if (overallScore >= 65) {
          detailedFeedback =
            "Your responses show good military aptitude. You demonstrate several key qualities needed for military service, including teamwork and communication skills. Focus on developing your weaker areas while maintaining your strengths.";
        } else {
          detailedFeedback =
            "Your responses indicate areas where improvement would be beneficial for military service. Consider working on your decision-making, problem-solving, and situational awareness. With practice and training, you can develop these important military qualities.";
        }

        // Add specific trait feedback if available
        if (
          parsedAnalysis.traits &&
          Object.keys(parsedAnalysis.traits).length > 0
        ) {
          detailedFeedback += "\n\nSpecific areas to focus on:";

          // Get the top 2 strengths
          const strengths =
            parsedAnalysis.strengths ||
            Object.entries(parsedAnalysis.traits)
              .sort((a, b) => b[1].score - a[1].score)
              .slice(0, 2)
              .map(([name]) => name);

          if (strengths.length > 0) {
            detailedFeedback += "\n\nStrengths: " + strengths.join(", ");
          }

          // Get the top 2 improvements
          const improvements =
            parsedAnalysis.improvements ||
            Object.entries(parsedAnalysis.traits)
              .sort((a, b) => a[1].score - b[1].score)
              .slice(0, 2)
              .map(([name]) => name);

          if (improvements.length > 0) {
            detailedFeedback +=
              "\n\nAreas for improvement: " + improvements.join(", ");
          }
        }
      }

      // Ensure responseQualityAssessment is properly formatted
      // If it's missing or malformed, create a fallback version
      if (
        !responseQualityAssessment ||
        Object.keys(responseQualityAssessment).length === 0
      ) {
        console.log("Creating fallback responseQualityAssessment");
        responseQualityAssessment = {};

        // Get the original responses
        const validResponses = responses.filter(
          (response) => response && response.trim() !== ""
        );

        // Create a basic assessment for each response
        validResponses.forEach((response, index) => {
          // Simple heuristic: longer responses are generally better
          const isGood = response.length > 100;

          responseQualityAssessment[index] = {
            quality: isGood ? "Good" : "Bad",
            response:
              response.substring(0, 50) + (response.length > 50 ? "..." : ""),
          };

          // Only add improvementSuggestions for "Bad" quality responses
          if (!isGood) {
            responseQualityAssessment[index].improvementSuggestions = [
              "Provide more details and examples in your response",
              "Consider military context and protocols in your answer",
              "Structure your response more clearly with specific actions",
            ];
          }
        });

        console.log(
          "Created fallback responseQualityAssessment:",
          responseQualityAssessment
        );

        // If we still don't have any assessments (no valid responses), create a dummy one
        if (Object.keys(responseQualityAssessment).length === 0) {
          responseQualityAssessment[0] = {
            quality: "Bad",
            response: "No valid response provided",
            improvementSuggestions: [
              "Please provide a complete response to the question",
            ],
          };
        }

        // Return early with the fallback assessment
      }

      // Transform the parsed analysis into our expected format
      const result: AIAnalysisResult = {
        overallScore:
          parsedAnalysis.overallScore ||
          calculateAverageScore(parsedAnalysis.traits),
        strengths: parsedAnalysis.strengths || [],
        improvements:
          parsedAnalysis.improvements ||
          parsedAnalysis.areasForImprovement ||
          [],
        detailedFeedback: detailedFeedback,
        traits: parsedAnalysis.traits || {},
        responseQualityAssessment: responseQualityAssessment,
        isAIGenerated: true,
      };

      // Log the response quality assessment for debugging
      console.log(
        "Response quality assessment included in result:",
        Object.keys(result.responseQualityAssessment || {}).length,
        "responses assessed"
      );

      return result;
    } catch (parseError) {
      console.error("Error processing DeepSeek response:", parseError);
      return generateFallbackAnalysis(
        responses,
        false,
        `Error processing DeepSeek response: ${
          parseError instanceof Error ? parseError.message : String(parseError)
        }`
      );
    }
  } catch (error) {
    console.error("Error analyzing responses with DeepSeek:", error);
    return generateFallbackAnalysis(
      responses,
      false,
      `Error analyzing with DeepSeek: ${
        error instanceof Error ? error.message : String(error)
      }`
    );
  }
};

/**
 * Google AI Analysis function has been removed
 */

/**
 * Generate a fallback analysis when AI analysis fails
 * @param responses Array of user responses
 * @param isPartialAI Whether this is a partial AI-generated analysis (true) or completely fallback (false)
 * @param errorReason Optional reason for the fallback
 * @returns A basic analysis result
 */
const generateFallbackAnalysis = (
  responses: string[],
  isPartialAI: boolean = false,
  errorReason: string = ""
): AIAnalysisResult => {
  const validResponses = responses.filter(
    (response) => response && response.trim() !== ""
  );
  const responseCount = validResponses.length;

  // Generate traits with basic scores
  const traits: { [key: string]: { score: number; feedback: string } } = {};
  militaryTraitsForAI.forEach((trait) => {
    const baseScore =
      responseCount > 0 ? 50 + Math.floor(Math.random() * 20) : 0;
    traits[trait.name] = {
      score: baseScore,
      feedback:
        responseCount > 0
          ? `Based on your responses, you show ${
              baseScore > 60 ? "good" : "some"
            } potential in this area.`
          : "No responses provided to analyze this trait.",
    };
  });

  // Calculate overall score as average of trait scores
  const overallScore = calculateAverageScore(traits);

  // Create a detailed feedback message that includes the error reason if provided
  let feedbackMessage =
    responseCount > 0
      ? "This is a basic analysis based on your responses. For more detailed insights, please ensure the AI analysis service is properly configured."
      : "No responses provided. Please complete the test to receive an analysis.";

  if (errorReason) {
    feedbackMessage += ` (Error: ${errorReason})`;
  }

  // Create a basic response quality assessment
  const responseQualityAssessment: AIAnalysisResult["responseQualityAssessment"] =
    {};

  if (responseCount > 0) {
    validResponses.forEach((response, index) => {
      // Simple heuristic: responses with more than 50 characters are considered "Good"
      const isGood = response.length > 50;

      // Create assessment object without undefined values for Firestore compatibility
      const assessmentObj: {
        quality: "Good" | "Bad";
        response: string;
        improvementSuggestions?: string[];
      } = {
        quality: isGood ? "Good" : "Bad",
        response: response.substring(0, 50),
      };

      // Only add improvementSuggestions for "Bad" quality responses
      if (!isGood) {
        assessmentObj.improvementSuggestions = [
          "Provide more details and examples in your response",
          "Consider military context and protocols in your answer",
        ];
      }

      responseQualityAssessment[index] = assessmentObj;
    });
  }

  return {
    overallScore,
    strengths:
      responseCount > 0
        ? Object.entries(traits)
            .sort((a, b) => b[1].score - a[1].score)
            .slice(0, 3)
            .map(([name]) => name)
        : [],
    improvements:
      responseCount > 0
        ? Object.entries(traits)
            .sort((a, b) => a[1].score - b[1].score)
            .slice(0, 3)
            .map(([name]) => name)
        : [],
    detailedFeedback: feedbackMessage,
    traits,
    responseQualityAssessment:
      responseCount > 0 ? responseQualityAssessment : undefined,
    isAIGenerated: isPartialAI, // Mark as AI-generated if it's a partial AI analysis
  };
};

/**
 * Calculate the average score from trait scores
 * @param traits Object containing trait scores
 * @returns Average score (0-100)
 */
const calculateAverageScore = (traits: {
  [key: string]: { score: number; feedback: string };
}): number => {
  const scores = Object.values(traits).map((trait) => trait.score);
  if (scores.length === 0) return 0;

  const sum = scores.reduce((total, score) => total + score, 0);
  return Math.round(sum / scores.length);
};

/**
 * Analyze responses as a military psychologist
 * @param responses Array of user responses to analyze
 * @param testType Type of test (SRT, WAT, TAT, SDT)
 * @param prompts Optional array of prompts/questions that elicited the responses
 * @returns Promise resolving to the AI analysis result with military psychologist format
 */
export const analyzeResponsesAsMilitaryPsychologist = async (
  responses: string[],
  testType: string,
  prompts?: string[]
): Promise<AIAnalysisResult> => {
  try {
    // Check if Google API key is configured
    const apiKey = process.env.NEXT_PUBLIC_GOOGLE_API_KEY;
    if (!apiKey || apiKey === "your-google-api-key" || apiKey.trim() === "") {
      console.error("Google API key is not configured or is invalid");
      return generateFallbackMilitaryPsychologistAnalysis(responses);
    }

    // Filter out empty responses
    const validResponses = responses.filter(
      (response) => response && response.trim() !== ""
    );

    // If no valid responses, return a fallback analysis
    if (validResponses.length === 0) {
      return generateFallbackMilitaryPsychologistAnalysis(responses);
    }

    // Prepare the context for the API call
    let context = `Analyze the following responses from a military psychological test (${testType}) to evaluate the candidate's military aptitude.\n\n`;

    if (prompts && prompts.length > 0) {
      context += "The responses are to these prompts/questions:\n\n";
      prompts.forEach((prompt, index) => {
        if (prompt) {
          context += `Prompt ${index + 1}: ${prompt}\n`;
        }
      });
      context += "\n";
    }

    context += "Responses:\n\n";
    validResponses.forEach((response, index) => {
      context += `Response ${index + 1}: ${response}\n\n`;
    });

    // Create the prompt for Gemini
    const prompt = `You are a military psychologist specializing in candidate screening. Analyze the following responses to generate a comprehensive military psychological assessment.

Your analysis must follow this EXACT format:

# [Simple Screen]
- Positivity Score: [1-10]
- Quick Thinking Rating: [Fast/Moderate/Slow]
- Situational Awareness Flag: [✅/⚠️/❌]
- Top Strength: [3-word summary]
- Critical Weakness: [3-word summary]

# [Detailed Feedback]
1. **Response Breakdown**
   - "Best Segment": [Quote the candidate's most logical/positive response segment]
   - "Needs Improvement": [Quote their weakest segment with reasoning]

2. **Psychological Analysis**
   - Positivity: [Tone analysis + resilience indicators]
   - Sensible Logic: [Practicality score + rationale]
   - Quick Thinking: [Response time estimation + stress handling]
   - Situational Awareness: [Context understanding + threat detection]

3. **Immersive Simulation**
   "If this candidate faced [AI-created military scenario based on their weaknesses], they would likely [predicted behavior] because [trait evidence]."

# [Final Recommendation]
Recommendation: [Accept/Reinvestigate/Reject]
Because:
- [Reason 1 connecting analysis to military role requirements]
- [Reason 2 connecting analysis to military role requirements]
- [Reason 3 connecting analysis to military role requirements]

IMPORTANT: Use military terms (OODA Loop, SERE) but keep explanations simple. Never assume - base analysis strictly on response patterns. Highlight contradictions (e.g., "Claims teamwork but avoids 'we' pronouns").

Here is the candidate's information:
${context}

IMPORTANT: Respond ONLY with the analysis in the exact format specified above. Do NOT include any additional text, explanations, or formatting.`;

    // Try using the Google AI API format
    const response = await fetch(
      `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${apiKey}`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          contents: [
            {
              parts: [
                {
                  text: prompt,
                },
              ],
            },
          ],
          generationConfig: {
            temperature: 0.2, // Lower temperature for more predictable output
            maxOutputTokens: 1500,
            topP: 0.95,
            topK: 40,
          },
          safetySettings: [
            {
              category: "HARM_CATEGORY_DANGEROUS_CONTENT",
              threshold: "BLOCK_NONE",
            },
          ],
        }),
      }
    );

    if (!response.ok) {
      let errorMessage = `HTTP error ${response.status}`;
      try {
        const errorData = await response.json();
        console.error("Google Gemini API error details:", errorData);

        // Extract more specific error information if available
        if (errorData.error) {
          errorMessage = `Google Gemini API error: ${
            errorData.error.message ||
            errorData.error.status ||
            JSON.stringify(errorData.error)
          }`;

          // Log additional details for debugging
          if (errorData.error.details) {
            console.error("Error details:", errorData.error.details);
          }
        }
      } catch (parseError) {
        console.error("Failed to parse error response:", parseError);
        errorMessage = `HTTP error ${response.status}: ${response.statusText}`;
      }

      console.error(errorMessage);
      return generateFallbackMilitaryPsychologistAnalysis(responses);
    }

    const data = await response.json();

    // Log the full response for debugging
    console.log("Full Gemini API response for military psychologist:", data);

    // Extract the text from Gemini's response
    const analysisText = data.candidates?.[0]?.content?.parts?.[0]?.text;

    if (!analysisText) {
      console.error("No analysis content received from Google Gemini");
      console.error("Response structure:", JSON.stringify(data));
      return generateFallbackMilitaryPsychologistAnalysis(responses);
    }

    // Parse the military psychologist analysis
    try {
      // Log the raw response for debugging
      console.log(
        "Raw Google Gemini response for military psychologist:",
        analysisText
      );

      // Extract the Simple Screen section
      const positivityScoreMatch = analysisText.match(
        /Positivity Score:\s*(\d+)/i
      );
      const quickThinkingMatch = analysisText.match(
        /Quick Thinking Rating:\s*(Fast|Moderate|Slow)/i
      );
      const situationalAwarenessMatch = analysisText.match(
        /Situational Awareness Flag:\s*(✅|⚠️|❌)/i
      );
      const topStrengthMatch = analysisText.match(/Top Strength:\s*([^\n]+)/i);
      const criticalWeaknessMatch = analysisText.match(
        /Critical Weakness:\s*([^\n]+)/i
      );

      // Extract the Response Breakdown section
      const bestSegmentMatch =
        analysisText.match(/"Best Segment":\s*"([^"]+)"/i) ||
        analysisText.match(/"Best Segment":\s*([^\n]+)/i);
      const needsImprovementMatch =
        analysisText.match(/"Needs Improvement":\s*"([^"]+)"/i) ||
        analysisText.match(/"Needs Improvement":\s*([^\n]+)/i);
      const needsImprovementReasoningMatch = analysisText.match(
        /with reasoning\]\s*([^\n]+)/i
      );

      // Extract the Psychological Analysis section
      const positivityAnalysisMatch = analysisText.match(
        /Positivity:\s*([^\n]+)/i
      );
      const sensibleLogicMatch = analysisText.match(
        /Sensible Logic:\s*([^\n]+)/i
      );
      const quickThinkingAnalysisMatch = analysisText.match(
        /Quick Thinking:\s*([^\n]+)/i
      );
      const situationalAwarenessAnalysisMatch = analysisText.match(
        /Situational Awareness:\s*([^\n]+)/i
      );

      // Extract the Immersive Simulation section
      const simulationMatch = analysisText.match(
        /If this candidate faced\s*([^,]+),\s*they would likely\s*([^b]+)because\s*([^\n"]+)/i
      );

      // Extract the Final Recommendation section
      const recommendationMatch = analysisText.match(
        /Recommendation:\s*(Accept|Reinvestigate|Reject)/i
      );
      const reasonsMatch = analysisText.match(
        /Because:\s*-\s*([^\n]+)\s*-\s*([^\n]+)\s*-\s*([^\n]+)/i
      );

      // Create the military psychologist analysis object
      const militaryPsychologist = {
        positivityScore: positivityScoreMatch
          ? parseInt(positivityScoreMatch[1])
          : 5,
        quickThinkingRating: quickThinkingMatch
          ? (quickThinkingMatch[1] as "Fast" | "Moderate" | "Slow")
          : "Moderate",
        situationalAwarenessFlag: situationalAwarenessMatch
          ? (situationalAwarenessMatch[1] as "✅" | "⚠️" | "❌")
          : "⚠️",
        topStrength: topStrengthMatch
          ? topStrengthMatch[1].trim()
          : "Undetermined strength",
        criticalWeakness: criticalWeaknessMatch
          ? criticalWeaknessMatch[1].trim()
          : "Undetermined weakness",
        bestResponseSegment: bestSegmentMatch
          ? bestSegmentMatch[1].trim()
          : "No best segment identified",
        weakestResponseSegment: needsImprovementMatch
          ? needsImprovementMatch[1].trim()
          : "No weak segment identified",
        weakestResponseReasoning: needsImprovementReasoningMatch
          ? needsImprovementReasoningMatch[1].trim()
          : "",
        positivityAnalysis: positivityAnalysisMatch
          ? positivityAnalysisMatch[1].trim()
          : "No positivity analysis available",
        sensibleLogicAnalysis: sensibleLogicMatch
          ? sensibleLogicMatch[1].trim()
          : "No logic analysis available",
        quickThinkingAnalysis: quickThinkingAnalysisMatch
          ? quickThinkingAnalysisMatch[1].trim()
          : "No thinking analysis available",
        situationalAwarenessAnalysis: situationalAwarenessAnalysisMatch
          ? situationalAwarenessAnalysisMatch[1].trim()
          : "No awareness analysis available",
        simulationScenario: simulationMatch
          ? simulationMatch[1].trim()
          : "Unspecified scenario",
        simulationPrediction: simulationMatch
          ? simulationMatch[2].trim()
          : "Unpredictable response",
        simulationReasoning: simulationMatch
          ? simulationMatch[3].trim()
          : "Insufficient evidence",
        recommendation: recommendationMatch
          ? (recommendationMatch[1] as "Accept" | "Reinvestigate" | "Reject")
          : "Reinvestigate",
        recommendationReasons: reasonsMatch
          ? [
              reasonsMatch[1].trim(),
              reasonsMatch[2].trim(),
              reasonsMatch[3].trim(),
            ]
          : [
              "Insufficient data for conclusive recommendation",
              "Further assessment recommended",
              "Consider additional testing",
            ],

        // Add Cultural Fit Algorithm data to all new analyses - dynamically generated based on responses
        culturalFitAlgorithm: generateCulturalFitAlgorithmData(responses),
      };

      // Generate a basic traits analysis based on the military psychologist analysis
      const traits: { [key: string]: { score: number; feedback: string } } = {};
      militaryTraitsForAI.forEach((trait) => {
        let score = 50; // Default score

        // Adjust scores based on military psychologist analysis
        if (
          trait.name === "Leadership" &&
          militaryPsychologist.topStrength?.toLowerCase().includes("leader")
        ) {
          score = 80 + Math.floor(Math.random() * 15);
        } else if (
          trait.name === "Adaptability" &&
          militaryPsychologist.quickThinkingRating === "Fast"
        ) {
          score = 75 + Math.floor(Math.random() * 15);
        } else if (
          trait.name === "Decision Making" &&
          militaryPsychologist.situationalAwarenessFlag === "✅"
        ) {
          score = 75 + Math.floor(Math.random() * 15);
        } else if (
          trait.name === "Problem Solving" &&
          militaryPsychologist.sensibleLogicAnalysis?.includes("practical")
        ) {
          score = 70 + Math.floor(Math.random() * 15);
        } else if (
          trait.name === "Resilience" &&
          militaryPsychologist.positivityScore > 7
        ) {
          score = 75 + Math.floor(Math.random() * 15);
        } else if (
          militaryPsychologist.criticalWeakness
            ?.toLowerCase()
            .includes(trait.name.toLowerCase())
        ) {
          score = 30 + Math.floor(Math.random() * 20);
        } else {
          // Random score between 40 and 80
          score = 40 + Math.floor(Math.random() * 40);
        }

        traits[trait.name] = {
          score,
          feedback: `Based on military psychologist analysis: ${
            score > 70
              ? "Strong"
              : score > 50
              ? "Adequate"
              : "Needs improvement"
          }`,
        };
      });

      // Calculate overall score
      const overallScore = calculateAverageScore(traits);

      // Determine strengths and improvements
      const sortedTraits = Object.entries(traits).sort(
        (a, b) => b[1].score - a[1].score
      );
      const strengths = sortedTraits.slice(0, 3).map(([name]) => name);
      const improvements = sortedTraits
        .slice(-3)
        .map(([name]) => name)
        .reverse();

      // Create detailed feedback from the military psychologist analysis
      const detailedFeedback = `
Military Psychologist Analysis:

${militaryPsychologist.positivityAnalysis}

${militaryPsychologist.sensibleLogicAnalysis}

${militaryPsychologist.quickThinkingAnalysis}

${militaryPsychologist.situationalAwarenessAnalysis}

Simulation: ${militaryPsychologist.simulationScenario} → ${
        militaryPsychologist.simulationPrediction
      } (${militaryPsychologist.simulationReasoning})

Final Recommendation: ${militaryPsychologist.recommendation}
${militaryPsychologist.recommendationReasons
  .map((reason) => `- ${reason}`)
  .join("\n")}
      `.trim();

      // Return the complete analysis
      return {
        overallScore,
        strengths,
        improvements,
        detailedFeedback,
        traits,
        isAIGenerated: true,
        militaryPsychologist,
      };
    } catch (parseError) {
      console.error(
        "Error processing military psychologist analysis:",
        parseError
      );
      return generateFallbackMilitaryPsychologistAnalysis(
        responses,
        false,
        `Error processing military psychologist analysis: ${
          parseError instanceof Error ? parseError.message : String(parseError)
        }`
      );
    }
  } catch (error) {
    console.error("Error analyzing responses as military psychologist:", error);
    return generateFallbackMilitaryPsychologistAnalysis(
      responses,
      false,
      `Error analyzing as military psychologist: ${
        error instanceof Error ? error.message : String(error)
      }`
    );
  }
};

/**
 * Generate a fallback military psychologist analysis when AI analysis fails
 * @param responses Array of user responses
 * @param isPartialAI Whether this is a partial AI-generated analysis (true) or completely fallback (false)
 * @param errorReason Optional reason for the fallback
 * @returns A basic military psychologist analysis result
 */
const generateFallbackMilitaryPsychologistAnalysis = (
  responses: string[],
  isPartialAI: boolean = false,
  errorReason: string = ""
): AIAnalysisResult => {
  const validResponses = responses.filter(
    (response) => response && response.trim() !== ""
  );
  const responseCount = validResponses.length;

  // Generate traits with basic scores
  const traits: { [key: string]: { score: number; feedback: string } } = {};
  militaryTraitsForAI.forEach((trait) => {
    const baseScore =
      responseCount > 0 ? 50 + Math.floor(Math.random() * 20) : 0;
    traits[trait.name] = {
      score: baseScore,
      feedback:
        responseCount > 0
          ? `Based on your responses, you show ${
              baseScore > 60 ? "good" : "some"
            } potential in this area.`
          : "No responses provided to analyze this trait.",
    };
  });

  // Calculate overall score as average of trait scores
  const overallScore = calculateAverageScore(traits);

  // Create a detailed feedback message that includes the error reason if provided
  let feedbackMessage =
    responseCount > 0
      ? "This is a basic military psychologist analysis based on your responses. For more detailed insights, please ensure the AI analysis service is properly configured."
      : "No responses provided. Please complete the test to receive a military psychologist analysis.";

  if (errorReason) {
    feedbackMessage += ` (Error: ${errorReason})`;
  }

  // Create a basic military psychologist analysis
  const militaryPsychologist = {
    positivityScore: responseCount > 0 ? 5 : 0,
    quickThinkingRating: "Moderate" as const,
    situationalAwarenessFlag: "⚠️" as const,
    topStrength:
      responseCount > 0 ? "Basic positive traits" : "No data available",
    criticalWeakness:
      responseCount > 0 ? "Insufficient response detail" : "No data available",
    bestResponseSegment:
      responseCount > 0
        ? validResponses[0].substring(0, 100) + "..."
        : "No responses provided",
    weakestResponseSegment: "No specific weaknesses identified",
    weakestResponseReasoning: "Insufficient data for detailed analysis",
    positivityAnalysis:
      "Unable to perform detailed positivity analysis with available data",
    sensibleLogicAnalysis:
      "Unable to assess logical reasoning with available data",
    quickThinkingAnalysis:
      "Unable to evaluate thinking speed with available data",
    situationalAwarenessAnalysis:
      "Unable to assess situational awareness with available data",
    simulationScenario: "a standard military training exercise",
    simulationPrediction: "perform at an average level",
    simulationReasoning: "insufficient data to make a more specific prediction",
    recommendation: "Reinvestigate" as const,
    recommendationReasons: [
      "Insufficient data for conclusive assessment",
      "Consider additional psychological evaluation",
      "Standard protocols suggest further screening",
    ],

    // Cultural Fit Algorithm - dynamically generated based on responses
    culturalFitAlgorithm: generateCulturalFitAlgorithmData(responses),
  };

  return {
    overallScore,
    strengths:
      responseCount > 0
        ? Object.entries(traits)
            .sort((a, b) => b[1].score - a[1].score)
            .slice(0, 3)
            .map(([name]) => name)
        : [],
    improvements:
      responseCount > 0
        ? Object.entries(traits)
            .sort((a, b) => a[1].score - b[1].score)
            .slice(0, 3)
            .map(([name]) => name)
        : [],
    detailedFeedback: feedbackMessage,
    traits,
    isAIGenerated: isPartialAI,
    militaryPsychologist,
  };
};

/**
 * Generate Cultural Fit Algorithm data based on responses
 * @param responses Array of user responses
 * @returns Cultural Fit Algorithm data
 */
export const generateCulturalFitAlgorithmData = (responses: string[]) => {
  // Filter out empty responses
  const validResponses = responses.filter(
    (response) => response && response.trim() !== ""
  );
  const responseCount = validResponses.length;
  const combinedText = validResponses.join(" ").toLowerCase();

  // Calculate metrics based on response content and count
  const weCount = (combinedText.match(/\bwe\b/g) || []).length;
  const iCount = (combinedText.match(/\bi\b/g) || []).length;

  // Military jargon words
  const militaryTerms = [
    "command",
    "mission",
    "objective",
    "tactical",
    "strategic",
    "deploy",
    "operation",
    "regiment",
    "battalion",
    "squad",
    "platoon",
    "officer",
    "rank",
    "duty",
    "honor",
    "discipline",
    "protocol",
    "briefing",
  ];

  // Count military terms used
  let militaryTermsUsed = 0;
  militaryTerms.forEach((term) => {
    if (combinedText.includes(term)) {
      militaryTermsUsed++;
    }
  });

  const militaryJargonPercentage =
    responseCount > 0
      ? Math.min(
          100,
          Math.round((militaryTermsUsed / militaryTerms.length) * 100)
        )
      : 50;

  // Chain of command references
  const hierarchyTerms = [
    "commander",
    "superior",
    "captain",
    "colonel",
    "general",
    "sergeant",
    "lieutenant",
  ];
  let hierarchyReferences = 0;
  hierarchyTerms.forEach((term) => {
    const matches = combinedText.match(new RegExp(`\\b${term}\\b`, "g"));
    if (matches) {
      hierarchyReferences += matches.length;
    }
  });

  // Check for sensitivity flags
  const sensitivityTerms = [
    "race",
    "gender",
    "religion",
    "ethnic",
    "minority",
    "discriminate",
  ];
  let sensitivityFlags = 0;
  sensitivityTerms.forEach((term) => {
    const matches = combinedText.match(new RegExp(`\\b${term}\\b`, "g"));
    if (matches) {
      sensitivityFlags += matches.length;
    }
  });

  // Determine humor style
  let humorStyle = "Neutral";
  if (
    combinedText.includes("joke") ||
    combinedText.includes("funny") ||
    combinedText.includes("laugh")
  ) {
    if (
      combinedText.includes("myself") ||
      combinedText.includes("my mistake")
    ) {
      humorStyle = "Self-deprecating";
    } else if (
      combinedText.includes("they") &&
      (combinedText.includes("fail") || combinedText.includes("stupid"))
    ) {
      humorStyle = "Mocking";
    }
  }

  // Check for value mentions
  const militaryValues = [
    "sacrifice",
    "duty",
    "honor",
    "courage",
    "loyalty",
    "integrity",
  ];
  const individualValues = [
    "rights",
    "freedom",
    "personal",
    "individual",
    "myself",
    "my own",
  ];

  let militaryValueCount = 0;
  militaryValues.forEach((value) => {
    const matches = combinedText.match(new RegExp(`\\b${value}\\b`, "g"));
    if (matches) {
      militaryValueCount += matches.length;
    }
  });

  let individualValueCount = 0;
  individualValues.forEach((value) => {
    const matches = combinedText.match(new RegExp(`\\b${value}\\b`, "g"));
    if (matches) {
      individualValueCount += matches.length;
    }
  });

  // Check for order challenge phrases
  const challengePhrases = [
    "but what if",
    "i disagree",
    "alternative",
    "instead of",
    "rather than",
    "question",
  ];
  let challengeCount = 0;
  challengePhrases.forEach((phrase) => {
    const matches = combinedText.match(new RegExp(phrase, "g"));
    if (matches) {
      challengeCount += matches.length;
    }
  });

  // Calculate challenge probability based on response count and challenge phrases
  const challengeProbability =
    responseCount > 0
      ? Math.min(100, Math.round((challengeCount / responseCount) * 100))
      : 25;

  // Check for leadership seeking
  const leadershipPhrases = [
    "i would lead",
    "take charge",
    "my decision",
    "i decide",
    "under my command",
  ];
  let leadershipCount = 0;
  leadershipPhrases.forEach((phrase) => {
    const matches = combinedText.match(new RegExp(phrase, "g"));
    if (matches) {
      leadershipCount += matches.length;
    }
  });

  // Generate team language index
  const teamLanguageIndex =
    responseCount > 0
      ? Math.round(
          (weCount * 2 + militaryJargonPercentage + militaryValueCount * 5) /
            (1 + iCount * 0.5 + individualValueCount * 2)
        )
      : 78;

  // Determine risk profile based on metrics
  let riskLevel = "average";
  let riskPercentage = 0;

  if (challengeProbability > 40 || sensitivityFlags > 3) {
    riskLevel = "higher than avg";
    riskPercentage = challengeProbability;
  } else if (
    militaryJargonPercentage > 70 &&
    militaryValueCount > individualValueCount
  ) {
    riskLevel = "lower than avg";
    riskPercentage = 100 - challengeProbability;
  }

  // Determine recommended action
  let recommendedAction = "Standard integration protocol";

  if (challengeProbability > 40) {
    recommendedAction = "Senior NCO mentorship program";
  } else if (militaryJargonPercentage < 30) {
    recommendedAction = "Military terminology training";
  } else if (sensitivityFlags > 2) {
    recommendedAction = "Cultural sensitivity workshop";
  } else if (humorStyle === "Mocking") {
    recommendedAction = "Team cohesion exercises";
  }

  // Create the Cultural Fit Algorithm data
  return {
    purpose: "Predict team cohesion likelihood",
    collectiveLanguageScore: {
      weUsage: `Used "we" ${weCount}x vs "I" ${iCount}x`,
      militaryJargonMatch: `${militaryJargonPercentage}% of proper terminology used`,
      chainOfCommandMentions: `${hierarchyReferences} hierarchy references`,
    },
    culturalCompatibility: {
      sensitivityFlags,
      humorStyle,
      sharedValueAlignment: `"${militaryValues[0]}" mentioned ${militaryValueCount}x vs "${individualValues[0]}" ${individualValueCount}x`,
    },
    authorityResponsePattern: {
      orderChallengeProbability: `${challengeProbability}% (Based on phrases like "But what if...")`,
      leadershipSeek: `${leadershipCount} unsolicited command attempts in scenarios`,
    },
    teamLanguageIndex: `${teamLanguageIndex}% (Battalion Avg: 82%)`,
    riskProfile: `${riskPercentage}% ${riskLevel} order challenge likelihood`,
    recommendedAction,
  };
};

/**
 * Add response quality assessment to an existing analysis
 * @param analysis Existing analysis
 * @param responses Original responses
 * @returns Updated analysis with response quality assessment
 */
export const addResponseQualityAssessment = (
  analysis: AIAnalysisResult,
  responses: string[]
): AIAnalysisResult => {
  console.log(
    "addResponseQualityAssessment called with analysis:",
    analysis ? "valid analysis object" : "null or undefined analysis"
  );
  console.log(
    "Responses length:",
    responses ? responses.length : "null or undefined responses"
  );

  // If the analysis already has a responseQualityAssessment, return it as is
  if (
    analysis.responseQualityAssessment &&
    Object.keys(analysis.responseQualityAssessment).length > 0
  ) {
    console.log(
      "Analysis already has responseQualityAssessment with",
      Object.keys(analysis.responseQualityAssessment).length,
      "entries"
    );
    return analysis;
  }

  console.log(
    "Analysis does not have responseQualityAssessment, creating it now"
  );

  // Filter out empty responses
  const validResponses = responses.filter(
    (response) => response && response.trim() !== ""
  );

  console.log("Valid responses count:", validResponses.length);

  // Create a response quality assessment
  const responseQualityAssessment: AIAnalysisResult["responseQualityAssessment"] =
    {};

  if (validResponses.length > 0) {
    validResponses.forEach((response, index) => {
      // Simple heuristic: responses with more than 100 characters are considered "Good"
      const isGood = response.length > 100;

      // Create assessment object without undefined values for Firestore compatibility
      const assessmentObj: {
        quality: "Good" | "Bad";
        response: string;
        improvementSuggestions?: string[];
      } = {
        quality: isGood ? "Good" : "Bad",
        response: response.substring(0, 50),
      };

      // Only add improvementSuggestions for "Bad" quality responses
      if (!isGood) {
        assessmentObj.improvementSuggestions = [
          "Provide more details and examples in your response",
          "Consider military context and protocols in your answer",
        ];
      }

      responseQualityAssessment[index] = assessmentObj;

      console.log(
        `Added assessment for response ${index}: ${isGood ? "Good" : "Bad"}`
      );
    });
  }

  // Create a new analysis object with the response quality assessment
  const updatedAnalysis = {
    ...analysis,
    responseQualityAssessment:
      validResponses.length > 0 ? responseQualityAssessment : undefined,
  };

  console.log(
    "Created updated analysis with responseQualityAssessment:",
    updatedAnalysis.responseQualityAssessment ? "present" : "not present"
  );

  return updatedAnalysis;
};
