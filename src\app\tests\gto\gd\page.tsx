"use client";

import {
  ArrowLeftIcon,
  UserGroupIcon,
  PlayIcon,
} from "@heroicons/react/24/outline";
import NavigationButton from "@/components/NavigationButton";
import React from "react";

// All 32 GD Topics
const gdTopics = [
  {
    id: "ai-agriculture",
    title: "AI in Agriculture",
    topic:
      "How can Nepal integrate AI into its agricultural practices to boost productivity?",
    keyAngles: ["Precision farming", "Resource allocation", "Farmer training"],
  },
  {
    id: "space-disaster",
    title: "Space Technology for Disaster Management",
    topic:
      "Should Nepal invest in space technology for better disaster management?",
    keyAngles: [
      "Satellite imagery",
      "Early warning systems",
      "Cost implications",
    ],
  },
  {
    id: "digital-art-craft",
    title: "Digital Marketing for Traditional Arts",
    topic:
      "Can Nepal's traditional art and craft industries benefit from digital marketing strategies?",
    keyAngles: [
      "Global reach",
      "E-commerce platforms",
      "Preserving cultural heritage",
    ],
  },
  {
    id: "education-digital-age",
    title: "Education for Digital Age",
    topic:
      "Is Nepal's education system adequately preparing students for the digital age?",
    keyAngles: ["Curriculum reforms", "Teacher training", "Digital divide"],
  },
  {
    id: "hydropower-export",
    title: "Hydropower Energy Export",
    topic:
      "How can Nepal leverage its hydropower potential to become a major energy exporter?",
    keyAngles: [
      "Infrastructure development",
      "International partnerships",
      "Environmental impact",
    ],
  },
  {
    id: "smart-cities",
    title: "Smart Cities Development",
    topic:
      "Should Nepal prioritize the development of smart cities to improve urban living?",
    keyAngles: [
      "Sustainable urban planning",
      "Technology integration",
      "Citizen engagement",
    ],
  },
  {
    id: "vr-ar-tourism",
    title: "VR/AR in Tourism",
    topic:
      "Can Nepal's tourism industry benefit from virtual reality and augmented reality technologies?",
    keyAngles: [
      "Enhanced visitor experiences",
      "Marketing strategies",
      "Technological investment",
    ],
  },
  {
    id: "brain-drain",
    title: "Addressing Brain Drain",
    topic:
      "How can Nepal address the brain drain of its skilled professionals?",
    keyAngles: [
      "Job creation",
      "Incentives for return",
      "International collaboration",
    ],
  },
  {
    id: "climate-change",
    title: "Climate Change Mitigation",
    topic:
      "Should Nepal adopt a more aggressive stance on climate change mitigation?",
    keyAngles: [
      "Carbon emissions reduction",
      "Renewable energy adoption",
      "International agreements",
    ],
  },
  {
    id: "telemedicine",
    title: "Telemedicine in Healthcare",
    topic: "Can Nepal's healthcare system be improved through telemedicine?",
    keyAngles: [
      "Access to remote areas",
      "Doctor-patient interaction",
      "Technological challenges",
    ],
  },
  {
    id: "youth-entrepreneurship",
    title: "Youth Entrepreneurship",
    topic: "How can Nepal promote entrepreneurship among its youth?",
    keyAngles: [
      "Startup ecosystem",
      "Funding opportunities",
      "Educational support",
    ],
  },
  {
    id: "pharmaceutical-industry",
    title: "Pharmaceutical Industry Development",
    topic: "Should Nepal focus on developing its own pharmaceutical industry?",
    keyAngles: [
      "Self-reliance",
      "Quality control",
      "International competition",
    ],
  },
  {
    id: "cultural-festivals",
    title: "Cultural Festivals for Unity",
    topic:
      "Can Nepal's cultural festivals be used to promote national unity and tourism?",
    keyAngles: [
      "Event management",
      "Cultural preservation",
      "Economic benefits",
    ],
  },
  {
    id: "waste-management",
    title: "Waste Management Improvement",
    topic: "How can Nepal improve its waste management practices?",
    keyAngles: [
      "Recycling programs",
      "Public awareness",
      "Government policies",
    ],
  },
  {
    id: "electric-vehicles",
    title: "Electric Vehicle Infrastructure",
    topic: "Should Nepal invest in electric vehicle infrastructure?",
    keyAngles: [
      "Environmental benefits",
      "Economic feasibility",
      "Public adoption",
    ],
  },
  {
    id: "traditional-medicine",
    title: "Traditional Medicine Integration",
    topic:
      "Can Nepal's traditional medicine practices be integrated with modern healthcare?",
    keyAngles: [
      "Complementary treatments",
      "Research and development",
      "Patient acceptance",
    ],
  },
  {
    id: "cyber-defense",
    title: "Cyber Defense Capabilities",
    topic: "How can Nepal enhance its cyber defense capabilities?",
    keyAngles: [
      "Training programs",
      "International cooperation",
      "Technological upgrades",
    ],
  },
  {
    id: "biodiversity-conservation",
    title: "Biodiversity Conservation",
    topic: "Should Nepal prioritize the conservation of its biodiversity?",
    keyAngles: [
      "Ecological importance",
      "Economic benefits",
      "Conservation strategies",
    ],
  },
  {
    id: "sustainable-textile",
    title: "Sustainable Textile Industry",
    topic:
      "Can Nepal's textile industry compete globally with sustainable practices?",
    keyAngles: [
      "Eco-friendly materials",
      "Fair labor practices",
      "Market demand",
    ],
  },
  {
    id: "public-transportation",
    title: "Public Transportation System",
    topic: "How can Nepal improve its public transportation system?",
    keyAngles: [
      "Infrastructure development",
      "Public-private partnerships",
      "User convenience",
    ],
  },
  {
    id: "it-outsourcing",
    title: "IT Outsourcing Industry",
    topic: "Should Nepal focus on developing its IT outsourcing industry?",
    keyAngles: ["Skilled workforce", "Global demand", "Economic benefits"],
  },
  {
    id: "organic-farming",
    title: "Organic Farming Practices",
    topic:
      "Can Nepal's agricultural sector benefit from organic farming practices?",
    keyAngles: ["Soil health", "Market demand", "Certification processes"],
  },
  {
    id: "youth-unemployment",
    title: "Youth Unemployment",
    topic: "How can Nepal address the issue of youth unemployment?",
    keyAngles: [
      "Skill development programs",
      "Job creation",
      "Entrepreneurship support",
    ],
  },
  {
    id: "nuclear-energy",
    title: "Nuclear Energy Alternative",
    topic:
      "Should Nepal invest in nuclear energy as an alternative to hydropower?",
    keyAngles: [
      "Energy security",
      "Environmental concerns",
      "Public perception",
    ],
  },
  {
    id: "film-industry",
    title: "Film Industry Economic Growth",
    topic: "Can Nepal's film industry contribute to its economic growth?",
    keyAngles: [
      "Cultural promotion",
      "Job creation",
      "International recognition",
    ],
  },
  {
    id: "domestic-cybersecurity",
    title: "Domestic Cybersecurity Infrastructure",
    topic:
      "Should Nepal prioritize developing domestic cybersecurity infrastructure over relying on foreign tech alliances?",
    keyAngles: [
      "Sovereignty vs. cost-effectiveness",
      "Skill gaps",
      "Geopolitical risks",
    ],
  },
  {
    id: "youth-it-startups",
    title: "Youth IT Startups for Military-Civilian Tech",
    topic:
      "Can Nepal's youth-driven IT startups bridge the gap between military and civilian technological needs?",
    keyAngles: [
      "Dual-use technologies",
      "Collaboration models",
      "Funding challenges",
    ],
  },
  {
    id: "digital-literacy-army",
    title: "Digital Literacy for Army Personnel",
    topic:
      "Is mandatory digital literacy training necessary for all Nepal Army personnel?",
    keyAngles: [
      "Operational efficiency",
      "Resistance to change",
      "Resource allocation",
    ],
  },
  {
    id: "open-source-security",
    title: "Open-Source Software vs National Security",
    topic:
      "How can Nepal balance leveraging open-source software with ensuring national security?",
    keyAngles: [
      "Vulnerability risks",
      "Cost savings",
      "Dependency on global communities",
    ],
  },
  {
    id: "ai-surveillance",
    title: "AI-Driven Border Surveillance",
    topic:
      "Should Nepal adopt AI-driven surveillance systems in border regions?",
    keyAngles: ["Ethical concerns", "Accuracy", "Manpower reduction"],
  },
  {
    id: "cyber-law-framework",
    title: "Cyber-Law Framework Adequacy",
    topic:
      "Is Nepal's current cyber-law framework adequate to combat modern threats?",
    keyAngles: [
      "Legal loopholes",
      "Enforcement challenges",
      "Public awareness",
    ],
  },
  {
    id: "ethical-hacking-hub",
    title: "Ethical Hacking Regional Hub",
    topic: "Can Nepal become a regional hub for ethical hacking talent?",
    keyAngles: [
      "Education reforms",
      "Global competitions",
      "Private-sector partnerships",
    ],
  },
];

export default function GroupDiscussionPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="mx-auto max-w-7xl px-6">
        <NavigationButton
          href="/tests/gto"
          className="mb-8 inline-flex items-center text-gray-600 hover:text-gray-900"
        >
          <ArrowLeftIcon className="h-5 w-5 mr-2" />
          Back to GTO Test
        </NavigationButton>

        <div className="bg-white rounded-2xl shadow-sm p-8 border-t-4 border-green-500 mb-8">
          <div className="flex items-center space-x-3 mb-6">
            <div className="p-2 rounded-full bg-green-100">
              <UserGroupIcon className="h-8 w-8 text-green-600" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900">
              Group Discussion (GD) Practice
            </h1>
          </div>

          <p className="text-lg text-gray-700 mb-8">
            Choose any topic below to start practicing your group discussion
            skills. Click on any card to begin your practice session
            immediately.
          </p>

          {/* Topics Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {gdTopics.map((topic, index) => (
              <NavigationButton
                key={topic.id}
                href={`/tests/gto/gd/practice?topic=${topic.id}`}
                className="block"
              >
                <div className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg hover:border-green-300 transition-all duration-200 cursor-pointer group">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <span className="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                        Topic {index + 1}
                      </span>
                    </div>
                    <PlayIcon className="h-5 w-5 text-gray-400 group-hover:text-green-600 transition-colors" />
                  </div>

                  <h3 className="text-lg font-semibold text-gray-900 mb-3 group-hover:text-green-700 transition-colors">
                    {topic.title}
                  </h3>

                  <p className="text-sm text-gray-600 mb-4 line-clamp-3">
                    {topic.topic}
                  </p>

                  <div className="space-y-2">
                    <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Key Discussion Points:
                    </p>
                    <div className="flex flex-wrap gap-1">
                      {topic.keyAngles.map((angle, angleIndex) => (
                        <span
                          key={angleIndex}
                          className="inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded"
                        >
                          {angle}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </NavigationButton>
            ))}
          </div>

          {/* Quick Tips Section */}
          <div className="mt-12 bg-green-50 border border-green-200 rounded-lg p-6">
            <h2 className="text-xl font-semibold text-green-900 mb-4">
              Quick GD Tips
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-medium text-green-800 mb-2">Do&apos;s</h3>
                <ul className="text-sm text-green-700 space-y-1">
                  <li>• Listen actively to others</li>
                  <li>• Support points with examples</li>
                  <li>• Maintain eye contact</li>
                  <li>• Be respectful and polite</li>
                  <li>• Take initiative when needed</li>
                </ul>
              </div>
              <div>
                <h3 className="font-medium text-red-800 mb-2">Don&apos;ts</h3>
                <ul className="text-sm text-red-700 space-y-1">
                  <li>• Don&apos;t interrupt others</li>
                  <li>• Don&apos;t be too passive</li>
                  <li>• Don&apos;t get emotional</li>
                  <li>• Don&apos;t deviate from topic</li>
                  <li>• Don&apos;t monopolize discussion</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
