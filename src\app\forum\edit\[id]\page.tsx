"use client";

import React, { useState, useEffect, use } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useAuth } from "@/context/GoogleAuthContext";
import { getTopic, updateTopic } from "@/services/forumService";
import { ForumTopic } from "@/types/forum";
import GuidelinesAcceptance from "@/components/forum/GuidelinesAcceptance";
import {
  ArrowLeftIcon,
  ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";

interface EditTopicPageProps {
  params: {
    id: string;
  };
}

export default function EditTopicPage({ params }: EditTopicPageProps) {
  const unwrappedParams = use(params);
  const topicId = unwrappedParams.id;
  const { user, loading } = useAuth();
  const router = useRouter();
  const [topic, setTopic] = useState<ForumTopic | null>(null);
  const [title, setTitle] = useState("");
  const [content, setContent] = useState("");
  const [tags, setTags] = useState<string[]>([]);
  const [guidelinesAccepted, setGuidelinesAccepted] = useState(true);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load topic when the component mounts
  useEffect(() => {
    if (!loading) {
      if (!user) {
        // Redirect to login if not authenticated
        router.push(`/login?redirect=/forum/edit/${topicId}`);
      } else {
        loadTopic();
      }
    }
  }, [user, loading, topicId]);

  // Load topic from Firestore
  const loadTopic = async () => {
    try {
      setIsLoading(true);
      const topicData = await getTopic(topicId);

      if (!topicData) {
        setError("Topic not found");
        return;
      }

      // Check if the current user is the author
      if (topicData.authorId !== user?.uid) {
        setError("You can only edit your own topics");
        return;
      }

      setTopic(topicData);
      setTitle(topicData.title);
      setContent(topicData.content);
      setTags(topicData.tags || []);
    } catch (error) {
      console.error("Error loading topic:", error);
      setError(
        "Failed to load topic. It may have been removed or you don't have permission to edit it."
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!guidelinesAccepted) {
      setError("You must accept the community guidelines to update a topic.");
      return;
    }

    try {
      setIsSubmitting(true);
      setError(null);

      if (!user) {
        throw new Error("You must be logged in to update a topic.");
      }

      // Update the topic
      await updateTopic(topicId, user.uid, title, content, tags);

      // Redirect to the topic page
      router.push(`/forum/topic/${topicId}`);
    } catch (error) {
      console.error("Error updating topic:", error);
      setError(
        error instanceof Error
          ? error.message
          : "An error occurred while updating your topic. Please try again."
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="mx-auto max-w-4xl px-6">
          <div className="flex justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error && !topic) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="mx-auto max-w-4xl px-6">
          <Link
            href="/forum"
            className="inline-flex items-center text-sm font-medium text-indigo-600 hover:text-indigo-500 mb-6"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-1" />
            Back to Forum
          </Link>

          <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="mx-auto max-w-4xl px-6">
        <Link
          href={`/forum/topic/${topicId}`}
          className="inline-flex items-center text-sm font-medium text-indigo-600 hover:text-indigo-500 mb-6"
        >
          <ArrowLeftIcon className="h-4 w-4 mr-1" />
          Back to Topic
        </Link>

        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="px-6 py-5 border-b border-gray-200">
            <h1 className="text-xl font-semibold text-gray-900">Edit Topic</h1>
          </div>

          <div className="p-4 sm:p-6">
            {/* Error message */}
            {error && (
              <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-red-700">{error}</p>
                  </div>
                </div>
              </div>
            )}

            <form onSubmit={handleSubmit}>
              {/* Title */}
              <div className="mb-6">
                <label
                  htmlFor="title"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Title
                </label>
                <input
                  type="text"
                  id="title"
                  name="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm px-4 py-3"
                  placeholder="Enter a descriptive title"
                  required
                  maxLength={100}
                />
              </div>

              {/* Content */}
              <div className="mb-6">
                <label
                  htmlFor="content"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Content
                </label>
                <textarea
                  id="content"
                  name="content"
                  value={content}
                  onChange={(e) => setContent(e.target.value)}
                  rows={8}
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm px-4 py-3"
                  placeholder="Share your thoughts, questions, or information..."
                  required
                />
              </div>

              {/* Community Guidelines */}
              <GuidelinesAcceptance
                accepted={guidelinesAccepted}
                onAcceptChange={setGuidelinesAccepted}
              />

              {/* Submit button */}
              <div className="mt-8 flex flex-col sm:flex-row justify-end gap-3">
                <Link
                  href={`/forum/topic/${topicId}`}
                  className="px-5 py-2.5 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Cancel
                </Link>
                <button
                  type="submit"
                  disabled={isSubmitting || !guidelinesAccepted}
                  className="px-5 py-2.5 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:bg-indigo-400 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? "Updating..." : "Update Topic"}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}
