"use client";

import { useState, useEffect, useRef } from "react";

interface TestTimerProps {
  initialTime: number;
  isPaused: boolean;
  isActive: boolean;
  onTimeUp: () => void;
  onTimeUpdate?: (timeLeft: number) => void;
}

export default function TestTimer({
  initialTime,
  isPaused,
  isActive,
  onTimeUp,
  onTimeUpdate,
}: TestTimerProps) {
  const [timeLeft, setTimeLeft] = useState(initialTime);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const startTimeRef = useRef<number>(0);
  const pausedTimeRef = useRef<number>(0);

  // Reset timer when initialTime changes
  useEffect(() => {
    setTimeLeft(initialTime);
    startTimeRef.current = Date.now();
    pausedTimeRef.current = 0;
  }, [initialTime]);

  useEffect(() => {
    // Clear any existing timer
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }

    // Don't start timer if not active or is paused
    if (!isActive || isPaused) {
      if (isPaused) {
        pausedTimeRef.current = Date.now() - startTimeRef.current;
      }
      return;
    }

    // Reset start time if coming from paused state
    if (pausedTimeRef.current > 0) {
      startTimeRef.current = Date.now() - pausedTimeRef.current;
      pausedTimeRef.current = 0;
    } else {
      startTimeRef.current = Date.now();
    }

    // Start the timer
    timerRef.current = setInterval(() => {
      const elapsedSeconds = Math.floor(
        (Date.now() - startTimeRef.current) / 1000
      );
      const newTimeLeft = Math.max(0, initialTime - elapsedSeconds);

      setTimeLeft(newTimeLeft);
      if (onTimeUpdate) {
        onTimeUpdate(newTimeLeft);
      }

      if (newTimeLeft === 0) {
        if (timerRef.current) {
          clearInterval(timerRef.current);
          timerRef.current = null;
        }
        onTimeUp();
      }
    }, 50);

    // Cleanup
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    };
  }, [isActive, isPaused, initialTime, onTimeUp, onTimeUpdate]);

  return timeLeft;
}
