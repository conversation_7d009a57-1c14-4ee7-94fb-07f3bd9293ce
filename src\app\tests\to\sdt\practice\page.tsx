"use client";

import { useRouter } from "next/navigation";
import SimpleButton from "@/components/SimpleButton";
import {
  ArrowLeftIcon,
  BookOpenIcon,
  ClockIcon,
} from "@heroicons/react/24/outline";

export default function SDTPractice() {
  const router = useRouter();

  // Using SimpleButton with href instead of onClick for better performance

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="mx-auto max-w-3xl px-6">
        <SimpleButton
          onClick={() => router.back()}
          className="mb-8 flex items-center text-gray-600 hover:text-gray-900"
        >
          <ArrowLeftIcon className="h-5 w-5 mr-2" />
          Back to TO Test
        </SimpleButton>

        <div className="bg-white rounded-2xl shadow-sm p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">
            Self Description Test (SDT) Practice
          </h1>
          <p className="text-gray-600 mb-8">
            The Self Description Test (SDT) evaluates your self-awareness,
            interpersonal skills, and ability to articulate your thoughts about
            yourself and your relationships. This test is a crucial part of the
            military selection process as it reveals your personality traits and
            how you perceive yourself in relation to others.
          </p>

          <div className="bg-amber-50 p-6 rounded-lg mb-8">
            <div className="flex items-center mb-4">
              <ClockIcon className="h-6 w-6 text-amber-600 mr-2" />
              <h2 className="text-xl font-semibold text-gray-900">
                Test Format
              </h2>
            </div>
            <ul className="list-disc list-inside space-y-2 text-gray-700 mb-4">
              <li>5 questions to be completed in 15 minutes</li>
              <li>All questions are displayed at once</li>
              <li>Answer each question thoughtfully and honestly</li>
              <li>The test will automatically submit when time expires</li>
            </ul>
            <div className="bg-white p-4 rounded-lg">
              <h3 className="font-medium mb-2 text-gray-900">Questions:</h3>
              <ol className="list-decimal list-inside space-y-1 text-gray-800">
                <li>What do your parents think of you?</li>
                <li>What do your teachers/employers think of you?</li>
                <li>What do your friends and colleagues think of you?</li>
                <li>What do you think about yourself?</li>
                <li>
                  What kind of person you would like to become or what
                  improvements you want to bring in yourself?
                </li>
              </ol>
            </div>
          </div>

          {/* Sample Answers Link */}
          <div className="bg-amber-50 p-3 sm:p-4 rounded-lg border border-amber-200 mb-4 sm:mb-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-0">
              <div className="flex items-start sm:items-center">
                <BookOpenIcon className="h-5 w-5 sm:h-6 sm:w-6 text-amber-600 mr-2 sm:mr-3 mt-0.5 sm:mt-0 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-amber-800 mb-0.5 sm:mb-1 text-sm sm:text-base">
                    Need help with SDT responses?
                  </h3>
                  <p className="text-xs sm:text-sm text-amber-700">
                    View sample answers for Self Description Test questions
                  </p>
                </div>
              </div>
              <SimpleButton
                onClick={() => router.push("/tests/to/sdt/sample-answers")}
                className="text-xs sm:text-sm font-medium text-white bg-amber-600 hover:bg-amber-700 py-2 px-3 sm:px-4 rounded-md transition-colors"
              >
                View Sample Answers
              </SimpleButton>
            </div>
          </div>

          <SimpleButton
            href="/tests/to/sdt/practice/SelfDescription/instructions"
            className="group relative overflow-hidden rounded-xl border border-amber-200 bg-white p-6 shadow-sm transition-all hover:border-amber-500 hover:shadow-md w-full text-left mt-8"
          >
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-4">
                <div className="rounded-lg bg-amber-50 p-3 text-amber-600 transition-colors group-hover:bg-amber-100">
                  <ClockIcon className="h-6 w-6" />
                </div>
                <div className="text-left">
                  <h3 className="text-lg font-semibold text-gray-900">
                    Self Description Test
                  </h3>
                  <p className="mt-1 text-sm text-gray-600">
                    Start the SDT practice session with 5 questions
                  </p>
                  <p className="mt-2 text-sm font-medium text-amber-600">
                    15 minute time limit
                  </p>
                </div>
              </div>
              <div className="rounded-full bg-gray-50 p-2 text-gray-400 transition-colors group-hover:bg-amber-50 group-hover:text-amber-600">
                <span className="text-amber-600 opacity-0 transition-opacity group-hover:opacity-100">
                  Start →
                </span>
              </div>
            </div>
            <div className="absolute inset-0 bg-gradient-to-r from-amber-50/0 via-amber-50/0 to-amber-50/0 opacity-0 transition-opacity group-hover:opacity-10" />
          </SimpleButton>
        </div>
      </div>
    </div>
  );
}
