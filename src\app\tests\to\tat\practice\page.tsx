"use client";

import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import SimpleButton from "@/components/SimpleButton";
import {
  ArrowLeftIcon,
  PlayIcon,
  BookOpenIcon,
} from "@heroicons/react/24/outline";
import { tatSets } from "../../../../../data/tat-sets";
import SetManagementButton from "@/components/SetManagementButton";
import AddSetModal from "@/components/AddSetModal";
import { useAuth } from "@/context/GoogleAuthContext";
import { addSet, getSets, SetData } from "@/services/setManagementService";

export default function TATPractice() {
  const router = useRouter();
  const { user } = useAuth();
  const [isAddSetModalOpen, setIsAddSetModalOpen] = useState(false);
  const [customSets, setCustomSets] = useState<SetData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load custom sets from Firestore
  useEffect(() => {
    const loadCustomSets = async () => {
      if (!user) {
        setIsLoading(false);
        return;
      }

      try {
        const sets = await getSets("tat");
        setCustomSets(sets);
      } catch (error) {
        console.error("Error loading custom TAT sets:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadCustomSets();
  }, [user]);

  const handleSetSelect = (setName: string) => {
    router.push(
      `/tests/to/tat/practice/${encodeURIComponent(setName)}/instructions`
    );
  };

  const handleAddSet = async (setData: SetData) => {
    if (!user) return;
    setError(null);

    try {
      await addSet(user.uid, "tat", setData);

      // Reload custom sets
      const sets = await getSets("tat");
      setCustomSets(sets);
    } catch (error) {
      console.error("Error adding TAT set:", error);
      if (error instanceof Error) {
        setError(error.message);
      } else {
        setError("An unknown error occurred");
      }
      throw error;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="mx-auto max-w-3xl px-6">
        <SimpleButton
          onClick={() => router.back()}
          className="mb-8 flex items-center text-gray-600 hover:text-gray-900"
        >
          <ArrowLeftIcon className="h-5 w-5 mr-2" />
          Back to TO Test
        </SimpleButton>

        <div className="bg-white rounded-2xl shadow-sm p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">
            TAT Practice
          </h1>

          <div className="space-y-6 text-gray-700">
            {/* Sample Answers Link */}
            <div className="bg-red-50 p-3 sm:p-4 rounded-lg border border-red-200 mb-8">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-0">
                <div className="flex items-start sm:items-center">
                  <BookOpenIcon className="h-5 w-5 sm:h-6 sm:w-6 text-red-600 mr-2 sm:mr-3 mt-0.5 sm:mt-0 flex-shrink-0" />
                  <div>
                    <h3 className="font-semibold text-red-800 mb-0.5 sm:mb-1 text-sm sm:text-base">
                      Need help with TAT stories?
                    </h3>
                    <p className="text-xs sm:text-sm text-red-700">
                      View sample stories for common TAT images
                    </p>
                  </div>
                </div>
                <SimpleButton
                  onClick={() => router.push("/tests/to/tat/sample-answers")}
                  className="px-3 py-1.5 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors text-sm"
                >
                  View Sample Stories
                </SimpleButton>
              </div>
            </div>

            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold">Select Image Set:</h2>
              <SetManagementButton
                onClick={() => setIsAddSetModalOpen(true)}
                testType="TAT"
              />
            </div>

            {/* Display errors */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
                <p className="text-red-700">{error}</p>
              </div>
            )}

            {isLoading ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-red-500"></div>
              </div>
            ) : (
              <div className="grid grid-cols-1 gap-6">
                {/* Default sets from data file */}
                {Object.entries(tatSets).map(([setName, setData]) => (
                  <SimpleButton
                    key={setName}
                    onClick={() => handleSetSelect(setName)}
                    className="group relative overflow-hidden rounded-xl border border-gray-200 bg-white p-6 shadow-sm transition-all hover:border-red-500 hover:shadow-md"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-4">
                        <div className="rounded-lg bg-red-50 p-3 text-red-600 transition-colors group-hover:bg-red-100">
                          <setData.icon className="h-6 w-6" />
                        </div>
                        <div className="text-left">
                          <h3 className="text-lg font-semibold text-gray-900">
                            {setName}
                          </h3>
                          <p className="mt-1 text-sm text-gray-600">
                            {setData.description}
                          </p>
                          <p className="mt-2 text-sm font-medium text-red-600">
                            {setData.images.length} images
                          </p>
                        </div>
                      </div>
                      <div className="rounded-full bg-gray-50 p-2 text-gray-400 transition-colors group-hover:bg-red-50 group-hover:text-red-600">
                        <PlayIcon className="h-5 w-5" />
                      </div>
                    </div>
                    <div className="absolute inset-0 bg-gradient-to-r from-red-50/0 via-red-50/0 to-red-50/0 opacity-0 transition-opacity group-hover:opacity-100" />
                  </SimpleButton>
                ))}

                {/* Custom sets from Firestore */}
                {customSets.map((set) => (
                  <SimpleButton
                    key={set.id}
                    onClick={() => handleSetSelect(set.name)}
                    className="group relative overflow-hidden rounded-xl border border-gray-200 bg-white p-6 shadow-sm transition-all hover:border-red-500 hover:shadow-md"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-4">
                        <div className="rounded-lg bg-red-50 p-3 text-red-600 transition-colors group-hover:bg-red-100">
                          {/* Use a default icon for custom sets */}
                          <PlayIcon className="h-6 w-6" />
                        </div>
                        <div className="text-left">
                          <div className="flex items-center">
                            <h3 className="text-lg font-semibold text-gray-900">
                              {set.name}
                            </h3>
                            <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                              Custom
                            </span>
                          </div>
                          <p className="mt-1 text-sm text-gray-600">
                            {set.description}
                          </p>
                          <p className="mt-2 text-sm font-medium text-red-600">
                            {set.images?.length || 0} images
                          </p>
                        </div>
                      </div>
                      <div className="rounded-full bg-gray-50 p-2 text-gray-400 transition-colors group-hover:bg-red-50 group-hover:text-red-600">
                        <PlayIcon className="h-5 w-5" />
                      </div>
                    </div>
                    <div className="absolute inset-0 bg-gradient-to-r from-red-50/0 via-red-50/0 to-red-50/0 opacity-0 transition-opacity group-hover:opacity-100" />
                  </SimpleButton>
                ))}
              </div>
            )}
          </div>

          {/* Add Set Modal */}
          <AddSetModal
            testType="TAT"
            isOpen={isAddSetModalOpen}
            onClose={() => setIsAddSetModalOpen(false)}
            onAddSet={handleAddSet}
          />
        </div>
      </div>
    </div>
  );
}
