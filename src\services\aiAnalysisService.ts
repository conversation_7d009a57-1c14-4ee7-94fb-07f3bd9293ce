import { db } from "@/lib/firebase";
import {
  collection,
  doc,
  setDoc,
  getDoc,
  getDocs,
  deleteDoc,
  query,
  where,
  orderBy,
  serverTimestamp,
} from "firebase/firestore";
import { AIAnalysisResult } from "./aiService";
import { TestType } from "./responseService";

// Interface for AI analysis document
export interface AIAnalysisDocument {
  userId: string;
  testType: TestType;
  setName: string;
  responseTimestamp: number; // Timestamp of the response this analysis is for
  analysis: AIAnalysisResult;
  createdAt: any;
  isAIGenerated: boolean;
  aiProvider?: "deepseek" | "military-psychologist"; // Which AI provider was used
  docId?: string; // Document ID for reference
}

/**
 * Save AI analysis result to Firestore
 * @param userId User ID
 * @param testType Type of test (SRT, WAT, TAT, SDT)
 * @param setName Set name
 * @param responseTimestamp Timestamp of the response this analysis is for
 * @param analysis AI analysis result
 * @returns Promise resolving to true if successful
 */
export const saveAIAnalysis = async (
  userId: string,
  testType: TestType,
  setName: string,
  responseTimestamp: number,
  analysis: AIAnalysisResult,
  aiProvider: "deepseek" | "military-psychologist" = "military-psychologist" // Default to Military Psychologist for backward compatibility
): Promise<boolean> => {
  try {
    console.log(
      `Saving AI analysis to cloud for user: ${userId}, test: ${testType}, set: ${setName}, timestamp: ${responseTimestamp}`
    );

    // Log whether responseQualityAssessment is present
    if (analysis.responseQualityAssessment) {
      console.log(
        "responseQualityAssessment is present in the analysis:",
        Object.keys(analysis.responseQualityAssessment).length,
        "responses assessed"
      );
    } else {
      console.warn("responseQualityAssessment is NOT present in the analysis");
    }

    // Create a unique ID for the analysis document that includes the AI provider
    const analysisId = `${userId}_${testType}_${setName}_${responseTimestamp}_${aiProvider}_analysis`;
    const analysisRef = doc(db, "aiAnalysis", analysisId);

    // Save the analysis document
    await setDoc(analysisRef, {
      userId,
      testType,
      setName,
      responseTimestamp,
      analysis,
      createdAt: serverTimestamp(),
      isAIGenerated: analysis.isAIGenerated,
      aiProvider,
    });

    console.log(`Successfully saved AI analysis with ID: ${analysisId}`);

    // Also save to localStorage as a fallback
    if (typeof window !== "undefined") {
      try {
        localStorage.setItem(
          `aiAnalysis_${userId}_${testType}_${setName}_${responseTimestamp}_${aiProvider}`,
          JSON.stringify({
            userId,
            testType,
            setName,
            responseTimestamp,
            analysis,
            createdAt: new Date().toISOString(),
            isAIGenerated: analysis.isAIGenerated,
            aiProvider,
          })
        );
        console.log("Saved AI analysis to localStorage as fallback");
      } catch (localStorageError) {
        console.error("Error saving to localStorage:", localStorageError);
      }
    }

    return true;
  } catch (error) {
    console.error(`Error saving AI analysis:`, error);

    // If there's a permission error, save to localStorage instead
    if (typeof window !== "undefined") {
      try {
        localStorage.setItem(
          `aiAnalysis_${userId}_${testType}_${setName}_${responseTimestamp}_${aiProvider}`,
          JSON.stringify({
            userId,
            testType,
            setName,
            responseTimestamp,
            analysis,
            createdAt: new Date().toISOString(),
            isAIGenerated: analysis.isAIGenerated,
            aiProvider,
          })
        );
        console.log(
          "Saved AI analysis to localStorage due to Firestore permission error"
        );
        return true; // Return true since we successfully saved to localStorage
      } catch (localStorageError) {
        console.error("Error saving to localStorage:", localStorageError);
      }
    }

    return false;
  }
};

/**
 * Get AI analysis for a specific response
 * @param userId User ID
 * @param testType Type of test (SRT, WAT, TAT, SDT)
 * @param setName Set name
 * @param responseTimestamp Timestamp of the response
 * @returns Promise resolving to the AI analysis document or null if not found
 */
export const getAIAnalysis = async (
  userId: string,
  testType: TestType,
  setName: string,
  responseTimestamp: number,
  aiProvider: "deepseek" | "military-psychologist" = "military-psychologist" // Default to Military Psychologist for backward compatibility
): Promise<AIAnalysisDocument | null> => {
  try {
    console.log(
      `Fetching AI analysis for user: ${userId}, test: ${testType}, set: ${setName}, timestamp: ${responseTimestamp}`
    );

    // Create the document ID that includes the AI provider
    const analysisId = `${userId}_${testType}_${setName}_${responseTimestamp}_${aiProvider}_analysis`;
    const analysisRef = doc(db, "aiAnalysis", analysisId);

    // Get the analysis document
    const analysisDoc = await getDoc(analysisRef);

    if (analysisDoc.exists()) {
      console.log(`AI analysis found with ID: ${analysisId}`);
      const data = analysisDoc.data() as AIAnalysisDocument;
      data.docId = analysisDoc.id;
      return data;
    }

    console.log(`No AI analysis found with ID: ${analysisId}`);

    // Check localStorage as fallback
    if (typeof window !== "undefined") {
      const localStorageKey = `aiAnalysis_${userId}_${testType}_${setName}_${responseTimestamp}_${aiProvider}`;
      const localData = localStorage.getItem(localStorageKey);

      if (localData) {
        try {
          console.log(
            `Found AI analysis in localStorage with key: ${localStorageKey}`
          );
          const parsedData = JSON.parse(localData) as AIAnalysisDocument;
          return parsedData;
        } catch (parseError) {
          console.error("Error parsing localStorage data:", parseError);
        }
      }
    }

    return null;
  } catch (error) {
    console.error(`Error getting AI analysis from Firestore:`, error);

    // Try localStorage as fallback
    if (typeof window !== "undefined") {
      const localStorageKey = `aiAnalysis_${userId}_${testType}_${setName}_${responseTimestamp}_${aiProvider}`;
      const localData = localStorage.getItem(localStorageKey);

      if (localData) {
        try {
          console.log(
            `Using localStorage fallback for AI analysis with key: ${localStorageKey}`
          );
          const parsedData = JSON.parse(localData) as AIAnalysisDocument;
          return parsedData;
        } catch (parseError) {
          console.error("Error parsing localStorage data:", parseError);
        }
      }
    }

    return null;
  }
};

/**
 * Get all AI analyses for a user
 * @param userId User ID
 * @returns Promise resolving to an array of AI analysis documents
 */
export const getAllAIAnalyses = async (
  userId: string
): Promise<AIAnalysisDocument[]> => {
  try {
    console.log(`Fetching all AI analyses for user: ${userId}`);

    const analysesRef = collection(db, "aiAnalysis");
    const q = query(
      analysesRef,
      where("userId", "==", userId),
      orderBy("createdAt", "desc")
    );

    const querySnapshot = await getDocs(q);
    const analyses = querySnapshot.docs.map((doc) => {
      const data = doc.data() as AIAnalysisDocument;
      data.docId = doc.id;
      return data;
    });

    console.log(`Found ${analyses.length} AI analyses for user: ${userId}`);

    // If we have Firestore results, return them
    if (analyses.length > 0) {
      return analyses;
    }

    // Otherwise, check localStorage as fallback
    if (typeof window !== "undefined") {
      const localAnalyses: AIAnalysisDocument[] = [];

      // Scan localStorage for AI analysis entries
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith(`aiAnalysis_${userId}`)) {
          try {
            const localData = localStorage.getItem(key);
            if (localData) {
              const parsedData = JSON.parse(localData) as AIAnalysisDocument;
              localAnalyses.push(parsedData);
            }
          } catch (parseError) {
            console.error(
              `Error parsing localStorage data for key ${key}:`,
              parseError
            );
          }
        }
      }

      if (localAnalyses.length > 0) {
        console.log(
          `Found ${localAnalyses.length} AI analyses in localStorage for user: ${userId}`
        );
        // Sort by timestamp (descending)
        return localAnalyses.sort(
          (a, b) => b.responseTimestamp - a.responseTimestamp
        );
      }
    }

    return [];
  } catch (error) {
    console.error(`Error getting all AI analyses from Firestore:`, error);

    // Try localStorage as fallback
    if (typeof window !== "undefined") {
      const localAnalyses: AIAnalysisDocument[] = [];

      // Scan localStorage for AI analysis entries
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith(`aiAnalysis_${userId}`)) {
          try {
            const localData = localStorage.getItem(key);
            if (localData) {
              const parsedData = JSON.parse(localData) as AIAnalysisDocument;
              localAnalyses.push(parsedData);
            }
          } catch (parseError) {
            console.error(
              `Error parsing localStorage data for key ${key}:`,
              parseError
            );
          }
        }
      }

      if (localAnalyses.length > 0) {
        console.log(
          `Using localStorage fallback: Found ${localAnalyses.length} AI analyses for user: ${userId}`
        );
        // Sort by timestamp (descending)
        return localAnalyses.sort(
          (a, b) => b.responseTimestamp - a.responseTimestamp
        );
      }
    }

    return [];
  }
};

/**
 * Get all AI analyses for a specific test type
 * @param userId User ID
 * @param testType Type of test (SRT, WAT, TAT, SDT)
 * @returns Promise resolving to an array of AI analysis documents
 */
export const getTestTypeAIAnalyses = async (
  userId: string,
  testType: TestType
): Promise<AIAnalysisDocument[]> => {
  try {
    console.log(
      `Fetching AI analyses for user: ${userId}, test type: ${testType}`
    );

    const analysesRef = collection(db, "aiAnalysis");
    const q = query(
      analysesRef,
      where("userId", "==", userId),
      where("testType", "==", testType),
      orderBy("createdAt", "desc")
    );

    const querySnapshot = await getDocs(q);
    const analyses = querySnapshot.docs.map((doc) => {
      const data = doc.data() as AIAnalysisDocument;
      data.docId = doc.id;
      return data;
    });

    console.log(
      `Found ${analyses.length} AI analyses for user: ${userId}, test type: ${testType}`
    );

    // If we have Firestore results, return them
    if (analyses.length > 0) {
      return analyses;
    }

    // Otherwise, check localStorage as fallback
    if (typeof window !== "undefined") {
      const localAnalyses: AIAnalysisDocument[] = [];

      // Scan localStorage for AI analysis entries
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith(`aiAnalysis_${userId}_${testType}`)) {
          try {
            const localData = localStorage.getItem(key);
            if (localData) {
              const parsedData = JSON.parse(localData) as AIAnalysisDocument;
              if (parsedData.testType === testType) {
                localAnalyses.push(parsedData);
              }
            }
          } catch (parseError) {
            console.error(
              `Error parsing localStorage data for key ${key}:`,
              parseError
            );
          }
        }
      }

      if (localAnalyses.length > 0) {
        console.log(
          `Found ${localAnalyses.length} AI analyses in localStorage for user: ${userId}, test type: ${testType}`
        );
        // Sort by timestamp (descending)
        return localAnalyses.sort(
          (a, b) => b.responseTimestamp - a.responseTimestamp
        );
      }
    }

    return [];
  } catch (error) {
    console.error(`Error getting test type AI analyses from Firestore:`, error);

    // Try localStorage as fallback
    if (typeof window !== "undefined") {
      const localAnalyses: AIAnalysisDocument[] = [];

      // Scan localStorage for AI analysis entries
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith(`aiAnalysis_${userId}_${testType}`)) {
          try {
            const localData = localStorage.getItem(key);
            if (localData) {
              const parsedData = JSON.parse(localData) as AIAnalysisDocument;
              if (parsedData.testType === testType) {
                localAnalyses.push(parsedData);
              }
            }
          } catch (parseError) {
            console.error(
              `Error parsing localStorage data for key ${key}:`,
              parseError
            );
          }
        }
      }

      if (localAnalyses.length > 0) {
        console.log(
          `Using localStorage fallback: Found ${localAnalyses.length} AI analyses for user: ${userId}, test type: ${testType}`
        );
        // Sort by timestamp (descending)
        return localAnalyses.sort(
          (a, b) => b.responseTimestamp - a.responseTimestamp
        );
      }
    }

    return [];
  }
};

/**
 * Get all AI analyses for a specific test set
 * @param userId User ID
 * @param testType Type of test (SRT, WAT, TAT, SDT)
 * @param setName Set name
 * @returns Promise resolving to an array of AI analysis documents
 */
/**
 * Delete an AI analysis
 * @param docId Document ID of the analysis to delete
 * @param userId User ID
 * @param testType Type of test (SRT, WAT, TAT, SDT)
 * @param setName Set name
 * @param responseTimestamp Timestamp of the response
 * @param aiProvider AI provider used for the analysis
 * @returns Promise resolving to true if successful
 */
export const deleteAIAnalysis = async (
  docId: string,
  userId: string,
  testType: TestType,
  setName: string,
  responseTimestamp: number,
  aiProvider: "deepseek" | "military-psychologist" = "military-psychologist"
): Promise<boolean> => {
  try {
    console.log(`Deleting AI analysis with ID: ${docId}`);

    // Delete from Firestore
    const analysisRef = doc(db, "aiAnalysis", docId);
    await deleteDoc(analysisRef);

    // Also delete from localStorage if it exists
    if (typeof window !== "undefined") {
      const localStorageKey = `aiAnalysis_${userId}_${testType}_${setName}_${responseTimestamp}_${aiProvider}`;
      localStorage.removeItem(localStorageKey);
    }

    return true;
  } catch (error) {
    console.error(`Error deleting AI analysis:`, error);
    return false;
  }
};

/**
 * Update an existing analysis with Cultural Fit Algorithm data
 * @param docId Document ID of the analysis to update
 * @param userId User ID
 * @param testType Type of test (SRT, WAT, TAT, SDT)
 * @param setName Set name
 * @param responseTimestamp Timestamp of the response
 * @param aiProvider AI provider used for the analysis
 * @returns Promise resolving to true if successful
 */
export const updateAnalysisWithCulturalFitAlgorithm = async (
  docId: string,
  userId: string,
  testType: TestType,
  setName: string,
  responseTimestamp: number,
  aiProvider: "deepseek" | "military-psychologist" = "military-psychologist"
): Promise<boolean> => {
  try {
    console.log(
      `Updating AI analysis with Cultural Fit Algorithm data: ${docId}`
    );

    // Get the existing analysis
    const analysisDoc = await getAIAnalysis(
      userId,
      testType,
      setName,
      responseTimestamp,
      aiProvider
    );

    if (!analysisDoc) {
      console.error(`Analysis not found with ID: ${docId}`);
      return false;
    }

    // Get the responses for this analysis
    const responseService = await import("./responseService");
    const responses = await responseService.getResponses(
      userId,
      testType,
      setName,
      responseTimestamp
    );

    // Add Cultural Fit Algorithm data to the analysis
    if (analysisDoc.analysis.militaryPsychologist) {
      // Import the function to generate Cultural Fit Algorithm data
      const aiService = await import("./aiService");

      // Use the responses to generate dynamic Cultural Fit Algorithm data
      const responsesArray = responses?.responses?.map((r) => r.response) || [];
      analysisDoc.analysis.militaryPsychologist.culturalFitAlgorithm =
        aiService.generateCulturalFitAlgorithmData(responsesArray);

      // Save the updated analysis
      const success = await saveAIAnalysis(
        userId,
        testType,
        setName,
        responseTimestamp,
        analysisDoc.analysis,
        aiProvider
      );

      return success;
    }

    console.error(
      `Analysis does not have militaryPsychologist field: ${docId}`
    );
    return false;
  } catch (error) {
    console.error(
      `Error updating AI analysis with Cultural Fit Algorithm:`,
      error
    );
    return false;
  }
};

export const getSetAIAnalyses = async (
  userId: string,
  testType: TestType,
  setName: string
): Promise<AIAnalysisDocument[]> => {
  try {
    console.log(
      `Fetching AI analyses for user: ${userId}, test type: ${testType}, set: ${setName}`
    );

    const analysesRef = collection(db, "aiAnalysis");
    const q = query(
      analysesRef,
      where("userId", "==", userId),
      where("testType", "==", testType),
      where("setName", "==", setName),
      orderBy("createdAt", "desc")
    );

    const querySnapshot = await getDocs(q);
    const analyses = querySnapshot.docs.map((doc) => {
      const data = doc.data() as AIAnalysisDocument;
      data.docId = doc.id;
      return data;
    });

    console.log(
      `Found ${analyses.length} AI analyses for user: ${userId}, test type: ${testType}, set: ${setName}`
    );

    // If we have Firestore results, return them
    if (analyses.length > 0) {
      return analyses;
    }

    // Otherwise, check localStorage as fallback
    if (typeof window !== "undefined") {
      const localAnalyses: AIAnalysisDocument[] = [];

      // Scan localStorage for AI analysis entries
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (
          key &&
          key.startsWith(`aiAnalysis_${userId}_${testType}_${setName}`)
        ) {
          try {
            const localData = localStorage.getItem(key);
            if (localData) {
              const parsedData = JSON.parse(localData) as AIAnalysisDocument;
              if (
                parsedData.testType === testType &&
                parsedData.setName === setName
              ) {
                localAnalyses.push(parsedData);
              }
            }
          } catch (parseError) {
            console.error(
              `Error parsing localStorage data for key ${key}:`,
              parseError
            );
          }
        }
      }

      if (localAnalyses.length > 0) {
        console.log(
          `Found ${localAnalyses.length} AI analyses in localStorage for user: ${userId}, test type: ${testType}, set: ${setName}`
        );
        // Sort by timestamp (descending)
        return localAnalyses.sort(
          (a, b) => b.responseTimestamp - a.responseTimestamp
        );
      }
    }

    return [];
  } catch (error) {
    console.error(`Error getting set AI analyses from Firestore:`, error);

    // Try localStorage as fallback
    if (typeof window !== "undefined") {
      const localAnalyses: AIAnalysisDocument[] = [];

      // Scan localStorage for AI analysis entries
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (
          key &&
          key.startsWith(`aiAnalysis_${userId}_${testType}_${setName}`)
        ) {
          try {
            const localData = localStorage.getItem(key);
            if (localData) {
              const parsedData = JSON.parse(localData) as AIAnalysisDocument;
              if (
                parsedData.testType === testType &&
                parsedData.setName === setName
              ) {
                localAnalyses.push(parsedData);
              }
            }
          } catch (parseError) {
            console.error(
              `Error parsing localStorage data for key ${key}:`,
              parseError
            );
          }
        }
      }

      if (localAnalyses.length > 0) {
        console.log(
          `Using localStorage fallback: Found ${localAnalyses.length} AI analyses for user: ${userId}, test type: ${testType}, set: ${setName}`
        );
        // Sort by timestamp (descending)
        return localAnalyses.sort(
          (a, b) => b.responseTimestamp - a.responseTimestamp
        );
      }
    }

    return [];
  }
};
