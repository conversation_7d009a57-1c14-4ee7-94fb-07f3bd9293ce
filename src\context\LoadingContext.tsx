"use client";

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useRef,
} from "react";
import { usePathname } from "next/navigation";

/**
 * This context is used for components that need to manage their own loading states.
 * It no longer controls a global loading overlay, but provides a shared API for
 * components to manage loading states consistently.
 */
interface LoadingContextType {
  isLoading: boolean;
  startLoading: () => void;
  stopLoading: () => void;
}

const LoadingContext = createContext<LoadingContextType>({
  isLoading: false,
  startLoading: () => {},
  stopLoading: () => {},
});

export const useLoading = () => useContext(LoadingContext);

export function LoadingProvider({ children }: { children: React.ReactNode }) {
  const [isLoading, setIsLoading] = useState(false);
  const pathname = usePathname();
  const prevPathRef = useRef(pathname);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Clear any existing timeout when unmounting
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  // Detect path changes to automatically stop loading
  useEffect(() => {
    if (pathname !== prevPathRef.current && isLoading) {
      // Path has changed, stop loading
      setIsLoading(false);
    }
    prevPathRef.current = pathname;
  }, [pathname, isLoading]);

  const startLoading = () => {
    setIsLoading(true);

    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Set a fallback timeout to stop loading after 800ms
    timeoutRef.current = setTimeout(() => {
      setIsLoading(false);
    }, 800);
  };

  const stopLoading = () => {
    setIsLoading(false);

    // Clear the timeout if it exists
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  };

  return (
    <LoadingContext.Provider value={{ isLoading, startLoading, stopLoading }}>
      {children}
    </LoadingContext.Provider>
  );
}
