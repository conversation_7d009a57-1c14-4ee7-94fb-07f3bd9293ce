"use client";

import Link from "next/link";
import Image from "next/image";
import { ArrowLeftIcon, MapIcon } from "@heroicons/react/24/outline";
import NavigationButton from "@/components/NavigationButton";

// Define the available GPE sets
const GPE_SETS = [
  {
    id: "set1",
    title: "Set 1",
    description:
      "Multiple emergencies require your immediate attention while returning from a volleyball championship.",
    imagePath: "/images/gto/gpe/img1.png",
  },
  {
    id: "set2",
    title: "Set 2",
    description:
      "Multiple emergencies in a river bank region require strategic planning and resource allocation.",
    imagePath: "/images/gto/gpe/img2.png",
  },
  {
    id: "set3",
    title: "Set 3",
    description:
      "Multiple emergencies in a river bank region require strategic planning and resource allocation.",
    imagePath: "/images/gto/gpe/img3.png",
  },
  {
    id: "set4",
    title: "Set 4",
    description:
      "Multiple emergencies in a forest  region require strategic planning and resource allocation.",
    imagePath: "/images/gto/gpe/img4.png",
  },
  {
    id: "set5",
    title: "Set 5",
    description:
      "Multiple emergencies in a forest  and river  region require strategic planning and resource allocation.",
    imagePath: "/images/gto/gpe/img5.png",
  },
];

export default function GPESetsPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="mx-auto max-w-4xl px-6">
        <NavigationButton
          href="/tests/gto/gpe"
          className="mb-8 inline-flex items-center text-gray-600 hover:text-gray-900"
        >
          <ArrowLeftIcon className="h-5 w-5 mr-2" />
          Back to GPE Overview
        </NavigationButton>

        <div className="bg-white rounded-2xl shadow-sm p-8 border-t-4 border-green-500">
          <div className="flex items-center space-x-3 mb-6">
            <div className="p-2 rounded-full bg-green-100">
              <MapIcon className="h-8 w-8 text-green-600" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900">
              Select GPE Simulation Set
            </h1>
          </div>

          <div className="prose prose-lg max-w-none mb-8">
            <p className="text-lg text-gray-700">
              Choose one of the available Group Planning Exercise simulation
              sets below. Each set presents a unique scenario that will test
              your planning, resource allocation, and decision-making skills.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-6 mt-8">
            {GPE_SETS.map((set) => (
              <Link
                key={set.id}
                href={`/tests/gto/gpe/${set.id}/test`}
                className="block group"
              >
                <div className="bg-white border border-gray-200 rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-200">
                  <div className="flex flex-col md:flex-row">
                    <div className="md:w-1/3 relative h-48 md:h-auto">
                      <Image
                        src={set.imagePath}
                        alt={set.title}
                        fill
                        className="object-cover"
                      />
                    </div>
                    <div className="p-6 md:w-2/3">
                      <h2 className="text-xl font-semibold text-gray-900 group-hover:text-green-600 transition-colors duration-200">
                        {set.title}
                      </h2>
                      <p className="mt-2 text-gray-600">{set.description}</p>
                      <div className="mt-4">
                        <span className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 group-hover:bg-green-700 transition-colors duration-200">
                          Start Simulation
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
