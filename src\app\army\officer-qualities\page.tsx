"use client";

import Link from "next/link";
import { ArrowLeftIcon, StarIcon } from "@heroicons/react/24/outline";

// Officer Like Qualities (OLQs) - The 15 authentic qualities tested in Nepal Army interviews
const officerQualities = [
  {
    id: "effective-intelligence",
    title: "Effective Intelligence",
    description:
      "The ability to analyze and solve practical problems using logical reasoning. This involves evaluating situations and devising effective solutions through strategic thinking and practical application of knowledge.",
    icon: StarIcon,
    color: "bg-blue-500",
    textColor: "text-blue-700",
    bgColor: "bg-blue-50",
    borderColor: "border-blue-200",
  },
  {
    id: "reasoning-ability",
    title: "Reasoning Ability",
    description:
      "The capacity to logically process information, identify patterns, and arrive at sound conclusions. This quality enables officers to think critically and make well-informed decisions based on available evidence.",
    icon: StarIcon,
    color: "bg-emerald-500",
    textColor: "text-emerald-700",
    bgColor: "bg-emerald-50",
    borderColor: "border-emerald-200",
  },
  {
    id: "organizing-ability",
    title: "Organizing Ability",
    description:
      "The capability to structure and manage resources efficiently to achieve goals. This involves planning, coordinating activities, and ensuring optimal utilization of available resources for mission success.",
    icon: StarIcon,
    color: "bg-red-500",
    textColor: "text-red-700",
    bgColor: "bg-red-50",
    borderColor: "border-red-200",
  },
  {
    id: "power-of-expression",
    title: "Power of Expression",
    description:
      "The ability to communicate thoughts and ideas clearly and effectively. This includes both verbal and written communication skills essential for leadership and team coordination.",
    icon: StarIcon,
    color: "bg-purple-500",
    textColor: "text-purple-700",
    bgColor: "bg-purple-50",
    borderColor: "border-purple-200",
  },
  {
    id: "social-adaptability",
    title: "Social Adaptability",
    description:
      "The ability to adjust and work harmoniously in a group or with people from diverse backgrounds. This quality enables effective integration and collaboration in multicultural environments.",
    icon: StarIcon,
    color: "bg-indigo-500",
    textColor: "text-indigo-700",
    bgColor: "bg-indigo-50",
    borderColor: "border-indigo-200",
  },
  {
    id: "cooperation",
    title: "Cooperation",
    description:
      "The willingness to work with others, placing the group's objectives above personal goals. This involves supporting team members and contributing to collective success.",
    icon: StarIcon,
    color: "bg-amber-500",
    textColor: "text-amber-700",
    bgColor: "bg-amber-50",
    borderColor: "border-amber-200",
  },
  {
    id: "sense-of-responsibility",
    title: "Sense of Responsibility",
    description:
      "A strong commitment to fulfilling duties and taking ownership of actions. This quality ensures accountability and reliability in all assigned tasks and decisions.",
    icon: StarIcon,
    color: "bg-teal-500",
    textColor: "text-teal-700",
    bgColor: "bg-teal-50",
    borderColor: "border-teal-200",
  },
  {
    id: "initiative",
    title: "Initiative",
    description:
      "The ability to take the first step in addressing challenges without waiting for instructions. This proactive approach is essential for leadership and problem-solving in dynamic situations.",
    icon: StarIcon,
    color: "bg-cyan-500",
    textColor: "text-cyan-700",
    bgColor: "bg-cyan-50",
    borderColor: "border-cyan-200",
  },
  {
    id: "self-confidence",
    title: "Self Confidence",
    description:
      "The belief in one's abilities to achieve goals and handle challenges. This quality provides the foundation for decisive action and effective leadership under pressure.",
    icon: StarIcon,
    color: "bg-pink-500",
    textColor: "text-pink-700",
    bgColor: "bg-pink-50",
    borderColor: "border-pink-200",
  },
  {
    id: "speed-of-decision",
    title: "Speed of Decision",
    description:
      "The ability to make quick and effective decisions, especially under pressure. This quality is crucial for military operations where rapid response can determine mission success.",
    icon: StarIcon,
    color: "bg-orange-500",
    textColor: "text-orange-700",
    bgColor: "bg-orange-50",
    borderColor: "border-orange-200",
  },
  {
    id: "ability-to-influence-group",
    title: "Ability to Influence Group",
    description:
      "The capacity to inspire and lead a group toward achieving a common goal. This involves motivating others and building consensus for effective team performance.",
    icon: StarIcon,
    color: "bg-lime-500",
    textColor: "text-lime-700",
    bgColor: "bg-lime-50",
    borderColor: "border-lime-200",
  },
  {
    id: "liveliness",
    title: "Liveliness",
    description:
      "A positive attitude and cheerful demeanor that uplifts others. This quality helps maintain morale and team spirit during challenging situations and prolonged operations.",
    icon: StarIcon,
    color: "bg-violet-500",
    textColor: "text-violet-700",
    bgColor: "bg-violet-50",
    borderColor: "border-violet-200",
  },
  {
    id: "determination",
    title: "Determination",
    description:
      "The ability to persistently work toward goals despite obstacles. This quality ensures mission completion even in the face of adversity and challenging circumstances.",
    icon: StarIcon,
    color: "bg-rose-500",
    textColor: "text-rose-700",
    bgColor: "bg-rose-50",
    borderColor: "border-rose-200",
  },
  {
    id: "courage",
    title: "Courage",
    description:
      "The mental and physical strength to face fears and challenges confidently. This quality is fundamental for military leadership and making bold decisions when required.",
    icon: StarIcon,
    color: "bg-sky-500",
    textColor: "text-sky-700",
    bgColor: "bg-sky-50",
    borderColor: "border-sky-200",
  },
  {
    id: "stamina",
    title: "Stamina",
    description:
      "Physical and mental endurance to sustain performance under pressure. This quality ensures consistent effectiveness during prolonged operations and demanding situations.",
    icon: StarIcon,
    color: "bg-emerald-600",
    textColor: "text-emerald-800",
    bgColor: "bg-emerald-100",
    borderColor: "border-emerald-300",
  },
];

export default function OfficerQualitiesPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        {/* Navigation */}
        <div className="mb-8">
          <Link
            href="/army"
            className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-indigo-600"
          >
            <ArrowLeftIcon className="mr-2 h-4 w-4" />
            Back to Army Page
          </Link>
        </div>

        {/* Page Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl">
            15 Officer Like Qualities (OLQs) for Nepal Army Interview
          </h1>
          <p className="mt-6 text-lg leading-8 text-gray-600 max-w-3xl mx-auto">
            The authentic 15 Officer Like Qualities (OLQs) tested in  Nepal Army interviews. These qualities are essential for success in military leadership roles and are evaluated through various tests during the selection process.
          </p>
        </div>

        {/* OLQ Factors Visualization */}
        <div className="bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 rounded-2xl p-8 mb-16 text-white">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">The 4 Factors of Officer Like Qualities</h2>
            <p className="text-slate-300 text-lg">Understanding the complete framework of military leadership assessment</p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left side - Factors */}
            <div className="space-y-8">
              {/* Factor 1 - Mind */}
              <div className="bg-slate-800/50 rounded-xl p-6 border border-slate-700">
                <div className="flex items-center mb-4">
                  <div className="bg-blue-500 p-3 rounded-full mr-4">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-blue-400">FACTOR 1 - MIND</h3>
                </div>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <span className="bg-blue-500/20 px-3 py-1 rounded-full">Effective Intelligence</span>
                  <span className="bg-blue-500/20 px-3 py-1 rounded-full">Reasoning Ability</span>
                  <span className="bg-blue-500/20 px-3 py-1 rounded-full">Organizing Ability</span>
                  <span className="bg-blue-500/20 px-3 py-1 rounded-full">Power of Expression</span>
                </div>
              </div>

              {/* Factor 2 - Heart */}
              <div className="bg-slate-800/50 rounded-xl p-6 border border-slate-700">
                <div className="flex items-center mb-4">
                  <div className="bg-red-500 p-3 rounded-full mr-4">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-red-400">FACTOR 2 - HEART</h3>
                </div>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <span className="bg-red-500/20 px-3 py-1 rounded-full">Social Adaptability</span>
                  <span className="bg-red-500/20 px-3 py-1 rounded-full">Cooperation</span>
                  <span className="bg-red-500/20 px-3 py-1 rounded-full">Sense of Responsibility</span>
                </div>
              </div>

              {/* Factor 3 - Guts */}
              <div className="bg-slate-800/50 rounded-xl p-6 border border-slate-700">
                <div className="flex items-center mb-4">
                  <div className="bg-orange-500 p-3 rounded-full mr-4">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-orange-400">FACTOR 3 - GUTS</h3>
                </div>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <span className="bg-orange-500/20 px-3 py-1 rounded-full">Initiative</span>
                  <span className="bg-orange-500/20 px-3 py-1 rounded-full">Self Confidence</span>
                  <span className="bg-orange-500/20 px-3 py-1 rounded-full">Speed of Decision</span>
                  <span className="bg-orange-500/20 px-3 py-1 rounded-full">Ability to Influence Group</span>
                  <span className="bg-orange-500/20 px-3 py-1 rounded-full">Liveliness</span>
                </div>
              </div>

              {/* Factor 4 - Limbs */}
              <div className="bg-slate-800/50 rounded-xl p-6 border border-slate-700">
                <div className="flex items-center mb-4">
                  <div className="bg-green-500 p-3 rounded-full mr-4">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-green-400">FACTOR 4 - LIMBS</h3>
                </div>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <span className="bg-green-500/20 px-3 py-1 rounded-full">Determination</span>
                  <span className="bg-green-500/20 px-3 py-1 rounded-full">Courage</span>
                  <span className="bg-green-500/20 px-3 py-1 rounded-full">Stamina</span>
                </div>
              </div>
            </div>

            {/* Right side - Soldier Silhouette */}
            <div className="flex justify-center items-center">
              <div className="relative">
                <svg className="w-80 h-80 text-slate-600" viewBox="0 0 400 400" fill="currentColor">
                  {/* Soldier silhouette */}
                  <path d="M200 50 C220 50 240 70 240 90 C240 110 220 130 200 130 C180 130 160 110 160 90 C160 70 180 50 200 50 Z" />
                  <path d="M180 130 L220 130 L230 180 L210 180 L210 200 L230 200 L240 250 L220 250 L210 280 L190 280 L180 250 L160 250 L170 200 L190 200 L190 180 L170 180 Z" />
                  <path d="M210 280 L210 350 L190 350 L190 280" />
                  <path d="M170 180 L140 200 L130 220 L140 240 L170 220" />
                  <path d="M230 180 L260 200 L270 220 L260 240 L230 220" />
                </svg>

                {/* Factor labels positioned around the silhouette */}
                <div className="absolute top-8 left-1/2 transform -translate-x-1/2 bg-blue-500 px-3 py-1 rounded-full text-xs font-bold">
                  MIND
                </div>
                <div className="absolute top-24 left-1/2 transform -translate-x-1/2 bg-red-500 px-3 py-1 rounded-full text-xs font-bold">
                  HEART
                </div>
                <div className="absolute top-40 left-1/2 transform -translate-x-1/2 bg-orange-500 px-3 py-1 rounded-full text-xs font-bold">
                  GUTS
                </div>
                <div className="absolute bottom-16 left-1/2 transform -translate-x-1/2 bg-green-500 px-3 py-1 rounded-full text-xs font-bold">
                  LIMBS
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Qualities Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {officerQualities.map((quality) => (
            <div
              key={quality.id}
              className={`rounded-xl border ${quality.borderColor} ${quality.bgColor} overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300`}
            >
              <div className="p-6">
                <div className="flex items-center mb-4">
                  <div className={`p-2 rounded-full ${quality.color} text-white mr-3`}>
                    <quality.icon className="h-5 w-5" />
                  </div>
                  <h2 className={`text-xl font-semibold ${quality.textColor}`}>
                    {quality.title}
                  </h2>
                </div>
                <p className="text-gray-700">{quality.description}</p>
              </div>
            </div>
          ))}
        </div>

        {/* Additional Information */}
        <div className="bg-white rounded-xl shadow-sm p-8 border border-gray-200 max-w-5xl mx-auto">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            Understanding OLQs in Nepal Army Interview Process
          </h2>
          <p className="text-gray-600 mb-6">
            Nepal Army evaluates these 15 Officer Like Qualities through a comprehensive 5-day assessment process. These qualities form the foundation of effective military leadership and are essential for success in commissioned officer roles in  Army
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Nepal Army Assessment Categories
              </h3>
              <p className="text-gray-600">
                OLQs are evaluated through four main categories: Planning and Organizing (intellectual capabilities), Social Adjustment (adaptability and teamwork), Social Effectiveness (leadership and initiative), and Dynamic Attributes (courage and resilience).
              </p>
            </div>
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Developing OLQs
              </h3>
              <p className="text-gray-600">
                These qualities can be developed through consistent practice, participating in group activities, enhancing communication skills, maintaining physical fitness, and working on mental agility through strategic thinking and problem-solving exercises.
              </p>
            </div>
          </div>

          <div className="bg-blue-50 rounded-lg p-6 border border-blue-200">
            <h3 className="text-lg font-medium text-blue-900 mb-3">
              Key Nepal Army Testing Methods
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800">
              <div>
                <strong>Psychological Tests:</strong> TAT, WAT, SRT, SDT
              </div>
              <div>
                <strong>Group Testing:</strong> GD, GPE, PGT, Command Task
              </div>
              <div>
                <strong>Personal Interview:</strong> One-on-one assessment
              </div>
              <div>
                <strong>Physical Tasks:</strong> Individual Obstacles, Group Obstacle Race
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
