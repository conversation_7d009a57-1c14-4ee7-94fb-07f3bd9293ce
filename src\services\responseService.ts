import { db } from "@/lib/firebase";
import {
  collection,
  doc,
  setDoc,
  getDoc,
  getDocs,
  deleteDoc,
  query,
  where,
  orderBy,
  serverTimestamp,
  DocumentData,
  QueryDocumentSnapshot,
} from "firebase/firestore";

// Test types
export type TestType =
  | "srt"
  | "wat"
  | "tat"
  | "sdt"
  | "piq"
  | "io"
  | "bc"
  | "lecturette";

// Response interface
export interface TestResponse {
  userId: string;
  testType: TestType;
  setName: string;
  responses: string[];
  questions?: string[];
  createdAt: any;
  updatedAt: any;
  timestamp?: number;
  attemptId?: string;
  isLatest?: boolean;
  docId?: string; // Document ID for deletion purposes
}

/**
 * Save test responses to Firestore
 * Each attempt is saved as a new document with a timestamp to maintain history
 * Returns the timestamp of the saved document
 */
export const saveResponses = async (
  userId: string,
  testType: TestType,
  setName: string,
  responses: string[],
  questions?: string[]
): Promise<number> => {
  try {
    console.log(
      `Saving responses to cloud for user: ${userId}, test: ${testType}, set: ${setName}`
    );

    // Create a timestamp for this attempt
    const timestamp = new Date().getTime();

    // Create a unique ID for the response document that includes the timestamp
    const responseId = `${userId}_${testType}_${setName}_${timestamp}`;
    const responseRef = doc(db, "responses", responseId);
    console.log(`Document ID: ${responseId}`);

    // Always create a new document for each attempt
    console.log(`Creating new response document with timestamp: ${timestamp}`);
    await setDoc(responseRef, {
      userId,
      testType,
      setName,
      responses,
      ...(questions && { questions }),
      timestamp, // Store the timestamp in the document for easier querying
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      attemptId: timestamp.toString(), // Store as string for easier reference
    });
    console.log(`Successfully created new response document`);

    // Also update the "latest" document for backward compatibility
    const latestResponseId = `${userId}_${testType}_${setName}_latest`;
    const latestResponseRef = doc(db, "responses", latestResponseId);

    await setDoc(latestResponseRef, {
      userId,
      testType,
      setName,
      responses,
      ...(questions && { questions }),
      timestamp,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      attemptId: timestamp.toString(),
      isLatest: true,
    });
    console.log(`Successfully updated latest response document`);

    // Also save to localStorage as backup for offline access
    if (typeof window !== "undefined") {
      // Save with set-specific key
      localStorage.setItem(
        `${testType}_responses_${setName}`,
        JSON.stringify(responses)
      );

      // Also save with generic key for backward compatibility
      localStorage.setItem(`${testType}_responses`, JSON.stringify(responses));

      if (questions) {
        // Save questions with set-specific key
        localStorage.setItem(
          `${testType}_questions_${setName}`,
          JSON.stringify(questions)
        );

        // Also save with generic key for backward compatibility
        localStorage.setItem(
          `${testType}_questions`,
          JSON.stringify(questions)
        );
      }

      // Save the timestamp to localStorage to prevent duplicate saves
      localStorage.setItem(
        `${testType}_${setName}_last_save_timestamp`,
        timestamp.toString()
      );
      localStorage.setItem(`${testType}_current_set`, setName);
    }

    return timestamp;
  } catch (error) {
    console.error(`Error saving ${testType} responses:`, error);
    throw error;
  }
};

/**
 * Get the latest test responses from Firestore for a specific set
 */
export const getResponses = async (
  userId: string,
  testType: TestType,
  setName: string
): Promise<TestResponse | null> => {
  try {
    console.log(
      `Fetching latest responses from cloud for user: ${userId}, test: ${testType}, set: ${setName}`
    );

    // First try to get the latest response document
    const latestResponseId = `${userId}_${testType}_${setName}_latest`;
    const latestResponseRef = doc(db, "responses", latestResponseId);
    console.log(`Looking for latest document ID: ${latestResponseId}`);

    // Get the latest response document
    let responseDoc = await getDoc(latestResponseRef);

    if (responseDoc.exists()) {
      console.log(`Latest document found in Firestore`);
      return responseDoc.data() as TestResponse;
    }

    // If latest document doesn't exist, try the old format document
    const oldFormatResponseId = `${userId}_${testType}_${setName}`;
    const oldFormatResponseRef = doc(db, "responses", oldFormatResponseId);
    console.log(`Looking for old format document ID: ${oldFormatResponseId}`);

    responseDoc = await getDoc(oldFormatResponseRef);

    if (responseDoc.exists()) {
      console.log(`Old format document found in Firestore`);
      return responseDoc.data() as TestResponse;
    }

    console.log(`No documents found in Firestore for this set`);

    return null;
  } catch (error) {
    console.error(`Error getting ${testType} responses:`, error);

    // Try to get from localStorage as fallback
    if (typeof window !== "undefined") {
      try {
        // Try set-specific key first
        let responses = localStorage.getItem(
          `${testType}_responses_${setName}`
        );
        let questions = localStorage.getItem(
          `${testType}_questions_${setName}`
        );

        // Fall back to generic key if needed
        if (!responses) {
          responses = localStorage.getItem(`${testType}_responses`);
          questions = localStorage.getItem(`${testType}_questions`);
          console.log(
            `Using generic localStorage key as fallback for ${testType}`
          );
        } else {
          console.log(`Using set-specific localStorage key for ${testType}`);
        }

        const storedSetName = localStorage.getItem(`${testType}_current_set`);

        if (
          responses &&
          (storedSetName === setName ||
            responses ===
              localStorage.getItem(`${testType}_responses_${setName}`))
        ) {
          return {
            userId,
            testType,
            setName,
            responses: JSON.parse(responses),
            ...(questions && { questions: JSON.parse(questions) }),
            createdAt: new Date(),
            updatedAt: new Date(),
          };
        }
      } catch (localError) {
        console.error("Error getting responses from localStorage:", localError);
      }
    }

    throw error;
  }
};

/**
 * Get all test responses for a user
 */
export const getAllUserResponses = async (
  userId: string
): Promise<TestResponse[]> => {
  try {
    console.log(`Fetching all responses for user: ${userId}`);

    const responsesRef = collection(db, "responses");
    const q = query(
      responsesRef,
      where("userId", "==", userId),
      orderBy("updatedAt", "desc")
    );

    const querySnapshot = await getDocs(q);
    console.log(`Found ${querySnapshot.docs.length} total responses for user`);

    // Filter out documents with "_latest" in their ID to avoid duplicates
    // Also filter out old format documents without timestamps to avoid more duplicates
    let allResponses = querySnapshot.docs
      .filter((doc) => !doc.id.includes("_latest"))
      .map((doc: QueryDocumentSnapshot<DocumentData>) => {
        const data = doc.data() as TestResponse;
        // Add the document ID to the response object for deletion purposes
        data.docId = doc.id;

        // Enhanced logging with more details
        console.log(`Processed response document: ${doc.id}`);
        console.log(`  - Test Type: ${data.testType}`);
        console.log(`  - Set Name: ${data.setName}`);
        console.log(`  - Timestamp: ${data.timestamp || "none"}`);
        console.log(`  - Response Count: ${data.responses?.length || 0}`);

        return data;
      });

    // Deduplicate responses by keeping only the latest attempt for each unique combination of testType and setName
    // This ensures we don't show duplicate entries in the profile view
    console.log(`Before deduplication: ${allResponses.length} responses`);

    // Group responses by testType and setName
    const responseGroups: Record<string, TestResponse[]> = {};

    allResponses.forEach((response) => {
      const key = `${response.testType}_${response.setName}`;
      if (!responseGroups[key]) {
        responseGroups[key] = [];
      }
      responseGroups[key].push(response);
    });

    // For each group, sort by timestamp and keep only the latest 2 responses
    // (keeping 2 instead of 1 to show history but avoid excessive duplication)
    const filteredResponses: TestResponse[] = [];

    Object.keys(responseGroups).forEach((key) => {
      const group = responseGroups[key];
      // Sort by timestamp (newest first)
      group.sort((a, b) => (b.timestamp || 0) - (a.timestamp || 0));

      // Keep all responses to show full history
      filteredResponses.push(...group);

      console.log(`Group ${key}: ${group.length} total, keeping all responses`);
    });

    console.log(`After deduplication: ${filteredResponses.length} responses`);

    console.log(
      `Returning ${filteredResponses.length} unique responses after filtering`
    );
    return filteredResponses;
  } catch (error) {
    console.error("Error getting all user responses:", error);

    // Return empty array instead of throwing error
    // This allows the profile page to load even if Firestore query fails
    return [];
  }
};

/**
 * Get all test responses for a specific test type
 */
export const getTestTypeResponses = async (
  userId: string,
  testType: TestType
): Promise<TestResponse[]> => {
  try {
    const responsesRef = collection(db, "responses");
    const q = query(
      responsesRef,
      where("userId", "==", userId),
      where("testType", "==", testType),
      orderBy("updatedAt", "desc")
    );

    const querySnapshot = await getDocs(q);

    // Filter out documents with "_latest" in their ID to avoid duplicates
    let allResponses = querySnapshot.docs
      .filter((doc) => !doc.id.includes("_latest"))
      .map((doc: QueryDocumentSnapshot<DocumentData>) => {
        const data = doc.data() as TestResponse;
        // Add the document ID to the response object for deletion purposes
        data.docId = doc.id;
        return data;
      });

    console.log(
      `Before deduplication: ${allResponses.length} responses for ${testType}`
    );

    // Group responses by setName
    const responseGroups: Record<string, TestResponse[]> = {};

    allResponses.forEach((response) => {
      const key = response.setName;
      if (!responseGroups[key]) {
        responseGroups[key] = [];
      }
      responseGroups[key].push(response);
    });

    // For each group, sort by timestamp and keep only the latest 2 responses
    const filteredResponses: TestResponse[] = [];

    Object.keys(responseGroups).forEach((key) => {
      const group = responseGroups[key];
      // Sort by timestamp (newest first)
      group.sort((a, b) => (b.timestamp || 0) - (a.timestamp || 0));

      // Keep all responses to show full history
      filteredResponses.push(...group);
    });

    console.log(
      `After deduplication: ${filteredResponses.length} responses for ${testType}`
    );

    return filteredResponses;
  } catch (error) {
    console.error(`Error getting ${testType} responses:`, error);
    // Return empty array instead of throwing error
    return [];
  }
};

/**
 * Get response history for a specific set
 * Returns all attempts for a specific set, ordered by timestamp (newest first)
 */
export const getResponseHistory = async (
  userId: string,
  testType: TestType,
  setName: string
): Promise<TestResponse[]> => {
  try {
    console.log(
      `Fetching response history for user: ${userId}, test: ${testType}, set: ${setName}`
    );

    const responsesRef = collection(db, "responses");
    const q = query(
      responsesRef,
      where("userId", "==", userId),
      where("testType", "==", testType),
      where("setName", "==", setName),
      orderBy("timestamp", "desc")
    );

    const querySnapshot = await getDocs(q);

    // Filter out documents with "_latest" in their ID to avoid duplicates
    let allResponses = querySnapshot.docs
      .filter(
        (doc) =>
          !doc.id.includes("_latest") &&
          !doc.id.endsWith(`${userId}_${testType}_${setName}`)
      )
      .map((doc: QueryDocumentSnapshot<DocumentData>) => {
        const data = doc.data() as TestResponse;
        // Add the document ID to the response object for deletion purposes
        data.docId = doc.id;
        console.log(
          `Found response document: ${doc.id}, timestamp: ${data.timestamp}`
        );
        return data;
      });

    console.log(
      `Found ${allResponses.length} historical responses for this set`
    );

    // Deduplicate responses by timestamp to avoid showing duplicate entries
    // This is especially important for the set view where we show all attempts
    console.log(`Before deduplication: ${allResponses.length} responses`);

    // Create a map of timestamp -> response to identify duplicates
    const responseMap = new Map<number, TestResponse>();

    // Sort by timestamp (newest first) before deduplication
    allResponses.sort((a, b) => (b.timestamp || 0) - (a.timestamp || 0));

    // Keep only one response per timestamp
    allResponses.forEach((response) => {
      if (response.timestamp && !responseMap.has(response.timestamp)) {
        responseMap.set(response.timestamp, response);
      }
    });

    // Convert map back to array
    const responses = Array.from(responseMap.values());

    console.log(`After deduplication: ${responses.length} responses`);
    return responses;
  } catch (error) {
    console.error(`Error getting response history:`, error);
    // Return empty array instead of throwing error
    return [];
  }
};

/**
 * Delete a specific response document
 */
export const deleteResponse = async (docId: string): Promise<void> => {
  try {
    console.log(`Deleting response document: ${docId}`);
    const responseRef = doc(db, "responses", docId);
    await deleteDoc(responseRef);

    // Also check if there's a "latest" version of this document and delete it
    if (docId.includes("_")) {
      const parts = docId.split("_");
      // Remove the timestamp part
      parts.pop();
      // Add "latest" suffix
      const latestDocId = [...parts, "latest"].join("_");
      console.log(`Checking for latest document: ${latestDocId}`);

      try {
        const latestRef = doc(db, "responses", latestDocId);
        const latestDoc = await getDoc(latestRef);
        if (latestDoc.exists()) {
          await deleteDoc(latestRef);
          console.log(`Successfully deleted latest document: ${latestDocId}`);
        }
      } catch (latestError) {
        console.error(`Error checking/deleting latest document:`, latestError);
        // Continue with the main deletion even if this fails
      }
    }

    console.log(`Successfully deleted response document: ${docId}`);
  } catch (error) {
    console.error(`Error deleting response:`, error);
    throw error;
  }
};

/**
 * Delete all responses for a user
 */
export const deleteAllUserResponses = async (userId: string): Promise<void> => {
  try {
    console.log(`Deleting all responses for user: ${userId}`);

    // Get all responses for the user
    const responsesRef = collection(db, "responses");
    const q = query(responsesRef, where("userId", "==", userId));

    const querySnapshot = await getDocs(q);

    // Delete each document
    const deletePromises = querySnapshot.docs.map((document) =>
      deleteDoc(doc(db, "responses", document.id))
    );

    await Promise.all(deletePromises);
    console.log(`Successfully deleted all responses for user: ${userId}`);
  } catch (error) {
    console.error(`Error deleting all user responses:`, error);
    throw error;
  }
};

/**
 * Delete all responses for a specific test type
 */
export const deleteTestTypeResponses = async (
  userId: string,
  testType: TestType
): Promise<void> => {
  try {
    console.log(`Deleting all ${testType} responses for user: ${userId}`);

    // Get all responses for the user and test type
    const responsesRef = collection(db, "responses");
    const q = query(
      responsesRef,
      where("userId", "==", userId),
      where("testType", "==", testType)
    );

    const querySnapshot = await getDocs(q);

    // Delete each document
    const deletePromises = querySnapshot.docs.map((document) =>
      deleteDoc(doc(db, "responses", document.id))
    );

    await Promise.all(deletePromises);
    console.log(
      `Successfully deleted all ${testType} responses for user: ${userId}`
    );
  } catch (error) {
    console.error(`Error deleting test type responses:`, error);
    throw error;
  }
};
