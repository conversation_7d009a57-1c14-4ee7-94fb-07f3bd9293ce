// Individual Obstacles Data Structure
// This file contains structured data for Individual Obstacles in GTO test

export interface Obstacle {
  id: number;
  name: string;
  description: string;
  instructions: string;
  points: number;
  image: string; // Path to the image
  tips: string[];
}

export const individualObstacles: Obstacle[] = [
  {
    id: 1,
    name: "Single Ramp",
    description: "A ramp that candidates must run over and land on a sand pit.",
    instructions: "Run from a distance over the ramp and land on the sand pit.",
    points: 5,
    image: "/images/gto/individual-obstacles/single-ramp.png",
    tips: [
      "Maintain momentum while approaching the ramp",
      "Land on your toes to absorb impact",
      "Keep your body slightly forward while running",
    ],
  },
  {
    id: 2,
    name: "Double Barrel Jump",
    description: "A long jump over drums buried in the ground lengthwise.",
    instructions:
      "Come running from a little distance and jump over the drum buried in ground lengthwise.",
    points: 5,
    image: "/images/gto/individual-obstacles/double-barrell.png",
    tips: [
      "Take a good run-up for momentum",
      "Push off strongly with both feet",
      "Focus on clearing the entire obstacle",
    ],
  },
  {
    id: 3,
    name: "Balancing Beam",
    description:
      "Three wooden beams placed one after another that candidates must cross.",
    instructions:
      "Climb from either side and get down from the other. Getting down in between is not permitted.",
    points: 8,
    image: "/images/gto/individual-obstacles/balancing-beam.png",
    tips: [
      "Keep your arms out for balance",
      "Look forward, not down at your feet",
      "Take small, controlled steps",
      "Stay focused and maintain your balance",
    ],
  },
  {
    id: 4,
    name: "Screen Jump",
    description:
      "A ramp similar to obstacle #1 with a red screen in front that candidates must jump over.",
    instructions:
      "Jump over the screen onto the sand pit. Requires momentum to jump over without stopping. If you touch the screen, it will fall down.",
    points: 6,
    image: "/images/gto/individual-obstacles/screen-jump.png",
    tips: [
      "Build up sufficient speed before the jump",
      "Focus on clearing the screen completely",
      "Don't waste time putting the screen back if it falls - staff will handle it",
    ],
  },
  {
    id: 5,
    name: "Parallel Ropes",
    description:
      "Two parallel ropes at approximately 15 feet height from the ground.",
    instructions:
      "Use the ladders and ropes on either side to go up. On reaching the top, hold one of the ropes and slide to the other side. Don't look down.",
    points: 10,
    image: "/images/gto/individual-obstacles/parallel-ropes.png",
    tips: [
      "Use the ladder if you don't have sufficient strength",
      "Maintain a firm grip on the ropes",
      "Keep your eyes forward, not down",
      "Use controlled movements to slide across",
    ],
  },
  {
    id: 6,
    name: "Tarzan's Swing",
    description:
      "A platform with a ladder at approximately 8 feet height with a rope for swinging.",
    instructions:
      "On reaching the top, hold the rope as high as possible. Stretch yourself and fold both legs to prevent touching the ground. Take a jump like Tarzan and land beyond the imaginary red lines on sand indicated by two red bricks.",
    points: 12,
    image: "/images/gto/individual-obstacles/tarzan-swing.png",
    tips: [
      "Hold the rope as high as possible for maximum swing",
      "Keep your legs folded to avoid touching the ground",
      "Aim to land beyond the red markers",
      "Use your body weight to generate momentum",
    ],
  },
  {
    id: 7,
    name: "Double Platform",
    description:
      "Two platforms, one at 10 feet and another at 5 feet, with sand pits in front.",
    instructions:
      "Climb up the higher platform using ladder-like bars and jump on the lower platform and then on the ground. Land on your toes to avoid tripping and falling down. Don't touch the sides painted red.",
    points: 8,
    image: "/images/gto/individual-obstacles/double-platform.png",
    tips: [
      "Land on your toes to absorb impact",
      "Maintain balance when landing on the lower platform",
      "Avoid touching the red-painted sides",
      "Use controlled jumps rather than reckless ones",
    ],
  },
  {
    id: 8,
    name: "Double Ditch",
    description:
      "Two ditches - one long and one short - separated by a flat surface.",
    instructions:
      "The longer ditch has a rope hanging. Come running, take a jump, hold the rope, and land on the flat surface. Then take another jump to cross over the smaller pit.",
    points: 10,
    image: "/images/gto/individual-obstacles/double-ditch.png",
    tips: [
      "Build momentum before reaching the first ditch",
      "Grip the rope firmly to swing across",
      "Maintain your balance on the middle platform",
      "Use your momentum to clear the second ditch",
    ],
  },
  {
    id: 9,
    name: "Commando Walk",
    description:
      "A narrow plank with a victory stand in the middle, fixed at a height of approximately 10 feet.",
    instructions:
      "There are ladders on either side. The sides of the ramp and victory stand are painted red and cannot be touched. Don't look down.",
    points: 10,
    image: "/images/gto/individual-obstacles/commando-walk.png",
    tips: [
      "Focus on a point ahead of you, not down",
      "Take slow, deliberate steps",
      "Keep your arms out for balance",
      "Stay calm and composed throughout",
    ],
  },
  {
    id: 10,
    name: "Tiger's Leap",
    description:
      "A platform with a ladder at a height of 10 feet with a rope in front.",
    instructions:
      "Leap like a tiger, hold the rope, and come down. Requires arm strength which can be developed by doing push-ups and practicing rope climbing.",
    points: 12,
    image: "/images/gto/individual-obstacles/tigers-leap.png",
    tips: [
      "Develop arm strength through push-ups and rope practice",
      "Leap forward with confidence",
      "Grip the rope firmly with both hands",
      "Control your descent to avoid injury",
    ],
  },
];

// Recommended sequence for maximum points
export const recommendedSequence = [10, 6, 8, 9, 4, 3, 5, 1, 2, 7, 10];

// Points to remember
export const pointsToRemember = [
  "Obstacles are arranged in clusters",
  "Object is to score maximum points by repeating high value obstacles",
  "Start with high value obstacles and do others before repeating",
  "Don't follow sequence 1 to 10 because criss-cross wastes time",
  "Obstacles 3 and 5 take maximum time",
  "Plan to take maximum points",
  "If running out of time, leave obstacles 1 & 2 and do 7 & 10 to get at least 52 points",
];

// Approach tips
export const approachTips = [
  "Have a definite plan",
  "Choose the correct sequence",
  "Show a sense of urgency - don't walk on the ground",
  "Don't break rules or touch red portions of obstacles",
  "Remain composed when attempting high-rise obstacles or ditches",
  "Don't keep looking at the GTO",
  "Don't give up or exceed time",
];

// Helper function to get an obstacle by ID
export const getObstacleById = (id: number): Obstacle | undefined => {
  return individualObstacles.find((obstacle) => obstacle.id === id);
};
