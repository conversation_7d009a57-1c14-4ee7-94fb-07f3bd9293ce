@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #ffffff; /* Override dark mode to always use light mode */
    --foreground: #171717;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;

  /* Content protection */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Ensure text is always visible */
p,
h1,
h2,
h3,
h4,
h5,
h6,
li,
span {
  color: inherit;
}

/* Override for white text in hero section */
.text-white {
  color: white !important;
}

/* Override for indigo text */
.text-indigo-700 {
  color: #4338ca !important;
}

/* Override for indigo hover states */
.hover\:text-indigo-600:hover {
  color: #4f46e5 !important;
}

/* Remove this rule if it's overriding the indigo color */
.text-gray-600 {
  color: #4b5563 !important;
}

.text-gray-700 {
  color: #374151 !important;
}

.text-gray-800 {
  color: #1f2937 !important;
}

/* Animation for warning message */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out forwards;
}

/* Content Protection Styles */
img {
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
  pointer-events: none;
}

/* Prevent image selection */
img::selection {
  background: transparent;
}

img::-moz-selection {
  background: transparent;
}

/* Cross-browser text selection prevention */
.no-select {
  -webkit-touch-callout: none; /* iOS Safari */
  -webkit-user-select: none; /* Safari */
  -khtml-user-select: none; /* Konqueror HTML */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* Internet Explorer/Edge */
  user-select: none; /* Non-prefixed version, currently supported by Chrome and Opera */
}

/* Allow text selection in specific areas */
.allow-select,
input,
textarea,
select {
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
  user-select: text !important;
  pointer-events: auto !important;
}

/* Content protection class */
.content-protected {
  /* Prevent printing */
  -webkit-print-color-adjust: exact;
  print-color-adjust: exact;
}

/* Image overlay protection */
.img-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  background-color: transparent;
}

/* Ensure all buttons have pointer cursor */
button,
[type="button"],
[type="reset"],
[type="submit"],
.button,
a[href]:not([href="#"]),
a[role="button"],
.cursor-pointer {
  cursor: pointer !important;
}

/* Hide scrollbar but allow scrolling */
.hide-scrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}
