import { db } from "@/lib/firebase";
import {
  collection,
  doc,
  setDoc,
  getDoc,
  getDocs,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  increment,
  serverTimestamp,
  Timestamp,
  DocumentData,
  QueryDocumentSnapshot,
  runTransaction,
} from "firebase/firestore";
import {
  ForumTopic,
  ForumReply,
  ContentReport,
  UserBan,
  TopicStatus,
  ReportStatus,
  ReportReason,
} from "@/types/forum";
import { filterInappropriateContent } from "@/utils/contentModeration";

/**
 * Check if a user is an admin
 * @param userId ID of the user to check
 * @returns Promise resolving to a boolean indicating if the user is an admin
 */
export const isAdmin = async (userId: string): Promise<boolean> => {
  try {
    const userRef = doc(db, "users", userId);
    const userDoc = await getDoc(userRef);

    if (!userDoc.exists()) {
      return false;
    }

    return userDoc.data().isAdmin === true;
  } catch (error) {
    console.error(`Error checking admin status for user ${userId}:`, error);
    return false; // Default to not admin in case of error
  }
};

// Number of topics per page
const TOPICS_PER_PAGE = 20;

// Number of replies per page
const REPLIES_PER_PAGE = 50;

// View count functionality has been removed to simplify the application

/**
 * Create a new forum topic
 * @param userId ID of the user creating the topic
 * @param userName Name of the user creating the topic
 * @param userPhotoURL Photo URL of the user creating the topic
 * @param title Title of the topic
 * @param content Content of the topic
 * @param tags Tags for the topic
 * @returns Promise resolving to the ID of the new topic
 */
export const createTopic = async (
  userId: string,
  userName: string,
  userPhotoURL: string | null,
  title: string,
  content: string,
  tags: string[] = []
): Promise<string> => {
  try {
    // Check if user is banned
    const isBanned = await checkUserBanned(userId);
    if (isBanned) {
      throw new Error("You are currently banned from posting in the forum.");
    }

    // Filter content for inappropriate material
    const filteredTitle = await filterInappropriateContent(title);
    const filteredContent = await filterInappropriateContent(content);

    // Create a new document reference
    const topicRef = doc(collection(db, "forumTopics"));
    const topicId = topicRef.id;

    // Create topic data
    const topicData: Omit<ForumTopic, "id"> = {
      title: filteredTitle,
      content: filteredContent,
      authorId: userId,
      authorName: userName,
      authorPhotoURL: userPhotoURL || undefined,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      lastReplyAt: serverTimestamp(), // Initialize lastReplyAt to the creation time
      status: "active",
      replyCount: 0,
      isPinned: false,
      tags: tags,
    };

    // Save to Firestore
    await setDoc(topicRef, topicData);

    // Try to update user's post count, but don't let it block topic creation
    try {
      await updateUserPostCount(userId);
    } catch (error) {
      console.error("Error updating user post count, but continuing:", error);
    }

    return topicId;
  } catch (error) {
    console.error("Error creating forum topic:", error);
    throw error;
  }
};

/**
 * Get forum topics with pagination
 * @param page Page number (1-based)
 * @param lastVisible Last visible document for pagination
 * @param filterTag Optional tag to filter by
 * @returns Promise resolving to topics and pagination info
 */
export const getTopics = async (
  page: number = 1,
  lastVisible?: QueryDocumentSnapshot<DocumentData>,
  filterTag?: string
): Promise<{
  topics: ForumTopic[];
  lastVisible: QueryDocumentSnapshot<DocumentData> | null;
  hasMore: boolean;
}> => {
  try {
    let topicsQuery;

    // Build query based on filter and pagination
    if (filterTag) {
      topicsQuery = query(
        collection(db, "forumTopics"),
        where("tags", "array-contains", filterTag),
        where("status", "==", "active"),
        orderBy("isPinned", "desc"),
        orderBy("createdAt", "desc"), // Use createdAt instead of lastReplyAt
        limit(TOPICS_PER_PAGE)
      );
    } else {
      topicsQuery = query(
        collection(db, "forumTopics"),
        where("status", "==", "active"),
        orderBy("isPinned", "desc"),
        orderBy("createdAt", "desc"), // Use createdAt instead of lastReplyAt
        limit(TOPICS_PER_PAGE)
      );
    }

    // If we have a last visible document, start after it
    if (lastVisible) {
      topicsQuery = query(topicsQuery, startAfter(lastVisible));
    }

    const topicsSnapshot = await getDocs(topicsQuery);
    const lastVisibleDoc =
      topicsSnapshot.docs.length > 0
        ? topicsSnapshot.docs[topicsSnapshot.docs.length - 1]
        : null;

    const topics: ForumTopic[] = [];
    topicsSnapshot.forEach((doc) => {
      const data = doc.data();
      topics.push({
        id: doc.id,
        ...data,
        createdAt: data.createdAt?.toDate(),
        updatedAt: data.updatedAt?.toDate(),
        lastReplyAt: data.lastReplyAt?.toDate(),
      } as ForumTopic);
    });

    // Check if there are more topics
    const hasMore = topics.length === TOPICS_PER_PAGE;

    return {
      topics,
      lastVisible: lastVisibleDoc,
      hasMore,
    };
  } catch (error) {
    console.error("Error getting forum topics:", error);
    throw error;
  }
};

/**
 * Get a single forum topic by ID
 * @param topicId ID of the topic to get
 * @param userId Optional user ID for incrementing view count
 * @returns Promise resolving to the topic
 */
export const getTopic = async (
  topicId: string,
  userId?: string
): Promise<ForumTopic | null> => {
  try {
    const topicRef = doc(db, "forumTopics", topicId);
    const topicDoc = await getDoc(topicRef);

    if (!topicDoc.exists()) {
      return null;
    }

    // View count incrementing has been removed to simplify the application

    const data = topicDoc.data();
    return {
      id: topicDoc.id,
      ...data,
      createdAt: data.createdAt?.toDate(),
      updatedAt: data.updatedAt?.toDate(),
      lastReplyAt: data.lastReplyAt?.toDate(),
    } as ForumTopic;
  } catch (error) {
    console.error(`Error getting forum topic ${topicId}:`, error);
    throw error;
  }
};

/**
 * Create a reply to a forum topic
 * @param topicId ID of the topic being replied to
 * @param userId ID of the user creating the reply
 * @param userName Name of the user creating the reply
 * @param userPhotoURL Photo URL of the user creating the reply
 * @param content Content of the reply
 * @param parentReplyId Optional ID of the parent reply for nested replies
 * @returns Promise resolving to the ID of the new reply
 */
export const createReply = async (
  topicId: string,
  userId: string,
  userName: string,
  userPhotoURL: string | null,
  content: string,
  parentReplyId?: string
): Promise<string> => {
  try {
    // Check if user is banned
    const isBanned = await checkUserBanned(userId);
    if (isBanned) {
      throw new Error("You are currently banned from posting in the forum.");
    }

    // Filter content for inappropriate material
    const filteredContent = await filterInappropriateContent(content);

    // Create a new document reference
    const replyRef = doc(collection(db, "forumReplies"));
    const replyId = replyRef.id;

    // Create reply data
    const replyData: Omit<ForumReply, "id"> = {
      topicId,
      content: filteredContent,
      authorId: userId,
      authorName: userName,
      authorPhotoURL: userPhotoURL || undefined,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      status: "active",
      isAnswer: false,
    };

    // Only add parentReplyId if it's defined (not undefined)
    if (parentReplyId) {
      replyData.parentReplyId = parentReplyId;
    }

    // Use a transaction to update both the reply and the topic
    await runTransaction(db, async (transaction) => {
      // First, perform all reads
      const topicRef = doc(db, "forumTopics", topicId);
      const topicDoc = await transaction.get(topicRef);

      if (!topicDoc.exists()) {
        throw new Error(`Topic ${topicId} not found`);
      }

      // Then, perform all writes
      transaction.set(replyRef, replyData);

      transaction.update(topicRef, {
        replyCount: increment(1),
        lastReplyAt: serverTimestamp(),
        lastReplyAuthorId: userId,
        lastReplyAuthorName: userName,
        updatedAt: serverTimestamp(),
      });
    });

    // Try to update user's reply count, but don't let it block reply creation
    try {
      await updateUserReplyCount(userId);
    } catch (error) {
      console.error("Error updating user reply count, but continuing:", error);
    }

    return replyId;
  } catch (error) {
    console.error("Error creating forum reply:", error);
    throw error;
  }
};

/**
 * Get replies for a forum topic with pagination
 * @param topicId ID of the topic to get replies for
 * @param page Page number (1-based)
 * @param lastVisible Last visible document for pagination
 * @returns Promise resolving to replies and pagination info
 */
export const getReplies = async (
  topicId: string,
  page: number = 1,
  lastVisible?: QueryDocumentSnapshot<DocumentData>
): Promise<{
  replies: ForumReply[];
  lastVisible: QueryDocumentSnapshot<DocumentData> | null;
  hasMore: boolean;
}> => {
  try {
    let repliesQuery = query(
      collection(db, "forumReplies"),
      where("topicId", "==", topicId),
      where("status", "==", "active"),
      orderBy("createdAt", "asc"),
      limit(REPLIES_PER_PAGE)
    );

    // If we have a last visible document, start after it
    if (lastVisible) {
      repliesQuery = query(repliesQuery, startAfter(lastVisible));
    }

    const repliesSnapshot = await getDocs(repliesQuery);
    const lastVisibleDoc =
      repliesSnapshot.docs.length > 0
        ? repliesSnapshot.docs[repliesSnapshot.docs.length - 1]
        : null;

    const replies: ForumReply[] = [];
    repliesSnapshot.forEach((doc) => {
      const data = doc.data();
      replies.push({
        id: doc.id,
        ...data,
        createdAt: data.createdAt?.toDate(),
        updatedAt: data.updatedAt?.toDate(),
      } as ForumReply);
    });

    // Check if there are more replies
    const hasMore = replies.length === REPLIES_PER_PAGE;

    return {
      replies,
      lastVisible: lastVisibleDoc,
      hasMore,
    };
  } catch (error) {
    console.error(`Error getting replies for topic ${topicId}:`, error);
    throw error;
  }
};

/**
 * Report inappropriate content
 * @param contentId ID of the content being reported
 * @param contentType Type of content (topic or reply)
 * @param reporterId ID of the user reporting the content
 * @param reporterName Name of the user reporting the content
 * @param reason Reason for the report
 * @param description Additional description of the report
 * @returns Promise resolving to the ID of the new report
 */
/**
 * Update an existing forum topic
 * @param topicId ID of the topic to update
 * @param userId ID of the user updating the topic (must be the author)
 * @param title New title for the topic
 * @param content New content for the topic
 * @param tags New tags for the topic
 * @returns Promise resolving to a boolean indicating success
 */
export const updateTopic = async (
  topicId: string,
  userId: string,
  title: string,
  content: string,
  tags: string[] = []
): Promise<boolean> => {
  try {
    const topicRef = doc(db, "forumTopics", topicId);
    const topicDoc = await getDoc(topicRef);

    if (!topicDoc.exists()) {
      throw new Error("Topic not found");
    }

    const topicData = topicDoc.data();

    // Verify the user is the author
    if (topicData.authorId !== userId) {
      throw new Error("You can only edit your own topics");
    }

    // Filter content for inappropriate material
    const filteredTitle = await filterInappropriateContent(title);
    const filteredContent = await filterInappropriateContent(content);

    // Update the topic
    await updateDoc(topicRef, {
      title: filteredTitle,
      content: filteredContent,
      tags: tags,
      updatedAt: serverTimestamp(),
    });

    return true;
  } catch (error) {
    console.error(`Error updating topic ${topicId}:`, error);
    throw error;
  }
};

/**
 * Delete a forum topic and all its replies
 * @param topicId ID of the topic to delete
 * @param userId ID of the user deleting the topic (must be the author)
 * @returns Promise resolving to a boolean indicating success
 */
export const deleteTopic = async (
  topicId: string,
  userId: string
): Promise<boolean> => {
  try {
    const topicRef = doc(db, "forumTopics", topicId);
    const topicDoc = await getDoc(topicRef);

    if (!topicDoc.exists()) {
      throw new Error("Topic not found");
    }

    const topicData = topicDoc.data();

    // Verify the user is the author
    if (topicData.authorId !== userId && !(await isAdmin(userId))) {
      throw new Error("You can only delete your own topics");
    }

    // First, find all replies to this topic
    const repliesQuery = query(
      collection(db, "forumReplies"),
      where("topicId", "==", topicId)
    );

    const repliesSnapshot = await getDocs(repliesQuery);

    // Delete all replies in batches (if there are many)
    const deletePromises = repliesSnapshot.docs.map((doc) =>
      deleteDoc(doc.ref)
    );
    await Promise.all(deletePromises);

    // Finally, delete the topic itself
    await deleteDoc(topicRef);

    return true;
  } catch (error) {
    console.error(`Error deleting topic ${topicId}:`, error);
    throw error;
  }
};

export const reportContent = async (
  contentId: string,
  contentType: "topic" | "reply",
  reporterId: string,
  reporterName: string,
  reason: ReportReason,
  description: string
): Promise<string> => {
  try {
    // Create a new document reference
    const reportRef = doc(collection(db, "contentReports"));
    const reportId = reportRef.id;

    // Create report data
    const reportData: Omit<ContentReport, "id"> = {
      contentId,
      contentType,
      reporterId,
      reporterName,
      reason,
      description,
      createdAt: serverTimestamp(),
      status: "pending",
    };

    // Save to Firestore
    await setDoc(reportRef, reportData);

    return reportId;
  } catch (error) {
    console.error("Error reporting content:", error);
    throw error;
  }
};

/**
 * Check if a user is banned from the forum
 * @param userId ID of the user to check
 * @returns Promise resolving to a boolean indicating if the user is banned
 */
export const checkUserBanned = async (userId: string): Promise<boolean> => {
  try {
    // First check if the userBans collection exists and is accessible
    try {
      const bansQuery = query(
        collection(db, "userBans"),
        where("userId", "==", userId),
        where("isActive", "==", true),
        limit(1)
      );

      const bansSnapshot = await getDocs(bansQuery);

      if (bansSnapshot.empty) {
        return false;
      }

      // Check if the ban has expired
      const banData = bansSnapshot.docs[0].data();
      if (banData.expiresAt) {
        const expiryDate = banData.expiresAt.toDate();
        if (expiryDate < new Date()) {
          // Ban has expired, try to update it to inactive
          try {
            await updateDoc(bansSnapshot.docs[0].ref, {
              isActive: false,
            });
          } catch (updateError) {
            console.error(
              `Error updating expired ban for user ${userId}:`,
              updateError
            );
            // Continue even if update fails
          }
          return false;
        }
      }

      return true;
    } catch (queryError) {
      console.error(`Error querying bans for user ${userId}:`, queryError);
      // If there's an error with the query (e.g., permissions), assume not banned
      return false;
    }
  } catch (error) {
    console.error(`Error checking ban status for user ${userId}:`, error);
    return false; // Default to not banned in case of error
  }
};

/**
 * Update a user's post count
 * @param userId ID of the user
 */
export const updateUserPostCount = async (userId: string): Promise<void> => {
  try {
    const userRef = doc(db, "forumUsers", userId);
    const userDoc = await getDoc(userRef);

    if (userDoc.exists()) {
      // Update existing user
      await updateDoc(userRef, {
        postCount: increment(1),
        lastPostAt: serverTimestamp(),
      });
    } else {
      // Create new user
      await setDoc(userRef, {
        id: userId,
        postCount: 1,
        replyCount: 0,
        isBanned: false,
        joinedAt: serverTimestamp(),
        lastPostAt: serverTimestamp(),
        reputation: 0,
        isModerator: false,
      });
    }
  } catch (error) {
    // Log the error but don't throw it - this should not block topic creation
    console.error(`Error updating post count for user ${userId}:`, error);
    // We'll just continue without updating the user stats
  }
};

/**
 * Update a user's reply count
 * @param userId ID of the user
 */
export const updateUserReplyCount = async (userId: string): Promise<void> => {
  try {
    const userRef = doc(db, "forumUsers", userId);
    const userDoc = await getDoc(userRef);

    if (userDoc.exists()) {
      // Update existing user
      await updateDoc(userRef, {
        replyCount: increment(1),
        lastPostAt: serverTimestamp(),
      });
    } else {
      // Create new user
      await setDoc(userRef, {
        id: userId,
        postCount: 0,
        replyCount: 1,
        isBanned: false,
        joinedAt: serverTimestamp(),
        lastPostAt: serverTimestamp(),
        reputation: 0,
        isModerator: false,
      });
    }
  } catch (error) {
    // Log the error but don't throw it - this should not block reply creation
    console.error(`Error updating reply count for user ${userId}:`, error);
    // We'll just continue without updating the user stats
  }
};

/**
 * Recalculate and update the reply count for a topic
 * @param topicId ID of the topic to update
 * @returns Promise resolving to the updated reply count
 */
export const recalculateTopicReplyCount = async (
  topicId: string
): Promise<number> => {
  try {
    // Count the active replies for this topic
    const repliesQuery = query(
      collection(db, "forumReplies"),
      where("topicId", "==", topicId),
      where("status", "==", "active")
    );

    const repliesSnapshot = await getDocs(repliesQuery);
    const replyCount = repliesSnapshot.size;

    // Update the topic with the correct count
    const topicRef = doc(db, "forumTopics", topicId);
    await updateDoc(topicRef, {
      replyCount: replyCount,
      updatedAt: serverTimestamp(),
    });

    return replyCount;
  } catch (error) {
    console.error(
      `Error recalculating reply count for topic ${topicId}:`,
      error
    );
    throw error;
  }
};
