"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/context/GoogleAuthContext";
import { useRoleCheck } from "@/utils/authUtils";
import {
  collection,
  getDocs,
  doc,
  getDoc,
  query,
  orderBy,
  limit,
  startAfter,
  DocumentData,
  QueryDocumentSnapshot,
} from "firebase/firestore";
import { db } from "@/lib/firebase";
import { grantAIAccess, revokeAIAccess } from "@/utils/adminUtils";
import {
  UserIcon,
  ShieldCheckIcon,
  ArrowLeftIcon,
  MagnifyingGlassIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  SparklesIcon,
} from "@heroicons/react/24/outline";
import ManageUserAIAccess from "@/components/ManageUserAIAccess";
import Image from "next/image";
import Link from "next/link";

interface User {
  id: string;
  name: string | null;
  email: string | null;
  photoURL: string | null;
  isAdmin: boolean;
  canAccessAIAnalysis: boolean;
  lastLogin?: Date;
  createdAt?: Date;
}

const USERS_PER_PAGE = 10;

export default function AdminUsersPage() {
  const router = useRouter();
  const { user, loading } = useAuth();
  const { isAdmin } = useRoleCheck();

  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [lastVisible, setLastVisible] =
    useState<QueryDocumentSnapshot<DocumentData> | null>(null);
  const [isFirstPage, setIsFirstPage] = useState(true);
  const [processingUser, setProcessingUser] = useState<string | null>(null);

  // Check if user is admin, if not redirect to home
  useEffect(() => {
    if (!loading && (!user || !isAdmin)) {
      router.push("/");
    }
  }, [user, loading, isAdmin, router]);

  // Load users from Firestore
  const loadUsers = async (
    searchTerm = "",
    startAfterDoc: QueryDocumentSnapshot<DocumentData> | null = null
  ) => {
    try {
      setIsLoading(true);
      setError(null);

      const usersRef = collection(db, "users");
      let usersQuery;

      if (searchTerm) {
        // If searching, we need to handle this differently
        // Firestore doesn't support text search directly, so we'll fetch and filter
        usersQuery = query(usersRef, orderBy("name"), limit(100));
      } else if (startAfterDoc) {
        // Pagination - get next page
        usersQuery = query(
          usersRef,
          orderBy("name"),
          startAfter(startAfterDoc),
          limit(USERS_PER_PAGE)
        );
        setIsFirstPage(false);
      } else {
        // First page
        usersQuery = query(usersRef, orderBy("name"), limit(USERS_PER_PAGE));
        setIsFirstPage(true);
      }

      const querySnapshot = await getDocs(usersQuery);

      if (querySnapshot.empty) {
        setUsers([]);
        setLastVisible(null);
        setIsLoading(false);
        return;
      }

      // Set the last visible document for pagination
      const lastDoc = querySnapshot.docs[querySnapshot.docs.length - 1];
      setLastVisible(lastDoc);

      let fetchedUsers: User[] = [];

      querySnapshot.forEach((doc) => {
        const userData = doc.data();

        // If searching, filter by name or email containing the search term
        if (
          searchTerm &&
          !(
            userData.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            userData.email?.toLowerCase().includes(searchTerm.toLowerCase())
          )
        ) {
          return;
        }

        fetchedUsers.push({
          id: doc.id,
          name: userData.name || null,
          email: userData.email || null,
          photoURL: userData.photoURL || null,
          isAdmin:
            userData.isAdmin === true ||
            (userData.roles && userData.roles.includes("admin")),
          canAccessAIAnalysis:
            userData.canAccessAIAnalysis === true ||
            userData.isAdmin === true ||
            (userData.roles && userData.roles.includes("admin")),
          lastLogin: userData.lastLogin?.toDate(),
          createdAt: userData.createdAt?.toDate(),
        });
      });

      // If searching, limit results to USERS_PER_PAGE
      if (searchTerm) {
        fetchedUsers = fetchedUsers.slice(0, USERS_PER_PAGE);
      }

      setUsers(fetchedUsers);
    } catch (err) {
      console.error("Error loading users:", err);
      setError("Failed to load users. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    if (user && isAdmin && db) {
      loadUsers();
    }
  }, [user, isAdmin]);

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    loadUsers(searchQuery);
  };

  // Handle next page
  const handleNextPage = () => {
    if (lastVisible) {
      loadUsers("", lastVisible);
    }
  };

  // Handle previous page
  const handlePreviousPage = () => {
    loadUsers();
  };

  // Toggle AI access
  const toggleAIAccess = async (userId: string, currentStatus: boolean) => {
    try {
      // Set the user being processed
      setProcessingUser(userId);
      setError(null);

      // Check if this user is an admin
      const userIsAdmin = users.find((u) => u.id === userId)?.isAdmin || false;

      // Cannot revoke AI access from admin users
      if (currentStatus && userIsAdmin) {
        setError(
          "Cannot revoke AI access from admin users. Admin users automatically have AI access."
        );
        return;
      }

      if (currentStatus) {
        // Revoke AI access
        await revokeAIAccess(userId);
      } else {
        // Grant AI access
        await grantAIAccess(userId);
      }

      // Update the local state
      setUsers(
        users.map((u) =>
          u.id === userId ? { ...u, canAccessAIAnalysis: !currentStatus } : u
        )
      );
    } catch (err) {
      console.error("Error toggling AI access:", err);

      if (err instanceof Error && err.message.includes("permission")) {
        setError(
          "Permission denied. Your Firestore security rules need to be updated to allow admin users to modify AI access."
        );
      } else {
        setError(
          `Failed to update AI access for user. Error: ${
            err instanceof Error ? err.message : "Unknown error"
          }`
        );
      }
    } finally {
      // Clear the processing state
      setProcessingUser(null);
    }
  };

  // If still loading auth state or not an admin, show loading
  if (loading || !isAdmin) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8 flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0">
          <Link
            href="/profile"
            className="inline-flex items-center text-gray-600 hover:text-gray-900"
          >
            <ArrowLeftIcon className="h-5 w-5 mr-2" />
            Back to Profile
          </Link>
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">
            User Administration
          </h1>
        </div>

        {/* Admin explanation */}
        <div className="mb-6 bg-blue-50 border-l-4 border-blue-400 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <ShieldCheckIcon className="h-5 w-5 text-blue-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800">
                About Admin Role
              </h3>
              <div className="mt-2 text-sm text-blue-700">
                <p>
                  Admins have full control over user permissions and AI access.
                  {isAdmin && (
                    <span className="font-bold">
                      {" "}
                      You are currently an Admin.
                    </span>
                  )}
                </p>
                <ul className="list-disc pl-5 mt-1 space-y-1">
                  <li>
                    Admins can assign or revoke admin privileges for other users
                  </li>
                  <li>
                    Admins can grant or revoke AI analysis access for non-admin
                    users
                  </li>
                  <li>
                    Admin users automatically have access to AI analysis
                    features
                  </li>
                  <li className="text-blue-600 font-medium">
                    Admins cannot revoke their own admin status from this UI
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Search bar */}
        <div className="mb-6">
          <form
            onSubmit={handleSearch}
            className="flex flex-col sm:flex-row w-full max-w-lg space-y-3 sm:space-y-0"
          >
            <div className="relative flex-grow">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
                placeholder="Search by name or email"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex space-x-2 sm:ml-3">
              <button
                type="submit"
                className="flex-1 sm:flex-none inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
              >
                Search
              </button>
              {searchQuery && (
                <button
                  type="button"
                  onClick={() => {
                    setSearchQuery("");
                    loadUsers("");
                  }}
                  className="flex-1 sm:flex-none inline-flex items-center justify-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
                >
                  Clear
                </button>
              )}
            </div>
          </form>
        </div>

        {/* Error message */}
        {error && (
          <div className="mb-4 p-4 bg-red-50 border-l-4 border-red-500 text-red-700">
            <p>{error}</p>
          </div>
        )}

        {/* Users table */}
        <div className="bg-white shadow overflow-hidden sm:rounded-lg overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th
                  scope="col"
                  className="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  User
                </th>
                <th
                  scope="col"
                  className="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell"
                >
                  Status
                </th>
                <th
                  scope="col"
                  className="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {isLoading ? (
                <tr>
                  <td colSpan={3} className="px-4 sm:px-6 py-4 text-center">
                    <div className="flex justify-center">
                      <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-purple-500"></div>
                    </div>
                  </td>
                </tr>
              ) : users.length === 0 ? (
                <tr>
                  <td
                    colSpan={3}
                    className="px-4 sm:px-6 py-4 text-center text-gray-500"
                  >
                    No users found
                  </td>
                </tr>
              ) : (
                users.map((user) => (
                  <tr key={user.id}>
                    <td className="px-4 sm:px-6 py-4">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10 relative">
                          {user.photoURL ? (
                            <Image
                              src={user.photoURL}
                              alt={user.name || "User"}
                              width={40}
                              height={40}
                              className="rounded-full"
                            />
                          ) : (
                            <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                              <UserIcon className="h-6 w-6 text-gray-500" />
                            </div>
                          )}
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            {user.name || "Unnamed User"}
                          </div>
                          <div className="text-sm text-gray-500">
                            {user.email || "No email"}
                          </div>
                          {/* Mobile-only status badges */}
                          <div className="sm:hidden mt-2 flex flex-col space-y-2">
                            <span
                              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                user.isAdmin
                                  ? "bg-purple-100 text-purple-800"
                                  : "bg-gray-100 text-gray-800"
                              }`}
                            >
                              {user.isAdmin ? (
                                <>
                                  <ShieldCheckIcon className="h-4 w-4 mr-1" />
                                  Admin
                                </>
                              ) : (
                                <>
                                  <UserIcon className="h-4 w-4 mr-1" />
                                  Regular User
                                </>
                              )}
                            </span>

                            <span
                              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                user.canAccessAIAnalysis
                                  ? "bg-blue-100 text-blue-800"
                                  : "bg-gray-100 text-gray-500"
                              }`}
                            >
                              {user.canAccessAIAnalysis ? (
                                <>
                                  <SparklesIcon className="h-4 w-4 mr-1" />
                                  AI Access Enabled
                                </>
                              ) : (
                                <>
                                  <SparklesIcon className="h-4 w-4 mr-1" />
                                  No AI Access
                                </>
                              )}
                            </span>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-4 sm:px-6 py-4 hidden sm:table-cell">
                      <div className="flex flex-col space-y-2">
                        <span
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            user.isAdmin
                              ? "bg-purple-100 text-purple-800"
                              : "bg-gray-100 text-gray-800"
                          }`}
                        >
                          {user.isAdmin ? (
                            <>
                              <ShieldCheckIcon className="h-4 w-4 mr-1" />
                              Admin
                            </>
                          ) : (
                            <>
                              <UserIcon className="h-4 w-4 mr-1" />
                              Regular User
                            </>
                          )}
                        </span>

                        <span
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            user.canAccessAIAnalysis
                              ? "bg-blue-100 text-blue-800"
                              : "bg-gray-100 text-gray-500"
                          }`}
                        >
                          {user.canAccessAIAnalysis ? (
                            <>
                              <SparklesIcon className="h-4 w-4 mr-1" />
                              AI Access Enabled
                            </>
                          ) : (
                            <>
                              <SparklesIcon className="h-4 w-4 mr-1" />
                              No AI Access
                            </>
                          )}
                        </span>
                      </div>
                    </td>
                    <td className="px-4 sm:px-6 py-4 text-sm text-gray-500">
                      <div className="flex flex-col space-y-2">
                        {/* AI Access Management Component */}
                        <ManageUserAIAccess
                          userId={user.id}
                          hasAIAccess={user.canAccessAIAnalysis}
                          isAdmin={user.isAdmin}
                          isProcessing={processingUser === user.id}
                          onAccessChanged={() => {
                            // Toggle AI access
                            toggleAIAccess(user.id, user.canAccessAIAnalysis);
                          }}
                        />
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        <div className="mt-4 flex justify-between">
          <button
            onClick={handlePreviousPage}
            disabled={isFirstPage || isLoading}
            className="inline-flex items-center px-3 sm:px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <ChevronLeftIcon className="h-5 w-5 mr-1" />
            <span className="hidden sm:inline">Previous</span>
            <span className="sm:hidden">Prev</span>
          </button>
          <button
            onClick={handleNextPage}
            disabled={!lastVisible || isLoading}
            className="inline-flex items-center px-3 sm:px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Next
            <ChevronRightIcon className="h-5 w-5 ml-1" />
          </button>
        </div>
      </div>
    </div>
  );
}
