"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import Image from "next/image";
import {
  ArrowLeftIcon,
  MapIcon,
  ClockIcon,
  ArrowRightIcon,
  InformationCircleIcon,
  PencilIcon,
  UserGroupIcon,
  PresentationChartBarIcon,
  MicrophoneIcon,
  StopIcon,
  PlayIcon,
  CheckIcon,
  MagnifyingGlassIcon,
} from "@heroicons/react/24/outline";
import NavigationButton from "@/components/NavigationButton";
import SimpleButton from "@/components/SimpleButton";
import { gpeScenarios, GPEScenario } from "@/data/gpe-scenarios";
import { ZoomableImage } from "@/components/ImageZoomModal";

// Define the steps of the GPE process
const GPE_STEPS = [
  {
    id: "intro",
    title: "Introduction",
    description: "Overview of the Group Planning Exercise",
    icon: InformationCircleIcon,
  },
  {
    id: "narration",
    title: "GTO Narration",
    description: "The GTO explains the situation and color codes",
    icon: MapIcon,
  },
  {
    id: "story",
    title: "Story Reading",
    description: "5 minutes to read and understand the situation",
    icon: ClockIcon,
    duration: 5 * 60, // 5 minutes in seconds
  },
  {
    id: "discussion",
    title: "Group Discussion",
    description: "20 minutes for group discussion",
    icon: UserGroupIcon,
    duration: 20 * 60, // 20 minutes in seconds
  },
  {
    id: "solution",
    title: "Solution Planning",
    description: "10 minutes to write your solution",
    icon: PencilIcon,
    duration: 10 * 60, // 10 minutes in seconds
  },
  {
    id: "presentation",
    title: "Final Solution Presentation",
    description: "Present your final solution",
    icon: PresentationChartBarIcon,
  },
];

// Recording interface
interface Recording {
  url: string;
  duration: number;
  blob: Blob;
}

export default function GPETestPage() {
  const params = useParams();
  const router = useRouter();
  const setId = params.setId as string;

  const [currentStep, setCurrentStep] = useState(0);
  const [selectedScenario, setSelectedScenario] = useState<GPEScenario | null>(
    null
  );
  const [timeLeft, setTimeLeft] = useState(0);
  const [isTimerActive, setIsTimerActive] = useState(false);
  const [solution, setSolution] = useState("");
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // Audio recording states
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [recordings, setRecordings] = useState<Record<string, Recording>>({});
  const [audioStream, setAudioStream] = useState<MediaStream | null>(null);
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(
    null
  );
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentPlayingKey, setCurrentPlayingKey] = useState<string | null>(
    null
  );

  // Audio recording refs
  const audioChunksRef = useRef<BlobPart[]>([]);
  const currentRecordingTimeRef = useRef<number>(0);
  const startTimeRef = useRef<number>(0);
  const audioPlayerRef = useRef<HTMLAudioElement | null>(null);

  // Load the scenario based on the setId
  useEffect(() => {
    // Extract the set number from the setId (e.g., "set1" -> 1)
    const setNumber = parseInt(setId.replace("set", ""), 10);

    if (
      !isNaN(setNumber) &&
      setNumber > 0 &&
      setNumber <= gpeScenarios.length
    ) {
      // Scenarios are 0-indexed in the array, but set IDs are 1-indexed
      setSelectedScenario(gpeScenarios[setNumber - 1]);
    } else {
      // Handle invalid set ID
      router.push("/tests/gto/gpe/sets");
    }
  }, [setId, router]);

  // Timer functionality
  useEffect(() => {
    if (isTimerActive && timeLeft > 0) {
      timerRef.current = setTimeout(() => {
        setTimeLeft((prev) => prev - 1);
      }, 1000);
    } else if (timeLeft === 0 && isTimerActive) {
      setIsTimerActive(false);
      // Auto-advance to next step when timer ends
      if (currentStep < GPE_STEPS.length - 1) {
        setCurrentStep((prev) => prev + 1);
      }
    }

    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, [timeLeft, isTimerActive, currentStep]);

  // Recording timer functionality
  useEffect(() => {
    if (isRecording) {
      // Set the start time when recording begins
      startTimeRef.current = Date.now();

      // Start a timer to update the recording time display
      const recordingTimer = setInterval(() => {
        const elapsedSeconds = Math.floor(
          (Date.now() - startTimeRef.current) / 1000
        );
        setRecordingTime(elapsedSeconds);
      }, 1000);

      // Clean up the timer when recording stops
      return () => {
        clearInterval(recordingTimer);
        setRecordingTime(0);
      };
    }
  }, [isRecording]);

  // Function to start the timer
  const startTimer = () => {
    // Scroll to top of the page
    window.scrollTo({ top: 0, behavior: "smooth" });
    setIsTimerActive(true);
  };

  // Format time as MM:SS
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  };

  // Handle next step button click
  const handleNextStep = () => {
    // If recording is in progress, stop it before moving to the next step
    if (isRecording) {
      stopRecording();
    }

    // Scroll to top of the page
    window.scrollTo({ top: 0, behavior: "smooth" });

    if (currentStep < GPE_STEPS.length - 1) {
      setCurrentStep((prev) => prev + 1);
      setIsTimerActive(false);

      // If the next step has a timer, initialize it
      const nextStepData = GPE_STEPS[currentStep + 1];
      if (nextStepData.duration) {
        setTimeLeft(nextStepData.duration);
      }
    }
  };

  // Handle submission
  const handleSubmit = () => {
    // If recording is in progress, stop it before submitting
    if (isRecording) {
      stopRecording();

      // We need to wait a moment for the recording to be processed
      setTimeout(() => {
        // Scroll to top of the page
        window.scrollTo({ top: 0, behavior: "smooth" });
        saveAndNavigate();
      }, 500);
    } else {
      // Scroll to top of the page
      window.scrollTo({ top: 0, behavior: "smooth" });
      saveAndNavigate();
    }
  };

  // Helper function to save data and navigate
  const saveAndNavigate = () => {
    // Save solution to localStorage
    localStorage.setItem(`gpe-solution-${setId}`, solution);

    // Save recordings to localStorage
    // We need to convert the recordings to a format that can be stored in localStorage
    // (removing the Blob objects which can't be serialized)
    const serializableRecordings: Record<
      string,
      { url: string; duration: number }
    > = {};
    Object.entries(recordings).forEach(([key, recording]) => {
      serializableRecordings[key] = {
        url: recording.url,
        duration: recording.duration,
      };
    });
    localStorage.setItem(
      `gpe-recordings-${setId}`,
      JSON.stringify(serializableRecordings)
    );

    // Navigate to the results page with the set ID
    router.push(`/tests/gto/gpe/${setId}/results`);
  };

  // Function to stop recording - defined with useCallback to avoid dependency issues
  const stopRecording = useCallback(() => {
    if (mediaRecorder && isRecording) {
      // Calculate final duration before stopping anything
      const finalDuration = Math.floor(
        (Date.now() - startTimeRef.current) / 1000
      );
      currentRecordingTimeRef.current = finalDuration;

      // Stop the recorder - this will trigger the onstop event
      mediaRecorder.stop();

      // Setting isRecording to false will trigger the useEffect cleanup
      setIsRecording(false);

      // Stop and release microphone
      if (audioStream) {
        audioStream.getTracks().forEach((track) => track.stop());
      }
    }
  }, [mediaRecorder, isRecording, audioStream]);

  // Function to start recording
  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      setAudioStream(stream);

      const recorder = new MediaRecorder(stream);
      setMediaRecorder(recorder);

      audioChunksRef.current = [];

      recorder.ondataavailable = (e) => {
        audioChunksRef.current.push(e.data);
      };

      recorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, {
          type: "audio/webm",
        });
        const audioUrl = URL.createObjectURL(audioBlob);

        // Save recording with the final recording time
        const finalDuration = currentRecordingTimeRef.current;

        // Use the current step as the recording key
        const recordingKey = GPE_STEPS[currentStep].id;

        setRecordings((prev) => ({
          ...prev,
          [recordingKey]: {
            url: audioUrl,
            duration: finalDuration,
            blob: audioBlob,
          },
        }));
      };

      recorder.start();
      setIsRecording(true);

      // If we're in the discussion section, start the timer automatically
      const currentStepId = GPE_STEPS[currentStep].id;
      if (currentStepId === "discussion" && !isTimerActive) {
        setIsTimerActive(true);
      }
    } catch (error) {
      console.error("Error accessing microphone:", error);
      alert("Could not access your microphone. Please check your permissions.");
    }
  };

  // Function to play recording
  const playRecording = (recordingKey: string) => {
    // If already playing this recording, stop it
    if (isPlaying && currentPlayingKey === recordingKey) {
      stopPlayback();
      return;
    }

    // If another recording is playing, stop it first
    if (isPlaying && audioPlayerRef.current) {
      stopPlayback();
    }

    // Create and play the new audio
    const audio = new Audio(recordings[recordingKey]?.url);
    audioPlayerRef.current = audio;

    // Set up event listeners
    audio.addEventListener("ended", () => {
      setIsPlaying(false);
      setCurrentPlayingKey(null);
      audioPlayerRef.current = null;
    });

    // Start playback
    audio
      .play()
      .then(() => {
        setIsPlaying(true);
        setCurrentPlayingKey(recordingKey);
      })
      .catch((error) => {
        console.error("Error playing audio:", error);
        setIsPlaying(false);
        setCurrentPlayingKey(null);
        audioPlayerRef.current = null;
      });
  };

  // Function to stop audio playback
  const stopPlayback = () => {
    if (audioPlayerRef.current) {
      audioPlayerRef.current.pause();
      audioPlayerRef.current.currentTime = 0;
      audioPlayerRef.current = null;
    }
    setIsPlaying(false);
    setCurrentPlayingKey(null);
  };

  // Render the map for the current scenario
  const renderMap = () => {
    if (!selectedScenario) return null;

    // Only show the map in the GTO Narration and Story Reading sections
    const currentStepId = GPE_STEPS[currentStep].id;
    if (currentStepId !== "narration" && currentStepId !== "story") {
      return null;
    }

    // Get the image number from the setId (e.g., "set1" -> 1)
    const setNumber = parseInt(setId.replace("set", ""), 10);
    const imagePath = `/images/gto/gpe/img${setNumber}.png`;

    // Only make the image zoomable in the story reading section
    if (currentStepId === "story") {
      return (
        <div className="mt-6 mb-8 relative">
          <div className="aspect-w-16 aspect-h-9 rounded-lg overflow-hidden shadow-lg border-2 border-green-100 hover:border-green-300 transition-colors duration-200">
            <ZoomableImage
              src={imagePath}
              alt={`Scenario map for ${selectedScenario.title}`}
              width={800}
              height={450}
              className="object-cover w-full h-full"
              title={`${selectedScenario.title} - Scenario Map`}
            />
          </div>
          <div className="text-center mt-2 text-sm text-gray-500 flex items-center justify-center">
            <MagnifyingGlassIcon className="h-4 w-4 mr-1 text-green-600" />
            <span>Click on the image to zoom</span>
          </div>
        </div>
      );
    } else {
      return (
        <div className="mt-6 mb-8 relative">
          <div className="aspect-w-16 aspect-h-9 rounded-lg overflow-hidden shadow-lg">
            <Image
              src={imagePath}
              alt={`Scenario map for ${selectedScenario.title}`}
              width={800}
              height={450}
              className="object-cover"
            />
          </div>
        </div>
      );
    }
  };

  // Render the content for the current step
  const renderStepContent = () => {
    if (!selectedScenario) return null;

    const currentStepId = GPE_STEPS[currentStep].id;

    switch (currentStepId) {
      case "intro":
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900">
              Welcome to the Interactive Group Planning Exercise
            </h2>
            <p className="text-gray-700">
              This simulation will guide you through the actual steps of a Group
              Planning Exercise (GPE) as conducted in military selection tests.
            </p>
            <div className="bg-green-50 p-6 rounded-lg">
              <h3 className="text-lg font-medium text-green-900 mb-4">
                The GPE process consists of the following steps:
              </h3>
              <ol className="list-decimal pl-5 space-y-2 text-green-800">
                <li>GTO narrates the situation and explains the situation</li>
                <li>
                  Candidates are given 5 minutes to read and understand the
                  situation
                </li>
                <li>Groups have 20 minutes for discussion and planning</li>
                <li>Candidates are given 10 minutes to write their solution</li>
                <li>A nominated candidate presents the final solution</li>
              </ol>
            </div>
            <p className="text-gray-700">
              In this simulation, you will experience each of these steps. Click
              "Next" to begin with the GTO Narration.
            </p>
          </div>
        );

      case "narration":
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900">
              GTO Narration: {selectedScenario.title}
            </h2>
            <p className="text-gray-700">
              The GTO is now explaining the situation using the map below. Pay
              attention to the locations and details.
            </p>

            {renderMap()}

            <div className="bg-gray-50 p-6 rounded-lg border border-gray-200">
              <h3 className="text-lg font-medium text-gray-800 mb-4">
                Situation Overview
              </h3>
              <p className="text-gray-700 mb-4">
                {selectedScenario.description}
              </p>
            </div>
          </div>
        );

      case "story":
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-gray-900">
                Story Reading: {selectedScenario.title}
              </h2>
              <div className="bg-green-100 text-green-800 px-4 py-2 rounded-full flex items-center">
                <ClockIcon className="h-5 w-5 mr-2" />
                <span className="font-mono font-medium">
                  {formatTime(timeLeft)}
                </span>
              </div>
            </div>
            <p className="text-gray-700">
              You now have 5 minutes to read and understand the detailed
              situation. Study the map and the scenario carefully.
            </p>

            {renderMap()}

            <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
              <h3 className="text-lg font-medium text-gray-800 mb-4">
                Detailed Situation
              </h3>
              <div className="prose prose-gray max-w-none">
                <p>{selectedScenario.situation}</p>
              </div>
            </div>

            <div className="bg-amber-50 p-4 rounded-lg border border-amber-200">
              <h4 className="font-medium mb-2 text-amber-800">
                Time Constraints:
              </h4>
              <ul className="list-disc pl-5 space-y-1 text-amber-700">
                <li>
                  Start Time: {selectedScenario.timeConstraints.startTime}
                </li>
                <li>End Time: {selectedScenario.timeConstraints.endTime}</li>
                <li>
                  Total Available Hours:{" "}
                  {selectedScenario.timeConstraints.totalAvailableHours}
                </li>
              </ul>
            </div>

            {!isTimerActive && (
              <div className="flex justify-center">
                <button
                  onClick={startTimer}
                  className="bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-6 rounded-lg shadow transition duration-200 flex items-center"
                >
                  <ClockIcon className="h-5 w-5 mr-2" />
                  Start 5-Minute Timer
                </button>
              </div>
            )}
          </div>
        );

      case "discussion":
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-gray-900">
                Group Discussion
              </h2>
              <div className="bg-green-100 text-green-800 px-4 py-2 rounded-full flex items-center">
                <ClockIcon className="h-5 w-5 mr-2" />
                <span className="font-mono font-medium">
                  {formatTime(timeLeft)}
                </span>
              </div>
            </div>
            <p className="text-gray-700">
              Your group now has 20 minutes to discuss the situation and develop
              a plan. Consider resource allocation, prioritization, and time
              management.
            </p>

            <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
              <h3 className="text-lg font-medium text-gray-800 mb-4">
                Discussion Guidelines
              </h3>
              <ul className="list-disc pl-5 space-y-2 text-gray-700">
                <li>Analyze the situation and identify the key challenges</li>
                <li>Prioritize the tasks based on urgency and importance</li>
                <li>Allocate resources efficiently</li>
                <li>Consider time constraints and distances</li>
                <li>Develop a clear, step-by-step plan</li>
              </ul>
            </div>

            {/* Recording Controls */}
            <div className="mt-8 mb-6 bg-gray-50 p-6 rounded-lg border border-gray-200">
              <h3 className="text-lg font-medium text-gray-800 mb-4">
                Record Your Discussion
              </h3>
              <p className="text-gray-600 mb-4">
                Use this recorder to capture your group's discussion. This will
                help you review your planning process later.
              </p>

              {!isRecording && !recordings[currentStepId] ? (
                <button
                  onClick={startRecording}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-base font-medium rounded-md text-white bg-red-600 hover:bg-red-700 shadow-sm"
                >
                  <MicrophoneIcon className="h-5 w-5 mr-2" />
                  Start Recording
                </button>
              ) : isRecording ? (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center text-red-600">
                      <span className="animate-pulse mr-2">●</span>
                      <span>
                        Recording...{" "}
                        {recordingTime > 0 ? formatTime(recordingTime) : "0:00"}
                      </span>
                    </div>
                    <p className="text-sm text-gray-500 italic">
                      Speak clearly and at a moderate pace
                    </p>
                  </div>
                  <button
                    onClick={stopRecording}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-base font-medium rounded-md text-white bg-gray-600 hover:bg-gray-700 shadow-sm"
                  >
                    <StopIcon className="h-5 w-5 mr-2" />
                    Stop Recording
                  </button>
                </div>
              ) : (
                <div className="space-y-4">
                  <button
                    onClick={() => playRecording(currentStepId)}
                    className={`inline-flex items-center px-4 py-2 border text-base font-medium rounded-md ${
                      isPlaying && currentPlayingKey === currentStepId
                        ? "border-red-300 text-red-700 bg-red-50 hover:bg-red-100"
                        : "border-indigo-300 text-indigo-700 bg-indigo-50 hover:bg-indigo-100"
                    }`}
                  >
                    {isPlaying && currentPlayingKey === currentStepId ? (
                      <>
                        <StopIcon className="h-5 w-5 mr-2" />
                        Stop Playing
                      </>
                    ) : (
                      <>
                        <PlayIcon className="h-5 w-5 mr-2" />
                        Play Recording (
                        {formatTime(recordings[currentStepId]?.duration || 0)})
                      </>
                    )}
                  </button>
                  <button
                    onClick={startRecording}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-base font-medium rounded-md text-white bg-red-600 hover:bg-red-700 shadow-sm"
                  >
                    <MicrophoneIcon className="h-5 w-5 mr-2" />
                    Record Again
                  </button>
                </div>
              )}
            </div>

            {/* Timer starts automatically when recording begins */}
          </div>
        );

      case "solution":
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-gray-900">
                Write Your Solution
              </h2>
              <div className="bg-green-100 text-green-800 px-4 py-2 rounded-full flex items-center">
                <ClockIcon className="h-5 w-5 mr-2" />
                <span className="font-mono font-medium">
                  {formatTime(timeLeft)}
                </span>
              </div>
            </div>
            <p className="text-gray-700">
              Based on your group discussion, you now have 10 minutes to write
              down your solution. Be clear, concise, and specific.
            </p>

            <div className="space-y-4">
              <div>
                <textarea
                  className="w-full h-64 p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  placeholder="Write your solution here. Consider resource allocation, prioritization, and time management..."
                  value={solution}
                  onChange={(e) => setSolution(e.target.value)}
                ></textarea>
              </div>
            </div>

            {!isTimerActive && (
              <div className="flex justify-center">
                <button
                  onClick={startTimer}
                  className="bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-6 rounded-lg shadow transition duration-200 flex items-center"
                >
                  <ClockIcon className="h-5 w-5 mr-2" />
                  Start 10-Minute Timer
                </button>
              </div>
            )}
          </div>
        );

      case "presentation":
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900">
              Final Solution Presentation
            </h2>
            <p className="text-gray-700">
              A nominated candidate from your group will now present the final
              solution to the GTO. The presentation should be clear, confident,
              and well-structured.
            </p>

            <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
              <h3 className="text-lg font-medium text-gray-800 mb-4">
                Your Written Solution
              </h3>
              <div className="bg-gray-50 p-4 rounded-lg border border-gray-200 min-h-[100px] whitespace-pre-wrap">
                {solution || "No solution written yet."}
              </div>
            </div>

            {/* Recording Controls */}
            <div className="mt-8 mb-6 bg-gray-50 p-6 rounded-lg border border-gray-200">
              <h3 className="text-lg font-medium text-gray-800 mb-4">
                Record Your Presentation
              </h3>
              <p className="text-gray-600 mb-4">
                Use this recorder to capture your presentation. This will help
                you review and improve your presentation skills.
              </p>

              {!isRecording && !recordings[currentStepId] ? (
                <button
                  onClick={startRecording}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-base font-medium rounded-md text-white bg-red-600 hover:bg-red-700 shadow-sm"
                >
                  <MicrophoneIcon className="h-5 w-5 mr-2" />
                  Start Recording
                </button>
              ) : isRecording ? (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center text-red-600">
                      <span className="animate-pulse mr-2">●</span>
                      <span>
                        Recording...{" "}
                        {recordingTime > 0 ? formatTime(recordingTime) : "0:00"}
                      </span>
                    </div>
                    <p className="text-sm text-gray-500 italic">
                      Speak clearly and at a moderate pace
                    </p>
                  </div>
                  <button
                    onClick={stopRecording}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-base font-medium rounded-md text-white bg-gray-600 hover:bg-gray-700 shadow-sm"
                  >
                    <StopIcon className="h-5 w-5 mr-2" />
                    Stop Recording
                  </button>
                </div>
              ) : (
                <div className="space-y-4">
                  <button
                    onClick={() => playRecording(currentStepId)}
                    className={`inline-flex items-center px-4 py-2 border text-base font-medium rounded-md ${
                      isPlaying && currentPlayingKey === currentStepId
                        ? "border-red-300 text-red-700 bg-red-50 hover:bg-red-100"
                        : "border-indigo-300 text-indigo-700 bg-indigo-50 hover:bg-indigo-100"
                    }`}
                  >
                    {isPlaying && currentPlayingKey === currentStepId ? (
                      <>
                        <StopIcon className="h-5 w-5 mr-2" />
                        Stop Playing
                      </>
                    ) : (
                      <>
                        <PlayIcon className="h-5 w-5 mr-2" />
                        Play Recording (
                        {formatTime(recordings[currentStepId]?.duration || 0)})
                      </>
                    )}
                  </button>
                  <button
                    onClick={startRecording}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-base font-medium rounded-md text-white bg-red-600 hover:bg-red-700 shadow-sm"
                  >
                    <MicrophoneIcon className="h-5 w-5 mr-2" />
                    Record Again
                  </button>
                </div>
              )}
            </div>

            <div className="bg-green-50 p-6 rounded-lg">
              <h3 className="text-lg font-medium text-green-900 mb-4">
                Presentation Tips
              </h3>
              <ul className="list-disc pl-5 space-y-2 text-green-800">
                <li>Start with a brief overview of the situation</li>
                <li>Clearly state your priorities and reasoning</li>
                <li>Explain your resource allocation decisions</li>
                <li>Present your plan in a logical, step-by-step manner</li>
                <li>Be prepared to answer questions about your solution</li>
              </ul>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  // Render the progress steps
  const renderProgressSteps = () => {
    return (
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {GPE_STEPS.map((step, index) => (
            <div
              key={step.id}
              className={`flex flex-col items-center ${
                index === GPE_STEPS.length - 1 ? "" : "relative"
              }`}
            >
              {/* Line connecting steps */}
              {index < GPE_STEPS.length - 1 && (
                <div
                  className={`absolute top-4 left-1/2 w-full h-0.5 ${
                    index < currentStep ? "bg-green-500" : "bg-gray-200"
                  }`}
                ></div>
              )}

              {/* Step circle */}
              <div
                className={`z-10 flex items-center justify-center w-8 h-8 rounded-full ${
                  index < currentStep
                    ? "bg-green-500 text-white"
                    : index === currentStep
                    ? "bg-green-500 text-white"
                    : "bg-gray-200 text-gray-500"
                }`}
              >
                {index < currentStep ? (
                  <CheckIcon className="w-5 h-5" />
                ) : (
                  <step.icon className="w-4 h-4" />
                )}
              </div>

              {/* Step label - only show on larger screens */}
              <span
                className={`hidden md:block mt-2 text-xs ${
                  index <= currentStep
                    ? "text-green-600 font-medium"
                    : "text-gray-500"
                }`}
              >
                {step.title}
              </span>
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="mx-auto max-w-5xl px-6">
        <button
          onClick={() => {
            // Scroll to top of the page
            window.scrollTo({ top: 0, behavior: "smooth" });
            // Navigate back to GPE Sets
            router.push("/tests/gto/gpe/sets");
          }}
          className="mb-8 inline-flex items-center text-gray-600 hover:text-gray-900"
        >
          <ArrowLeftIcon className="h-5 w-5 mr-2" />
          Back to GPE Sets
        </button>

        <div className="bg-white rounded-2xl shadow-sm p-8 border-t-4 border-green-500">
          <div className="flex items-center space-x-3 mb-6">
            <div className="p-2 rounded-full bg-green-100">
              <MapIcon className="h-8 w-8 text-green-600" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900">
              {selectedScenario
                ? selectedScenario.title
                : "Group Planning Exercise"}
              <span className="ml-2 text-lg font-normal text-gray-500">
                (Set {setId.replace("set", "")})
              </span>
            </h1>
          </div>

          {/* Progress steps */}
          {renderProgressSteps()}

          {/* Step content */}
          <div className="mb-8">{renderStepContent()}</div>

          {/* Navigation buttons */}
          <div className="flex justify-end mt-8">
            {currentStep === GPE_STEPS.length - 1 ? (
              <SimpleButton
                onClick={handleSubmit}
                className="flex items-center bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg shadow"
              >
                Submit
                <ArrowRightIcon className="h-5 w-5 ml-2" />
              </SimpleButton>
            ) : (
              <SimpleButton
                onClick={handleNextStep}
                className="flex items-center"
              >
                Next
                <ArrowRightIcon className="h-5 w-5 ml-2" />
              </SimpleButton>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
