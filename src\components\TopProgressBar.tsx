"use client";

import { useEffect, Suspense } from "react";
import { usePathname, useSearchParams } from "next/navigation";
import NProgress from "nprogress";

// Configure NProgress
NProgress.configure({
  minimum: 0.3,
  easing: "ease",
  speed: 500,
  showSpinner: false,
});

// Client component that uses searchParams
function ProgressBarContent() {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  useEffect(() => {
    // Create a CSS style element for NProgress
    const style = document.createElement("style");
    style.textContent = `
      /* Make clicks pass-through */
      #nprogress {
        pointer-events: none;
      }

      #nprogress .bar {
        background: #2563eb;
        position: fixed;
        z-index: 9999;
        top: 0;
        left: 0;
        width: 100%;
        height: 3px;
      }

      /* Fancy blur effect */
      #nprogress .peg {
        display: block;
        position: absolute;
        right: 0px;
        width: 100px;
        height: 100%;
        box-shadow: 0 0 10px #2563eb, 0 0 5px #2563eb;
        opacity: 1.0;
        transform: rotate(3deg) translate(0px, -4px);
      }
    `;
    document.head.appendChild(style);

    return () => {
      // Clean up the style element when component unmounts
      document.head.removeChild(style);
    };
  }, []);

  // Track route changes
  useEffect(() => {
    let currentPath = pathname + searchParams.toString();
    let timeoutId: NodeJS.Timeout | null = null;

    // Start NProgress when route changes
    NProgress.start();

    // Complete NProgress after a short delay
    timeoutId = setTimeout(() => {
      NProgress.done();
    }, 300);

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
      NProgress.done();
    };
  }, [pathname, searchParams]);

  // Handle click events on links
  useEffect(() => {
    const handleLinkClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      const link = target.closest("a");

      if (
        link &&
        link.href &&
        link.href.startsWith(window.location.origin) &&
        !link.hasAttribute("download") &&
        link.target !== "_blank"
      ) {
        // Start NProgress
        NProgress.start();
      }
    };

    document.addEventListener("click", handleLinkClick);
    return () => document.removeEventListener("click", handleLinkClick);
  }, []);

  return null; // This component doesn't render anything visible
}

// Wrap the component that uses searchParams in Suspense
export default function TopProgressBar() {
  return (
    <Suspense fallback={null}>
      <ProgressBarContent />
    </Suspense>
  );
}
