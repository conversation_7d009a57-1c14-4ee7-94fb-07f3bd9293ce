"use client";

import Image, { ImageProps } from "next/image";
import { useState, useEffect } from "react";

interface ProtectedImageProps extends Omit<ImageProps, "onContextMenu"> {
  showProtectionOverlay?: boolean;
}

export default function ProtectedImage({
  showProtectionOverlay = false,
  ...props
}: ProtectedImageProps) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Prevent right-click context menu
  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();
    return false;
  };

  // Prevent drag start
  const handleDragStart = (e: React.DragEvent) => {
    e.preventDefault();
    return false;
  };

  // Prevent mouse down (which can be used for dragging)
  const handleMouseDown = (e: React.MouseEvent) => {
    if (e.button === 2) {
      // Right click
      e.preventDefault();
      return false;
    }
  };

  return (
    <div className="relative" style={{ display: "inline-block" }}>
      <Image
        {...props}
        onContextMenu={handleContextMenu}
        onDragStart={handleDragStart}
        onMouseDown={handleMouseDown}
        style={{
          ...props.style,
          pointerEvents: "none", // Prevents drag-and-drop
        }}
        draggable={false}
        alt="Protected Image"
      />
      {/* Optional transparent overlay to prevent selection */}
      {showProtectionOverlay && isClient && (
        <div
          className="absolute inset-0 bg-transparent"
          onContextMenu={handleContextMenu}
          style={{ pointerEvents: "auto" }}
        />
      )}
    </div>
  );
}
