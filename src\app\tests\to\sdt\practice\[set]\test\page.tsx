"use client";

import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import NavigationButton from "@/components/NavigationButton";
import LoadingButton from "@/components/LoadingButton";
import SimpleButton from "@/components/SimpleButton";

import { useAuth } from "@/context/GoogleAuthContext";
import {
  ArrowLeftIcon,
  CheckIcon,
  ClockIcon,
} from "@heroicons/react/24/outline";
import { use } from "react";
import { sdtQuestionSets, getAllSDTQuestions } from "@/data/sdt-questions";
import { saveResponses } from "@/services/responseService";

export default function SDTTest({
  params,
}: {
  params: Promise<{ set: string }>;
}) {
  const router = useRouter();
  const { user } = useAuth();
  const { set } = use(params);
  const setName = decodeURIComponent(set);
  const [responses, setResponses] = useState<string[]>([]);
  const [questions, setQuestions] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeLeft, setTimeLeft] = useState(15 * 60); // 15 minutes in seconds
  const [isSaving, setIsSaving] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const firstTextareaRef = useRef<HTMLTextAreaElement>(null);

  // Get the questions based on the set
  useEffect(() => {
    try {
      let questionList: string[] = [];

      if (setName === "Complete") {
        questionList = getAllSDTQuestions();
      } else if (sdtQuestionSets[setName]) {
        questionList = sdtQuestionSets[setName].questions;
      } else {
        throw new Error(
          `Set "${setName}" not found. Please select a valid set.`
        );
      }

      setQuestions(questionList);
      setResponses(Array(questionList.length).fill(""));
      setLoading(false);
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "An unknown error occurred"
      );
      setLoading(false);
    }
  }, [setName]);

  // Focus the first textarea when the page loads
  useEffect(() => {
    if (firstTextareaRef.current) {
      firstTextareaRef.current.focus();
    }
  }, [loading]);

  // Timer countdown
  useEffect(() => {
    if (timeLeft <= 0) {
      // Don't automatically submit when timer expires
      // Just stop the timer and show a message
      console.log("Time expired - please submit your responses");
      return;
    }

    const timer = setInterval(() => {
      setTimeLeft((prevTime) => prevTime - 1);
    }, 1000);

    return () => clearInterval(timer);
  }, [timeLeft]);

  // Format time as MM:SS
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, "0")}:${remainingSeconds
      .toString()
      .padStart(2, "0")}`;
  };

  const handleResponseChange = (index: number, value: string) => {
    const newResponses = [...responses];
    newResponses[index] = value;
    setResponses(newResponses);
  };

  const handleComplete = async () => {
    if (isSubmitting) return;
    setIsSubmitting(true);

    // Stop the timer by setting timeLeft to 0
    setTimeLeft(0);

    console.log("Submitting SDT responses...");

    // Save responses to localStorage with set-specific keys
    localStorage.setItem(`sdt_responses_${setName}`, JSON.stringify(responses));
    localStorage.setItem("sdt_responses", JSON.stringify(responses)); // Keep for backward compatibility
    localStorage.setItem(`sdt_questions_${setName}`, JSON.stringify(questions));
    localStorage.setItem("sdt_questions", JSON.stringify(questions)); // Keep for backward compatibility
    localStorage.setItem("sdt_current_set", setName);

    // Save to cloud storage if user is logged in
    if (user) {
      setIsSaving(true);
      try {
        const timestamp = await saveResponses(
          user.uid,
          "sdt",
          setName,
          responses,
          questions
        );
        console.log("Saved responses to Firestore, timestamp:", timestamp);
        // Save the timestamp to localStorage to prevent duplicate saves
        localStorage.setItem(
          `sdt_${setName}_last_save_timestamp`,
          timestamp.toString()
        );
      } catch (error) {
        console.error("Error saving responses to cloud:", error);
        // Continue anyway since we have localStorage backup
      } finally {
        setIsSaving(false);
      }
    }

    // Navigate to results page
    router.push(
      `/tests/to/sdt/practice/${encodeURIComponent(setName)}/results`
    );
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="mx-auto max-w-3xl px-6 text-center">
          <p className="text-gray-600">Loading questions...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="mx-auto max-w-3xl px-6">
          <div className="bg-white rounded-2xl shadow-sm p-8 text-center">
            <h2 className="text-2xl font-bold text-red-600 mb-4">Error</h2>
            <p className="text-gray-600 mb-8">{error}</p>
            <LoadingButton
              onClick={() => router.push("/tests/to/sdt/practice")}
              className="inline-flex justify-center rounded-md bg-amber-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-amber-700"
              loadingText="Loading..."
            >
              Return to Practice Sets
            </LoadingButton>
          </div>
        </div>
      </div>
    );
  }

  if (questions.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="mx-auto max-w-3xl px-6">
          <div className="bg-white rounded-2xl shadow-sm p-8 text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              No Questions Available
            </h2>
            <p className="text-gray-600 mb-8">
              There are no questions available for this set. Please select a
              different set.
            </p>
            <LoadingButton
              onClick={() => router.push("/tests/to/sdt/practice")}
              className="inline-flex justify-center rounded-md bg-amber-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-amber-700"
              loadingText="Loading..."
            >
              Return to Practice Sets
            </LoadingButton>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="mx-auto max-w-3xl px-6">
        <LoadingButton
          onClick={() => router.push("/tests/to")}
          className="mb-8 flex items-center text-gray-600 hover:text-gray-900"
          loadingText="Loading..."
        >
          <ArrowLeftIcon className="h-5 w-5 mr-2" />
          Back to TO Test
        </LoadingButton>

        <div className="bg-white rounded-2xl shadow-sm p-8">
          <div className="flex justify-between items-center mb-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Self Description Test
              </h1>
              <p className="text-sm text-gray-500">
                Answer all 5 questions within the time limit
              </p>
            </div>
            <div className="text-sm font-medium text-red-600 bg-red-50 px-3 py-1 rounded-full flex items-center">
              <ClockIcon className="h-4 w-4 mr-1" />
              Time Left: {formatTime(timeLeft)}
            </div>
          </div>

          <div className="bg-amber-50 p-4 rounded-lg mb-6">
            <h2 className="text-sm font-medium text-amber-800 mb-2">
              Test Instructions:
            </h2>
            <ul className="text-sm text-amber-700 space-y-1 list-disc list-inside">
              <li>You have 15 minutes to answer all 5 questions</li>
              <li>Be honest and provide specific examples when possible</li>
              <li>
                Your responses reveal your self-awareness and interpersonal
                understanding
              </li>
              <li>Click the Submit Test button when you're finished</li>
            </ul>
          </div>

          <div className="space-y-8">
            {questions.map((question, index) => (
              <div key={index} className="space-y-4">
                <div className="bg-amber-50 p-6 rounded-lg">
                  <p className="text-lg text-gray-900 font-medium">
                    {index + 1}. {question}
                  </p>
                </div>

                <div className="space-y-2">
                  <label
                    htmlFor={`response-${index}`}
                    className="block text-sm font-medium text-gray-700"
                  >
                    Your Answer:
                  </label>
                  <textarea
                    ref={index === 0 ? firstTextareaRef : null}
                    id={`response-${index}`}
                    name={`response-${index}`}
                    rows={5}
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-amber-500 focus:ring-amber-500"
                    placeholder="Type your answer here..."
                    value={responses[index]}
                    onChange={(e) =>
                      handleResponseChange(index, e.target.value)
                    }
                  />
                </div>
              </div>
            ))}

            <div className="flex justify-center pt-6">
              <LoadingButton
                onClick={handleComplete}
                disabled={isSubmitting}
                className="inline-flex items-center rounded-md bg-amber-600 px-6 py-3 text-sm font-medium text-white shadow-sm hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                loadingText="Submitting..."
                isLoading={isSubmitting}
              >
                Submit Test
                <CheckIcon className="h-4 w-4 ml-2" />
              </LoadingButton>
            </div>

            {timeLeft <= 0 && (
              <div className="mt-4 p-4 bg-red-50 rounded-lg text-center">
                <p className="text-red-600 font-medium">
                  Time has expired. Please submit your responses now.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
