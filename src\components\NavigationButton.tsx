"use client";

import Link from "next/link";
import { useRouter, usePathname } from "next/navigation";
import { useState, useEffect, useRef } from "react";

interface NavigationButtonProps {
  href: string;
  className?: string;
  children: React.ReactNode;
  onClick?: () => void;
  loadingText?: string;
}

export default function NavigationButton({
  href,
  className = "",
  children,
  onClick,
  loadingText = "Loading...",
}: NavigationButtonProps) {
  const router = useRouter();
  const pathname = usePathname();
  const [isLoading, setIsLoading] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Effect to clear loading state when pathname changes
  useEffect(() => {
    if (isLoading) {
      setIsLoading(false);
    }

    // Clean up any timeouts when unmounting
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [pathname, isLoading]);

  const handleClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    // Don't do anything if already loading
    if (isLoading) return;

    // Start the loading state
    setIsLoading(true);

    // Execute any additional onClick handler if provided
    if (onClick) {
      onClick();
    }

    // Let the Link component handle the navigation
    // No need to call e.preventDefault() or manually navigate

    // Set a fallback timeout to clear loading state
    timeoutRef.current = setTimeout(() => {
      setIsLoading(false);
    }, 800);
  };

  return (
    <Link
      href={href}
      onClick={handleClick}
      className={`${className} ${isLoading ? "opacity-90 cursor-wait" : ""}`}
    >
      {isLoading ? (
        <span className="inline-flex items-center">
          <svg
            className="animate-spin -ml-1 mr-2 h-4 w-4 text-current"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
          {loadingText}
        </span>
      ) : (
        children
      )}
    </Link>
  );
}
