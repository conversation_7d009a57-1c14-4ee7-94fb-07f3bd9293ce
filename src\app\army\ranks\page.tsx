"use client";

import Link from "next/link";
import {
  ArrowLeftIcon,
  UserGroupIcon,
  ArrowRightIcon,
} from "@heroicons/react/24/outline";

// Define the rank types
const rankTypes = [
  {
    title: "Commissioned Officers",
    description:
      "Explore the hierarchy and responsibilities of commissioned officers in the Nepal Army, from Officer Cadet to General.",
    href: "/army/ranks/officers",
    bgColor: "bg-amber-50",
    textColor: "text-amber-700",
    borderColor: "border-amber-200",
    hoverBg: "hover:bg-amber-100",
    iconBg: "bg-amber-100",
  },
  {
    title: "NCO & Enlisted Ranks",
    description:
      "Learn about the backbone of Nepal Army from Followers to Subedar Major, including both enlisted personnel and non-commissioned officers.",
    href: "/army/ranks/non-commissioned-officers",
    bgColor: "bg-emerald-50",
    textColor: "text-emerald-700",
    borderColor: "border-emerald-200",
    hoverBg: "hover:bg-emerald-100",
    iconBg: "bg-emerald-100",
  },
];

export default function ArmyRanksPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        {/* Navigation */}
        <div className="mb-8">
          <Link
            href="/army"
            className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-indigo-600"
          >
            <ArrowLeftIcon className="mr-2 h-4 w-4" />
            Back to Army Page
          </Link>
        </div>

        {/* Page Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl">
            Nepal Army Rank Structure
          </h1>
          <p className="mt-6 text-lg leading-8 text-gray-600 max-w-3xl mx-auto">
            Explore the complete hierarchy of ranks in the Nepal Army, from the
            highest commanding officers to the backbone of the military
            structure.
          </p>
        </div>

        {/* Rank Types Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16 max-w-5xl mx-auto">
          {rankTypes.map((rankType) => (
            <Link
              key={rankType.title}
              href={rankType.href}
              className={`group relative overflow-hidden rounded-2xl ${rankType.bgColor} border ${rankType.borderColor} shadow-sm hover:shadow-md transition-all duration-300 flex flex-col h-full`}
            >
              {/* Triangular pointed corner */}
              <div className="absolute top-0 right-0 w-0 h-0 border-t-[60px] border-r-[60px] border-b-0 border-l-0 border-t-white/20 border-r-transparent"></div>

              <div className="p-8 flex flex-col h-full">
                <div className="flex items-center mb-4">
                  <div
                    className={`p-3 rounded-xl ${rankType.iconBg} ${rankType.textColor} mr-4`}
                  >
                    <UserGroupIcon className="h-6 w-6" />
                  </div>
                  <h2 className={`text-xl font-semibold ${rankType.textColor}`}>
                    {rankType.title}
                  </h2>
                </div>

                <p className="text-gray-700 mb-6 flex-grow">
                  {rankType.description}
                </p>

                <div
                  className={`mt-auto flex items-center ${rankType.textColor} font-medium`}
                >
                  <span>Explore Ranks</span>
                  <ArrowRightIcon
                    className={`ml-2 h-5 w-5 transform transition-transform duration-300 group-hover:translate-x-1`}
                  />
                </div>
              </div>
            </Link>
          ))}
        </div>

        {/* Additional Information */}
        <div className="bg-white rounded-xl shadow-md p-8 border border-gray-200 max-w-5xl mx-auto">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">
            Understanding Military Ranks
          </h2>
          <p className="text-gray-600 mb-8 text-lg">
            Military ranks in the Nepal Army follow a hierarchical structure
            that defines authority, responsibility, and command relationships.
            The rank structure is divided into commissioned officers, who
            receive their authority from the President, and non-commissioned
            officers, who rise through the enlisted ranks.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-amber-50 rounded-xl p-6 border border-amber-100">
              <h3 className="text-xl font-semibold text-amber-800 mb-3">
                Commissioned Officers
              </h3>
              <p className="text-amber-700">
                Commissioned officers are the leaders of the military,
                responsible for planning operations, making strategic decisions,
                and commanding units. They typically receive their commission
                after completing officer training at the Nepali Military
                Academy.
              </p>
            </div>
            <div className="bg-emerald-50 rounded-xl p-6 border border-emerald-100">
              <h3 className="text-xl font-semibold text-emerald-800 mb-3">
                Non-Commissioned Officers
              </h3>
              <p className="text-emerald-700">
                Non-commissioned officers (NCOs) form the backbone of the
                military structure. They are responsible for executing the
                orders of commissioned officers, training and mentoring junior
                enlisted personnel, and maintaining discipline and standards.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
