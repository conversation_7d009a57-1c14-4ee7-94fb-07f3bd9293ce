"use client";

import { useState } from "react";
import { AIAnalysisResult } from "@/services/aiService";
import {
  SparklesIcon,
  TrashIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
} from "@heroicons/react/24/outline";
import {
  AIAnalysisDocument,
  deleteAIAnalysis,
} from "@/services/aiAnalysisService";

interface MilitaryPsychologistDisplayProps {
  analysis: AIAnalysisDocument;
  isExpanded?: boolean;
  onDelete?: () => void;
}

export default function MilitaryPsychologistDisplay({
  analysis,
  isExpanded = false,
  onDelete,
}: MilitaryPsychologistDisplayProps) {
  const [expanded, setExpanded] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showRecommendation, setShowRecommendation] = useState(false);
  const result = analysis.analysis;
  const militaryPsychologist = result.militaryPsychologist;

  console.log("MilitaryPsychologistDisplay received analysis:", analysis);
  console.log("militaryPsychologist field:", militaryPsychologist);

  if (!militaryPsychologist) {
    console.error("Military psychologist analysis is missing in the data");
    return (
      <div className="bg-white shadow rounded-lg p-6 mt-4">
        <div className="text-center text-gray-500">
          <p>Military psychologist analysis not available.</p>
          <p className="text-xs mt-2">
            Debug info: Analysis object exists but militaryPsychologist field is
            missing
          </p>
        </div>
      </div>
    );
  }

  const handleDelete = async () => {
    if (!analysis.docId) return;

    setIsDeleting(true);
    try {
      const success = await deleteAIAnalysis(
        analysis.docId,
        analysis.userId,
        analysis.testType,
        analysis.setName,
        analysis.responseTimestamp,
        analysis.aiProvider as "google" | "deepseek"
      );

      if (success && onDelete) {
        onDelete();
      }
    } catch (error) {
      console.error("Error deleting analysis:", error);
    } finally {
      setIsDeleting(false);
      setShowDeleteConfirm(false);
    }
  };

  // Format date
  const formatDate = (timestamp?: number) => {
    if (!timestamp) return "Unknown date";
    return new Date(timestamp).toLocaleString();
  };

  // Get test type display name
  const getTestTypeDisplay = (testType: string) => {
    switch (testType) {
      case "srt":
        return "Situation Reaction Test";
      case "wat":
        return "Word Association Test";
      case "tat":
        return "Thematic Apperception Test";
      case "sdt":
        return "Self Description Test";
      default:
        return testType.toUpperCase();
    }
  };

  // Get situational awareness icon
  const getSituationalAwarenessIcon = () => {
    switch (militaryPsychologist.situationalAwarenessFlag) {
      case "✅":
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case "⚠️":
        return <ExclamationTriangleIcon className="h-5 w-5 text-amber-500" />;
      case "❌":
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <ExclamationTriangleIcon className="h-5 w-5 text-amber-500" />;
    }
  };

  // Get recommendation color
  const getRecommendationColor = () => {
    switch (militaryPsychologist.recommendation) {
      case "Accept":
        return "text-green-700 bg-green-50";
      case "Reinvestigate":
        return "text-amber-700 bg-amber-50";
      case "Reject":
        return "text-red-700 bg-red-50";
      default:
        return "text-gray-700 bg-gray-50";
    }
  };

  return (
    <div className="bg-white shadow rounded-lg p-4 sm:p-6 mt-4">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4 space-y-3 sm:space-y-0">
        <div>
          <div className="flex items-center justify-center sm:justify-start">
            <SparklesIcon className="h-5 w-5 mr-2 text-blue-500" />
            <h2 className="text-lg sm:text-xl font-bold text-gray-900">
              Military Psychologist Analysis
            </h2>
          </div>
          <p className="text-sm text-gray-500 mt-1 text-center sm:text-left">
            Generated on: {formatDate(analysis.responseTimestamp)}
          </p>
        </div>
      </div>

      <div className="space-y-6">
        {/* Simple Screen */}
        <div className="bg-blue-50 rounded-lg p-3 sm:p-4">
          <h3 className="text-lg font-medium text-blue-800 mb-3 text-center sm:text-left">
            Simple Screen
          </h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3 sm:gap-4">
            <div className="bg-white rounded-md p-3 shadow-sm">
              <div className="text-sm font-medium text-gray-500 mb-1 text-center sm:text-left">
                Positivity Score
              </div>
              <div className="text-2xl font-bold text-blue-600 text-center sm:text-left">
                {militaryPsychologist.positivityScore}/10
              </div>
            </div>
            <div className="bg-white rounded-md p-3 shadow-sm">
              <div className="text-sm font-medium text-gray-500 mb-1 text-center sm:text-left">
                Quick Thinking
              </div>
              <div className="text-xl font-bold text-blue-600 text-center sm:text-left">
                {militaryPsychologist.quickThinkingRating}
              </div>
            </div>
            <div className="bg-white rounded-md p-3 shadow-sm">
              <div className="text-sm font-medium text-gray-500 mb-1 text-center sm:text-left">
                Situational Awareness
              </div>
              <div className="flex items-center justify-center sm:justify-start">
                {getSituationalAwarenessIcon()}
                <span className="ml-2 text-sm font-medium">
                  {militaryPsychologist.situationalAwarenessFlag === "✅" &&
                    "Strong"}
                  {militaryPsychologist.situationalAwarenessFlag === "⚠️" &&
                    "Moderate"}
                  {militaryPsychologist.situationalAwarenessFlag === "❌" &&
                    "Needs Improvement"}
                  {!["✅", "⚠️", "❌"].includes(
                    militaryPsychologist.situationalAwarenessFlag
                  ) && "Not Evaluated"}
                </span>
              </div>
            </div>
            <div className="bg-white rounded-md p-3 shadow-sm">
              <div className="text-sm font-medium text-gray-500 mb-1 text-center sm:text-left">
                Top Strength
              </div>
              <div className="text-md font-bold text-green-600 text-center sm:text-left">
                {militaryPsychologist.topStrength}
              </div>
            </div>
            <div className="bg-white rounded-md p-3 shadow-sm">
              <div className="text-sm font-medium text-gray-500 mb-1 text-center sm:text-left">
                Critical Weakness
              </div>
              <div className="text-md font-bold text-red-600 text-center sm:text-left">
                {militaryPsychologist.criticalWeakness}
              </div>
            </div>
          </div>
        </div>

        {/* Detailed Feedback - always show */}
        <div className="bg-gray-50 rounded-lg p-3 sm:p-4">
          <h3 className="text-lg font-medium text-gray-900 mb-3 text-center sm:text-left">
            Detailed Feedback
          </h3>

          {/* Cultural Fit Algorithm - always show */}
          <div className="mb-4">
            <h4 className="text-md font-medium text-gray-800 mb-2 text-center sm:text-left">
              Cultural Fit Algorithm
            </h4>
            <div className="bg-gray-100 rounded-md p-3">
              {militaryPsychologist.culturalFitAlgorithm ? (
                <>
                  <p className="text-sm text-gray-700 mb-2 font-medium text-center sm:text-left">
                    Purpose:{" "}
                    {militaryPsychologist.culturalFitAlgorithm.purpose ||
                      "Predict team cohesion likelihood"}
                  </p>

                  {/* Collective Language Score */}
                  <div className="mb-3">
                    <h5 className="text-sm font-medium text-gray-700 mb-1 text-center sm:text-left">
                      1. Collective Language Score
                    </h5>
                    <ul className="space-y-1 pl-5 text-sm text-gray-700 list-disc">
                      {militaryPsychologist.culturalFitAlgorithm
                        .collectiveLanguageScore?.weUsage && (
                        <li>
                          <span className="font-medium">We Usage:</span>{" "}
                          {
                            militaryPsychologist.culturalFitAlgorithm
                              .collectiveLanguageScore.weUsage
                          }
                        </li>
                      )}
                      {militaryPsychologist.culturalFitAlgorithm
                        .collectiveLanguageScore?.militaryJargonMatch && (
                        <li>
                          <span className="font-medium">
                            Military Jargon Match:
                          </span>{" "}
                          {
                            militaryPsychologist.culturalFitAlgorithm
                              .collectiveLanguageScore.militaryJargonMatch
                          }
                        </li>
                      )}
                      {militaryPsychologist.culturalFitAlgorithm
                        .collectiveLanguageScore?.chainOfCommandMentions && (
                        <li>
                          <span className="font-medium">
                            Chain-of-Command Mentions:
                          </span>{" "}
                          {
                            militaryPsychologist.culturalFitAlgorithm
                              .collectiveLanguageScore.chainOfCommandMentions
                          }
                        </li>
                      )}
                    </ul>
                  </div>

                  {/* Cultural Compatibility Check */}
                  <div className="mb-3">
                    <h5 className="text-sm font-medium text-gray-700 mb-1 text-center sm:text-left">
                      2. Cultural Compatibility Check
                    </h5>
                    <ul className="space-y-1 pl-5 text-sm text-gray-700 list-disc">
                      {militaryPsychologist.culturalFitAlgorithm
                        .culturalCompatibility?.sensitivityFlags !==
                        undefined && (
                        <li>
                          <span className="font-medium">
                            Sensitivity Flags:
                          </span>{" "}
                          {
                            militaryPsychologist.culturalFitAlgorithm
                              .culturalCompatibility.sensitivityFlags
                          }{" "}
                          (Triggers: racial/gender/religion terms)
                        </li>
                      )}
                      {militaryPsychologist.culturalFitAlgorithm
                        .culturalCompatibility?.humorStyle && (
                        <li>
                          <span className="font-medium">Humor Style:</span>{" "}
                          {
                            militaryPsychologist.culturalFitAlgorithm
                              .culturalCompatibility.humorStyle
                          }
                        </li>
                      )}
                      {militaryPsychologist.culturalFitAlgorithm
                        .culturalCompatibility?.sharedValueAlignment && (
                        <li>
                          <span className="font-medium">
                            Shared Value Alignment:
                          </span>{" "}
                          {
                            militaryPsychologist.culturalFitAlgorithm
                              .culturalCompatibility.sharedValueAlignment
                          }
                        </li>
                      )}
                    </ul>
                  </div>

                  {/* Authority Response Pattern */}
                  <div className="mb-3">
                    <h5 className="text-sm font-medium text-gray-700 mb-1 text-center sm:text-left">
                      3. Authority Response Pattern
                    </h5>
                    <ul className="space-y-1 pl-5 text-sm text-gray-700 list-disc">
                      {militaryPsychologist.culturalFitAlgorithm
                        .authorityResponsePattern
                        ?.orderChallengeProbability && (
                        <li>
                          <span className="font-medium">
                            Order Challenge Probability:
                          </span>{" "}
                          {
                            militaryPsychologist.culturalFitAlgorithm
                              .authorityResponsePattern
                              .orderChallengeProbability
                          }
                        </li>
                      )}
                      {militaryPsychologist.culturalFitAlgorithm
                        .authorityResponsePattern?.leadershipSeek && (
                        <li>
                          <span className="font-medium">Leadership Seek:</span>{" "}
                          {
                            militaryPsychologist.culturalFitAlgorithm
                              .authorityResponsePattern.leadershipSeek
                          }
                        </li>
                      )}
                    </ul>
                  </div>

                  {/* Risk Profile */}
                  <div className="mt-4 p-2 bg-amber-50 rounded-md">
                    <div className="flex items-start">
                      <span className="inline-flex items-center justify-center h-5 w-5 rounded-full bg-amber-100 text-amber-800 font-bold text-xs mr-2 mt-0.5">
                        !
                      </span>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-700 text-center sm:text-left">
                          Risk Profile:
                        </p>
                        <p className="text-sm text-red-600 text-center sm:text-left">
                          {
                            militaryPsychologist.culturalFitAlgorithm
                              .riskProfile
                          }
                        </p>
                        {militaryPsychologist.culturalFitAlgorithm
                          .recommendedAction && (
                          <p className="text-sm text-gray-700 mt-1 text-center sm:text-left">
                            <span className="font-medium">Requires:</span>{" "}
                            {
                              militaryPsychologist.culturalFitAlgorithm
                                .recommendedAction
                            }
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                </>
              ) : (
                <div className="text-center py-4">
                  <p className="text-sm text-gray-700">
                    Cultural Fit Algorithm data is not available for this
                    analysis.
                  </p>
                  <p className="text-xs text-gray-500 mt-2">
                    This feature is available for new analyses generated after
                    the latest update.
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Psychological Analysis - show if expanded */}
          {expanded && (
            <div className="mb-4">
              <h4 className="text-md font-medium text-gray-800 mb-2 text-center sm:text-left">
                Psychological Analysis
              </h4>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="bg-white rounded-md p-3 shadow-sm">
                  <div className="text-sm font-medium text-gray-700 mb-1 text-center sm:text-left">
                    Positivity
                  </div>
                  <div className="text-sm text-gray-600 text-center sm:text-left">
                    {militaryPsychologist.positivityAnalysis}
                  </div>
                </div>
                <div className="bg-white rounded-md p-3 shadow-sm">
                  <div className="text-sm font-medium text-gray-700 mb-1 text-center sm:text-left">
                    Sensible Logic
                  </div>
                  <div className="text-sm text-gray-600 text-center sm:text-left">
                    {militaryPsychologist.sensibleLogicAnalysis}
                  </div>
                </div>
                <div className="bg-white rounded-md p-3 shadow-sm">
                  <div className="text-sm font-medium text-gray-700 mb-1 text-center sm:text-left">
                    Quick Thinking
                  </div>
                  <div className="text-sm text-gray-600 text-center sm:text-left">
                    {militaryPsychologist.quickThinkingAnalysis}
                  </div>
                </div>
                <div className="bg-white rounded-md p-3 shadow-sm">
                  <div className="text-sm font-medium text-gray-700 mb-1 text-center sm:text-left">
                    Situational Awareness
                  </div>
                  <div className="text-sm text-gray-600 text-center sm:text-left">
                    {militaryPsychologist.situationalAwarenessAnalysis}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Immersive Simulation - show if expanded */}
          {expanded && (
            <div className="mb-4">
              <h4 className="text-md font-medium text-gray-800 mb-2 text-center sm:text-left">
                Immersive Simulation
              </h4>
              <div className="bg-blue-50 rounded-md p-3">
                <p className="text-sm text-gray-700 text-center sm:text-left">
                  If this candidate faced{" "}
                  <span className="font-medium">
                    {militaryPsychologist.simulationScenario}
                  </span>
                  , they would likely{" "}
                  <span className="font-medium">
                    {militaryPsychologist.simulationPrediction}
                  </span>{" "}
                  because
                  <span className="font-medium">
                    {" "}
                    {militaryPsychologist.simulationReasoning}
                  </span>
                  .
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Advanced Analysis Toggle Button */}
        <div className="mt-6 border-t pt-4">
          <button
            onClick={() => setExpanded(!expanded)}
            className="w-full sm:w-auto mx-auto flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            {expanded ? (
              <>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 mr-2"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z"
                    clipRule="evenodd"
                  />
                </svg>
                Hide Advanced Analysis
              </>
            ) : (
              <>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 mr-2"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                    clipRule="evenodd"
                  />
                </svg>
                Show Advanced Analysis
              </>
            )}
          </button>
          {!expanded && (
            <p className="text-xs text-gray-500 mt-2 text-center">
              Click to view detailed psychological analysis and immersive
              simulation
            </p>
          )}
        </div>

        {/* Check Recommendation Button */}
        {!showRecommendation ? (
          <div className="flex justify-center">
            <button
              onClick={() => setShowRecommendation(true)}
              className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 w-full sm:w-auto"
            >
              Check if you are recommended
            </button>
          </div>
        ) : (
          /* Final Recommendation */
          <div className={`rounded-lg p-4 ${getRecommendationColor()}`}>
            <h3 className="text-lg font-medium mb-2 text-center sm:text-left">
              Final Recommendation
            </h3>
            <div className="text-xl font-bold mb-2 text-center sm:text-left">
              {militaryPsychologist.recommendation}
            </div>
            <div className="text-sm">
              <ul className="list-disc pl-5 space-y-1 text-left">
                {militaryPsychologist.recommendationReasons.map(
                  (reason, index) => (
                    <li key={index}>{reason}</li>
                  )
                )}
              </ul>
            </div>
          </div>
        )}

        {/* AI Generated Notice */}
        <div className="text-xs text-gray-500 mt-4 flex items-start sm:items-center">
          <SparklesIcon className="h-3 w-3 mr-1 mt-0.5 sm:mt-0 flex-shrink-0" />
          <span className="text-center sm:text-left">
            {result.isAIGenerated
              ? "This analysis was generated using AI technology. Although we try our best to make this app accurate, we are using AI for analyzing the response so sometimes the information might be inaccurate."
              : "This is a basic analysis. AI analysis feature is currently not available. Although we try our best to make this app accurate, we are using AI for analyzing the response so sometimes the information might be inaccurate."}
          </span>
        </div>
      </div>
    </div>
  );
}
