"use client";

import { useState, useEffect, useRef } from "react";
import {
  MicrophoneIcon,
  PlayIcon,
  StopIcon,
  ArrowRightIcon,
  ArrowLeftIcon,
  CheckCircleIcon,
  XCircleIcon,
  QuestionMarkCircleIcon,
  TrophyIcon,
  ClockIcon,
  UserIcon,
  UserGroupIcon,
  ChevronRightIcon,
  ChevronDownIcon,
  ChatBubbleLeftRightIcon,
} from "@heroicons/react/24/outline";
import { questionCategories } from "./questionData";
import AudioRecordingManager from "@/components/AudioRecordingManager";

// Recording interface
interface Recording {
  url: string;
  duration: number;
  blob: Blob;
}

// Question Group interface for rapid fire questions
interface QuestionGroup {
  name: string;
  questions: string[];
}

export default function IOPage() {
  // Practice state
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [isRecording, setIsRecording] = useState(false);
  const [recordings, setRecordings] = useState<Record<string, Recording>>({});
  const [isPracticeStarted, setIsPracticeStarted] = useState(false);
  const [isPracticeComplete, setIsPracticeComplete] = useState(false);
  const [audioStream, setAudioStream] = useState<MediaStream | null>(null);
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(
    null
  );

  // Define an interface for audio players (both HTML Audio and Speech)
  interface AudioPlayer {
    pause: () => void;
  }

  // Rapid fire state
  const [selectedGroupIndex, setSelectedGroupIndex] = useState<number | null>(
    null
  );
  const [expandedGroup, setExpandedGroup] = useState<number | null>(null);
  const [isPlayingQuestions, setIsPlayingQuestions] = useState(false);
  const [questionAudioPlayer, setQuestionAudioPlayer] =
    useState<AudioPlayer | null>(null);

  // Rapid fire test state
  const [testStarted, setTestStarted] = useState(false);
  const [testCompleted, setTestCompleted] = useState(false);

  // Recording time state
  const [recordingTime, setRecordingTime] = useState(0);
  const recordingTimerRef = useRef<NodeJS.Timeout | null>(null);
  const currentRecordingTimeRef = useRef(0);
  const audioChunksRef = useRef<BlobPart[]>([]);

  // Playback state
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentPlayingKey, setCurrentPlayingKey] = useState<string | null>(
    null
  );
  const audioPlayerRef = useRef<HTMLAudioElement | null>(null);

  // This function has been removed as we no longer need the timer

  // Clean up resources when component unmounts
  useEffect(() => {
    return () => {
      // Stop any recording
      if (mediaRecorder && mediaRecorder.state !== "inactive") {
        mediaRecorder.stop();
      }

      // Stop any audio stream
      if (audioStream) {
        audioStream.getTracks().forEach((track) => track.stop());
      }

      // Clear any timers
      if (recordingTimerRef.current) {
        clearInterval(recordingTimerRef.current);
      }

      // Stop any audio playback
      if (audioPlayerRef.current) {
        audioPlayerRef.current.pause();
        audioPlayerRef.current = null;
      }

      // Stop any question audio playback
      if (questionAudioPlayer) {
        questionAudioPlayer.pause();
      }
    };
  }, [mediaRecorder, audioStream, questionAudioPlayer]);

  // Handle recording timer
  useEffect(() => {
    if (isRecording) {
      // Reset timer
      setRecordingTime(0);
      currentRecordingTimeRef.current = 0;

      // Start timer
      recordingTimerRef.current = setInterval(() => {
        setRecordingTime((prevTime) => {
          const newTime = prevTime + 1;
          currentRecordingTimeRef.current = newTime;
          return newTime;
        });
      }, 1000);
    } else {
      // Clear timer
      if (recordingTimerRef.current) {
        clearInterval(recordingTimerRef.current);
        recordingTimerRef.current = null;
      }
    }

    return () => {
      if (recordingTimerRef.current) {
        clearInterval(recordingTimerRef.current);
      }
    };
  }, [isRecording]);

  // Function to start recording
  const startRecording = async () => {
    try {
      console.log("Starting recording...");

      // Stop any playing question audio first
      if (isPlayingQuestions || questionAudioPlayer) {
        stopQuestionPlayback();
        console.log("Stopped question audio before recording");
      }

      // Reset the audio chunks array
      audioChunksRef.current = [];

      // Get microphone access
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      setAudioStream(stream);
      console.log("Audio stream obtained");

      // Create a new MediaRecorder with the stream
      const recorder = new MediaRecorder(stream, { mimeType: "audio/webm" });

      // Set up event handlers before starting the recorder
      recorder.ondataavailable = (e) => {
        console.log("Data available from recorder", e.data.size);
        if (e.data.size > 0) {
          audioChunksRef.current.push(e.data);
        }
      };

      // Store the recorder in state
      setMediaRecorder(recorder);
      console.log("MediaRecorder created");

      recorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, {
          type: "audio/webm",
        });
        const audioUrl = URL.createObjectURL(audioBlob);

        // Save recording for current question with the final recording time
        const finalDuration = currentRecordingTimeRef.current;

        // For rapid fire questions, use a different key format to distinguish from normal questions
        let recordingKey;
        if (
          (selectedCategory === "rapid-fire" ||
            selectedCategory === "most-asked") &&
          selectedGroupIndex !== null
        ) {
          // Use a unique key for each question in the rapid fire group
          recordingKey = `${selectedCategory}-group-${selectedGroupIndex}-question-${currentQuestionIndex}`;
        } else {
          // For normal questions
          recordingKey = `${selectedCategory}-${currentQuestionIndex}`;
        }

        console.log("Saving recording with key:", recordingKey);

        setRecordings((prev) => ({
          ...prev,
          [recordingKey]: {
            url: audioUrl,
            duration: finalDuration,
            blob: audioBlob,
          },
        }));
      };

      // Start the recorder with a timeslice to get data periodically
      // This ensures we get data even if stop() is called before the recording naturally ends
      recorder.start(1000); // Get data every second
      console.log("MediaRecorder started");

      // Set recording state to true - the useEffect will handle the timer
      setIsRecording(true);
      console.log("Recording state set to true");
    } catch (error) {
      console.error("Error accessing microphone:", error);
      alert("Could not access your microphone. Please check your permissions.");
    }
  };

  // Function to stop recording
  const stopRecording = () => {
    console.log("Stopping recording...");

    // Make sure we have a valid mediaRecorder before stopping
    if (mediaRecorder && mediaRecorder.state !== "inactive") {
      try {
        // Stop the media recorder - this will trigger the onstop event
        mediaRecorder.stop();
        console.log("MediaRecorder stopped");
      } catch (error) {
        console.error("Error stopping MediaRecorder:", error);
      }
    } else {
      console.warn("MediaRecorder not available or already inactive");
    }

    // Stop all audio tracks
    if (audioStream) {
      try {
        audioStream.getTracks().forEach((track) => {
          track.stop();
          console.log("Audio track stopped");
        });
        setAudioStream(null);
      } catch (error) {
        console.error("Error stopping audio tracks:", error);
      }
    }

    // Update state
    setIsRecording(false);
    setMediaRecorder(null);

    // Don't automatically go to the next question or submit
    // Let the user click the Next button when they're ready
    console.log("Recording stopped and state updated");
  };

  // Function to play recording
  const playRecording = (recordingKey: string) => {
    // If already playing this recording, stop it
    if (isPlaying && currentPlayingKey === recordingKey) {
      stopPlayback();
      return;
    }

    // If another recording is playing, stop it first
    if (isPlaying && audioPlayerRef.current) {
      stopPlayback();
    }

    // Create and play the new audio
    const audio = new Audio(recordings[recordingKey]?.url);
    audioPlayerRef.current = audio;

    // Set up event listeners
    audio.addEventListener("ended", () => {
      setIsPlaying(false);
      setCurrentPlayingKey(null);
      audioPlayerRef.current = null;
    });

    audio.play().catch((error) => {
      console.error("Error playing audio:", error);
      setIsPlaying(false);
      setCurrentPlayingKey(null);
      audioPlayerRef.current = null;
    });

    setIsPlaying(true);
    setCurrentPlayingKey(recordingKey);
  };

  // Function to stop playback
  const stopPlayback = () => {
    if (audioPlayerRef.current) {
      audioPlayerRef.current.pause();
      audioPlayerRef.current = null;
    }
    setIsPlaying(false);
    setCurrentPlayingKey(null);
  };

  // Function to format time (seconds to MM:SS)
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  };

  // Function to handle category selection
  const selectCategory = (categoryId: string) => {
    // Scroll to top of the page before starting the test
    window.scrollTo({ top: 0, behavior: "smooth" });

    setSelectedCategory(categoryId);
    setCurrentQuestionIndex(0);
    setIsPracticeStarted(true);

    // Reset any previous practice state
    setIsPracticeComplete(false);
    setRecordings({});
  };

  // Function to go to next question
  const goToNextQuestion = () => {
    // Stop and save any ongoing recording first
    if (isRecording) {
      stopRecording();
    }

    // Stop any playing audio
    if (isPlaying) {
      stopPlayback();
    }

    const category = questionCategories.find((c) => c.id === selectedCategory);
    if (!category) return;

    // Check if it's a normal category with questions array
    if ("questions" in category && Array.isArray(category.questions)) {
      // If we're at the last question, complete the practice
      if (currentQuestionIndex >= category.questions.length - 1) {
        setIsPracticeComplete(true);
      } else {
        // Otherwise, go to the next question
        setCurrentQuestionIndex(currentQuestionIndex + 1);
      }
    }
  };

  // Function to reset state
  const resetState = () => {
    setIsPracticeStarted(false);
    setSelectedCategory(null);
    setRecordings({});
    setIsPracticeComplete(false);
    setCurrentQuestionIndex(0);
    setSelectedGroupIndex(null);
    setExpandedGroup(null);
    stopQuestionPlayback();
  };

  // Function to stop question playback
  const stopQuestionPlayback = () => {
    if (questionAudioPlayer) {
      questionAudioPlayer.pause();
      setQuestionAudioPlayer(null);
      console.log("Question audio player stopped");
    }

    // Always cancel any speech synthesis that might be playing
    if (window.speechSynthesis) {
      window.speechSynthesis.cancel();
      console.log("Speech synthesis canceled");
    }

    setIsPlayingQuestions(false);
  };

  // Determine what content to render based on the current state
  const renderContent = () => {
    if (!isPracticeStarted) {
      return renderMainPage();
    }

    if (isPracticeComplete) {
      return renderResultsPage();
    }

    return renderPracticePage();
  };

  // Main page content
  const renderMainPage = () => {
    return (
      <>
        <div className="mx-auto max-w-3xl text-center">
          <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
            Interviewing Officer (IO) Practice
          </h2>
          <p className="mt-6 text-lg leading-8 text-gray-600 max-w-3xl mx-auto">
            The IO test involves two officers: one who asks questions and
            another who analyzes your responses. The interview begins with basic
            questions about your background, followed by a rapid-fire section
            where you&apos;ll need to answer multiple questions in sequence.
          </p>
          <div className="mt-6 flex justify-center">
            <div className="inline-flex items-center rounded-md bg-indigo-50 px-6 py-4 text-lg font-medium text-indigo-700 ring-1 ring-inset ring-indigo-700/10">
              <MicrophoneIcon className="h-6 w-6 mr-3 text-indigo-500" />
              <span>
                This is a speaking test with audio recording to help you
                practice your responses
              </span>
            </div>
          </div>

          {/* Practice Button */}
          <div className="mt-8">
            <button
              onClick={() => {
                document.getElementById("question-categories")?.scrollIntoView({
                  behavior: "smooth",
                  block: "start",
                });
              }}
              className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 shadow-sm"
            >
              <PlayIcon className="h-5 w-5 mr-2" />
              Start Practice Session
            </button>
            <p className="mt-2 text-sm text-gray-500">
              Select a question category below to practice with voice recording
            </p>
          </div>
        </div>

        <div id="question-categories" className="mx-auto mt-16 max-w-5xl">
          <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">
            Question Categories
          </h3>

          <div className="mb-8 bg-indigo-50 rounded-xl border border-indigo-200 p-6">
            <p className="text-center text-gray-700">
              The Interviewing Officer test evaluates your personality,
              motivation, and suitability for a military career. Practice
              answering questions across various categories to improve your
              communication skills and prepare for your interview.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="rounded-xl border-2 p-6 bg-orange-50 border-orange-200 transition-transform hover:scale-105">
              <div className="flex items-center space-x-3 mb-4">
                <div className="p-2 rounded-full bg-white">
                  <ChatBubbleLeftRightIcon className="h-8 w-8 text-orange-600" />
                </div>
                <h3 className="text-xl font-bold leading-8 text-gray-900">
                  50 Most Asked Questions
                </h3>
              </div>
              <p className="text-sm leading-6 text-gray-700 mb-4 min-h-[60px]">
                The most common questions asked in military interviews, grouped
                by topic and delivered in the same format as CIQs
              </p>
              <div className="flex justify-between items-center">
                <span className="text-lg font-semibold">6 Groups</span>
                <button
                  onClick={() => {
                    // Find the most-asked questions category
                    const mostAskedCategory = questionCategories.find(
                      (cat) => cat.id === "most-asked"
                    );

                    if (mostAskedCategory) {
                      // Select the category with the questions
                      selectCategory("most-asked");
                    }
                  }}
                  className="inline-flex items-center rounded-md bg-orange-600 hover:bg-orange-500 px-4 py-2 text-sm font-semibold text-white shadow-sm focus-visible:outline-2 focus-visible:outline-offset-2"
                >
                  Practice
                </button>
              </div>
            </div>

            <div className="rounded-xl border-2 p-6 bg-purple-50 border-purple-200 transition-transform hover:scale-105">
              <div className="flex items-center space-x-3 mb-4">
                <div className="p-2 rounded-full bg-white">
                  <TrophyIcon className="h-8 w-8 text-purple-600" />
                </div>
                <h3 className="text-xl font-bold leading-8 text-gray-900">
                  Sample Answers
                </h3>
              </div>
              <p className="text-sm leading-6 text-gray-700 mb-4 min-h-[60px]">
                Model answers for common interview questions to help you prepare
                effective responses
              </p>
              <div className="flex justify-between items-center">
                <span className="text-lg font-semibold">20 Questions</span>
                <button
                  onClick={() => {
                    // Find the sample answers category
                    const sampleAnswersCategory = questionCategories.find(
                      (cat) => cat.id === "sample-answers"
                    );

                    if (sampleAnswersCategory) {
                      // Select the category with the questions
                      selectCategory("sample-answers");
                    }
                  }}
                  className="inline-flex items-center rounded-md bg-purple-600 hover:bg-purple-500 px-4 py-2 text-sm font-semibold text-white shadow-sm focus-visible:outline-2 focus-visible:outline-offset-2"
                >
                  View Answers
                </button>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
            <div className="rounded-xl border-2 p-6 bg-blue-50 border-blue-200 transition-transform hover:scale-105">
              <div className="flex items-center space-x-3 mb-4">
                <div className="p-2 rounded-full bg-white">
                  <UserIcon className="h-8 w-8 text-blue-600" />
                </div>
                <h3 className="text-xl font-bold leading-8 text-gray-900">
                  Normal questions
                </h3>
              </div>
              <p className="text-sm leading-6 text-gray-700 mb-4 min-h-[60px]">
                Standard interview questions about your background, motivation,
                and suitability for the Nepal Army
              </p>
              <div className="flex justify-between items-center">
                <span className="text-lg font-semibold">30 Questions</span>
                <button
                  onClick={() => {
                    // Find the normal questions category
                    const normalCategory = questionCategories.find(
                      (cat) => cat.id === "normal"
                    );

                    if (normalCategory) {
                      // Select the category with the questions
                      selectCategory("normal");
                    }
                  }}
                  className="inline-flex items-center rounded-md bg-blue-600 hover:bg-blue-500 px-4 py-2 text-sm font-semibold text-white shadow-sm focus-visible:outline-2 focus-visible:outline-offset-2"
                >
                  Practice
                </button>
              </div>
            </div>

            <div className="rounded-xl border-2 p-6 bg-yellow-50 border-yellow-200 transition-transform hover:scale-105">
              <div className="flex items-center space-x-3 mb-4">
                <div className="p-2 rounded-full bg-white">
                  <QuestionMarkCircleIcon className="h-8 w-8 text-yellow-600" />
                </div>
                <h3 className="text-xl font-bold leading-8 text-gray-900">
                  Command Information Questions (CIQs)
                </h3>
              </div>
              <p className="text-sm leading-6 text-gray-700 mb-4 min-h-[60px]">
                Command Information Questions (CIQs) are series of related
                questions asked in succession. Each CIQ group focuses on a
                specific aspect of your personality and background.
              </p>
              <div className="flex justify-between items-center">
                <span className="text-lg font-semibold">6 Groups</span>
                <button
                  onClick={() => {
                    // Find the rapid-fire questions category
                    const rapidFireCategory = questionCategories.find(
                      (cat) => cat.id === "rapid-fire"
                    );

                    if (rapidFireCategory) {
                      // Select the category with the questions
                      selectCategory("rapid-fire");
                    }
                  }}
                  className="inline-flex items-center rounded-md bg-yellow-600 hover:bg-yellow-500 px-4 py-2 text-sm font-semibold text-white shadow-sm focus-visible:outline-2 focus-visible:outline-offset-2"
                >
                  Practice
                </button>
              </div>
            </div>
          </div>

          <div className="mt-16 grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="p-6 bg-gray-50 rounded-xl border border-gray-200">
              <div className="flex items-center space-x-3 mb-4">
                <ClockIcon className="h-6 w-6 text-indigo-600" />
                <h3 className="text-lg font-semibold text-gray-900">
                  Interview Format
                </h3>
              </div>
              <p className="text-gray-700">
                The IO interview typically involves two officers: one who asks
                questions and another who analyzes your responses. The interview
                begins with basic questions about your background, followed by a
                rapid-fire section where you&apos;ll need to answer 7-8
                questions in sequence without pausing.
              </p>
            </div>

            <div className="p-6 bg-yellow-50 rounded-xl border border-yellow-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <QuestionMarkCircleIcon className="h-6 w-6 text-yellow-600 mr-3" />
                Preparation Tips
              </h3>
              <ul className="list-disc pl-5 space-y-2 text-gray-700">
                <li>
                  Research the Nepal Army&apos;s technical divisions and current
                  initiatives
                </li>
                <li>
                  Practice articulating your experiences with the STAR method
                  (Situation, Task, Action, Result)
                </li>
                <li>
                  Prepare specific examples that demonstrate your technical
                  skills and leadership potential
                </li>
                <li>
                  For rapid-fire questions, practice giving concise but complete
                  answers
                </li>
              </ul>
            </div>
          </div>
        </div>
      </>
    );
  };

  // Practice page content
  const renderPracticePage = () => {
    const category = questionCategories.find((c) => c.id === selectedCategory);
    if (!category) return <div>Category not found</div>;

    // Handle different category types
    if (category.id === "rapid-fire" || category.id === "most-asked") {
      return renderRapidFirePracticePage(category as RapidFireCategory);
    } else {
      return renderNormalPracticePage(category as NormalCategory);
    }
  };

  // Define TypeScript interfaces for category types
  interface CategoryBase {
    id: string;
    name: string;
    icon: React.ElementType;
    buttonColor: string;
  }

  interface NormalCategory extends CategoryBase {
    questions: string[];
    sampleAnswers?: string[];
  }

  interface RapidFireCategory extends CategoryBase {
    questionGroups: QuestionGroup[];
  }

  // Normal questions practice page
  const renderNormalPracticePage = (category: NormalCategory) => {
    const currentQuestion = category.questions[currentQuestionIndex];
    const recordingKey = `${selectedCategory}-${currentQuestionIndex}`;
    const hasRecording = !!recordings[recordingKey];

    return (
      <div className="mx-auto max-w-3xl">
        {/* Header */}
        <div className="mb-8 flex justify-between items-center">
          <button
            onClick={() => {
              // Confirm before exiting if there are recordings
              if (Object.keys(recordings).length > 0) {
                if (
                  window.confirm(
                    "Are you sure you want to exit? Your recordings will be lost."
                  )
                ) {
                  resetState();
                }
              } else {
                resetState();
              }
            }}
            className="inline-flex items-center text-gray-600 hover:text-gray-900"
          >
            <ArrowLeftIcon className="h-5 w-5 mr-2" />
            Back to Categories
          </button>
          <div className="text-sm font-medium text-gray-500">
            Question {currentQuestionIndex + 1} of {category.questions.length}
          </div>
        </div>

        {/* Two Officers Layout */}
        <div className="mb-6 bg-indigo-50 rounded-xl p-4 border border-indigo-200">
          <h2 className="text-lg font-semibold text-indigo-800 mb-2">
            Interviewing Officers
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-white rounded-lg p-4 border border-indigo-100 flex items-center">
              <div className="p-2 rounded-full bg-blue-100 mr-3">
                <UserIcon className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <h3 className="font-medium text-gray-900">
                  Questioning Officer
                </h3>
                <p className="text-sm text-gray-600">
                  Asks questions to assess your background and suitability
                </p>
              </div>
            </div>
            <div className="bg-white rounded-lg p-4 border border-indigo-100 flex items-center">
              <div className="p-2 rounded-full bg-purple-100 mr-3">
                <UserGroupIcon className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <h3 className="font-medium text-gray-900">Analyzing Officer</h3>
                <p className="text-sm text-gray-600">
                  Observes your responses and evaluates your potential
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Category Title */}
        <div className="mb-6 flex items-center space-x-3">
          <div className="p-2 rounded-full bg-indigo-100">
            <category.icon className="h-6 w-6 text-indigo-600" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900">{category.name}</h2>
        </div>

        {/* Question Card */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
          <h3 className="text-xl font-semibold text-gray-900 mb-4">
            {currentQuestion}
          </h3>

          {/* Sample Answer Section (only for sample-answers category) */}
          {category.id === "sample-answers" && category.sampleAnswers && (
            <div className="mt-6 mb-6">
              <div className="bg-purple-50 rounded-lg p-4 border border-purple-200">
                <h4 className="font-medium text-purple-800 mb-2">
                  Sample Answer:
                </h4>
                <p className="text-gray-700">
                  {category.sampleAnswers[currentQuestionIndex]}
                </p>
              </div>
            </div>
          )}

          {/* Recording Controls (only for non-sample-answers categories) */}
          {category.id !== "sample-answers" && (
            <div className="mt-8">
              {!isRecording && !hasRecording && (
                <button
                  onClick={startRecording}
                  className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
                >
                  <MicrophoneIcon className="h-5 w-5 mr-2" />
                  Start Recording
                </button>
              )}

              {isRecording && (
                <div className="flex flex-col items-center space-y-4">
                  <div className="text-lg font-semibold text-red-600 animate-pulse">
                    Recording... {formatTime(recordingTime)}
                  </div>
                  <button
                    onClick={goToNextQuestion}
                    className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
                  >
                    <ArrowRightIcon className="h-5 w-5 mr-2" />
                    Next Question
                  </button>
                </div>
              )}

              {hasRecording && !isRecording && (
                <div className="flex flex-col items-center space-y-4">
                  <div className="text-sm text-gray-500">
                    Recording: {formatTime(recordings[recordingKey].duration)}
                  </div>
                  <div className="flex space-x-4">
                    <button
                      onClick={() => playRecording(recordingKey)}
                      className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
                    >
                      {isPlaying && currentPlayingKey === recordingKey ? (
                        <>
                          <StopIcon className="h-5 w-5 mr-2" />
                          Stop
                        </>
                      ) : (
                        <>
                          <PlayIcon className="h-5 w-5 mr-2" />
                          Play
                        </>
                      )}
                    </button>
                    <button
                      onClick={() => {
                        // Remove the recording
                        setRecordings((prev) => {
                          const newRecordings = { ...prev };
                          delete newRecordings[recordingKey];
                          return newRecordings;
                        });
                      }}
                      className="inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                    >
                      <XCircleIcon className="h-5 w-5 mr-2" />
                      Delete
                    </button>
                    <button
                      onClick={startRecording}
                      className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
                    >
                      <MicrophoneIcon className="h-5 w-5 mr-2" />
                      Re-record
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Navigation Buttons */}
        <div className="flex justify-end">
          {(!isRecording || category.id === "sample-answers") && (
            <button
              onClick={goToNextQuestion}
              className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white ${
                category.id === "sample-answers"
                  ? "bg-purple-600 hover:bg-purple-700"
                  : "bg-indigo-600 hover:bg-indigo-700"
              }`}
            >
              {currentQuestionIndex === category.questions.length - 1
                ? "Finish"
                : "Next Question"}
              <ArrowRightIcon className="h-5 w-5 ml-2" />
            </button>
          )}
        </div>
      </div>
    );
  };

  // Rapid fire questions practice page
  const renderRapidFirePracticePage = (category: RapidFireCategory) => {
    // If no group is selected yet, show the group selection screen
    if (selectedGroupIndex === null) {
      // Determine if this is the "50 Most Asked Questions" category
      const isMostAskedCategory = category.id === "most-asked";

      // Set colors based on category
      const bgColor = isMostAskedCategory ? "bg-orange-50" : "bg-yellow-50";
      const borderColor = isMostAskedCategory
        ? "border-orange-200"
        : "border-yellow-200";
      const iconBgColor = isMostAskedCategory
        ? "bg-orange-100"
        : "bg-yellow-100";
      const iconTextColor = isMostAskedCategory
        ? "text-orange-600"
        : "text-yellow-600";
      const buttonBgColor = isMostAskedCategory
        ? "bg-orange-600 hover:bg-orange-500"
        : "bg-yellow-600 hover:bg-yellow-500";

      return (
        <div className="mx-auto max-w-3xl">
          {/* Header */}
          <div className="mb-8 flex justify-between items-center">
            <button
              onClick={() => {
                setIsPracticeStarted(false);
                setSelectedCategory(null);
              }}
              className="inline-flex items-center text-gray-600 hover:text-gray-900"
            >
              <ArrowLeftIcon className="h-5 w-5 mr-2" />
              Back to Categories
            </button>
            <div className="text-sm font-medium text-gray-500">
              Select a question group
            </div>
          </div>

          {/* Two Officers Layout */}
          <div className="mb-6 bg-indigo-50 rounded-xl p-4 border border-indigo-200">
            <h2 className="text-lg font-semibold text-indigo-800 mb-2">
              Interviewing Officers
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-white rounded-lg p-4 border border-indigo-100 flex items-center">
                <div className="p-2 rounded-full bg-blue-100 mr-3">
                  <UserIcon className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">
                    Questioning Officer
                  </h3>
                  <p className="text-sm text-gray-600">
                    Asks questions to assess your background and suitability
                  </p>
                </div>
              </div>
              <div className="bg-white rounded-lg p-4 border border-indigo-100 flex items-center">
                <div className="p-2 rounded-full bg-purple-100 mr-3">
                  <UserGroupIcon className="h-6 w-6 text-purple-600" />
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">
                    Analyzing Officer
                  </h3>
                  <p className="text-sm text-gray-600">
                    Observes your responses and evaluates your potential
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Category Title */}
          <div className="mb-6 flex items-center space-x-3">
            <div className={`p-2 rounded-full ${iconBgColor}`}>
              <category.icon className={`h-6 w-6 ${iconTextColor}`} />
            </div>
            <h2 className="text-2xl font-bold text-gray-900">
              {category.name}
            </h2>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
            <h3 className="text-xl font-semibold text-gray-900 mb-6">
              {isMostAskedCategory
                ? "Frequently Asked Questions (FAQ) Instructions"
                : "Command Information Questions (CIQ) Instructions"}
            </h3>
            <div
              className={`${bgColor} border ${borderColor} rounded-lg p-4 mb-6`}
            >
              <h4
                className={`font-medium ${
                  isMostAskedCategory ? "text-orange-800" : "text-yellow-800"
                } mb-2 flex items-center`}
              >
                <QuestionMarkCircleIcon
                  className={`h-5 w-5 mr-2 ${iconTextColor}`}
                />
                {isMostAskedCategory
                  ? "How the FAQ Test Works:"
                  : "How the CIQ Test Works:"}
              </h4>
              <ul className="list-disc pl-5 space-y-2 text-gray-700">
                <li>
                  In a real Nepal army interview, the IO asks multiple related
                  questions at once
                </li>
                <li>
                  You need to remember all questions and answer them in sequence
                </li>
                <li>
                  Each {isMostAskedCategory ? "FAQ" : "CIQ"} group focuses on a
                  specific aspect of your personality
                </li>
                <li>
                  You will only hear the questions (they will not be displayed)
                </li>
                <li>
                  Click &quot;Play Question&quot; to hear the full set of
                  questions
                </li>
                <li>Listen carefully and remember all questions</li>
                <li>
                  Click &quot;Start Recording&quot; when you&apos;re ready to
                  answer all questions
                </li>
                <li>
                  Click &quot;Stop Recording&quot; when you&apos;ve finished
                  answering all questions
                </li>
                <li>
                  Click &quot;Submit Response&quot; when you&apos;re satisfied
                  with your recording
                </li>
                <li>
                  After submission, you will see the text of all questions and
                  your recording
                </li>
              </ul>
            </div>

            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Select a {isMostAskedCategory ? "FAQ" : "CIQ"} Group
            </h3>

            <div className="space-y-4">
              {category.questionGroups.map(
                (group: QuestionGroup, index: number) => (
                  <div
                    key={index}
                    className="border border-gray-200 rounded-lg overflow-hidden"
                  >
                    <div
                      className={`flex justify-between items-center p-4 ${
                        isMostAskedCategory ? "bg-orange-50" : "bg-gray-50"
                      } cursor-pointer`}
                      onClick={() =>
                        setExpandedGroup(expandedGroup === index ? null : index)
                      }
                    >
                      <h4 className="font-medium text-gray-900">
                        {group.name}
                      </h4>
                      <button className="text-gray-500">
                        {expandedGroup === index ? (
                          <ChevronDownIcon className="h-5 w-5" />
                        ) : (
                          <ChevronRightIcon className="h-5 w-5" />
                        )}
                      </button>
                    </div>

                    {expandedGroup === index && (
                      <div className="p-4 border-t border-gray-200">
                        <ul className="list-disc pl-5 space-y-2 text-gray-700 mb-4">
                          {group.questions.map((question, qIndex) => (
                            <li key={qIndex} className="text-sm">
                              {question}
                            </li>
                          ))}
                        </ul>
                        <div className="flex justify-center">
                          <button
                            onClick={() => {
                              // Scroll to top of the page before starting the test
                              window.scrollTo({ top: 0, behavior: "smooth" });
                              setSelectedGroupIndex(index);
                              setCurrentQuestionIndex(0);
                            }}
                            className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white ${buttonBgColor}`}
                          >
                            Start Test
                            <ArrowRightIcon className="h-5 w-5 ml-2" />
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                )
              )}
            </div>
          </div>
        </div>
      );
    }

    // If a group is selected, show the rapid fire practice
    const selectedGroup = category.questionGroups[selectedGroupIndex];
    // Use a unique key for each question in the group
    const recordingKey = `${selectedCategory}-group-${selectedGroupIndex}-question-${currentQuestionIndex}`;
    const hasRecording = !!recordings[recordingKey];

    // Function to play the current question in the rapid fire test
    const playCurrentRapidFireQuestion = () => {
      // If already playing, stop it
      if (isPlayingQuestions && questionAudioPlayer) {
        questionAudioPlayer.pause();
        setIsPlayingQuestions(false);
        setQuestionAudioPlayer(null);
        return;
      }

      // Create a speech synthesis utterance
      const speech = new SpeechSynthesisUtterance();
      speech.lang = "en-US";
      speech.rate = 0.9;
      speech.pitch = 1;

      // Set the text to the full set of questions
      speech.text = selectedGroup.questions[0];

      // Play the speech
      window.speechSynthesis.speak(speech);

      // Store the speech object
      const speechObj = {
        pause: () => {
          window.speechSynthesis.cancel();
          console.log("Speech synthesis canceled");
        },
      };
      setQuestionAudioPlayer(speechObj);
      setIsPlayingQuestions(true);

      // Set up event listener for when speech ends
      speech.onend = () => {
        console.log("Speech ended naturally");
        setIsPlayingQuestions(false);
        setQuestionAudioPlayer(null);
      };
    };

    // Start the rapid fire test
    const startRapidFireTest = () => {
      // Scroll to top of the page before starting the test
      window.scrollTo({ top: 0, behavior: "smooth" });
      setTestStarted(true);
      setCurrentQuestionIndex(0);
      // Timer functionality removed
    };

    // This function has been removed as we now use completeTest directly

    // Complete the test
    const completeTest = () => {
      // Stop any playing audio
      if (isPlayingQuestions && questionAudioPlayer) {
        questionAudioPlayer.pause();
        setIsPlayingQuestions(false);
        setQuestionAudioPlayer(null);
      }

      // Scroll to top of the page
      window.scrollTo({ top: 0, behavior: "smooth" });

      // Mark the test as completed and show results
      setIsPracticeComplete(true);
    };

    // If test not started yet, show instructions and start button
    if (!testStarted) {
      return (
        <div className="mx-auto max-w-3xl">
          {/* Header */}
          <div className="mb-8 flex justify-between items-center">
            <button
              onClick={() => {
                setSelectedGroupIndex(null);
                setCurrentQuestionIndex(0);
              }}
              className="inline-flex items-center text-gray-600 hover:text-gray-900"
            >
              <ArrowLeftIcon className="h-5 w-5 mr-2" />
              Back to Groups
            </button>
            <div className="text-sm font-medium text-gray-500">
              {selectedGroup.name} Group
            </div>
          </div>

          {/* Two Officers Layout */}
          <div className="mb-6 bg-indigo-50 rounded-xl p-4 border border-indigo-200">
            <h2 className="text-lg font-semibold text-indigo-800 mb-2">
              Interviewing Officers
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-white rounded-lg p-4 border border-indigo-100 flex items-center">
                <div className="p-2 rounded-full bg-blue-100 mr-3">
                  <UserIcon className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">
                    Questioning Officer
                  </h3>
                  <p className="text-sm text-gray-600">
                    Asks questions to assess your background and suitability
                  </p>
                </div>
              </div>
              <div className="bg-white rounded-lg p-4 border border-indigo-100 flex items-center">
                <div className="p-2 rounded-full bg-purple-100 mr-3">
                  <UserGroupIcon className="h-6 w-6 text-purple-600" />
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">
                    Analyzing Officer
                  </h3>
                  <p className="text-sm text-gray-600">
                    Observes your responses and evaluates your potential
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Category Title */}
          <div className="mb-6 flex items-center space-x-3">
            {/* Determine if this is the "50 Most Asked Questions" category */}
            {category.id === "most-asked" ? (
              <>
                <div className="p-2 rounded-full bg-orange-100">
                  <category.icon className="h-6 w-6 text-orange-600" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900">
                  FAQ Test: {selectedGroup.name}{" "}
                  <span className="text-sm font-normal text-orange-600">
                    (Audio-Only)
                  </span>
                </h2>
              </>
            ) : (
              <>
                <div className="p-2 rounded-full bg-yellow-100">
                  <category.icon className="h-6 w-6 text-yellow-600" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900">
                  CIQ Test: {selectedGroup.name}{" "}
                  <span className="text-sm font-normal text-yellow-600">
                    (Audio-Only)
                  </span>
                </h2>
              </>
            )}
          </div>

          {/* Instructions Card */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
            <h3 className="text-xl font-semibold text-gray-900 mb-6">
              Test Instructions
            </h3>

            {category.id === "most-asked" ? (
              <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-6">
                <h4 className="font-medium text-orange-800 mb-2 flex items-center">
                  <QuestionMarkCircleIcon className="h-5 w-5 mr-2 text-orange-600" />
                  How the FAQ Test Works:
                </h4>
                <ul className="list-disc pl-5 space-y-2 text-gray-700">
                  <li>
                    In a real Nepal army interview, the IO asks multiple related
                    questions about common topics
                  </li>
                  <li>
                    You will only hear the questions (they will not be
                    displayed)
                  </li>
                  <li>
                    Click &quot;Play Question&quot; to hear the full set of
                    questions
                  </li>
                  <li>Listen carefully and remember all questions</li>
                  <li>
                    Click &quot;Start Recording&quot; when you&apos;re ready to
                    answer all questions
                  </li>
                  <li>
                    Click &quot;Stop Recording&quot; when you&apos;ve finished
                    answering all questions
                  </li>
                  <li>
                    Click &quot;Submit Response&quot; when you&apos;re satisfied
                    with your recording
                  </li>
                  <li>
                    After submission, you will see the text of all questions and
                    your recording
                  </li>
                </ul>
              </div>
            ) : (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                <h4 className="font-medium text-yellow-800 mb-2 flex items-center">
                  <QuestionMarkCircleIcon className="h-5 w-5 mr-2 text-yellow-600" />
                  How the CIQ Test Works:
                </h4>
                <ul className="list-disc pl-5 space-y-2 text-gray-700">
                  <li>
                    In a real Nepal army interview, the IO asks multiple related
                    questions at once
                  </li>
                  <li>
                    You will only hear the questions (they will not be
                    displayed)
                  </li>
                  <li>
                    Click &quot;Play Question&quot; to hear the full set of
                    questions
                  </li>
                  <li>Listen carefully and remember all questions</li>
                  <li>
                    Click &quot;Start Recording&quot; when you&apos;re ready to
                    answer all questions
                  </li>
                  <li>
                    Click &quot;Stop Recording&quot; when you&apos;ve finished
                    answering all questions
                  </li>
                  <li>
                    Click &quot;Submit Response&quot; when you&apos;re satisfied
                    with your recording
                  </li>
                  <li>
                    After submission, you will see the text of all questions and
                    your recording
                  </li>
                </ul>
              </div>
            )}

            <div className="mt-8 flex justify-center">
              <button
                onClick={startRapidFireTest}
                className={`inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white shadow-sm ${
                  category.id === "most-asked"
                    ? "bg-orange-600 hover:bg-orange-500"
                    : "bg-yellow-600 hover:bg-yellow-500"
                }`}
              >
                <PlayIcon className="h-5 w-5 mr-2" />
                Start {category.id === "most-asked" ? "FAQ" : "CIQ"} Test
              </button>
            </div>
          </div>
        </div>
      );
    }

    // If test is completed, show the results with recordings
    if (testCompleted && hasRecording) {
      return (
        <div className="mx-auto max-w-3xl">
          {/* Header */}
          <div className="mb-8 flex justify-between items-center">
            <button
              onClick={() => {
                if (
                  window.confirm(
                    "Are you sure you want to exit? Your recording will be lost."
                  )
                ) {
                  setSelectedGroupIndex(null);
                  setCurrentQuestionIndex(0);
                  setTestStarted(false);
                  setTestCompleted(false);
                }
              }}
              className="inline-flex items-center text-gray-600 hover:text-gray-900"
            >
              <ArrowLeftIcon className="h-5 w-5 mr-2" />
              Back to Groups
            </button>
            <div className="text-sm font-medium text-gray-500">
              Test Completed
            </div>
          </div>

          {/* Category Title */}
          <div className="mb-6 flex items-center space-x-3">
            <div className="p-2 rounded-full bg-green-100">
              <CheckCircleIcon className="h-6 w-6 text-green-600" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900">
              {category.id === "most-asked" ? "FAQ" : "CIQ"} Test Completed:{" "}
              {selectedGroup.name}
            </h2>
          </div>

          {/* Results Card */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
            <h3 className="text-xl font-semibold text-gray-900 mb-6">
              Your Responses
            </h3>

            <div className="mb-6">
              <div className="flex items-center space-x-3 mb-4">
                <MicrophoneIcon className="h-5 w-5 text-indigo-600" />
                <h4 className="font-medium text-gray-900">Full Recording</h4>
              </div>

              <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-500">
                    Recording: {formatTime(recordings[recordingKey].duration)}
                  </div>
                  <div className="flex space-x-3">
                    <button
                      onClick={() => playRecording(recordingKey)}
                      className="inline-flex items-center justify-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
                    >
                      {isPlaying && currentPlayingKey === recordingKey ? (
                        <>
                          <StopIcon className="h-4 w-4 mr-1" />
                          Stop
                        </>
                      ) : (
                        <>
                          <PlayIcon className="h-4 w-4 mr-1" />
                          Play
                        </>
                      )}
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-8">
              <h4 className="font-medium text-gray-900 mb-4">
                Questions Asked:
              </h4>
              <div className="space-y-4">
                <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                  <p className="font-medium text-gray-900 mb-2">
                    {selectedGroup.questions[0]}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Navigation Buttons */}
          <div className="flex justify-end">
            <button
              onClick={() => {
                setIsPracticeComplete(true);
              }}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
            >
              Complete Practice
              <ArrowRightIcon className="h-5 w-5 ml-2" />
            </button>
          </div>
        </div>
      );
    }

    // Main test interface
    return (
      <div className="mx-auto max-w-3xl">
        {/* Header */}
        <div className="mb-8 flex justify-between items-center">
          <button
            onClick={() => {
              if (isRecording) {
                stopRecording();
              }
              setTestStarted(false);
            }}
            className="inline-flex items-center text-gray-600 hover:text-gray-900"
          >
            <ArrowLeftIcon className="h-5 w-5 mr-2" />
            Back to Groups
          </button>
          <div className="text-sm font-medium text-gray-500">
            {selectedGroup.name} Group
          </div>
        </div>

        {/* Current Question Card */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
          <h3 className="text-xl font-semibold text-gray-900 mb-4">
            {selectedGroup.name}{" "}
            <span
              className={`text-sm font-normal ${
                category.id === "most-asked"
                  ? "text-orange-600"
                  : "text-yellow-600"
              }`}
            >
              ({category.id === "most-asked" ? "FAQ" : "CIQ"} Audio-Only Test)
            </span>
          </h3>

          <div
            className={`p-4 ${
              category.id === "most-asked"
                ? "bg-orange-50 border border-orange-200"
                : "bg-yellow-50 border border-yellow-200"
            } rounded-lg mb-6`}
          >
            <p className="text-lg text-center">
              Click "Play Question" to hear the{" "}
              {category.id === "most-asked"
                ? "Frequently Asked Questions"
                : "Command Information Questions"}
              .
              <br />
              <span className="text-sm text-gray-600 mt-2 block">
                (Questions will only be displayed after you submit your
                response)
              </span>
            </p>
          </div>

          {/* Play Question Button */}
          <div className="mb-6 flex justify-center">
            <button
              onClick={playCurrentRapidFireQuestion}
              className={`inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white shadow-md ${
                isPlayingQuestions
                  ? "bg-red-600 hover:bg-red-500 animate-pulse"
                  : category.id === "most-asked"
                  ? "bg-orange-600 hover:bg-orange-500"
                  : "bg-yellow-600 hover:bg-yellow-500"
              } transition-colors duration-200`}
            >
              {isPlayingQuestions ? (
                <>
                  <StopIcon className="h-6 w-6 mr-2" />
                  Stop Audio
                </>
              ) : (
                <>
                  <PlayIcon className="h-6 w-6 mr-2" />
                  Play Questions (Audio Only)
                </>
              )}
            </button>
          </div>

          {/* Recording Controls */}
          <div className="mt-8">
            {!isRecording && !hasRecording && (
              <div className="flex flex-col items-center">
                <button
                  onClick={startRecording}
                  className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
                >
                  <MicrophoneIcon className="h-5 w-5 mr-2" />
                  Start Recording
                </button>
              </div>
            )}

            {isRecording && (
              <div className="flex flex-col items-center space-y-4">
                <div className="text-lg font-semibold text-red-600 animate-pulse">
                  Recording... {formatTime(recordingTime)}
                </div>
                <button
                  onClick={stopRecording}
                  className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700"
                >
                  <StopIcon className="h-5 w-5 mr-2" />
                  Stop Recording
                </button>
              </div>
            )}

            {hasRecording && !isRecording && (
              <div className="flex flex-col items-center space-y-4">
                <div className="text-sm text-gray-500">
                  Recording: {formatTime(recordings[recordingKey].duration)}
                </div>
                <div className="flex space-x-4">
                  <button
                    onClick={() => playRecording(recordingKey)}
                    className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
                  >
                    {isPlaying && currentPlayingKey === recordingKey ? (
                      <>
                        <StopIcon className="h-5 w-5 mr-2" />
                        Stop
                      </>
                    ) : (
                      <>
                        <PlayIcon className="h-5 w-5 mr-2" />
                        Play
                      </>
                    )}
                  </button>
                  <button
                    onClick={startRecording}
                    className="inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                  >
                    <MicrophoneIcon className="h-5 w-5 mr-2" />
                    Re-record
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Navigation Buttons */}
        <div className="flex justify-end">
          {!isRecording && hasRecording && (
            <button
              onClick={completeTest}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
            >
              Submit Response
              <ArrowRightIcon className="h-5 w-5 ml-2" />
            </button>
          )}
        </div>
      </div>
    );
  };

  // Results page content
  const renderResultsPage = () => {
    const category = questionCategories.find((c) => c.id === selectedCategory);

    // Safety check - if category is undefined, show a fallback
    if (!category) {
      return (
        <div className="mx-auto max-w-3xl text-center">
          <p>Something went wrong. Please go back and try again.</p>
          <button
            onClick={() => {
              setIsPracticeStarted(false);
              setSelectedCategory(null);
            }}
            className="mt-4 inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            Back to Categories
          </button>
        </div>
      );
    }

    // Handle different category types
    if (category.id === "rapid-fire" || category.id === "most-asked") {
      return renderRapidFireResultsPage(category as RapidFireCategory);
    } else {
      return renderNormalResultsPage(category as NormalCategory);
    }
  };

  // Normal questions results page
  const renderNormalResultsPage = (category: NormalCategory) => {
    return (
      <div className="mx-auto max-w-3xl">
        <div className="text-center">
          <div className="inline-flex items-center justify-center p-2 bg-green-100 rounded-full mb-4">
            <CheckCircleIcon className="h-8 w-8 text-green-600" />
          </div>
          <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
            Practice Complete!
          </h2>
          <p className="mt-4 text-lg text-gray-600">
            You&apos;ve completed the {category.name} practice session
          </p>
        </div>

        {/* Two Officers Layout */}
        <div className="mt-8 mb-6 bg-indigo-50 rounded-xl p-4 border border-indigo-200">
          <h2 className="text-lg font-semibold text-indigo-800 mb-2">
            Interviewing Officers
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-white rounded-lg p-4 border border-indigo-100 flex items-center">
              <div className="p-2 rounded-full bg-blue-100 mr-3">
                <UserIcon className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <h3 className="font-medium text-gray-900">
                  Questioning Officer
                </h3>
                <p className="text-sm text-gray-600">
                  Asked questions to assess your background and suitability
                </p>
              </div>
            </div>
            <div className="bg-white rounded-lg p-4 border border-indigo-100 flex items-center">
              <div className="p-2 rounded-full bg-purple-100 mr-3">
                <UserGroupIcon className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <h3 className="font-medium text-gray-900">Analyzing Officer</h3>
                <p className="text-sm text-gray-600">
                  Observed your responses and evaluated your potential
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-8">
          <h3 className="text-xl font-bold text-gray-900 mb-6">
            Your Recorded Responses
          </h3>

          <div className="space-y-6">
            {category.questions.map((question: string, index: number) => {
              const recordingKey = `${selectedCategory}-${index}`;
              const hasRecording = !!recordings[recordingKey];

              return (
                <div
                  key={index}
                  className="bg-white rounded-xl shadow-sm border border-gray-200 p-6"
                >
                  <h4 className="text-lg font-semibold text-gray-900 mb-4">
                    {index + 1}. {question}
                  </h4>

                  {hasRecording ? (
                    <div className="flex items-center space-x-4">
                      <div className="text-sm text-gray-500">
                        Recording:{" "}
                        {formatTime(recordings[recordingKey].duration)}
                      </div>
                      <button
                        onClick={() => playRecording(recordingKey)}
                        className="inline-flex items-center justify-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
                      >
                        {isPlaying && currentPlayingKey === recordingKey ? (
                          <>
                            <StopIcon className="h-4 w-4 mr-1" />
                            Stop
                          </>
                        ) : (
                          <>
                            <PlayIcon className="h-4 w-4 mr-1" />
                            Play
                          </>
                        )}
                      </button>
                    </div>
                  ) : (
                    <div className="text-sm text-gray-500 italic">
                      No recording for this question
                    </div>
                  )}
                </div>
              );
            })}
          </div>

          <div className="mt-8 flex justify-center space-x-4">
            <button
              onClick={() => {
                // Scroll to top of the page
                window.scrollTo({ top: 0, behavior: "smooth" });
                resetState();
              }}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              <ArrowLeftIcon className="h-5 w-5 mr-2" />
              Back to Categories
            </button>
            <button
              onClick={() => {
                // Stop any playing audio
                stopPlayback();
                // Scroll to top of the page
                window.scrollTo({ top: 0, behavior: "smooth" });
                // Reset to first question
                setCurrentQuestionIndex(0);
                // Clear all recordings
                setRecordings({});
                // Exit results screen
                setIsPracticeComplete(false);
              }}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
            >
              Practice Again
              <ArrowRightIcon className="h-4 w-4 ml-2" />
            </button>
          </div>
        </div>
      </div>
    );
  };

  // Rapid fire questions results page
  const renderRapidFireResultsPage = (category: RapidFireCategory) => {
    // Get all the rapid fire recordings
    const rapidFireRecordings = Object.keys(recordings).filter((key) =>
      key.startsWith(`${selectedCategory}-group-`)
    );

    return (
      <div className="mx-auto max-w-3xl">
        <div className="text-center">
          <div className="inline-flex items-center justify-center p-2 bg-green-100 rounded-full mb-4">
            <CheckCircleIcon className="h-8 w-8 text-green-600" />
          </div>
          <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
            Practice Complete!
          </h2>
          <p className="mt-4 text-lg text-gray-600">
            You&apos;ve completed the {category.name} practice session
          </p>
        </div>

        {/* Two Officers Layout */}
        <div className="mt-8 mb-6 bg-indigo-50 rounded-xl p-4 border border-indigo-200">
          <h2 className="text-lg font-semibold text-indigo-800 mb-2">
            Interviewing Officers
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-white rounded-lg p-4 border border-indigo-100 flex items-center">
              <div className="p-2 rounded-full bg-blue-100 mr-3">
                <UserIcon className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <h3 className="font-medium text-gray-900">
                  Questioning Officer
                </h3>
                <p className="text-sm text-gray-600">
                  Asked rapid-fire questions to assess your quick thinking
                </p>
              </div>
            </div>
            <div className="bg-white rounded-lg p-4 border border-indigo-100 flex items-center">
              <div className="p-2 rounded-full bg-purple-100 mr-3">
                <UserGroupIcon className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <h3 className="font-medium text-gray-900">Analyzing Officer</h3>
                <p className="text-sm text-gray-600">
                  Observed your responses and evaluated your potential
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-8">
          <h3 className="text-xl font-bold text-gray-900 mb-6">
            Your Recorded Responses
          </h3>

          <div className="space-y-6">
            {rapidFireRecordings.length > 0 ? (
              category.questionGroups.map(
                (group: QuestionGroup, groupIndex: number) => {
                  // Check if there are any recordings for this group
                  const groupRecordings = Object.keys(recordings).filter(
                    (key) =>
                      key.startsWith(
                        `${selectedCategory}-group-${groupIndex}-question-`
                      )
                  );

                  if (groupRecordings.length === 0) return null;

                  return (
                    <div
                      key={groupIndex}
                      className="bg-white rounded-xl shadow-sm border border-gray-200 p-6"
                    >
                      <h4 className="text-lg font-semibold text-gray-900 mb-4">
                        {group.name} Group
                      </h4>

                      <div className="space-y-4">
                        <div className="border-t border-gray-100 pt-4 first:border-t-0 first:pt-0">
                          <h5 className="font-medium text-gray-800 mb-2">
                            {group.questions[0]}
                          </h5>

                          {groupRecordings.length > 0 ? (
                            <div className="flex items-center space-x-4">
                              <div className="text-sm text-gray-500">
                                Recording:{" "}
                                {formatTime(
                                  recordings[groupRecordings[0]].duration
                                )}
                              </div>
                              <button
                                onClick={() =>
                                  playRecording(groupRecordings[0])
                                }
                                className="inline-flex items-center justify-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
                              >
                                {isPlaying &&
                                currentPlayingKey === groupRecordings[0] ? (
                                  <>
                                    <StopIcon className="h-4 w-4 mr-1" />
                                    Stop
                                  </>
                                ) : (
                                  <>
                                    <PlayIcon className="h-4 w-4 mr-1" />
                                    Play
                                  </>
                                )}
                              </button>
                            </div>
                          ) : (
                            <div className="text-sm text-gray-500 italic">
                              No recording for this{" "}
                              {category.id === "most-asked" ? "FAQ" : "CIQ"}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                }
              )
            ) : (
              <div className="text-center p-8 bg-gray-50 rounded-xl border border-gray-200">
                <p className="text-gray-600">
                  No recordings found. Try practicing with some question groups.
                </p>
              </div>
            )}
          </div>

          {/* Audio Recording Manager */}
          <div className="mt-12">
            <AudioRecordingManager
              recordings={recordings}
              testType="IO"
              isPlaying={isPlaying}
              currentPlayingKey={currentPlayingKey}
              onPlay={playRecording}
              onStop={stopPlayback}
              className="bg-gray-50 rounded-xl p-6 border border-gray-200"
            />
          </div>

          <div className="mt-8 flex justify-center space-x-4">
            <button
              onClick={() => {
                // Scroll to top of the page
                window.scrollTo({ top: 0, behavior: "smooth" });
                resetState();
              }}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              <ArrowLeftIcon className="h-5 w-5 mr-2" />
              Back to Categories
            </button>
            <button
              onClick={() => {
                // Stop any playing audio
                stopPlayback();
                // Scroll to top of the page
                window.scrollTo({ top: 0, behavior: "smooth" });
                // Reset to first question
                setCurrentQuestionIndex(0);
                // Clear all recordings
                setRecordings({});
                // Exit results screen
                setIsPracticeComplete(false);
              }}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
            >
              Practice Again
              <ArrowRightIcon className="h-4 w-4 ml-2" />
            </button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="bg-white py-24 sm:py-32">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">{renderContent()}</div>
    </div>
  );
}
