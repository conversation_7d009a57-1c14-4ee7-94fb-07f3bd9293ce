import { jsPDF } from "jspdf";
import "jspdf-autotable";
import { militaryTraitCategories } from "./evaluationUtils";

// Helper function to add watermark and social links to all pages of a PDF
const addWatermark = (doc: jsPDF) => {
  const totalPages = doc.getNumberOfPages();

  // Save the current state
  const fontSize = doc.getFontSize();
  const textColor = doc.getTextColor();

  // Set watermark properties
  doc.setFontSize(10);
  doc.setTextColor(150, 150, 150); // Light gray color

  // Add watermark and links to each page
  for (let i = 1; i <= totalPages; i++) {
    doc.setPage(i);

    const pageWidth = doc.internal.pageSize.getWidth();
    const pageHeight = doc.internal.pageSize.getHeight();

    // Add a divider line
    doc.setDrawColor(200, 200, 200);
    doc.line(15, pageHeight - 15, pageWidth - 15, pageHeight - 15);

    // Add website name at the bottom right of the page
    doc.textWithLink(
      "balaramshiwakoti.com.np",
      pageWidth - 15,
      pageHeight - 10,
      {
        align: "right",
        url: "https://balaramshiwakoti.com.np",
      }
    );

    // Add Facebook link at the bottom left of the page
    doc.setTextColor(59, 89, 152); // Facebook blue color
    doc.setFontSize(12);
    doc.textWithLink("My Facebook", 15, pageHeight - 10, {
      url: "https://www.facebook.com/hercules.shiwakoti",
    });
    doc.setFontSize(10);
  }

  // Restore the original state
  doc.setFontSize(fontSize);
  doc.setTextColor(textColor);

  return doc;
};

/**
 * Generate a simple PDF with TAT test results without trying to load images
 */
export const generateSimpleTATPDF = (
  setName: string,
  responses: string[],
  images: string[]
): Blob => {
  console.log(`Generating simple TAT PDF for set: ${setName}`);

  // Create a new PDF document
  const doc = new jsPDF();

  // Add title
  doc.setFontSize(18);
  doc.text(`TAT Practice Set ${setName} - Results`, 14, 20);

  // Add date
  const today = new Date();
  const dateStr = today.toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
  doc.setFontSize(12);
  doc.setTextColor(100, 100, 100);
  doc.text(`Generated on: ${dateStr}`, 14, 35);

  // Add responses directly
  let yPos = 50;

  doc.setFontSize(16);
  doc.setTextColor(0, 0, 0);
  doc.text("Your Responses", 14, yPos);
  yPos += 10;

  for (let index = 0; index < images.length; index++) {
    if (yPos > 250) {
      doc.addPage();
      yPos = 20;
    }

    doc.setFontSize(12);
    doc.setTextColor(0, 0, 0);
    doc.text(`${index + 1}. Image ${index + 1}`, 14, yPos);
    yPos += 10;

    // Add a placeholder for the image
    doc.setDrawColor(200, 200, 200);
    doc.setFillColor(240, 240, 240);
    doc.rect(14, yPos, 180, 40, "F");

    doc.setTextColor(100, 100, 100);
    doc.setFontSize(10);
    doc.text("Image reference (see online version)", 104, yPos + 20, {
      align: "center",
    });
    doc.setTextColor(0, 0, 0);

    yPos += 50;

    // Add the response
    doc.setFontSize(12);
    doc.text("Your Story:", 14, yPos);
    yPos += 7;

    const response = responses[index] || "Not answered";
    const splitText = doc.splitTextToSize(response, 180);
    doc.text(splitText, 14, yPos);
    yPos += splitText.length * 5 + 15;
  }

  // Add watermark to all pages
  addWatermark(doc);

  // Return the PDF as a blob
  return doc.output("blob");
};
