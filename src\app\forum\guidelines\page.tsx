"use client";

import React from "react";
import Link from "next/link";
import { ArrowLeftIcon } from "@heroicons/react/24/outline";

export default function CommunityGuidelinesPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="mx-auto max-w-3xl px-6">
        <Link
          href="/forum"
          className="inline-flex items-center text-sm font-medium text-indigo-600 hover:text-indigo-500 mb-6"
        >
          <ArrowLeftIcon className="h-4 w-4 mr-1" />
          Back to Forum
        </Link>

        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="px-6 py-5 border-b border-gray-200">
            <h1 className="text-xl font-semibold text-gray-900">
              Community Guidelines
            </h1>
          </div>

          <div className="p-6 prose prose-indigo max-w-none">
            <div className="bg-blue-50 border-l-4 border-blue-500 p-4 mb-8">
              <p className="text-blue-700 text-xl font-bold text-center">
                Be kind. No bullying or hate speech. Keep comments relevant.
              </p>
            </div>

            <div className="bg-white shadow-md rounded-lg p-6 mb-8">
              <h2 className="text-xl font-bold text-gray-800 mb-4 text-center">
                Community Guidelines
              </h2>

              <p className="mb-6">
                Welcome to our Military Selection Test Preparation Forum. Our
                guidelines are simple:
              </p>

              <ul className="space-y-4">
                <li className="flex items-start">
                  <span className="bg-indigo-100 text-indigo-800 font-bold rounded-full w-8 h-8 flex items-center justify-center mr-3 mt-0.5">
                    1
                  </span>
                  <div>
                    <span className="font-bold text-lg">Respect others.</span>
                    <p className="text-gray-600">
                      Treat everyone with courtesy. No discrimination, bullying,
                      or harassment.
                    </p>
                  </div>
                </li>

                <li className="flex items-start">
                  <span className="bg-green-100 text-green-800 font-bold rounded-full w-8 h-8 flex items-center justify-center mr-3 mt-0.5">
                    2
                  </span>
                  <div>
                    <span className="font-bold text-lg">Stay on topic.</span>
                    <p className="text-gray-600">
                      Keep discussions relevant to military selection
                      preparation.
                    </p>
                  </div>
                </li>

                <li className="flex items-start">
                  <span className="bg-amber-100 text-amber-800 font-bold rounded-full w-8 h-8 flex items-center justify-center mr-3 mt-0.5">
                    3
                  </span>
                  <div>
                    <span className="font-bold text-lg">No cheating.</span>
                    <p className="text-gray-600">
                      Don't share actual exam questions or confidential
                      materials.
                    </p>
                  </div>
                </li>

                <li className="flex items-start">
                  <span className="bg-red-100 text-red-800 font-bold rounded-full w-8 h-8 flex items-center justify-center mr-3 mt-0.5">
                    4
                  </span>
                  <div>
                    <span className="font-bold text-lg">Protect privacy.</span>
                    <p className="text-gray-600">
                      Don't share personal information about yourself or others.
                    </p>
                  </div>
                </li>
              </ul>
            </div>

            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-8">
              <h3 className="font-semibold text-gray-700 mb-2">Violations</h3>
              <p className="text-gray-600">
                Violations may result in content removal, warnings, temporary
                suspension, or permanent bans depending on severity.
              </p>
            </div>

            <div className="bg-indigo-50 border border-indigo-200 rounded-lg p-4">
              <h3 className="font-semibold text-indigo-700 mb-2">Questions?</h3>
              <p className="text-indigo-600">
                Contact us at{" "}
                <span className="font-medium">
                  <EMAIL>
                </span>{" "}
                or use the "Feedback & Suggestions" category in the forum.
              </p>
            </div>

            <div className="mt-8 text-center">
              <Link
                href="/forum"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Return to Forum
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
