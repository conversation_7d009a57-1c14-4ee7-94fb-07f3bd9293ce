// Content protection utilities

// Function to disable right-click context menu
export const disableContextMenu = (event: MouseEvent) => {
  // Allow right-click on form elements and links
  if (
    event.target instanceof HTMLInputElement ||
    event.target instanceof HTMLTextAreaElement ||
    event.target instanceof HTMLSelectElement ||
    event.target instanceof HTMLAnchorElement ||
    (event.target instanceof HTMLElement && event.target.closest("a"))
  ) {
    return true;
  }

  event.preventDefault();
  return false;
};

// Function to disable text selection
export const disableTextSelection = () => {
  document.body.style.userSelect = "none";

  // Add CSS class for cross-browser support
  document.body.classList.add("no-select");

  // Enable selection on form elements and links
  const interactiveElements = document.querySelectorAll(
    "input, textarea, select, a, button"
  );
  interactiveElements.forEach((el) => {
    (el as HTMLElement).style.userSelect = "text";
    el.classList.add("allow-select");
  });
};

// Function to disable copy/cut/paste
export const disableCopyPaste = (event: ClipboardEvent) => {
  // Allow copy/paste in form elements and links
  if (
    event.target instanceof HTMLInputElement ||
    event.target instanceof HTMLTextAreaElement ||
    event.target instanceof HTMLSelectElement ||
    event.target instanceof HTMLAnchorElement ||
    (event.target instanceof HTMLElement && event.target.closest("a"))
  ) {
    return true;
  }

  event.preventDefault();
  return false;
};

// Function to disable keyboard shortcuts
export const disableKeyboardShortcuts = (event: KeyboardEvent) => {
  // Allow keyboard shortcuts in form elements and links
  if (
    event.target instanceof HTMLInputElement ||
    event.target instanceof HTMLTextAreaElement ||
    event.target instanceof HTMLSelectElement ||
    event.target instanceof HTMLAnchorElement ||
    (event.target instanceof HTMLElement && event.target.closest("a"))
  ) {
    return true;
  }

  // Prevent Ctrl+S (Save), Ctrl+P (Print), Ctrl+C (Copy), Ctrl+X (Cut), Ctrl+U (View Source)
  if (
    (event.ctrlKey || event.metaKey) &&
    (event.key === "s" ||
      event.key === "p" ||
      event.key === "c" ||
      event.key === "x" ||
      event.key === "u" ||
      event.key === "a")
  ) {
    event.preventDefault();
    return false;
  }

  // Prevent F12 key (Developer Tools)
  if (event.key === "F12") {
    event.preventDefault();
    return false;
  }

  // Prevent PrintScreen key
  if (event.key === "PrintScreen") {
    event.preventDefault();
    return false;
  }

  return true;
};

// Function to disable image dragging
export const disableImageDrag = () => {
  const images = document.getElementsByTagName("img");
  for (let i = 0; i < images.length; i++) {
    images[i].setAttribute("draggable", "false");
    images[i].style.pointerEvents = "none";

    // Add transparent overlay to prevent right-click save
    const parent = images[i].parentNode;
    if (parent && !parent.querySelector(".img-overlay")) {
      const overlay = document.createElement("div");
      overlay.className = "img-overlay";
      overlay.style.position = "absolute";
      overlay.style.top = "0";
      overlay.style.left = "0";
      overlay.style.width = "100%";
      overlay.style.height = "100%";
      overlay.style.zIndex = "10";

      // Only add overlay if parent has position relative/absolute
      const parentPosition = window.getComputedStyle(
        parent as Element
      ).position;
      if (parentPosition === "static") {
        (parent as HTMLElement).style.position = "relative";
      }

      parent.appendChild(overlay);
    }
  }
};

// Function to disable saving images
export const disableSaveImage = () => {
  // Override the native Image constructor
  const originalImage = window.Image;

  // @ts-expect-error - Custom implementation to protect images
  window.Image = function () {
    const image = new originalImage();
    image.crossOrigin = "anonymous";

    // Override the src property
    let originalSrc = "";
    Object.defineProperty(image, "src", {
      get: function () {
        return originalSrc;
      },
      set: function (value) {
        originalSrc = value;
        this.setAttribute("draggable", "false");
        this.style.pointerEvents = "none";
      },
    });

    return image;
  };
};

// Function to initialize all content protection features
export const initContentProtection = () => {
  if (typeof window !== "undefined") {
    // Disable right-click
    document.addEventListener("contextmenu", disableContextMenu);

    // Disable text selection
    disableTextSelection();

    // Disable copy/cut/paste
    document.addEventListener("copy", disableCopyPaste);
    document.addEventListener("cut", disableCopyPaste);
    document.addEventListener("paste", disableCopyPaste);

    // Disable keyboard shortcuts
    document.addEventListener("keydown", disableKeyboardShortcuts);

    // Disable image dragging
    disableImageDrag();

    // Disable saving images
    disableSaveImage();

    // Re-apply protections after DOM changes
    const observer = new MutationObserver(() => {
      disableImageDrag();
      disableTextSelection();
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });

    // Add CSS class to body for additional protection
    document.body.classList.add("content-protected");
  }
};
