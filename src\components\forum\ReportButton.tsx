"use client";

import React, { useState } from "react";
import { useAuth } from "@/context/GoogleAuthContext";
import { reportContent } from "@/services/forumService";
import { ReportReason, reportReasonDisplayNames } from "@/types/forum";
import { FlagIcon, XMarkIcon } from "@heroicons/react/24/outline";

interface ReportButtonProps {
  contentId: string;
  contentType: "topic" | "reply";
  authorName: string;
}

export default function ReportButton({
  contentId,
  contentType,
  authorName,
}: ReportButtonProps) {
  const { user } = useAuth();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [reason, setReason] = useState<ReportReason>("other");
  const [description, setDescription] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // Handle report submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user) {
      setError("You must be logged in to report content.");
      return;
    }

    if (!reason) {
      setError("Please select a reason for your report.");
      return;
    }

    try {
      setIsSubmitting(true);
      setError(null);

      await reportContent(
        contentId,
        contentType,
        user.uid,
        user.displayName || "Anonymous",
        reason,
        description
      );

      setSuccess(true);
      setTimeout(() => {
        setIsModalOpen(false);
        setSuccess(false);
        setReason("other");
        setDescription("");
      }, 2000);
    } catch (error) {
      console.error("Error reporting content:", error);
      setError(
        error instanceof Error
          ? error.message
          : "An error occurred while submitting your report. Please try again."
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <button
        type="button"
        onClick={() => setIsModalOpen(true)}
        className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700"
      >
        <FlagIcon className="h-4 w-4 mr-1" />
        Report
      </button>

      {/* Report Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* Background overlay */}
          <div
            className="fixed inset-0 bg-black bg-opacity-50"
            onClick={() => !isSubmitting && setIsModalOpen(false)}
          ></div>

          {/* Modal */}
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 z-10">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">
                Report {contentType === "topic" ? "Topic" : "Reply"}
              </h3>
              <button
                onClick={() => !isSubmitting && setIsModalOpen(false)}
                className="text-gray-400 hover:text-gray-500"
                disabled={isSubmitting}
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            </div>

            {success ? (
              <div className="bg-green-50 border-l-4 border-green-400 p-4 mb-4">
                <div className="flex">
                  <div className="ml-3">
                    <p className="text-sm text-green-700">
                      Thank you for your report. Our moderators will review it
                      shortly.
                    </p>
                  </div>
                </div>
              </div>
            ) : (
              <form onSubmit={handleSubmit}>
                {/* Error message */}
                {error && (
                  <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-4">
                    <div className="flex">
                      <div className="ml-3">
                        <p className="text-sm text-red-700">{error}</p>
                      </div>
                    </div>
                  </div>
                )}

                <p className="text-sm text-gray-600 mb-4">
                  You are reporting content by <strong>{authorName}</strong>.
                  Please select a reason for your report and provide any
                  additional details that might help our moderators.
                </p>

                {/* Reason selection */}
                <div className="mb-4">
                  <label
                    htmlFor="reason"
                    className="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Reason
                  </label>
                  <select
                    id="reason"
                    name="reason"
                    value={reason}
                    onChange={(e) => setReason(e.target.value as ReportReason)}
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    required
                  >
                    {Object.entries(reportReasonDisplayNames).map(
                      ([value, label]) => (
                        <option key={value} value={value}>
                          {label}
                        </option>
                      )
                    )}
                  </select>
                </div>

                {/* Description */}
                <div className="mb-4">
                  <label
                    htmlFor="description"
                    className="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Additional Details
                  </label>
                  <textarea
                    id="description"
                    name="description"
                    rows={3}
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    placeholder="Please provide any additional details about why you're reporting this content..."
                  ></textarea>
                </div>

                {/* Submit button */}
                <div className="flex justify-end">
                  <button
                    type="button"
                    onClick={() => setIsModalOpen(false)}
                    className="mr-3 px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    disabled={isSubmitting}
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:bg-red-400 disabled:cursor-not-allowed"
                  >
                    {isSubmitting ? "Submitting..." : "Submit Report"}
                  </button>
                </div>
              </form>
            )}
          </div>
        </div>
      )}
    </>
  );
}
