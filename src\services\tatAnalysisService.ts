/**
 * TAT Analysis Service
 * This service provides a specialized implementation for analyzing TAT responses
 * to avoid JSON parsing issues with the DeepSeek API
 */

import OpenAI from "openai";
import { AIAnalysisResult } from "./aiService";
// Import militaryTraitsForAI from aiService
import { militaryTraitsForAI } from "./aiService";

/**
 * Analyze TAT responses using DeepSeek API
 * This is a specialized implementation to handle TAT responses more reliably
 * @param responses Array of user responses to analyze
 * @param prompts Optional array of prompts/questions that elicited the responses
 * @returns Promise resolving to the AI analysis result
 */
export const analyzeTATResponsesWithDeepSeek = async (
  responses: string[],
  prompts?: string[]
): Promise<AIAnalysisResult> => {
  try {
    // Check if DeepSeek API key is configured
    const apiKey = process.env.NEXT_PUBLIC_DEEPSEEK_API_KEY;
    if (!apiKey || apiKey === "your-deepseek-api-key" || apiKey.trim() === "") {
      console.error("DeepSeek API key is not configured or is invalid");
      return generateTATFallbackAnalysis(responses);
    }

    // Filter out empty responses
    const validResponses = responses.filter(
      (response) => response && response.trim() !== ""
    );

    // If no valid responses, return a fallback analysis
    if (validResponses.length === 0) {
      return generateTATFallbackAnalysis(responses);
    }

    // Prepare the context for the API call
    let context = `Analyze the following responses from a military Thematic Apperception Test (TAT) to evaluate the candidate's military aptitude.\n\n`;

    if (prompts && prompts.length > 0) {
      context += "The responses are to these image descriptions:\n\n";
      prompts.forEach((prompt, index) => {
        if (prompt) {
          context += `Image ${index + 1}: ${prompt}\n\n`;
        }
      });
    }

    context += "Responses:\n\n";
    validResponses.forEach((response, index) => {
      context += `Response ${index + 1}: ${response}\n\n`;
    });

    // Create the prompt for DeepSeek
    const prompt = `You are a military psychological assessment expert. Analyze the candidate's responses to a Thematic Apperception Test (TAT) to evaluate their military aptitude.

In a TAT, candidates are shown ambiguous images and asked to create stories about them. This test reveals underlying motives, concerns, and personality traits.

For military TAT analysis, consider:
1. Themes of leadership, teamwork, and duty
2. Problem-solving approach
3. Emotional regulation
4. Moral reasoning
5. Conflict resolution style

Please provide your analysis in the following format:

OVERALL_SCORE: [A number between 0-100]

STRENGTHS:
- [Strength 1]
- [Strength 2]
- [Strength 3]

IMPROVEMENTS:
- [Improvement 1]
- [Improvement 2]
- [Improvement 3]

DETAILED_FEEDBACK:
[Provide a detailed paragraph about the candidate's military aptitude based on their responses]

RESPONSE_QUALITY:
[For each response, indicate if it's "Good", "Moderate", or "Bad" based on the quality:
- "Good": Rich narrative with clear themes of leadership, duty, or service
- "Moderate": Acceptable but could be improved in depth or military relevance
- "Bad": Superficial, disorganized, or inappropriate for military context
Provide brief improvement suggestions for "Moderate" and "Bad" responses.]

Here is the candidate's information:
${context}

IMPORTANT: Follow the exact format specified above. Do not include any additional sections or formatting.`;

    // Initialize OpenAI client with OpenRouter configuration
    const openai = new OpenAI({
      baseURL: "https://openrouter.ai/api/v1",
      apiKey: apiKey,
      dangerouslyAllowBrowser: true, // Required when running in browser environment
      defaultHeaders: {
        "HTTP-Referer":
          process.env.NEXT_PUBLIC_APP_URL || "https://balaramshiwakoti.com.np",
        "X-Title": "Military Aptitude Analysis",
      },
    });

    // Call the DeepSeek API via OpenRouter
    let analysisText;
    try {
      console.log("Sending request to DeepSeek API for TAT analysis...");
      const completion = await openai.chat.completions.create({
        model: "deepseek/deepseek-chat-v3-0324:free",
        messages: [
          {
            role: "user",
            content: prompt,
          },
        ],
        temperature: 0.1, // Very low temperature for more predictable, structured output
        max_tokens: 1500,
      });

      // Extract the response text
      analysisText = completion.choices[0]?.message?.content;
      console.log("Received response from DeepSeek API for TAT analysis");

      if (!analysisText) {
        console.error("No analysis content received from DeepSeek for TAT");
        return generateTATFallbackAnalysis(
          responses,
          "No content received from DeepSeek API"
        );
      }
    } catch (apiError: any) {
      console.error("Error calling DeepSeek API for TAT:", apiError);
      return generateTATFallbackAnalysis(
        responses,
        `Error calling DeepSeek API: ${apiError.message || "Unknown error"}`
      );
    }

    // Parse the structured text response
    console.log("Raw DeepSeek TAT response:", analysisText);

    // Extract the overall score
    const overallScoreMatch = analysisText.match(/OVERALL_SCORE:\s*(\d+)/i);
    const overallScore = overallScoreMatch
      ? parseInt(overallScoreMatch[1])
      : 65;

    // Extract strengths
    const strengthsSection = analysisText.match(
      /STRENGTHS:([\s\S]*?)(?=IMPROVEMENTS:|$)/i
    );
    const strengths = strengthsSection
      ? strengthsSection[1]
          .split("\n")
          .filter((line) => line.trim().startsWith("-"))
          .map((line) => line.replace(/^-\s*/, "").trim())
      : ["Emotional Intelligence", "Narrative Skills", "Perception"];

    // Extract improvements
    const improvementsSection = analysisText.match(
      /IMPROVEMENTS:([\s\S]*?)(?=DETAILED_FEEDBACK:|$)/i
    );
    const improvements = improvementsSection
      ? improvementsSection[1]
          .split("\n")
          .filter((line) => line.trim().startsWith("-"))
          .map((line) => line.replace(/^-\s*/, "").trim())
      : ["Military Context", "Leadership Focus", "Structured Thinking"];

    // Extract detailed feedback
    const detailedFeedbackSection = analysisText.match(
      /DETAILED_FEEDBACK:([\s\S]*?)(?=RESPONSE_QUALITY:|$)/i
    );
    const detailedFeedback = detailedFeedbackSection
      ? detailedFeedbackSection[1].trim()
      : "Based on the responses, the candidate shows potential for military service but needs development in key areas.";

    // Extract response quality assessments
    const responseQualitySection = analysisText.match(
      /RESPONSE_QUALITY:([\s\S]*?)$/i
    );
    const responseQualityText = responseQualitySection
      ? responseQualitySection[1]
      : "";

    // Create response quality assessment object
    const responseQualityAssessment: {
      [key: number]: {
        quality: "Good" | "Moderate" | "Bad";
        response: string;
        improvementSuggestions?: string[];
      };
    } = {};

    // Parse response quality from the text
    validResponses.forEach((response, index) => {
      // Look for mentions of this response in the quality section
      const responseMatch = new RegExp(
        `Response\\s*${index + 1}[^\\n]*?(Good|Moderate|Bad)`,
        "i"
      ).exec(responseQualityText);

      // Determine the quality of the response
      let quality: "Good" | "Moderate" | "Bad" = "Moderate"; // Default to moderate

      // If we have a direct evaluation from the AI, use that
      if (responseMatch) {
        const aiQuality = responseMatch[1].toLowerCase();
        if (aiQuality === "good") quality = "Good";
        else if (aiQuality === "bad") quality = "Bad";
        else quality = "Moderate";

        console.log(`Response ${index + 1} evaluated by AI as ${quality}`);
      }
      // Otherwise, use a simple heuristic
      else {
        // Simple heuristic: longer responses are generally better for TAT
        quality = response.length > 200 ? "Good" : response.length > 100 ? "Moderate" : "Bad";
      }

      // Create the assessment object
      responseQualityAssessment[index] = {
        quality: quality,
        response:
          response.substring(0, 50) + (response.length > 50 ? "..." : ""),
      };

      // Add improvement suggestions based on quality
      if (quality !== "Good") {
        // Try to extract specific suggestions from the text
        const suggestionMatch = new RegExp(
          `Response\\s*${
            index + 1
          }[^\\n]*?(Moderate|Bad)[^\\n]*?suggestions?:([^\\n]*)`,
          "i"
        ).exec(responseQualityText);

        if (suggestionMatch) {
          responseQualityAssessment[index].improvementSuggestions = [
            suggestionMatch[2].trim(),
          ];
        } else if (quality === "Bad") {
          responseQualityAssessment[index].improvementSuggestions = [
            "Develop a more detailed narrative with character motivations",
            "Include military themes or leadership elements in your story",
            "Structure your response with a clear beginning, middle, and end",
          ];
        } else if (quality === "Moderate") {
          responseQualityAssessment[index].improvementSuggestions = [
            "Add more military context to your narrative",
            "Explore themes of duty, leadership, or teamwork in your story",
          ];
        }
      }
    });

    // Create traits object based on strengths and improvements
    const traits: { [key: string]: { score: number; feedback: string } } = {};

    // Add traits based on strengths (high scores)
    strengths.forEach((strength) => {
      traits[strength] = {
        score: 75 + Math.floor(Math.random() * 15),
        feedback: `You demonstrate strong ${strength.toLowerCase()} in your responses.`,
      };
    });

    // Add traits based on improvements (lower scores)
    improvements.forEach((improvement) => {
      traits[improvement] = {
        score: 40 + Math.floor(Math.random() * 15),
        feedback: `You should focus on improving your ${improvement.toLowerCase()}.`,
      };
    });

    // Add any missing traits from our standard list
    militaryTraitsForAI.forEach((trait) => {
      if (!traits[trait.name]) {
        traits[trait.name] = {
          score: 50 + Math.floor(Math.random() * 10),
          feedback: "You show average potential in this area.",
        };
      }
    });

    // Construct the final analysis result
    const result: AIAnalysisResult = {
      overallScore,
      strengths,
      improvements,
      detailedFeedback,
      traits,
      responseQualityAssessment,
      isAIGenerated: true,
    };

    return result;
  } catch (error: any) {
    console.error("Error in TAT analysis:", error);
    return generateTATFallbackAnalysis(
      responses,
      `Error in TAT analysis: ${error.message || "Unknown error"}`
    );
  }
};

/**
 * Generate a fallback analysis for TAT responses
 * @param responses Array of user responses
 * @param errorMessage Optional error message to include in the feedback
 * @returns A basic analysis result
 */
const generateTATFallbackAnalysis = (
  responses: string[],
  errorMessage?: string
): AIAnalysisResult => {
  // Filter valid responses
  const validResponses = responses.filter(
    (response) => response && response.trim() !== ""
  );

  // Create response quality assessment
  const responseQualityAssessment: {
    [key: number]: {
      quality: "Good" | "Moderate" | "Bad";
      response: string;
      improvementSuggestions?: string[];
    };
  } = {};

  // Add basic assessment for each response
  validResponses.forEach((response, index) => {
    // Simple heuristic: longer responses are generally better for TAT
    const quality: "Good" | "Moderate" | "Bad" = 
      response.length > 200 ? "Good" : response.length > 100 ? "Moderate" : "Bad";

    responseQualityAssessment[index] = {
      quality: quality,
      response: response.substring(0, 50) + (response.length > 50 ? "..." : ""),
    };

    // Add improvement suggestions based on quality
    if (quality !== "Good") {
      if (quality === "Bad") {
        responseQualityAssessment[index].improvementSuggestions = [
          "Develop a more detailed narrative with character motivations",
          "Include military themes or leadership elements in your story",
          "Structure your response with a clear beginning, middle, and end",
        ];
      } else if (quality === "Moderate") {
        responseQualityAssessment[index].improvementSuggestions = [
          "Add more military context to your narrative",
          "Explore themes of duty, leadership, or teamwork in your story",
        ];
      }
    }
  });

  // Create traits
  const traits: { [key: string]: { score: number; feedback: string } } = {};
  militaryTraitsForAI.forEach((trait) => {
    traits[trait.name] = {
      score: 60,
      feedback: "Score estimated due to analysis limitations.",
    };
  });

  // Create the fallback analysis
  return {
    overallScore: 65,
    strengths: ["Emotional Intelligence", "Narrative Skills", "Perception"],
    improvements: ["Military Context", "Leadership Focus", "Structured Thinking"],
    detailedFeedback: errorMessage
      ? `Analysis could not be completed: ${errorMessage}. This is a fallback analysis.`
      : "This is a fallback analysis based on limited information.",
    traits,
    responseQualityAssessment,
    isAIGenerated: true,
  };
};
