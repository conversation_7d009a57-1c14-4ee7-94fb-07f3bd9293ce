"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import {
  ArrowLeftIcon,
  BookOpenIcon,
  XMarkIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  PhotoIcon,
  DocumentTextIcon,
  ClockIcon,
} from "@heroicons/react/24/outline";
import SimpleButton from "@/components/SimpleButton";
import { tatSampleAnswers } from "@/data/tat_sample_answers_updated";

// Constants for pagination
const ITEMS_PER_PAGE = 6;

// Helper function for pagination
const paginateStories = (
  stories: typeof tatSampleAnswers,
  currentPage: number,
  itemsPerPage: number
) => {
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const items = stories.slice(startIndex, endIndex);
  const totalPages = Math.ceil(stories.length / itemsPerPage);

  return {
    items,
    totalPages,
    currentPage,
    hasNextPage: currentPage < totalPages,
    hasPreviousPage: currentPage > 1,
  };
};

// Dynamically generate available sets from the data
const getAvailableSets = (data: typeof tatSampleAnswers) => {
  // Extract unique set names from the data
  const uniqueSets = [...new Set(data.map((item) => item.set))].sort();
  // Add "All Sets" option at the beginning
  return ["All Sets", ...uniqueSets];
};

// Get the available sets
const AVAILABLE_SETS = getAvailableSets(tatSampleAnswers);

export default function TATSampleAnswers() {
  const router = useRouter();
  const [selectedStory, setSelectedStory] = useState<
    (typeof tatSampleAnswers)[0] | null
  >(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedSet, setSelectedSet] = useState<string>("All Sets");

  // Filter stories by selected set
  const filteredStories =
    selectedSet === "All Sets"
      ? tatSampleAnswers
      : tatSampleAnswers.filter((story) => story.set === selectedSet);

  // Get paginated data
  const paginatedData = paginateStories(
    filteredStories,
    currentPage,
    ITEMS_PER_PAGE
  );

  // Effect to scroll to top when page changes
  useEffect(() => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }, [currentPage]);

  // Separate effect to reset page when set changes
  useEffect(() => {
    // Reset to page 1 when set changes
    setCurrentPage(1);
    // Scroll to top when set changes
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }, [selectedSet]);

  // Handle page navigation with scroll to top
  const handleNextPage = () => {
    if (currentPage < paginatedData.totalPages) {
      setCurrentPage(currentPage + 1);
      // Scroll is handled by the useEffect hook
    }
  };

  const handlePreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
      // Scroll is handled by the useEffect hook
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-6 sm:py-12">
      <div className="mx-auto max-w-7xl px-4 sm:px-6">
        {/* Navigation */}
        <div className="mb-8">
          <SimpleButton
            href="/tests/to/tat/practice"
            className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-red-600"
          >
            <ArrowLeftIcon className="h-5 w-5 mr-2" />
            Back to TAT Practice
          </SimpleButton>
        </div>

        {/* Page Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
            TAT Sample Stories
          </h1>
          <p className="mt-4 text-lg leading-8 text-gray-600 max-w-3xl mx-auto">
            This page provides sample stories for common TAT images. Each story
            demonstrates effective storytelling for TAT responses.
          </p>
        </div>

        {/* Set Selection */}
        {!selectedStory && (
          <div className="mb-8">
            <div className="bg-white rounded-xl shadow-sm p-4 border border-red-100">
              <h2 className="text-lg font-semibold text-gray-800 mb-3">
                Select a Set
              </h2>
              <div className="flex flex-wrap gap-2">
                {AVAILABLE_SETS.map((set) => (
                  <button
                    key={set}
                    onClick={() => setSelectedSet(set)}
                    className={`px-4 py-2 rounded-md transition-colors ${
                      selectedSet === set
                        ? "bg-red-600 text-white"
                        : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                    }`}
                  >
                    {set === "All Sets" ? (
                      set
                    ) : (
                      <span className="flex items-center">
                        {/* Dynamically select icon based on set number */}
                        {(() => {
                          // Extract set number if it follows the pattern "Set X"
                          const setMatch = set.match(/Set (\d+)/);
                          const setNumber = setMatch
                            ? parseInt(setMatch[1])
                            : 0;

                          // Choose icon based on set number (cycling through available icons)
                          switch (setNumber % 3) {
                            case 1:
                              return <PhotoIcon className="h-4 w-4 mr-1" />;
                            case 2:
                              return (
                                <DocumentTextIcon className="h-4 w-4 mr-1" />
                              );
                            case 0:
                              return <ClockIcon className="h-4 w-4 mr-1" />;
                            default:
                              return null;
                          }
                        })()}
                        {set}
                      </span>
                    )}
                  </button>
                ))}
              </div>
              <p className="mt-3 text-sm text-gray-500">
                {selectedSet === "All Sets"
                  ? "Showing all TAT sample stories"
                  : `Showing ${filteredStories.length} stories from ${selectedSet}`}
              </p>
            </div>
          </div>
        )}

        {/* Story Cards Grid */}
        {!selectedStory && (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-8">
              {paginatedData.items.map((story, index) => (
                <div
                  key={index}
                  className="bg-white rounded-xl shadow-md overflow-hidden border border-red-200 hover:shadow-lg transition-all duration-300 cursor-pointer"
                  onClick={() => setSelectedStory(story)}
                >
                  <div className="relative h-48 w-full bg-gray-100">
                    <Image
                      src={story.imageUrl}
                      alt={`TAT Image ${story.imageId}`}
                      fill
                      style={{ objectFit: "contain" }}
                      className="rounded-t-xl"
                    />
                  </div>
                  <div className="p-6">
                    <div className="inline-flex items-center justify-center p-2 rounded-full bg-red-100 text-red-700 mb-4">
                      <BookOpenIcon className="h-5 w-5" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-3">
                      TAT Story {story.imageId}
                    </h3>
                    <p className="text-gray-600 mb-4 line-clamp-4">
                      {story.story.substring(0, 150)}...
                    </p>
                    <div className="flex justify-end">
                      <button className="inline-flex items-center text-sm font-medium text-red-600 hover:text-red-800">
                        Read full story
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Pagination controls */}
            {paginatedData.totalPages > 1 && (
              <div className="flex justify-center items-center space-x-4 mt-8">
                <button
                  onClick={handlePreviousPage}
                  disabled={!paginatedData.hasPreviousPage}
                  className={`p-2 rounded-full ${
                    paginatedData.hasPreviousPage
                      ? "bg-red-100 text-red-700 hover:bg-red-200"
                      : "bg-gray-100 text-gray-400 cursor-not-allowed"
                  }`}
                >
                  <ChevronLeftIcon className="h-5 w-5" />
                </button>
                <span className="text-gray-700">
                  Page {currentPage} of {paginatedData.totalPages}
                </span>
                <button
                  onClick={handleNextPage}
                  disabled={!paginatedData.hasNextPage}
                  className={`p-2 rounded-full ${
                    paginatedData.hasNextPage
                      ? "bg-red-100 text-red-700 hover:bg-red-200"
                      : "bg-gray-100 text-gray-400 cursor-not-allowed"
                  }`}
                >
                  <ChevronRightIcon className="h-5 w-5" />
                </button>
              </div>
            )}

            {/* Bottom Return to Practice Button */}
            <div className="flex justify-center mt-12">
              <SimpleButton
                href="/tests/to/tat/practice"
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors flex items-center"
              >
                <ArrowLeftIcon className="h-4 w-4 mr-2" />
                Return to TAT Practice
              </SimpleButton>
            </div>
          </>
        )}

        {/* Detailed Story View */}
        {selectedStory && (
          <div className="bg-white rounded-xl shadow-md p-8 border border-gray-200 mb-12 relative">
            <button
              onClick={() => {
                setSelectedStory(null);
                // Scroll to top immediately
                window.scrollTo({
                  top: 0,
                  behavior: "smooth",
                });
              }}
              className="absolute top-4 right-4 p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
              aria-label="Close"
            >
              <XMarkIcon className="h-5 w-5 text-gray-600" />
            </button>

            {/* Set indicator */}
            <div className="absolute top-4 left-4 px-3 py-1 rounded-full bg-red-100 text-red-700 text-sm font-medium flex items-center">
              {/* Dynamically select icon based on set number */}
              {(() => {
                // Extract set number if it follows the pattern "Set X"
                const setMatch = selectedStory.set.match(/Set (\d+)/);
                const setNumber = setMatch ? parseInt(setMatch[1]) : 0;

                // Choose icon based on set number (cycling through available icons)
                switch (setNumber % 3) {
                  case 1:
                    return <PhotoIcon className="h-4 w-4 mr-1" />;
                  case 2:
                    return <DocumentTextIcon className="h-4 w-4 mr-1" />;
                  case 0:
                    return <ClockIcon className="h-4 w-4 mr-1" />;
                  default:
                    return null;
                }
              })()}
              {selectedStory.set}
            </div>
            <div className="mb-6">
              <div className="relative h-64 w-full max-w-2xl mx-auto mb-6 bg-gray-100 rounded-lg">
                <Image
                  src={selectedStory.imageUrl}
                  alt={`TAT Image ${selectedStory.imageId}`}
                  fill
                  style={{ objectFit: "contain" }}
                  className="rounded-lg"
                />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center">
                <div className="inline-flex items-center justify-center p-2 rounded-full bg-red-100 text-red-700 mr-3">
                  <BookOpenIcon className="h-6 w-6" />
                </div>
                TAT Story {selectedStory.imageId}
              </h2>
            </div>
            <div className="mb-8">
              <p className="text-gray-700 whitespace-pre-line">
                {selectedStory.story}
              </p>
            </div>
            <div className="flex justify-center">
              <button
                onClick={() => {
                  setSelectedStory(null);
                  // Scroll to top immediately
                  window.scrollTo({
                    top: 0,
                    behavior: "smooth",
                  });
                }}
                className="inline-flex items-center px-4 py-2 rounded-md bg-red-100 text-red-700 hover:bg-red-200 transition-colors"
              >
                <ArrowLeftIcon className="mr-2 h-4 w-4" />
                {selectedSet === "All Sets"
                  ? "Back to all stories"
                  : `Back to ${selectedSet} stories`}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
