import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import Navigation from "@/components/Navigation";
import Link from "next/link";
import ContentProtection from "@/components/ContentProtection";
import CopyProtectionWarning from "@/components/CopyProtectionWarning";
import ContentProtectionWrapper from "@/components/ContentProtectionWrapper";
import { LoadingProvider } from "@/context/LoadingContext";
import { GoogleAuthProvider } from "@/context/GoogleAuthContext";
import TopProgressBar from "@/components/TopProgressBar";
import CookieConsentWrapper from "@/components/CookieConsentWrapper";

import FirebaseInitializer from "@/components/FirebaseInitializer";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Military Selection Test Preparation",
  description:
    "Prepare for your military selection psychological tests (GunaMapak Test) in Nepal",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={`${inter.className} bg-white text-gray-800`}>
        <ContentProtection />
        <CopyProtectionWarning />
        <FirebaseInitializer />
        <TopProgressBar />
        <LoadingProvider>
          <GoogleAuthProvider>
            <ContentProtectionWrapper>
              <Navigation />
              <main className="min-h-screen">{children}</main>

              {/* Comprehensive Footer (appears on all pages) */}
              <footer className="bg-neutral-900 text-white py-12">
                <div className="mx-auto max-w-7xl px-6 lg:px-8">
                  {/* Main Footer Content */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12 border-t border-gray-800 pt-8">
                    {/* Quick Links */}
                    <div>
                      <h4 className="text-lg font-medium mb-4">Quick Links</h4>
                      <ul className="space-y-2 text-gray-400 text-sm">
                        <li>
                          <Link
                            href="/"
                            className="hover:text-white transition-colors"
                          >
                            Home
                          </Link>
                        </li>
                        <li>
                          <Link
                            href="/tests/to"
                            className="hover:text-white transition-colors"
                          >
                            Technical Officer Tests
                          </Link>
                        </li>
                        <li>
                          <Link
                            href="/tests/gto"
                            className="hover:text-white transition-colors"
                          >
                            Group Testing Officer
                          </Link>
                        </li>
                        <li>
                          <Link
                            href="/tests/io"
                            className="hover:text-white transition-colors"
                          >
                            Interviewing Officer
                          </Link>
                        </li>
                        <li>
                          <Link
                            href="/tests/bc"
                            className="hover:text-white transition-colors"
                          >
                            Board Conference
                          </Link>
                        </li>
                        <li>
                          <Link
                            href="/#about"
                            className="hover:text-white transition-colors"
                          >
                            About Us
                          </Link>
                        </li>
                      </ul>
                    </div>

                    {/* Nepal Army Section */}
                    <div>
                      <h4 className="text-lg font-medium mb-4">Nepal Army</h4>
                      <ul className="space-y-2 text-gray-400 text-sm">
                        <li>
                          <Link
                            href="/army/history"
                            className="hover:text-white transition-colors"
                          >
                            Historical Timeline
                          </Link>
                        </li>
                        <li>
                          <Link
                            href="/army/officer-qualities"
                            className="hover:text-white transition-colors"
                          >
                            Officer Like Qualities
                          </Link>
                        </li>
                        <li>
                          <Link
                            href="/army/ranks"
                            className="hover:text-white transition-colors"
                          >
                            Ranks and Structure
                          </Link>
                        </li>
                        <li>
                          <Link
                            href="/army/achievements"
                            className="hover:text-white transition-colors"
                          >
                            Modern Achievements
                          </Link>
                        </li>
                      </ul>
                    </div>

                    {/* Contact Information */}
                    <div>
                      <h4 className="text-lg font-medium mb-4">Contact Us</h4>
                      <ul className="space-y-2 text-gray-400 text-sm">
                        <li className="flex items-start">
                          <span className="mr-2">📍</span>
                          <span>Kathmandu, Nepal</span>
                        </li>
                        <li className="flex items-start">
                          <span className="mr-2">📧</span>
                          <a
                            href="mailto:<EMAIL>"
                            className="hover:text-white transition-colors"
                          >
                            <EMAIL>
                          </a>
                        </li>
                        <li className="flex items-start">
                          <span className="mr-2">📱</span>
                          <span>+977 9800000000</span>
                        </li>
                      </ul>
                    </div>
                  </div>

                  {/* Social Media Links */}
                  <div className="flex justify-center space-x-6 mb-8">
                    <a
                      href="https://www.facebook.com/hercules.shiwakoti"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-gray-400 hover:text-white transition-colors"
                    >
                      <span className="sr-only">Facebook</span>
                      <svg
                        className="h-6 w-6"
                        fill="currentColor"
                        viewBox="0 0 24 24"
                        aria-hidden="true"
                      >
                        <path
                          fillRule="evenodd"
                          d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </a>
                    <a
                      href="https://balaramshiwakoti.com.np"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-gray-400 hover:text-white transition-colors"
                    >
                      <span className="sr-only">Website</span>
                      <svg
                        className="h-6 w-6"
                        fill="currentColor"
                        viewBox="0 0 24 24"
                        aria-hidden="true"
                      >
                        <path
                          fillRule="evenodd"
                          d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.95-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </a>
                    <a
                      href="https://www.youtube.com"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-gray-400 hover:text-white transition-colors"
                    >
                      <span className="sr-only">YouTube</span>
                      <svg
                        className="h-6 w-6"
                        fill="currentColor"
                        viewBox="0 0 24 24"
                        aria-hidden="true"
                      >
                        <path
                          fillRule="evenodd"
                          d="M19.812 5.418c.861.23 1.538.907 1.768 1.768C21.998 8.746 22 12 22 12s0 3.255-.418 4.814a2.504 2.504 0 0 1-1.768 1.768c-1.56.419-7.814.419-7.814.419s-6.255 0-7.814-.419a2.505 2.505 0 0 1-1.768-1.768C2 15.255 2 12 2 12s0-3.255.417-4.814a2.507 2.507 0 0 1 1.768-1.768C5.744 5 11.998 5 11.998 5s6.255 0 7.814.418ZM15.194 12 10 15V9l5.194 3Z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </a>
                  </div>

                  {/* Copyright and Legal */}
                  <div className="border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center">
                    <p className="text-gray-400 text-sm mb-4 md:mb-0">
                      © {new Date().getFullYear()} MilSelect. All rights
                      reserved.
                    </p>
                    <div className="flex space-x-6 text-sm text-gray-400">
                      <Link
                        href="/privacy"
                        className="hover:text-white transition-colors"
                      >
                        Privacy Policy
                      </Link>
                      <Link
                        href="/terms"
                        className="hover:text-white transition-colors"
                      >
                        Terms of Service
                      </Link>
                      <Link
                        href="/cookies"
                        className="hover:text-white transition-colors"
                      >
                        Cookie Policy
                      </Link>
                    </div>
                  </div>
                </div>
              </footer>
              <CookieConsentWrapper />
            </ContentProtectionWrapper>
          </GoogleAuthProvider>
        </LoadingProvider>
      </body>
    </html>
  );
}
