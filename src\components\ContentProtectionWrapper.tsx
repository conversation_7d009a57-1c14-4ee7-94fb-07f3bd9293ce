"use client";

import React, { useEffect, useState } from "react";
import { replaceImagesWithProtectedImages } from "@/utils/replaceImages";

interface ContentProtectionWrapperProps {
  children: React.ReactNode;
}

export default function ContentProtectionWrapper({
  children,
}: ContentProtectionWrapperProps) {
  // Use state to track client-side rendering
  const [isClient, setIsClient] = useState(false);

  // Only process children on the client side to avoid hydration mismatch
  const renderedChildren = isClient
    ? replaceImagesWithProtectedImages(children)
    : children;

  useEffect(() => {
    // Mark as client-side rendered
    setIsClient(true);

    // Add additional protection for dynamically loaded images
    const protectDynamicImages = () => {
      const images = document.querySelectorAll("img");
      images.forEach((img) => {
        // Prevent right-click
        img.addEventListener("contextmenu", (e) => e.preventDefault());

        // Prevent drag
        img.setAttribute("draggable", "false");

        // Prevent pointer events
        img.style.pointerEvents = "none";
      });
    };

    // Apply protection initially
    protectDynamicImages();

    // Set up observer to protect images that are added dynamically
    const observer = new MutationObserver(protectDynamicImages);
    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });

    return () => {
      observer.disconnect();
    };
  }, []);

  return <>{renderedChildren}</>;
}
