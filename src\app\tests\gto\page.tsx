"use client";

import Link from "next/link";
import NavigationButton from "@/components/NavigationButton";
import {
  UserGroupIcon,
  MapIcon,
  PuzzlePieceIcon,
  FlagIcon,
  UserIcon,
  MegaphoneIcon,
  BoltIcon,
  CommandLineIcon,
  CheckCircleIcon,
  ArrowRightIcon,
} from "@heroicons/react/24/outline";

// Speech, Discussion, and Solution tasks
const speechDiscussionTasks = [
  {
    name: "Group Discussion (GD)",
    description:
      "Topic-based discussion to assess communication and leadership",
    icon: UserGroupIcon,
    color: "blue",
    href: "/tests/gto/gd",
    shortName: "gd",
  },
  {
    name: "Group Planning Exercise (GPE)",
    description: "Map-based problem-solving exercise",
    icon: MapIcon,
    color: "green",
    href: "/tests/gto/gpe",
    shortName: "gpe",
  },
  {
    name: "Lecturette",
    description: "Brief individual speech task",
    icon: MegaphoneIcon,
    color: "indigo",
    href: "/tests/gto/lecturette",
    shortName: "lecturette",
  },
];

// Physical and Ground Activity tasks
const physicalGroundTasks = [
  {
    name: "Progressive Group Task (PGT)",
    description: "Obstacle course requiring group effort",
    icon: PuzzlePieceIcon,
    color: "purple",
    href: "/tests/gto/pgt",
    shortName: "pgt",
  },
  {
    name: "Snake Race",
    description: "Competitive group obstacle race",
    icon: FlagIcon,
    color: "red",
    href: "/tests/gto/snake-race",
    shortName: "snake-race",
  },
  {
    name: "Half Group Task (HGT)",
    description: "Smaller group obstacle task",
    icon: UserIcon,
    color: "amber",
    href: "/tests/gto/hgt",
    shortName: "hgt",
  },
  {
    name: "Individual Obstacles ",
    description: "Timed individual obstacle course",
    icon: BoltIcon,
    color: "cyan",
    href: "/tests/gto/individual-obstacles",
    shortName: "individual-obstacles",
  },
  {
    name: "Command Task (CT)",
    description: "Task where one candidate leads others",
    icon: CommandLineIcon,
    color: "emerald",
    href: "/tests/gto/command-task",
    shortName: "command-task",
  },
  {
    name: "Final Group Task (FGT)",
    description: "Final group obstacle task",
    icon: CheckCircleIcon,
    color: "teal",
    href: "/tests/gto/fgt",
    shortName: "fgt",
  },
];

export default function GTOPage() {
  const getColorClasses = (color: string) => {
    const colorMap: Record<string, string> = {
      blue: "bg-white hover:bg-blue-50 ring-blue-200 hover:ring-blue-300",
      green: "bg-white hover:bg-green-50 ring-green-200 hover:ring-green-300",
      purple:
        "bg-white hover:bg-purple-50 ring-purple-200 hover:ring-purple-300",
      red: "bg-white hover:bg-red-50 ring-red-200 hover:ring-red-300",
      amber: "bg-white hover:bg-amber-50 ring-amber-200 hover:ring-amber-300",
      indigo:
        "bg-white hover:bg-indigo-50 ring-indigo-200 hover:ring-indigo-300",
      cyan: "bg-white hover:bg-cyan-50 ring-cyan-200 hover:ring-cyan-300",
      emerald:
        "bg-white hover:bg-emerald-50 ring-emerald-200 hover:ring-emerald-300",
      teal: "bg-white hover:bg-teal-50 ring-teal-200 hover:ring-teal-300",
    };

    return `transition-all duration-300 ${colorMap[color] || ""}`;
  };

  const getIconColorClass = (color: string) => {
    const colorMap: Record<string, string> = {
      blue: "text-blue-600",
      green: "text-green-600",
      purple: "text-purple-600",
      red: "text-red-600",
      amber: "text-amber-600",
      indigo: "text-indigo-600",
      cyan: "text-cyan-600",
      emerald: "text-emerald-600",
      teal: "text-teal-600",
    };

    return colorMap[color] || "text-gray-600";
  };

  const getIconBgClass = (color: string) => {
    const colorMap: Record<string, string> = {
      blue: "bg-blue-100",
      green: "bg-green-100",
      purple: "bg-purple-100",
      red: "bg-red-100",
      amber: "bg-amber-100",
      indigo: "bg-indigo-100",
      cyan: "bg-cyan-100",
      emerald: "bg-emerald-100",
      teal: "bg-teal-100",
    };

    return colorMap[color] || "bg-gray-100";
  };

  const getBorderClass = (color: string) => {
    const colorMap: Record<string, string> = {
      blue: "border-t-4 border-blue-500",
      green: "border-t-4 border-green-500",
      purple: "border-t-4 border-purple-500",
      red: "border-t-4 border-red-500",
      amber: "border-t-4 border-amber-500",
      indigo: "border-t-4 border-indigo-500",
      cyan: "border-t-4 border-cyan-500",
      emerald: "border-t-4 border-emerald-500",
      teal: "border-t-4 border-teal-500",
    };

    return colorMap[color] || "";
  };

  return (
    <div className="bg-white py-16 sm:py-24">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-3xl text-center mb-16">
          <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
            Group Testing Officer Test (GTO)
          </h2>
          <p className="mt-4 text-lg leading-8 text-gray-600">
            A comprehensive two-day assessment of your group dynamics,
            problem-solving abilities, and leadership potential.
          </p>
          <div className="mt-6 text-sm text-gray-500">
            <p>Click on any task card to learn more about each GTO task.</p>
          </div>
        </div>

        {/* Speech, Discussion, and Solution Tasks Section */}
        <div className="mb-16">
          <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">
            Speech, Discussion, and Solution Tasks
          </h3>
          <p className="text-center text-gray-600 mb-8 max-w-3xl mx-auto">
            These tasks focus on verbal communication, problem-solving, and
            presentation skills. They involve providing speeches, discussing
            topics, and developing solutions.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-16">
            {speechDiscussionTasks.map((task) => (
              <NavigationButton
                key={task.name}
                href={task.href}
                className={`rounded-xl p-6 ring-2 shadow-sm cursor-pointer ${getColorClasses(
                  task.color
                )} ${getBorderClass(task.color)}`}
              >
                <div className="flex items-center space-x-3">
                  <div
                    className={`p-2 rounded-full ${getIconBgClass(task.color)}`}
                  >
                    <task.icon
                      className={`h-6 w-6 ${getIconColorClass(task.color)}`}
                    />
                  </div>
                  <h3 className="text-lg font-semibold leading-8 text-gray-900">
                    {task.name}
                  </h3>
                </div>
                <p className="mt-2 text-sm leading-6 text-gray-600">
                  {task.description}
                </p>

                <div className="mt-4 flex justify-end">
                  <span
                    className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${getIconBgClass(
                      task.color
                    )} ${getIconColorClass(task.color)}`}
                  >
                    Learn more <ArrowRightIcon className="ml-1 h-3 w-3" />
                  </span>
                </div>
              </NavigationButton>
            ))}
          </div>

          {/* Physical and Ground Activity Tasks Section */}
          <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">
            Physical and Ground Activity Tasks
          </h3>
          <p className="text-center text-gray-600 mb-8 max-w-3xl mx-auto">
            These tasks involve physical activities performed on the ground,
            either individually or in groups. They test physical fitness,
            coordination, and teamwork in practical scenarios.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {physicalGroundTasks.map((task) => (
              <NavigationButton
                key={task.name}
                href={task.href}
                className={`rounded-xl p-6 ring-2 shadow-sm cursor-pointer ${getColorClasses(
                  task.color
                )} ${getBorderClass(task.color)}`}
              >
                <div className="flex items-center space-x-3">
                  <div
                    className={`p-2 rounded-full ${getIconBgClass(task.color)}`}
                  >
                    <task.icon
                      className={`h-6 w-6 ${getIconColorClass(task.color)}`}
                    />
                  </div>
                  <h3 className="text-lg font-semibold leading-8 text-gray-900">
                    {task.name}
                  </h3>
                </div>
                <p className="mt-2 text-sm leading-6 text-gray-600">
                  {task.description}
                </p>

                <div className="mt-4 flex justify-end">
                  <span
                    className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${getIconBgClass(
                      task.color
                    )} ${getIconColorClass(task.color)}`}
                  >
                    Learn more <ArrowRightIcon className="ml-1 h-3 w-3" />
                  </span>
                </div>
              </NavigationButton>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
